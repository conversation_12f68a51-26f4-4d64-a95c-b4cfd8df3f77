import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:newarc_platform/classes/renovationContact.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

class CustomDropdown2 extends StatefulWidget {
  final bool isMaster;
  final RenovationContact renovationContact;
  final Function updateStage;
  double? width = 32;
  double? height = 150;
  double? fontSize = 12;

  CustomDropdown2({
    Key? key,
    required this.isMaster,
    required this.renovationContact,
    required this.updateStage,
    this.width,
    this.height,
    this.fontSize,
  }) : super(key: key);

  @override
  State<CustomDropdown2> createState() => _CustomButtonTestState();
}

class _CustomButtonTestState extends State<CustomDropdown2> {
  String? contactStatus;
  GlobalKey? dropdownkey = new GlobalKey();

  @override
  void initState() {
    contactStatus = widget.renovationContact.contactStatus!
        .toCapitalized()
        .replaceAll('-', ' ');
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return DropdownButtonHideUnderline(
      child: DropdownButton2(
        isExpanded: true,
        customButton: Container(
          height: widget.height,
          width: widget.width,
          padding: const EdgeInsets.symmetric(horizontal: 8),
          decoration: BoxDecoration(
            color: Color(0xffededed),
            borderRadius: BorderRadius.circular(7),
          ),
          child: Container(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: NarFormLabelWidget(
                      label: contactStatus!,
                      textColor: Colors.black,
                      fontWeight: '500',
                      fontSize: widget.fontSize),
                ),
                SvgPicture.asset(
                  'assets/icons/arrow_down.svg',
                  width: 10,
                  color: Colors.black,
                )
              ],
            ),
          ),
        ),
        items: [
          ...MenuItems.firstItems.map(
            (item) => DropdownMenuItem<MenuItem>(
              value: item,
              child: MenuItems.buildItem(item),
            ),
          ),
        ],
        onChanged: (value) {
          value as MenuItem;
          setState(() {
            widget.renovationContact.contactStatus = value.text;
            contactStatus = value.text;
          });
          MenuItems.onChanged(
              context, value, widget.renovationContact, widget.updateStage);
        },
        menuItemStyleData: MenuItemStyleData(
          height: 48,
          padding: const EdgeInsets.only(left: 16, right: 16),

        ),
        dropdownStyleData: DropdownStyleData(
          width: 200,
          padding: const EdgeInsets.symmetric(vertical: 6),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(7),
            color: Colors.white,
          ),
          elevation: 8,
          offset: const Offset(0, 8),
        )
      ),
    );
  }
}

class MenuItem {
  final String text;
  final IconData icon;

  const MenuItem({
    required this.text,
    required this.icon,
  });
}

class MenuItems {
  static const firstEncounter =
      MenuItem(text: 'Primo incontro', icon: Icons.home);
  static const toQuote = MenuItem(text: 'Da prev.', icon: Icons.share);
  static const quoteSent =
      MenuItem(text: 'Prev. inviato', icon: Icons.settings);

  static const quoteRefused =
      MenuItem(text: 'Prev. rifiutato', icon: Icons.logout);
  static const acquired = MenuItem(text: 'Acquisito', icon: Icons.logout);

  static const List<MenuItem> emptyList = [];

  static const List<MenuItem> firstItems = [
    firstEncounter,
    toQuote,
    quoteSent,
    quoteRefused,
    acquired
  ];

  static Widget buildItem(MenuItem item) {
    return NarFormLabelWidget(
      label: item.text,
      textColor: Colors.black,
      fontSize: 12,
      fontWeight: '500',
    );
  }

  static onChanged(BuildContext context, MenuItem item,
      RenovationContact renovationContact, Function updateStage) {
    updateStage(renovationContact, item.text);
    return;
  }
}
