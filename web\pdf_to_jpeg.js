window.convertPdfToJpeg = async function (pdfBytes, zipName, filename) {
    const pdf = await pdfjsLib.getDocument({data: pdfBytes}).promise;
    const zip = new JSZip();

    for (let pageNumber = 1; pageNumber <= pdf.numPages; pageNumber++) {
        const page = await pdf.getPage(pageNumber);
        const scale = 1.0;
        const viewport = page.getViewport({scale: scale});

        const canvas = document.createElement("canvas");
        const context = canvas.getContext("2d");
        canvas.height = viewport.height;
        canvas.width = viewport.width;

        const renderContext = {canvasContext: context, viewport: viewport};
        await page.render(renderContext).promise;

        const imgDataUrl = canvas.toDataURL("image/jpeg", 0.85);
        const imgBase64 = imgDataUrl.split(',')[1]; // Extract base64 data
        zip.file(`${filename}_${pageNumber}.jpeg`, imgBase64, {base64: true});
    }

    zip.generateAsync({type: "blob"}).then(function (content) {
        const link = document.createElement("a");
        link.href = URL.createObjectURL(content);
        link.download = zipName+"_portali.zip";
        link.click();
    });
};


window.zipAndDownloadFiles = async function (fileUrls, zipName, assignFilename) {
    if (!Array.isArray(fileUrls)) {
        console.error("fileUrls is not an array", fileUrls);
        return;
    }

    const zip = new JSZip();

    // Process each file URL
    const fetchPromises = fileUrls.map(async (fileUrl, index) => {
        try {
            const response = await fetch(fileUrl);
            if (!response.ok) throw new Error(`Failed to fetch: ${fileUrl}`);

            const blob = await response.blob();
            
            const cleanUrl = new URL(fileUrl).pathname;
            
            const filename = cleanUrl.substring(cleanUrl.lastIndexOf('/') + 1);
            const extension = filename.includes('.') ? filename.split('.').pop().split('?')[0] : 'jpg';
            
            zip.file(`${assignFilename}_${index + 1}.${extension}`, blob);
        } catch (error) {
            console.error("Error fetching file:", fileUrl, error);
        }
    });

    // Wait for all fetch requests to complete
    await Promise.all(fetchPromises);

    // Generate and download the ZIP file
    zip.generateAsync({ type: "blob" }).then((content) => {
        const link = document.createElement("a");
        link.href = URL.createObjectURL(content);
        link.download = `${zipName}_files.zip`;
        link.click();
    });
};

window.convertPdfBytesToJpegArrays = async function (pdfBytes, quality = 0.5, scale = 1.0) {
    const pdf = await pdfjsLib.getDocument({ data: pdfBytes }).promise;
    const result = [];

    for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
      const page = await pdf.getPage(pageNum);
      const viewport = page.getViewport({ scale });

      const canvas = document.createElement('canvas');
      canvas.width = viewport.width;
      canvas.height = viewport.height;
      const ctx = canvas.getContext('2d');

      await page.render({ canvasContext: ctx, viewport }).promise;

      const dataUrl = canvas.toDataURL('image/jpeg', quality);
      const base64 = dataUrl.split(',')[1];
      result.push(base64);
    }

    await pdf.destroy();
    return result;
 };