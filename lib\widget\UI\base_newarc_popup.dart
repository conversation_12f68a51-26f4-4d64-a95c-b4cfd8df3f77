import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

class BaseNewarcPopup extends StatefulWidget {
  final String? title;
  final Widget? column;
  final String? buttonText;
  final String? secondButtonText;
  final bool? noButton;
  final bool? isSecondButtonVisible;
  final Function? onPressed;
  final Function? onPressedSecondButton;
  final List<String> formErrorMessage;
  final bool? disableButton;
  final bool? disableSecondButton;
  final bool? isShowCloseIcon;
  final Color? buttonColor;
  final Color? buttonTextColor;
  final Color? secondButtonColor;
  final TextAlign? titleTextAlign;
  final double borderRadius;
  final double horizontalMargin;
  final Color closeIconColor;
  final Color errorMessageColor;
  final bool isLoading;
  final double bottomPadding;

  const BaseNewarcPopup({
    required this.title,
    required this.column,
    this.buttonText,
    this.secondButtonText,
    this.onPressed,
    this.onPressedSecondButton,
    this.noButton = false,
    this.isSecondButtonVisible = false,
    this.formErrorMessage = const [],
    this.disableButton = false,
    this.disableSecondButton = false,
    this.isShowCloseIcon = true,
    this.buttonColor,
    this.buttonTextColor = Colors.white,
    this.secondButtonColor,
    this.titleTextAlign,
    this.borderRadius = 15,
    this.horizontalMargin = 20,
    this.closeIconColor = Colors.black,
    this.errorMessageColor = Colors.black,
    this.isLoading = false,
    this.bottomPadding = 20,
    Key? key,
  }) : super(key: key);

  @override
  State<BaseNewarcPopup> createState() => _BaseNewarcPopupState();
}

class _BaseNewarcPopupState extends State<BaseNewarcPopup> {
  TextStyle sectionTitleStyle =
      TextStyle(fontSize: 16, fontWeight: FontWeight.bold);

  final _formKey = GlobalKey<FormState>();
  bool loading = false;
  bool clicked = false;
  List<String>? formErrorMessage;
  bool disableButton = false;


  bool showOptionMissing(String? option) {
    try {
      return (option == null || option.isEmpty) && clicked;
    } catch (e) {
      // print({e,s});
      return false;
    }
  }

  @override
  void initState() {
    formErrorMessage = widget.formErrorMessage;
    super.initState();
  }

  @override
  void didUpdateWidget(BaseNewarcPopup oldWidget) {
    super.didUpdateWidget(oldWidget);
    formErrorMessage = widget.formErrorMessage;
    disableButton = widget.disableButton!;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: widget.horizontalMargin,
      ),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        child: Stack(
                children: [
                  widget.isShowCloseIcon ?? true
                    ? (widget.isLoading && loading) 
                      ? SizedBox.shrink()
                      : Positioned(
                        top: 15,
                        right: 15,
                        child: Container(
                          height: 15,
                          width: 15,
                          child: MouseRegion(
                            cursor: SystemMouseCursors.click,
                            child: GestureDetector(
                                child: SvgPicture.asset(
                                  'assets/icons/close-popup.svg',
                                  width: 15,
                                  color: widget.closeIconColor,
                                ),
                                onTap: () {
                                  Navigator.pop(context);
                                }),
                          ),
                        ),
                      ) 
                    : SizedBox.shrink(),
                  Form(
                    key: _formKey,
                    child: Padding(
                      padding: EdgeInsets.only(bottom:  widget.bottomPadding, top: widget.title != '' ? 20 : 0 ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if( widget.title != '' ) Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: 20,
                            ),
                            child: NarFormLabelWidget(
                              label: widget.title,
                              textColor: Colors.black,
                              fontSize: 17,
                              fontWeight: '700',
                              textAlign: widget.titleTextAlign,
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(
                              left: 40.0,
                              right: 40.0,
                              top: 25,
                            ),
                            child: (loading && widget.isLoading) ? Center(
                              child: CircularProgressIndicator(color: Theme.of(context).primaryColor))
                            : widget.column,
                          ),
                          SizedBox(height: 10),
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              formErrorMessage!.length == 0 || formErrorMessage![0] == ''
                              ? Container()
                              : NarFormLabelWidget(
                                label: formErrorMessage!.length > 0
                                    ? formErrorMessage!.join('')
                                    : '',
                                fontSize: 12,
                                fontWeight: 'bold',
                                textColor: widget.errorMessageColor,
                              ),
                            ],
                          ),
                          SizedBox(height: 5),
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              widget.noButton!
                                  ? Container()
                                  : Row(
                                    children: [
                                      (loading && widget.isLoading) ? Container()
                                      : BaseNewarcButton(
                                          color: widget.buttonColor,
                                          disableButton: disableButton,
                                          notAccent: disableButton,
                                          textColor: widget.buttonTextColor,
                                          buttonText: widget.buttonText == null
                                              ? 'OK'
                                              : widget.buttonText,
                                          onPressed: () async {
                                            if (_formKey.currentState!.validate()) {
                                              setState(() {
                                                loading = true;
                                              });
                                              try {
                                                if (widget.onPressed != null) {
                                                  bool response =
                                                      await widget.onPressed!();
                                                  if (response) {
                                                    Navigator.of(context)
                                                        .pop(response);
                                                  } else if (response == false) {
                                                    setState(() {
                                                      loading = false;
                                                    });
                                                    print("BaseNewarcPopup: No response");
                                                    // Navigator.of(context).pop(false);
                                                  } else {
                                                    Navigator.of(context)
                                                        .pop(response);
                                                  }
                                                } else {
                                                  Navigator.of(context).pop(true);
                                                }
                                              } catch (e) {
                                                setState(() {
                                                  loading = false;
                                                });
                                                Navigator.of(context).pop(true);
                                              }
                                            } else {
                                              setState(() {
                                                formErrorMessage!.clear();
                                                formErrorMessage!.add('Controlla tutti i campi.');
                                              });
                                            }
                                          }),

                                      widget.isSecondButtonVisible ?? false ?
                                      Padding(
                                        padding: const EdgeInsets.only(left: 10),
                                        child: BaseNewarcButton(
                                            color: widget.secondButtonColor,
                                            disableButton: widget.disableSecondButton,
                                            notAccent: widget.disableSecondButton,
                                            buttonText: widget.secondButtonText == null
                                                ? 'OK'
                                                : widget.secondButtonText,
                                            onPressed: () async {
                                              if (_formKey.currentState!.validate()) {
                                                setState(() {
                                                  loading = true;
                                                });
                                                try {
                                                  if (widget.onPressedSecondButton != null) {
                                                    bool response =
                                                    await widget.onPressedSecondButton!();
                                                    if (response) {
                                                      Navigator.of(context)
                                                          .pop(response);
                                                    } else if (response == false) {
                                                      setState(() {
                                                        loading = false;
                                                      });
                                                      print("BaseNewarcPopup: No response");
                                                      // Navigator.of(context).pop(false);
                                                    } else {
                                                      Navigator.of(context)
                                                          .pop(response);
                                                    }
                                                  } else {
                                                    Navigator.of(context).pop(true);
                                                  }
                                                } catch (e) {
                                                  setState(() {
                                                    loading = false;
                                                  });
                                                  Navigator.of(context).pop(true);
                                                }
                                              } else {
                                                setState(() {
                                                  formErrorMessage!.clear();
                                                  formErrorMessage!.add('Controlla tutti i campi.');
                                                });
                                              }
                                            }),
                                      ) : Container()
                                    ],
                                  )
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}
