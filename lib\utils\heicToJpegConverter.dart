import 'dart:async';
import 'dart:typed_data';
import 'dart:js_interop';
import 'package:web/web.dart' as web;


@JS('convertHeicToJpeg')
external JSPromise _convertHeicToJpeg(web.Blob heicFile);

class HeicToJpegService {
  /// Convert Blob to Uint8List
  Future<Uint8List> _blobToUint8List(web.Blob blob) {
    final completer = Completer<Uint8List>();
    final reader = web.FileReader();

    // Success handler
    reader.onloadend = ((JSAny? _) {
      final result = reader.result as JSArrayBuffer;
      final buffer = result.toDart; // JSArrayBuffer → ByteBuffer
      completer.complete(Uint8List.view(buffer));
    }).toJS;

    // Error handler
    reader.onerror = ((JSAny? _) {
      completer.completeError(reader.error?.toString() ?? 'Unknown error');
    }).toJS;

    reader.readAsArrayBuffer(blob);

    return completer.future;
  }

  /// Convert HEIC → JPEG
  Future<Uint8List> convertHeicToJpeg(Uint8List fileBytes) async {
    // Wrap Dart bytes in a Blob
    final blob = web.Blob([fileBytes.toJS].toJS);

    // Call the JS function (returns JSPromise<Blob>)
    final convertedBlob = await _convertHeicToJpeg(blob).toDart as web.Blob;

    // Convert Blob → Uint8List
    return _blobToUint8List(convertedBlob);
  }
}