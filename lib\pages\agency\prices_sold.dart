
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/proposedEstate.dart';
import 'package:newarc_platform/functions/various.dart';
import 'package:newarc_platform/widget/agency/prezzi_venduto_popup.dart';


class PricesSoldView extends StatefulWidget {
  final Agency agency;
  final AgencyUser agencyUser;
  final responsive;

  const PricesSoldView(
      {Key? key,
      required this.agency,
      required this.agencyUser,
      required this.responsive})
      : super(key: key);

  @override
  State<PricesSoldView> createState() => _PricesSoldViewwState();
}

class _PricesSoldViewwState extends State<PricesSoldView> {
  String query = "";

  @override
  void initState() {
    initialFetch(widget.agency);
    super.initState();
  }

  getColor(String status) {
    switch (status) {
      case 'Da contattare':
        return Color(0xff5FBCEC);
      case 'Contattato':
        return Color(0xffFFC633);
      case 'Non interessato':
        return Color(0xffFF5E53);
      case 'Acquisito':
        return Color(0xff489B79);
    }
  }

  List<DropdownMenuItem> buildDropdownTestItems(List _testList) {
    List<DropdownMenuItem> items = [];
    for (var i in _testList) {
      items.add(
        DropdownMenuItem(
          value: i,
          child: Container(
            decoration: BoxDecoration(
              color: getColor(i['keyword']),
              borderRadius: BorderRadius.circular(8),
            ),
            padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
            child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    i['keyword'],
                    style: TextStyle(
                        color: Colors.white, fontWeight: FontWeight.w600),
                  ),
                ]),
          ),
        ),
      );
    }
    return items;
  }

  bool loading = true;
  List<ProposedEstate> estates = [];
  late List<DocumentSnapshot> documentList;
  late int totalRecords = 0;
  String currentlyShowing = '';
  final recordsPerPage = 20;
  late int pageCounter = 0;
  int totalPages = 0;
  bool disablePreviousButton = true;
  bool disableNextButton = false;
  List<dynamic> cacheFirestore = [];

  Widget dataTablePagination() {
    return Padding(
        padding: EdgeInsets.symmetric(vertical: 10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
                "Page ${pageCounter.toString()} of ${totalPages} Pages, Showing ${currentlyShowing} of Total  ${totalRecords.toString()}"),
            SizedBox(width: 5),
            TextButton(
              child: Icon(Icons.arrow_back_ios,
                  size: 20,
                  color: disablePreviousButton ? Colors.grey : Colors.black),
              onPressed: () {
                if (disablePreviousButton == true) return;
                fetchPrev(widget.agency);
              },
            ),
            // SizedBox(width: 5),
            TextButton(
              child: Icon(Icons.arrow_forward_ios,
                  size: 20,
                  color: disableNextButton ? Colors.grey : Colors.black),
              onPressed: () {
                if (disableNextButton == true) return;
                fetchNext(widget.agency);
              },
            ),
            TextButton(
              child: Icon(Icons.refresh,
                  size: 20,
                  color: disableNextButton ? Colors.grey : Colors.black),
              onPressed: () {
                // if (disableNextButton == true) return;
                // fetchNextContacts(widget.agency);
                cacheFirestore.clear();
                initialFetch(widget.agency);
              },
            )
          ],
        ));
  }

  List<DataRow> loadingRow() {
    List<DataCell> list = [];

    list.add(DataCell(Text('')));
    list.add(DataCell(Text('')));
    list.add(DataCell(Padding(
        padding: EdgeInsets.symmetric(vertical: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(
              color: Theme.of(context).primaryColor,
            ),
            Text(
              'Loading',
              style: TextStyle(fontSize: 15, fontWeight: FontWeight.w900),
              textAlign: TextAlign.center,
            )
          ],
        ))));
    list.add(DataCell(Text('')));
    list.add(DataCell(Text('')));

    return [DataRow(cells: list)];
  }

  Future<void> initialFetch(Agency agency) async {
    QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
    QuerySnapshot<Map<String, dynamic>> collectionSnapshotCounter;

    try {
      if (widget.agencyUser.role == 'master') {
        collectionSnapshot = await FirebaseFirestore.instance
            .collection('soldbyagencies')
            .orderBy('insertion_timestamp', descending: true)
            .limit(20)
            .where('agencyId', isEqualTo: agency.id)
            .get();
        collectionSnapshotCounter = await FirebaseFirestore.instance
            .collection('soldbyagencies')
            .orderBy('insertion_timestamp', descending: true)
            .where('agencyId', isEqualTo: agency.id)
            .get();
      } else {
        collectionSnapshot = await FirebaseFirestore.instance
            .collection('soldbyagencies')
            .orderBy('insertion_timestamp', descending: true)
            .limit(20)
            .get();
        collectionSnapshotCounter = await FirebaseFirestore.instance
            .collection('soldbyagencies')
            .orderBy('insertion_timestamp', descending: true)
            .get();
      }

      totalRecords = collectionSnapshotCounter.docs.length;

      totalPages = (totalRecords / recordsPerPage).ceil();

      if (totalRecords > recordsPerPage) {
        currentlyShowing = '1-' + recordsPerPage.toString();
      }

      documentList = collectionSnapshot.docs;
      generateContacts(collectionSnapshot);
    } catch (e) {
      setState(() {
        loading = false;
      });
    }
  }

  fetchNext(Agency agency) async {
    setState(() {
      loading = true;
    });

    pageCounter++;

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      int indexOfSnapshot = isRecordExists(pageCounter);

      print('Next Page');

      if (indexOfSnapshot > -1) {
        print('Record Exists');
        collectionSnapshot = cacheFirestore[indexOfSnapshot]['snapshot'];
      } else {
        print('Fetch New Record');
        if (widget.agencyUser.role != 'master') {
          collectionSnapshot = await FirebaseFirestore.instance
              .collection('soldbyagencies')
              .orderBy('insertion_timestamp', descending: true)
              .limit(recordsPerPage)
              .where('assignedAgencyId', isEqualTo: agency.id)
              .startAfterDocument(documentList[documentList.length - 1])
              .get();
        } else {
          collectionSnapshot = await FirebaseFirestore.instance
              .collection('soldbyagencies')
              .orderBy('insertion_timestamp', descending: true)
              .startAfterDocument(documentList[documentList.length - 1])
              .limit(recordsPerPage)
              .get();
        }
      }

      documentList = collectionSnapshot.docs;

      generateContacts(collectionSnapshot);
    } catch (e) {
      setState(() {
        loading = false;
      });
      print(e.toString());
    }
  }

  fetchPrev(Agency agency) async {
    setState(() {
      loading = true;
    });

    pageCounter--;

    print('Prev Page');

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      int indexOfSnapshot = isRecordExists(pageCounter);
      if (indexOfSnapshot > -1) {
        print('Record exists');
        collectionSnapshot = cacheFirestore[indexOfSnapshot]['snapshot'];
      } else {
        print('Fetch new record');
        if (widget.agencyUser.role != 'master') {
          collectionSnapshot = await FirebaseFirestore.instance
              .collection('soldbyagencies')
              .orderBy('insertion_timestamp', descending: true)
              .where('assignedAgencyId', isEqualTo: agency.id)
              .endBeforeDocument(documentList[documentList.length - 1])
              .limit(recordsPerPage)
              .get();
        } else {
          collectionSnapshot = await FirebaseFirestore.instance
              .collection('soldbyagencies')
              .orderBy('insertion_timestamp', descending: true)
              .endBeforeDocument(documentList[documentList.length - 1])
              .limit(recordsPerPage)
              .get();
        }
      }

      documentList = collectionSnapshot.docs;
      generateContacts(collectionSnapshot);
    } catch (e) {
      setState(() {
        loading = false;
      });
      print(e.toString());
    }
  }

  int isRecordExists(pageCounter) {
    int isRecordExists =
        cacheFirestore.indexWhere((record) => record['key'] == pageCounter);
    return isRecordExists;
  }

  generateContacts(collectionSnapshot) {
    // If a record already doesn't exists then store
    if (isRecordExists(pageCounter) < 0) {
      cacheFirestore.add({'key': pageCounter, 'snapshot': collectionSnapshot});
    }

    if (pageCounter <= 1) {
      disablePreviousButton = true;
    } else {
      disablePreviousButton = false;
    }

    if (pageCounter == totalPages || totalPages == 1) {
      disableNextButton = true;
    } else {
      disableNextButton = false;
    }

    List<ProposedEstate> _estates = [];
    for (var element in collectionSnapshot.docs) {
      try {
        var _tmp = ProposedEstate.fromDocument(element.data(), element.id);
        print(_tmp.toJson());
        _estates.add(_tmp);
      } catch (e) {
        print("error in document ${element.id}");
        print(e);
      }
    }

    _estates
        .sort((a, b) => b.insertionTimestamp!.compareTo(a.insertionTimestamp!));

    int lastRecordNumber = pageCounter * recordsPerPage;
    if (totalPages == 1) {
      currentlyShowing = "1" + '-' + _estates.length.toString();
    } else if (_estates.length == recordsPerPage) {
      currentlyShowing = (lastRecordNumber - (recordsPerPage - 1)).toString() +
          '-' +
          lastRecordNumber.toString();
    } else if (_estates.length > 0 && _estates.length < recordsPerPage) {
      int prevLastRecordNumber = (pageCounter - 1) * recordsPerPage;

      currentlyShowing = (lastRecordNumber - (recordsPerPage - 1)).toString() +
          '-' +
          (prevLastRecordNumber + _estates.length).toString();
    }

    setState(() {
      estates = _estates;
      loading = false;
    });
  }

  Future<bool> showSuggestImmobilePopup() async {
    var result = await showGeneralDialog(
      context: context,
      barrierLabel: "Barrier",
      barrierDismissible: true,
      barrierColor: Colors.black.withOpacity(0.5),
      pageBuilder: (_, __, ___) {
        return Center(
            child: PrezziVendutoPopup(
          agency: widget.agency,
        ));
      },
    );
    if (result != null && result == true) {
      return true;
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Prezzi Venduto',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 22),
              ),
              widget.agencyUser.role == 'master'
                  ? Container()
                  : GestureDetector(
                      onTap: () async {
                        if (await showSuggestImmobilePopup()) {
                          setState(() {
                            loading = true;
                            initialFetch(widget.agency);
                          });
                        }
                      },
                      child: Container(
                        height: 40,
                        width: 160,
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor,
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                "Prezzi nuovo",
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Icon(
                                Icons.add,
                                color: Colors.white,
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
            ]),
        SizedBox(height: 20),
        Expanded(
          child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
              ),
              padding: EdgeInsets.symmetric(horizontal: 10),
              child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      dataTablePagination(),
                      Expanded(
                          child: DataTable2(
                        dataRowHeight: loading ? 300 : 50,
                        minWidth: 1500,
                        columns: getColumns(),
                        empty: Text('Nessun record trovato!'),
                        rows: loading
                            ? loadingRow()
                            : List.generate(estates.length, (int index) {
                                return DataRow(
                                  cells: getDataRow(
                                    estates.elementAt(index),
                                  ),
                                );
                              }),
                      )),
                      dataTablePagination(),
                    ],
                  ))),
        ),
      ],
    );
  }

  List<DataCell> getDataRow(ProposedEstate estate) {
    List<DataCell> list = [];

    list.add(DataCell(Text(
      estate.address!,
      style: TextStyle(fontWeight: FontWeight.bold),
    )));
    list.add(DataCell(Text(
      estate.zona!,
      style: TextStyle(fontWeight: FontWeight.bold),
    )));

    if (estate.link == null) {
      list.add(DataCell(Text(
        "No Link",
        style: TextStyle(fontWeight: FontWeight.bold),
      )));
    } else {
      list.add(DataCell(GestureDetector(
        onTap: () {
          launchCustomUrl(estate.link!);
        },
        child: Text(
          estate.link!,
          style: TextStyle(
              fontWeight: FontWeight.bold,
              decoration: TextDecoration.underline),
        ),
      )));
    }

    list.add(DataCell(Text(
      estate.margineTrattativa!,
      style: TextStyle(fontWeight: FontWeight.bold),
    )));

    list.add(DataCell(Text(
      estate.possibilityCessionePreliminare!,
      style: TextStyle(fontWeight: FontWeight.bold),
    )));

    if (widget.agencyUser.role == 'master') {
      list.add(DataCell(Text(
        estate.agencyName ?? '-',
        style: TextStyle(fontWeight: FontWeight.bold),
      )));
    }

    return list;
  }

  List<DataColumn> getColumns() {
    List<DataColumn> list = [];

    list.add(DataColumn2(
      size: ColumnSize.L,
      fixedWidth: 120,
      label: Text(
        'Indirizzo',
        style: TextStyle(),
      ),
    ));

    list.add(DataColumn2(
      size: ColumnSize.M,
      label: SelectableText(
        'Zona',
        style: TextStyle(),
      ),
    ));

    list.add(DataColumn2(
      size: ColumnSize.M,
      fixedWidth: 250,
      label: SelectableText(
        'Link',
        style: TextStyle(),
      ),
    ));

    list.add(DataColumn2(
      size: ColumnSize.L,
      label: SelectableText(
        'Margine trattativa',
        style: TextStyle(),
      ),
    ));

    list.add(DataColumn2(
      size: ColumnSize.M,
      label: Text(
        'Possibilità C.P.',
        style: TextStyle(),
      ),
    ));

    if (widget.agencyUser.role == 'master') {
      list.add(DataColumn2(
        size: ColumnSize.M,
        label: Text(
          'Agenzia proponente',
          style: TextStyle(),
        ),
      ));
    }

    return list;
  }
}
