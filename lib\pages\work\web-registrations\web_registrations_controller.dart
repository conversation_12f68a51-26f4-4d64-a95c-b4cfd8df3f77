import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/user.dart';

class WebRegistrationsController extends GetxController {
  RxBool loadingContacts = false.obs;
  List<NewarcUser> users = [];
  List<NewarcUser> displayUsers = [];
  String query = "";
  
  List<Map> filters = [];

  List testList = [
    {'no': 0, 'keyword': 'Filtra per stato'},
    {'no': 1, 'keyword': 'Da contattare'},
    {'no': 2, 'keyword': 'Contattato --'},
    {'no': 3, 'keyword': 'Non interessato'},
    {'no': 4, 'keyword': 'Acquisito'}
  ];

  var selectedStatus;

  List<DocumentSnapshot> documentList = [];
  int totalRecords = 0;
  String currentlyShowing = '';
  int recordsPerPage = 20;
  int pageCounter = 1;
  int totalPages = 0;
  bool disablePreviousButton = true;
  bool disableNextButton = false;
  List<dynamic> cacheFirestore = [];
  TextEditingController searchTextController = new TextEditingController();

  String agencyFilter = '';
  String cityFilter = '';
  String newarcTypeFilter = '';

  NewarcUser? newarcUser;

  List<Agency> agencyList = [];

  clearFilter() {
    agencyFilter = '';
    cityFilter = '';
    newarcTypeFilter = '';
    
  }
}
