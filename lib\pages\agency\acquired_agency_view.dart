import 'dart:ui';
import 'package:data_table_2/data_table_2.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/acquiredContact.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/alert.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/input.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/widget/agency/edit_agency_popup.dart';
import 'package:newarc_platform/widget/agency/immobile_popup.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;

class AcquiredAgencyView extends StatefulWidget {
  final Agency agency;

  const AcquiredAgencyView({Key? key, required this.agency}) : super(key: key);

  @override
  State<AcquiredAgencyView> createState() => _AcquiredAgencyViewState();
}

class _AcquiredAgencyViewState extends State<AcquiredAgencyView> {
  bool loading = true;
  List<Agency> contacts = [];
  String query = "";
  final recordsPerPage = 20;
  TextEditingController? filterCity = new TextEditingController();
  TextEditingController? searchTextController = new TextEditingController();

  @override
  void initState() {
    fetchContacts();
    super.initState();
  }

  List<DropdownMenuItem> buildDropdownTestItems(List _testList) {
    List<DropdownMenuItem> items = [];
    for (var i in _testList) {
      items.add(
        DropdownMenuItem(
          value: i,
          child: Container(
            decoration: BoxDecoration(
              color: Color(0xff489B79),
              borderRadius: BorderRadius.circular(8),
            ),
            padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
            child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    i['keyword'],
                    style: TextStyle(
                        color: Colors.white, fontWeight: FontWeight.w600),
                  ),
                ]),
          ),
        ),
      );
    }
    return items;
  }

  Future<void> fetchContacts() async {
    QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
    Query<Map<String, dynamic>> collectionSnapshotQuery;

    // QuerySnapshot<Map<String, dynamic>> collectionSnapshot;

    collectionSnapshotQuery =
        await FirebaseFirestore.instance.collection(appConfig.COLLECT_AGENCIES);

    if (searchTextController!.text != '') {
      collectionSnapshotQuery = collectionSnapshotQuery
          .orderBy('name')
          .startAt([searchTextController!.text.toUpperCase()]).endAt(
              [searchTextController!.text.toLowerCase()]);
      //.where('name', isGreaterThanOrEqualTo: searchTextController!.text)
      //.where('name',
      //    isLessThanOrEqualTo: searchTextController!.text + "\uf7ff");
    }

    collectionSnapshotQuery.limit(recordsPerPage);
    collectionSnapshot = await collectionSnapshotQuery.get();

    List<Agency> _contacts = [];
    for (var element in collectionSnapshot.docs) {
      var _tmp = Agency.fromDocument(element.data(), element.id);
      _contacts.add(_tmp);
      // try {

      // } catch (e) {
      //   print("error in document ${element.id}");
      //   print(e);
      // }
    }

    setState(() {
      contacts = _contacts;
      loading = false;
    });
  }

  void showHousePopup(AcquiredContact acquiredContact) {
    showGeneralDialog(
      context: context,
      barrierLabel: "Barrier",
      barrierDismissible: true,
      barrierColor: Colors.black.withOpacity(0.5),
      pageBuilder: (_, __, ___) {
        return Center(
            child: ImmobilePopup(
          acquiredContact: acquiredContact,
        ));
      },
    );
  }

  // @override
  // Widget build(BuildContext context) {
  //   return Column(

  //     children: [
  //       Row(
  //           mainAxisSize: MainAxisSize.max,
  //           mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //           children: [
  //             Text(
  //               'Agenzie registratesi',
  //               style: TextStyle(fontWeight: FontWeight.bold, fontSize: 22),
  //             ),
  //             /*Expanded(
  //               child: Padding(
  //                 padding: const EdgeInsets.symmetric(horizontal: 50),
  //                 child: TextField(
  //                   decoration: InputDecoration(hintText: "Cerca"),
  //                   onChanged: (String _query) {
  //                     setState(() {
  //                       query = _query.toLowerCase();
  //                     });
  //                   },
  //                 ),
  //               ),
  //             ),*/
  //           ]),
  //       SizedBox(height: 20),
  //       loading
  //           ? Center(
  //               child: CircularProgressIndicator(
  //                 color: Theme.of(context).primaryColor,
  //               ),
  //             )
  //           : Expanded(
  //               child: DataTable(
  //                 columns: getColumns(),
  //                 rows: List.generate(contacts.length, (int index) {
  //                   return DataRow(
  //                     cells: getDataRow(
  //                       contacts.elementAt(index),
  //                     ),
  //                   );
  //                 }),
  //               ),
  //             ),
  //     ],
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    double pageWidth = MediaQuery.of(context).size.width - 260;
    if (pageWidth < 1800) {
      pageWidth = 1800;
    }

    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        /*Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              NarFormLabelWidget(
                label: 'Agenzie iscritte',
                fontSize: 22,
                fontWeight: 'bold',
              ),
              SizedBox(width: 30),
              Container(
                  width: 220,
                  child: NarInputWidget(
                    hintText: "Cerca agenzia",
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    controller: searchTextController,
                    borderWidth: 1,
                  )
                
                  ),
              SizedBox(width: 10),
              Container(
                height: 35,
                width: 61,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(7),
                  color: Theme.of(context).primaryColor,
                ),
                child: TextButton(
                  onPressed: () {
                    print('Cerca');
                    setState(() {
                      loading = true;
                    });
                    fetchContacts();
                  },
                  child: Center(
                    child: NarFormLabelWidget(
                      label: "Cerca",
                      fontSize: 13,
                      fontWeight: 'bold',
                      textColor: Colors.white,
                    ),
                  ),
                ),
              ),
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        NarFormLabelWidget(
                          label: 'Città',
                          fontSize: 10,
                          textColor: Color(0xff585858),
                          fontWeight: '500',
                        ),
                        Row(
                          children: [
                            Container(
                              height: 32,
                              width: 165,
                              child: NarSelectBoxWidget(
                                options: ["Torino", "Milano", "Roma"],
                                controller: filterCity,
                                onChanged: () {},
                              ),
                            ),
                            SizedBox(width: 10),
                            Container(
                              height: 35,
                              width: 61,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(7),
                                color: Theme.of(context).primaryColor,
                              ),
                              child: TextButton(
                                onPressed: () {},
                                child: Center(
                                  child: Text(
                                    "Filtra",
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 13,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ]),*/
        SizedBox(height: 20),
        loading
            ? Center(child: CircularProgressIndicator())
            : Expanded(
                child: Container(
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(
                        width: 1, color: Color.fromRGBO(219, 219, 219, 1))),
                padding: EdgeInsets.symmetric(horizontal: 0),
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Expanded(
                        child: DataTable2(
                          key: widget.key,
                          dataRowHeight: loading ? 300 : 70,
                          isHorizontalScrollBarVisible: true,
                          minWidth: 1500,
                          columnSpacing: 10,
                          horizontalMargin: 10,
                          headingRowHeight: 25,
                          columns: getColumns(pageWidth),
                          rows: List.generate(contacts.length, (int index) {
                            return DataRow(
                              cells: getDataRow(
                                contacts.elementAt(index),
                              ),
                            );
                          }, growable: true),
                        ),
                      ),
                      // dataTablePagination(),
                    ],
                  ),
                ),
              )),
      ],
    );
  }

  /*bool filterFunction(Agency contact) {
    bool _show = true;

    if (!contact.contactFullName!.toLowerCase().contains(query) &&
        !contact.address.toLowerCase().contains(query) &&
        !contact.contactEmail!.toLowerCase().contains(query)) {
      _show = false;
    }

    return _show;
  }*/

  Future<bool> updateAgencyActiveStatus(Agency agency) async {
    final FirebaseFirestore _db = FirebaseFirestore.instance;
    return await _db
        .collection(appConfig.COLLECT_AGENCIES)
        .doc(agency.id)
        .update(agency.toMap())
        .then((value) {
      return true;
    }).onError((error, stackTrace) {
      // print({error, stackTrace});
      return false;
    });
  }

  Color? activeColor;
  List<DataCell> getDataRow(Agency agency) {
    List<DataCell> list = [];

    Color colorBlack = Color.fromRGBO(0, 0, 0, 1);
    Color colorGrey = Color.fromRGBO(131, 131, 131, 1);
    activeColor = agency.isActive == true ? colorBlack : colorGrey;

    list.add(DataCell(NarFormLabelWidget(
      label: agency.name! + " - " + agency.address!,
      fontSize: 13,
      fontWeight: 'bold',
      textColor: activeColor,
    )));

    list.add(DataCell(NarFormLabelWidget(
      label: agency.email,
      fontSize: 13,
      fontWeight: 'bold',
      textColor: activeColor,
    )));

    list.add(DataCell(NarFormLabelWidget(
      label: agency.points.toString(),
      fontSize: 13,
      fontWeight: 'bold',
      textColor: activeColor,
    )));

    list.add(DataCell(NarFormLabelWidget(
      label: agency.phone!,
      fontSize: 13,
      fontWeight: 'bold',
      textColor: activeColor,
    )));

    list.add(DataCell(NarFormLabelWidget(
      label: DateTime.fromMillisecondsSinceEpoch(agency.registrationDate!)
              .day
              .toString() +
          '/' +
          DateTime.fromMillisecondsSinceEpoch(agency.registrationDate!)
              .month
              .toString() +
          '/' +
          DateTime.fromMillisecondsSinceEpoch(agency.registrationDate!)
              .year
              .toString(),
      fontSize: 13,
      fontWeight: 'bold',
      textColor: activeColor,
    )));

    list.add(DataCell(
      Switch(
        // This bool value toggles the switch.
        value: agency.isActive!,
        activeThumbColor: Theme.of(context).primaryColor,
        onChanged: (bool value) async {
          // This is called when the user toggles the switch.

          agency.isActive = value;
          setState(() {
            agency.isActive = value;
            activeColor = value == true ? colorBlack : colorGrey;
            //print({value, activeColor});
          });

          bool status = await updateAgencyActiveStatus(agency);

          if (status == false) {
            await showAlertDialog(context, "Errore",
                "Si è verificato un errore nell\'aggiornare lo stato.");

            setState(() {
              agency.isActive = !value;
              activeColor = value == true ? colorGrey : colorBlack;
            });
          }
        },
      ),
    ));

    list.add(DataCell(
      MouseRegion(
        cursor: SystemMouseCursors.click,
        child: GestureDetector(
          onTap: () async {
            if (await showEditAgencyPopup(agency)) {
              setState(() {
                loading = true;
              });
              fetchContacts();
            }
          },
          child: Container(
            height: 30,
            width: 30,
            child: SvgPicture.asset('assets/icons/edit.svg',
                height: 20, color: Color(0xff5b5b5b)),
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: Color.fromRGBO(231, 231, 231, 1),
              borderRadius: BorderRadius.circular(7.0),
            ),
          ),
        ),
      ),
    ));

    return list;
  }

  List<DataColumn> getColumns(double pageWidth) {
    List<DataColumn> list = [];

    list.add(DataColumn2(
        fixedWidth: 0.2 * pageWidth,
        label: NarFormLabelWidget(
          label: 'Agenzia',
          fontSize: 13,
          textColor: Color.fromRGBO(131, 131, 131, 1),
        )));

    list.add(DataColumn2(
        fixedWidth: 0.1 * pageWidth,
        label: NarFormLabelWidget(
          label: 'Email',
          fontSize: 13,
          textColor: Color.fromRGBO(131, 131, 131, 1),
        )));

    list.add(DataColumn2(
        fixedWidth: 0.05 * pageWidth,
        label: NarFormLabelWidget(
          label: 'Punti',
          fontSize: 13,
          textColor: Color.fromRGBO(131, 131, 131, 1),
        )));

    list.add(DataColumn2(
        fixedWidth: 0.08 * pageWidth,
        label: NarFormLabelWidget(
          label: 'Telefono',
          fontSize: 13,
          textColor: Color.fromRGBO(131, 131, 131, 1),
        )));

    list.add(DataColumn2(
        fixedWidth: 0.08 * pageWidth,
        label: NarFormLabelWidget(
          label: 'Data iscrizione',
          fontSize: 13,
          textColor: Color.fromRGBO(131, 131, 131, 1),
        )));

    list.add(DataColumn2(
        fixedWidth: 0.08 * pageWidth,
        label: NarFormLabelWidget(
          label: 'Attiva/Disattiva',
          fontSize: 13,
          textColor: Color.fromRGBO(131, 131, 131, 1),
        )));

    list.add(DataColumn2(
        fixedWidth: 0.06 * pageWidth,
        label: NarFormLabelWidget(
          label: 'Modifica',
          fontSize: 13,
          textColor: Color.fromRGBO(131, 131, 131, 1),
        )));

    return list;
  }

  Future<bool> showEditAgencyPopup(Agency agency) async {
    var result = await showGeneralDialog(
      context: context,
      barrierLabel: "Barrier",
      barrierDismissible: true,
      barrierColor: Colors.black.withOpacity(0.5),
      pageBuilder: (_, __, ___) {
        return Center(
            child: EditAgencyPopup(
          agency: agency,
        ));
      },
    );
    if (result != null && result == true) {
      return true;
    }
    return false;
  }
}
