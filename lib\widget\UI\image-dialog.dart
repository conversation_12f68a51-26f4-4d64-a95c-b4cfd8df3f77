import 'package:flutter/material.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:image_picker/image_picker.dart';

class MyDialog extends StatefulWidget {

  Color? textColor;
  List<dynamic>? images = [];
  // Function? imageContainer;
  // Future<dynamic> fuc? getImageFromGallery;
  MyDialog({
    this.textColor,
    this.images,
    // this.imageContainer,
    // this.getImageFromGallery
  });


  @override
  _MyDialogState createState() => new _MyDialogState();
}

class _MyDialogState extends State<MyDialog> {

  ImagePicker picker = ImagePicker();

  getImageFromGallery() async {
    
    await picker.pickMultiImage( imageQuality: 50 ).then((value){
      widget.images = value;

      // displayImages = Row(
      //   children: widget.images!.map((image){
      //                             return imageContainer(image);
      //                           }).toList()
      // );
      // updateCounters();
      setState(() { });
    }); 
  }
  
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
              title: Stack(
                alignment: Alignment.center,
                children: [
                  NarFormLabelWidget(label: 'Carica immagini', fontSize: 22, textColor: Colors.black, fontWeight: 'bold', textAlign: TextAlign.center),
                  Positioned(
                    top: 0.0,
                    right: 0.0,
                    child:ElevatedButton(

                      child: Icon(Icons.close),
                      onPressed: (){
                        Navigator.pop(context);
                      },
                      style: ButtonStyle(
                        // padding: MaterialStateProperty.all(
                        //   EdgeInsets.symmetric(vertical: 22, horizontal: 20)
                        // ),
                        // shape: MaterialStateProperty.all<OutlinedBorder>(RoundedRectangleBorder(
                        //   borderRadius: BorderRadius.circular(widget.borderRadius ?? 25),
                        //   side: BorderSide(color: widget.borderSideColor ?? Colors.black))) ,
                        shadowColor: WidgetStateProperty.all<Color>(Colors.transparent),
                        foregroundColor: WidgetStateProperty.all<Color>(widget.textColor??Colors.black),
                        backgroundColor: WidgetStateProperty.all<Color>(Colors.transparent),
                        overlayColor: WidgetStateProperty.all(Colors.transparent),
                      ),
                      // shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(80)),
                      // backgroundColor: Colors.white,
                      // mini: true,
                      // elevation: 5.0,
                    ),
                  ),
              ],),
              content:  SingleChildScrollView(
                    
                    child: Container(
                      height: 500,
                      width: 800,
                      child: Column(
                        children: [
                          
                          Expanded(
                            flex: 40,
                            child: GestureDetector(
                              onTap: () async {
                                await getImageFromGallery();
                                setState(() {
                                });
                                print(widget.images);
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Color.fromRGBO(240, 240, 240, 1),
                                  // shape: BoxShape.circle,
                                  borderRadius: BorderRadius.all(Radius.circular(10.0))
                                ),
                                width: 400,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Icon(Icons.upload_file, size: 60, color: Color.fromRGBO(128, 128, 128, 1),),
                                    SizedBox(height: 30,),
                                    NarFormLabelWidget(label: "Clicca per caricare\no trascina le immagini.", fontWeight: '700', fontSize: 18, textColor: Color.fromRGBO(128, 128, 128, 1), textAlign: TextAlign.center,)
                                  ],
                                ),
                              ),
                            ),
                          ),
                          SizedBox(height: 30,),
                          Expanded(
                            flex: 60,
                            child: Row(
                              children: widget.images!.map<Widget>((image){
                                                        return Image.network(image!.path, height: 100,);
                                                      }).toList(),
                            )
                          )
                          
                        ],
                      ),
                    ),
                  ),
                
              actions: <Widget>[
                // TextButton(
                //   child: const Text('Approve'),
                //   onPressed: () {
                //     setState(() {
                      
                //     });
                //   },
                // ),
              ],
              
            );
  }
}