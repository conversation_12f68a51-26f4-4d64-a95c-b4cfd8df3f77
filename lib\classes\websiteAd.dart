import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';

/*{
    propertyId: 'propertyId',
    title: '',
    area: '',
    locali: '',
    baths: '',
    addressInfo: '',
    price: '',
    projectType: '',
    agencyLogo: '',
    agencyId: '',
    Images: [],
    children: [
        {
            propertyId: 'propertyId',
            title: '',
            area: '',
            locali: '',
            baths: '',
            price: '',
            Image: '',
        },
        {
            propertyId: 'propertyId',
            title: '',
            area: '',
            locali: '',
            baths: '',
            price: '',
            Image: '',
        },
    ],
    isArchived: true,
    isActive: true,
    publicationDate: 'date'

}*/

class WebsiteAd {
  
  String? newarcHomeId = '';
  bool? isArchived = false;
  bool? isActive = true;
  int? publicationDate = 0; 
  String? title = '';
  String? type = '';
  double? floorArea = 0.0;
  double? roomCount = 0.0;
  double? baths = 0.0;
  String? floors = '';
  double? price = 0.0;
  String? projectType = '';
  String? status = '';
  String? agencyId = '';
  String? agencyLogoImage = '';
  List<String>? images = [];
  BaseAddressInfo? addressInfo;
  String? zone = '';
  List<WebsiteAdChild>? children = []; 
  Map<String, dynamic>? publicStatus;


  WebsiteAd({
    this.newarcHomeId,
    this.isArchived,
    this.isActive,
    this.publicationDate,
    this.title,
    this.type,
    this.floorArea,
    this.roomCount,
    this.baths,
    this.floors,
    this.price,
    this.projectType,
    this.status,
    this.agencyId,
    this.agencyLogoImage,
    this.images,
    this.addressInfo,
    this.children,
    this.publicStatus,
    this.zone
  });

  WebsiteAd.empty(){

    this.newarcHomeId = '';
    this.isArchived = false;
    this.isActive = true;
    this.publicationDate = DateTime.now().millisecondsSinceEpoch;
    this.title = '';
    this.type = '';
    this.floorArea = 0.0;
    this.roomCount = 0.0;
    this.baths = 0.0;
    this.floors = '';
    this.price = 0.0;
    this.projectType = '';
    this.status = '';
    this.agencyId = '';
    this.agencyLogoImage = '';
    this.images= [];
    this.addressInfo = BaseAddressInfo.empty();
    this.children = [];
    this.publicStatus = {};
    this.zone = '';
  }

  WebsiteAd.fromDocument(DocumentSnapshot<Map<String, dynamic>> doc) {

    this.newarcHomeId = doc['newarcHomeId'];
    this.isArchived = doc['isArchived'];
    this.isActive = doc['isActive'];
    this.publicationDate = doc['publicationDate'];
    this.title = doc['title'];
    this.type = doc['type'];
    this.floorArea = double.tryParse(doc['floorArea'].toString());
    this.roomCount = double.tryParse(doc['roomCount'].toString());
    this.baths =  double.tryParse(doc['baths'].toString());
    this.floors =  doc['floors'];
    this.price = doc['price'];
    this.projectType = doc['projectType'];
    this.status = doc['status'];
    this.agencyId = doc['agencyId'];
    this.agencyLogoImage = doc['agencyLogoImage'];
    this.images= doc['images'];
    this.addressInfo = doc['addressInfo'] != null
        ? BaseAddressInfo.fromMap(doc['addressInfo'])
        : BaseAddressInfo.empty();
    this.children = List<WebsiteAdChild>.from(
      doc.data()!['children'].map((child) {
        return new WebsiteAdChild.fromMap(child);
      })
    );
    this.publicStatus = doc.data()!['publicStatus'] != null
        ? doc.data()!['publicStatus'] as Map<String, dynamic>
        : {};
    this.zone = doc['zone'];
    
  }

  Map<String, dynamic> toMap() {
    return {
      'newarcHomeId': this.newarcHomeId,
      'isArchived': this.isArchived,
      'isActive': this.isActive,
      'publicationDate': this.publicationDate,
      'title': this.title,
      'floorArea': this.floorArea,
      'roomCount': this.roomCount,
      'baths': this.baths,
      'floors': this.floors,
      'price': this.price,
      'projectType': this.projectType,
      'status': this.status,
      'agencyId': this.agencyId,
      'agencyLogoImage': this.agencyLogoImage,
      'images': this.images,
      'addressInfo': this.addressInfo!.toMap(),
      'children': this.children!.map((e) => e.toMap() ),
      'publicStatus': publicStatus,
      'zone': zone,
    };
  }

  
}

class WebsiteAdChild {

  String? newarcHomeId = '';
  bool? isArchived = false;
  bool? isActive = true;
  String? type = '';
  double? floorArea = 0.0;
  double? roomCount = 0.0;
  double? baths = 0.0;
  String? floors = '';
  double? price = 0.0;
  String? image = '';


  WebsiteAdChild({
    this.newarcHomeId,
    this.isArchived,
    this.isActive,
    this.type,
    this.floorArea,
    this.roomCount,
    this.baths,
    this.floors,
    this.price,
    this.image
  });


  WebsiteAdChild.fromMap(Map<String, dynamic> child) {
    
    newarcHomeId = child['newarcHomeId'];
    isArchived = child['isArchived'];
    isActive = child['isActive'];
    type = child['type'];
    floorArea = double.tryParse(child['floorArea'].toString());
    roomCount = double.tryParse(child['roomCount'].toString());
    floors = child['floors'].toString();
    baths = double.tryParse(child['baths'].toString());
    price = child['price'];
    image = child['image'];
    
  }

  Map<String, dynamic> toMap() {
    return {
      'newarcHomeId': this.newarcHomeId,
      'isArchived': this.isArchived,
      'isActive': this.isActive,
      'type': this.type,
      'floorArea': this.floorArea,
      'roomCount': this.roomCount,
      'floos': this.floors,
      'baths': this.baths,
      'price': this.price,
      'image': this.image
      
    };
  }

}