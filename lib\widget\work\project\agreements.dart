
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/classes/agreement.dart';
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/classes/supplier.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:intl/intl.dart';

class Agreements extends StatefulWidget {
  final NewarcProject? project;
  final List<Supplier>? suppliers;
  final Function? updateProject;

  const Agreements(
      {Key? key, this.project, this.updateProject, this.suppliers})
      : super(key: key);

  @override
  State<Agreements> createState() => _AgreementsState();
}

class _AgreementsState extends State<Agreements> {
  
  TextEditingController contDate = new TextEditingController();
  TextEditingController contTitle = new TextEditingController();
  
  int jobStart = 0;
  List<bool> isAnimated = [];
  final List<List> allFiles = [];
  List<TextEditingController> contVT = [];

  List<String> fileProgressMessage = [''];
  String progressMessage = '';
  int keepOpen = -1;
  
  @override
  void initState() {
    super.initState();

    setInitialValues();
  }

  @protected
  void didUpdateWidget(Agreements oldWidget) {
    super.didUpdateWidget(oldWidget);

    setInitialValues();
  }

  setInitialValues() {
    
    allFiles.clear();
    isAnimated.clear();
    
    int counter = 0;
     widget.project!.agreements!.map((e){ 

      allFiles.add( e.images! ); 
      isAnimated.add(false); 

      if( keepOpen > -1 && counter == keepOpen ) {
        isAnimated[keepOpen] = true;
      }
      contVT.add( new TextEditingController() );
      contVT[counter].text = e.virtualTourLink!;

      counter++;
    }).toList();

  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.max,
        children: [
          Expanded(
            child: ListView(
              children: [
                SizedBox(height: 20),
                Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      flex: 1,
                      child: NarFormLabelWidget(
                        label: 'Aggiornamenti di cantiere',
                        fontSize: 20,
                        fontWeight: 'bold',
                        
                      ),
                    ),
                    MouseRegion(
                      cursor: SystemMouseCursors.click,
                      child: BaseNewarcButton(
                        onPressed: () {
                          // addMaterialPopup(context, NewarcMaterial.empty());
                          addAgreementPopup(context);
                        },
                        buttonText: 'Nuovo aggiornamento',
                      ),
                    )
                  ],
                ),
                SizedBox(height: 30),
                Container(
                  // color: Colors.grey,
                  // width: 200,
                  child: ListView(
                    shrinkWrap: true,
                    children: [
                      Column(
                        children: widget.project!.agreements!.reversed.map((e) {
                          return agreementWrapper(context, e);
                        }).toList(),
                      )
                      
                    ],
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future saveProject() async {
    final FirebaseFirestore _db = FirebaseFirestore.instance;
    await _db
        .collection(appConfig.COLLECT_NEWARC_PROJECTS)
        .doc(widget.project!.id)
        .update(widget.project!.toMap());

    widget.updateProject!(widget.project);
    setInitialValues();
  }

  Widget agreementWrapper(BuildContext context, Agreement agreementRow) {

    // try {
    int count = widget.project!.agreements!.indexWhere( (e) => agreementRow.uid == e.uid );
    progressMessage = '';

    return Column(
      children: [
        
        Container(
          decoration: BoxDecoration(
            color: Color(0xffF1F1F1),
            borderRadius: BorderRadius.circular(10),
          ),
          margin: EdgeInsets.only(bottom: 5),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          NarFormLabelWidget(
                            label: agreementRow.title,
                            fontSize: 16,
                            textColor: Colors.black,
                            fontWeight: 'bold',
                          ),

                          Row(
                            children: [
                              NarFormLabelWidget(
                                label: timestampToUtcDate(agreementRow.date!),
                                fontSize: 16,
                                textColor: Colors.black,
                                fontWeight: 'bold',
                              ),
                              SizedBox(width: 20,),
                              isAnimated[count]
                              ? GestureDetector(
                                onTap: (){
                                  deleteDialog( context, agreementRow);
                                },
                                child: MouseRegion(
                                  cursor: SystemMouseCursors.click,
                                  child: Image.asset(
                                      'assets/icons/trash-process.png',
                                      height: 16,
                                      color: Color(0xFF5B5B5B),
                                    ),
                                ),
                              )
                              : GestureDetector(
                                onTap: (){
                                  setState(() {
                                    isAnimated[count] = true;  
                                    keepOpen = count;
                                  });
                                  
                                },
                                child: MouseRegion(
                                  cursor: SystemMouseCursors.click,
                                  child: Image.asset(
                                      'assets/icons/arrow_down.png',
                                      // height: 8,
                                      width: 14,
                                      color: Color(0xFF5B5B5B),
                                    ),
                                ),
                              )
                            ],
                          )
                      
                          
                        ],
                      ),
                    ),
                    

                    isAnimated[count] == true
                    ? Padding(
                      padding: const EdgeInsets.only(bottom: 0, top: 8.0, left:8, right: 8),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Container(
                            margin: EdgeInsets.only(top:10),
                            constraints: BoxConstraints(
                              minHeight: 100
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      NarFormLabelWidget(
                                        label: 'Virtual Tour Link',
                                        fontSize: 13,
                                        textColor: Colors.black,
                                        fontWeight: '700',
                                      ),
                                      SizedBox(height:5),
                                      Row(
                                        children: [
                                          CustomTextFormField(
                                            minLines: 2,
                                            label: '',
                                            controller: contVT[count],
                                            labelColor: Colors.black,
                                            onChangedCallback: ( value ) async {
                                              
                                              widget.project!.agreements![count].virtualTourLink = contVT[count].text;
                                              // saveProject();
                                              final FirebaseFirestore _db = FirebaseFirestore.instance;
                                              await _db
                                                  .collection(appConfig.COLLECT_NEWARC_PROJECTS)
                                                  .doc(widget.project!.id)
                                                  .update(widget.project!.toMap());
                                                                      
                                              widget.updateProject!(widget.project);
                                              setState((){});
                                            },
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                )
                              ],
                            ),
                          )
                      
                        ],
                      ),
                    )
                    : Container(),
                    
                    isAnimated[count] == true ? SizedBox(height: 15) : Container(),

                    isAnimated[count] == true
                    ? Padding(
                      padding: const EdgeInsets.only(top: 0, bottom: 8.0, right: 8, left: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          GestureDetector(
                            onTap: (){
                              setState(() {
                                isAnimated[count] = false;  
                                keepOpen = -1;
                              });
                              
                            },
                            child: MouseRegion(
                              cursor: SystemMouseCursors.click,
                              child: Image.asset(
                                  'assets/icons/arrow_up.png',
                                  width: 14,
                                  color: Color(0xFF5B5B5B),
                                ),
                            ),
                          )
                        ],
                      ),
                    )
                    : Container()
                  ],
                ),
              ),
              
            ],
          ),
        ),
      ],
    );

    // } catch (e,s) {
    //   print({e,s});
    //   return Container();
    // }
  }

  deleteAgreementFiles(Agreement agreementRow) async {
    try {
      await deleteDirectory("projects/${widget.project!.id}/agreements/${agreementRow.uid}/");
    } catch(e) {

    }
    
  }
  
  formatDateForParsing(String dateString) {
    List splittedDate = dateString.split('/');
    return splittedDate[2] + '-' + splittedDate[1] + '-' + splittedDate[0];
  }

  deleteDialog(BuildContext context, Agreement agreementRow) {
    return showDialog(
        context: context,
        builder: (BuildContext _bc1) {
          return StatefulBuilder(
              builder: (BuildContext _bc2, StateSetter _setState) {
            return Center(
                child: BaseNewarcPopup(
              title: 'Rimuovi aggiornamento',
              buttonText: 'Rimuovi',
              onPressed: () async {

                await deleteAgreementFiles(agreementRow);

                widget.project!.agreements!.removeWhere((e) =>  e.uid == agreementRow.uid  );
                await saveProject();
                
                setInitialValues();
                setState(() {
                  
                });
                
                return true;

                // Navigator.pop(context);
              },
              column: Container(
                  height: 99,
                  width: 465,
                  child: Center(
                    child: NarFormLabelWidget(
                        overflow: TextOverflow.visible,
                        label: 'Vuoi davvero eliminare questa aggiornamento?',
                        textAlign: TextAlign.center,
                        fontSize: 18,
                        fontWeight: '600',
                        height: 1.5,
                        textColor: Color(0xFF696969)),
                  )),
            ));
          });
        });
  }

  addAgreementPopup(BuildContext context) async {
    
    contDate.text = '';
    contTitle.text = '';
    
    return showDialog(
        context: context,
        builder: (BuildContext _bc1) {
          return StatefulBuilder(
              builder: (BuildContext _bc2, StateSetter _setState) {
            return Center(
                child: BaseNewarcPopup(
              title: 'Nuovo aggiornamento',
              buttonText: 'Aggiungi',
              // noButton: !hasVendor!,
              onPressed: () async {
                _setState(() {
                  progressMessage = 'Salvataggio in corso...';
                });

                try {
                  bool isEditMode = false;
                  
                  Agreement _agreement = new Agreement({
                    'indexPlace': widget.project!.agreements!.length + 1,
                    'title': contTitle.text,
                    'date' : jobStart,
                    'virtualTourLink': '',
                    'uid': generateRandomString(8),
                    'images': []
                  });

                  widget.project!.agreements!.add(_agreement);

                  jobStart = 0;

                  final FirebaseFirestore _db = FirebaseFirestore.instance;
                  DocumentSnapshot<Map<String, dynamic>> snapshot = await _db
                    .collection(appConfig.COLLECT_NEWARC_PROJECTS)
                    .doc(widget.project!.id)
                    .get();
                  
                  if (snapshot.data() != null) {
                    NewarcProject _tmpProject = NewarcProject.fromDocument(snapshot.data()!, snapshot.id);
                    widget.project!.userNotifications = _tmpProject.userNotifications;
                  }

                  
                  bool hasError = false;                
                  
                  await saveProject();

                  _setState(() {
                    progressMessage = 'Saved!';
                  });
                  setState(() {});

                } catch (e, s) {
                  print({e, s});
                }

                setInitialValues();

                return true;
              },
              column: Container(
                height: 190,
                width: 480,
                padding: EdgeInsets.only(left: 40, right: 40),
                child: ListView(
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          flex: 1,
                          child: Column(
                            // mainAxisSize: MainAxisSize.max,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  CustomTextFormField(
                                    label: "Nome aggiornamento",
                                    controller: contTitle,
                                    validator: (value) {
                                      if (value == '') {
                                        return 'Required!';
                                      }
                                  
                                      return null;
                                    }
                                  ),
                                ],
                              ),
                              SizedBox(height: 10,),
                              Row(
                                children: [
                                  CustomTextFormField(
                                    label: "Data cantiere",
                                    controller: contDate,
                                    suffixIcon: Container(
                                      padding: const EdgeInsets.all(10),
                                      height: 20,
                                      width: 20,
                                      child: Image.asset('assets/icons/calendar.png'),
                                    ),
                                    // validationMessage: 'Required!',
                                    validator: (value) {
                                      if (value == '') {
                                        return 'Required!';
                                      }
                                  
                                      return null;
                                    },
                                    onTap: () async {
                                      DateTime? pickedDate = await showDatePicker(
                                          context: context,
                                          initialDate: contDate.text == ''
                                              ? DateTime.now()
                                              : DateTime.tryParse(formatDateForParsing(
                                                  contDate.text))!,
                                          firstDate: DateTime(1950),
                                          lastDate: DateTime(2300));
                                  
                                      if (pickedDate != null) {
                                        jobStart = pickedDate.millisecondsSinceEpoch;
                                        String formattedDate = DateFormat('dd/MM/yyyy').format(pickedDate);
                                  
                                        widget.project!.jobStartDate = pickedDate.millisecondsSinceEpoch;
                                        await saveProject();
                                  
                                        setState(() {
                                          contDate.text = formattedDate; //set output date to TextField value.
                                        });
                                      } else {
                                        widget.project!.jobStartDate = 0;
                                        await saveProject();
                                      }
                                    },
                                    onChangedCallback: (value) async {
                                      if (value == '') {
                                        widget.project!.jobStartDate = 0;
                                        await saveProject();
                                      }
                                    },
                                  ),
                                ],
                              ),
                              
                            ],
                          ),
                        ),
                      ],
                    ),
                    
                    SizedBox(
                      height: 5,
                    ),
                    NarFormLabelWidget(
                      label: progressMessage,
                      textAlign: TextAlign.center,
                      textColor: Color(0xff696969),
                      fontSize: 13,
                      fontWeight: '600',
                    ),
                  ],
                ),
              ),
            ));
          });
        });
  }
  
}
