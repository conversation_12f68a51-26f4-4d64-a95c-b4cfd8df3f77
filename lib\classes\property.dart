import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:async';
import 'package:cross_file/cross_file.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/utils/various.dart';

class Property {
  String? collectionName = 'newarcHomes';
  String? firebaseId;
  String? propertyName;
  
  String? code;
  int? codeCounter;
  int? year;

  String? zone;
  String? type;
  dynamic location;
  String? civic;
  String? city;
  String? description;
  String? mq;
  String? baths;
  String? locals;
  
  String? bathsIns;
  String? localsIns;
  String? fixtureIns;
  String? InternalDoorsIns;
  String? WalkableIns;
  String? qualityOfTheArea;
  String? LighRenoIns;
  
  String? LighRenoInsMin;
  String? LighRenoInsMax;

  String? materialStandardLight;
  String? materialPremiumLight;
  String? materialStandardLightMin;
  String? materialStandardLightMax;
  String? materialPremiumLightMin;
  String? materialPremiumLightMax;

  String? FullRenoIns;
  
  String? FullRenoInsMin;
  String? FullRenoInsMax;
  
  String? materialStandardFull;
  String? materialPremiumFull;
  String? materialFixtureFull;

  String? materialStandardFullMin;
  String? materialStandardFullMax;
  String? materialPremiumFullMin;
  String? materialPremiumFullMax;
  String? materialFixtureFullMin;
  String? materialFixtureFullMax;
  

  String? bedrooms;
  String? floors;

  List<PropertyConfigStyles>? styles;
  int? insertTimestamp;
  int? updateTimestamp;
  int? archivedTimestamp;
  String? insertUid;
  String? updateUid;
  List<dynamic>? picturePaths = [];
  
  List<dynamic>? photoDayTimePaths = [];
  List<dynamic>? photoNightTimePaths = [];
  List<dynamic>? photoBeforeAfterPaths = [];

  List<dynamic>? videoRenderPaths = [];
  List<dynamic>? qrPaths = [];

  List<dynamic>? photoBrochureCoverPaths = [];
  List<dynamic>? photoBrochureCoverActualPaths = [];
  List<dynamic>? photoBrochureRenderPaths = [];
  List<dynamic>? videoBrochureVTPaths = [];
  String? brochureProjectDescription;
  String? brochureActualDescription;

  String? projectEnergyClass;
  String? actualEnergyClass;
  
  List<dynamic>? photographs = [];
  List<dynamic>? currentPlan = [];

  List<dynamic>? propertyFeatures;
  List<dynamic>? currentFeatures;
  List<XFile>? pictures;


  dynamic amenitiesCount;
  List<dynamic>? amenities;
  // dynamic? optional;
  List<PropertyOptionalFeature>? optional = [];

  int? startDate;
  int? endDate;
  int? limitDate;
  
  String? virtualTour;
  String? currentVirtualTour;

  // Stati
  bool? isActive;
  bool? isArchived;
  Map<String, dynamic>? publicStatus; //String representing special status of the house visible online
  int? publicationDate;

  double? basePrice;

  bool? activateNightTimePicture;
  bool? activateVirtualTourProject;
  bool? activateVideoRender;
  bool? activateVirtualTourCurrent;

  dynamic associatedProject;
  AgencyUser? assignedAgency;

  int? clicks;
  int? sentToAgencyCounter;
  String? projectType;

  BaseAddressInfo? addressInfo;

  Map? brochurePdfPath;

  /* Cuts */
  String? cutsCount; //Single or Multiple
  List<String>? children = [];
  List<dynamic>? buildingPictures = [];

  String? projectStatus = '';

  Property(
      {this.firebaseId,
      this.publicationDate,
      this.propertyName,
      this.zone,
      this.type,
      this.projectType,
      this.location,
      this.addressInfo,
      this.civic,
      this.city,
      this.description,
      this.mq,
      this.baths,
      this.locals,
      /*this.bathsAfter,
      this.localsAfter,
      this.InternalDoorsAfter,
      this.fixtureAfter,*/
      this.bathsIns,
      this.localsIns,
      this.fixtureIns,
      this.InternalDoorsIns,
      this.WalkableIns,
      this.qualityOfTheArea,
      /*this.DemolitionIns,
      this.ConstructionIns,
      this.LinearMeterIns,
      this.GeneralWallsIns,
      this.BathroomWallsIns,*/
      this.LighRenoIns,
      this.FullRenoIns,
      this.LighRenoInsMin,
      this.LighRenoInsMax,
      this.FullRenoInsMin,
      this.FullRenoInsMax,
      this.materialStandardLight,
      this.materialPremiumLight,
      this.materialStandardFull,
      this.materialPremiumFull,
      this.materialFixtureFull,
      this.bedrooms,
      this.floors,
      this.propertyFeatures,
      this.currentFeatures,
      this.styles,
      this.insertTimestamp,
      this.updateTimestamp,
      this.archivedTimestamp,
      this.insertUid,
      this.updateUid,
      this.startDate,
      this.endDate,
      this.limitDate,
      this.picturePaths,
      this.photoDayTimePaths,
      this.photoNightTimePaths,
      this.photoBeforeAfterPaths,
      this.videoRenderPaths,

      this.photoBrochureCoverPaths,
      this.photoBrochureCoverActualPaths,
      this.photoBrochureRenderPaths,
      this.videoBrochureVTPaths,
      this.brochureProjectDescription,
      this.brochureActualDescription,
      this.projectEnergyClass,
      this.actualEnergyClass,

      this.qrPaths,
      this.photographs,
      this.currentPlan,
      this.pictures,
      this.amenitiesCount,
      this.amenities,
      this.optional,
      this.publicStatus,
      this.isActive,
      this.isArchived,
      this.virtualTour,
      this.currentVirtualTour,
      this.basePrice,
      this.activateNightTimePicture,
      this.activateVirtualTourProject,
      this.activateVideoRender,
      this.activateVirtualTourCurrent,

      this.code,
      this.codeCounter,
      this.year,
      this.associatedProject,
      this.assignedAgency,
      this.clicks,
      this.sentToAgencyCounter,
      this.brochurePdfPath,
      this.materialStandardFullMin,
      this.materialStandardFullMax,
      this.materialPremiumFullMin,
      this.materialPremiumFullMax,
      this.materialFixtureFullMin,
      this.materialFixtureFullMax,

      this.materialStandardLightMin,
      this.materialStandardLightMax,
      this.materialPremiumLightMin,
      this.materialPremiumLightMax,
      this.cutsCount,
      this.children,
      this.buildingPictures,
      this.projectStatus
    });

  final FirebaseFirestore _db = FirebaseFirestore.instance;

  Property.empty(){
    this.publicationDate = DateTime.now().millisecondsSinceEpoch;
    this.firebaseId = '';
    this.propertyName = '';
    this.zone = '';
    this.type = '';
    this.projectType = '';
    this.location = {};
    this.addressInfo = BaseAddressInfo.empty();
    this.civic = '';
    this.city = '';
    this.description = '';
    this.mq = '';
    this.baths = '';
    this.locals = '';
    
    /*this.bathsAfter = '';
    this.localsAfter = '';
    this.InternalDoorsAfter = '';
    this.fixtureAfter = '';*/

    this.bathsIns = '';
    this.localsIns = '';
    this.fixtureIns = '';
    this.InternalDoorsIns = '';
    this.WalkableIns = '';
    this.qualityOfTheArea = '';
    /*this.DemolitionIns = '';
    this.ConstructionIns = '';
    this.LinearMeterIns = '';
    this.GeneralWallsIns = '';
    this.BathroomWallsIns = '';*/
    this.LighRenoIns = '';
    this.FullRenoIns = '';

    this.LighRenoInsMin = '';
    this.LighRenoInsMax = '';
    this.FullRenoInsMin = '';
    this.FullRenoInsMax = '';
    
    this.materialStandardLight = '';
    this.materialPremiumLight = '';
    this.materialStandardFull = '';
    this.materialPremiumFull = '';
    this.materialFixtureFull = '';

    this.bedrooms = '';
    this.floors = '';
    this.picturePaths = [];
    this.photoDayTimePaths = [];
    this.photoNightTimePaths = [];
    this.photoBeforeAfterPaths = [];
    this.videoRenderPaths = [];
    this.qrPaths = [];
    this.photographs = [];
    this.currentPlan = [];
    this.propertyFeatures = [];
    this.currentFeatures = [];
    this.amenities = [];
    this.amenitiesCount = 0;
    this.isActive = true;
    this.isArchived = false;
    this.publicStatus = {};

    this.photoBrochureCoverPaths = [];
    this.photoBrochureCoverActualPaths = [];
    this.photoBrochureRenderPaths = [];
    this.videoBrochureVTPaths = [];
    this.brochureProjectDescription = '';
    this.brochureActualDescription = '';

    this.projectEnergyClass = '';
    this.actualEnergyClass = '';

    

    this.styles = [];

    this.optional = [];

    this.startDate = 0;
    this.endDate = 0;
    this.limitDate = 0;
    this.virtualTour = '';
    this.currentVirtualTour = '';
    

    this.insertUid = '';
    this.updateUid = '';
    this.insertTimestamp = 0;
    this.updateTimestamp = 0;
    this.archivedTimestamp = 0;
    this.basePrice = 0;
    
    this.activateNightTimePicture = true;
    this.activateVirtualTourProject = true;
    this.activateVideoRender = true;
    this.activateVirtualTourCurrent = true;
    
    this.code = '';
    this.codeCounter = 0;
    this.year = 0;
    this.clicks = 0;
    this.sentToAgencyCounter = 0;
    this.brochurePdfPath = {};

    this.materialStandardFullMin = '';
    this.materialStandardFullMax = '';
    this.materialPremiumFullMin = '';
    this.materialPremiumFullMax = '';
    this.materialFixtureFullMin = '';
    this.materialFixtureFullMax = '';

    this.materialStandardLightMin = '';
    this.materialStandardLightMax = '';
    this.materialPremiumLightMin = '';
    this.materialPremiumLightMax = '';
    this.cutsCount = '';
    this.children = [];
    this.buildingPictures = [];
    this.projectStatus = '';
  }

  
  setData(DocumentSnapshot<Map<String, dynamic>> doc) {
    this.firebaseId = doc.id;
    this.propertyName =
        doc.data()!['propertyName'] != null ? doc.data()!['propertyName'] : '';
    this.zone = doc.data()!['zone'] != null ? doc.data()!['zone'] : '';
    this.type = doc.data()!['type'];
    this.publicationDate = doc.data()!['publicationDate'];
    this.projectType = doc.data()!['projectType'];
    
    this.location = doc.data()!['location'];
    this.addressInfo = doc.data()!['addressInfo'] != null
        ? BaseAddressInfo.fromMap(doc.data()!['addressInfo'])
        : BaseAddressInfo.empty();
    this.civic = doc.data()!['civic']??'';
    this.city = doc.data()!['city'];
    this.description = doc.data()!['description'];
    this.mq = doc.data()!['mq'];
    this.baths = doc.data()!['baths'];
    this.locals = doc.data()!['locals'];
    /*this.bathsAfter = doc.data()!['bathsAfter'];
    this.localsAfter = doc.data()!['localsAfter'];
    this.InternalDoorsAfter = doc.data()!['InternalDoorsAfter'];
    this.fixtureAfter = doc.data()!['fixtureAfter'];*/

    this.bathsIns = doc.data()!['bathsIns']??'';
    this.localsIns = doc.data()!['localsIns']??'';
    this.fixtureIns = doc.data()!['fixtureIns']??'';
    this.InternalDoorsIns = doc.data()!['InternalDoorsIns']??'';
    this.WalkableIns = doc.data()!['WalkableIns']??'';
    this.qualityOfTheArea = doc.data()!['qualityOfTheArea']??'';
    
    /*this.DemolitionIns = doc.data()!['DemolitionIns']??'';
    this.ConstructionIns = doc.data()!['ConstructionIns']??'';
    this.LinearMeterIns = doc.data()!['LinearMeterIns']??'';
    this.GeneralWallsIns = doc.data()!['GeneralWallsIns']??'';
    this.BathroomWallsIns = doc.data()!['BathroomWallsIns']??'';*/
    this.LighRenoIns = doc.data()!['LighRenoIns']??'';
    this.FullRenoIns = doc.data()!['FullRenoIns']??'';

    this.LighRenoInsMin = doc.data()!['LighRenoInsMin']??'';
    this.LighRenoInsMax = doc.data()!['LighRenoInsMax']??'';
    this.FullRenoInsMin = doc.data()!['FullRenoInsMin']??'';
    this.FullRenoInsMax = doc.data()!['FullRenoInsMax']??'';

    this.materialStandardLight = doc.data()!['materialStandardLight']??'';
    this.materialPremiumLight = doc.data()!['materialPremiumLight']??'';
    this.materialStandardFull = doc.data()!['materialStandardFull']??'';
    this.materialPremiumFull = doc.data()!['materialPremiumFull']??'';
    this.materialFixtureFull = doc.data()!['materialFixtureFull']??'';

    this.bedrooms = doc.data()!['bedrooms'];
    this.floors = doc.data()!['floors'];
    this.picturePaths = doc.data()!['picturePaths'] != null
        ? doc.data()!['picturePaths'].cast<String>()
        : [];
    
    this.photoDayTimePaths = doc.data()!['photoDayTimePaths'] != null
        ? doc.data()!['photoDayTimePaths'].cast<String>()
        : [];
    this.photoNightTimePaths = doc.data()!['photoNightTimePaths'] != null
        ? doc.data()!['photoNightTimePaths'].cast<String>()
        : [];
    this.photoBeforeAfterPaths = doc.data()!['photoBeforeAfterPaths'] != null
        ? doc.data()!['photoBeforeAfterPaths'].cast<String>()
        : [];
        
    this.videoRenderPaths = doc.data()!['videoRenderPaths'] != null
        ? doc.data()!['videoRenderPaths'].cast<String>()
        : [];
    
    this.photoBrochureCoverPaths = doc.data()!['photoBrochureCoverPaths'] != null
        ? doc.data()!['photoBrochureCoverPaths'].cast<String>()
        : [];
    this.photoBrochureCoverActualPaths = doc.data()!['photoBrochureCoverActualPaths'] != null
        ? doc.data()!['photoBrochureCoverActualPaths'].cast<String>()
        : [];
    this.photoBrochureRenderPaths = doc.data()!['photoBrochureRenderPaths'] != null
        ? doc.data()!['photoBrochureRenderPaths'].cast<String>()
        : [];
    this.videoBrochureVTPaths = doc.data()!['videoBrochureVTPaths'] != null
        ? doc.data()!['videoBrochureVTPaths'].cast<String>()
        : [];
    this.brochureProjectDescription = doc.data()!['brochureProjectDescription']??'';
    this.brochureActualDescription = doc.data()!['brochureActualDescription']??'';

    this.projectEnergyClass = doc.data()!['projectEnergyClass']??'';
    this.actualEnergyClass = doc.data()!['actualEnergyClass']??'';

    this.qrPaths = doc.data()!['qrPaths'] != null
        ? doc.data()!['qrPaths'].cast<String>()
        : [];

    this.photographs = doc.data()!['photographs'] != null
        ? doc.data()!['photographs'].cast<String>()
        : [];
    this.currentPlan = doc.data()!['currenPlan'] != null
        ? doc.data()!['currenPlan'].cast<String>()
        : [];
    
    this.propertyFeatures = doc.data()!['propertyFeatures'] != null
        ? doc.data()!['propertyFeatures'].cast<String>()
        : [];
    this.currentFeatures = doc.data()!['currentFeatures'] != null
        ? doc.data()!['currentFeatures'].cast<String>()
        : [];
    this.amenities = doc.data()!['amenities'];
    this.amenitiesCount = doc.data()!['amenitiesCount'];
    this.isActive = doc.data()!['isActive'] == null ? true : doc.data()!['isActive'];
    this.isArchived = doc.data()!['isArchived'] == null ? false : doc.data()!['isArchived'];
    this.publicStatus = doc.data()!['publicStatus'] != null
        ? doc.data()!['publicStatus'] as Map<String, dynamic>
        : {};

    if (doc.data()!['styles'] != null && doc.data()!['styles'].length > 0 ) {
      this.styles =
          List<PropertyConfigStyles>.from(doc.data()!['styles'].map((config) {
        return new PropertyConfigStyles.fromMap(config);
      }));
    }

    if (doc.data()!['optional'] != null && doc.data()!['optional'].length > 0 ) {
      //Nelle case attualmente in produzione la mappa è malformata
      //[{title: Condizionatore, price: 1399, description: Climatizzatore Ariston Alys Plus R-32 trial split, Potenza 9000 btu, wifi, 12 velocità}, {price: 249, title: Antifurto, description: Pannello di controllo, Sensori volumetrici con fotocamera, Cartelli dissuasori, Sirena, Sensori porte e finestre, App e lettore chiavi, Fumogeno zero vision}, {description: Incendio e scoppio, Eventi naturali, Arredi e contenuto, Furto, title: Assicurazione casa, price: 599}, {price: 7899, description: Cucina ArTre modello Up Design in composizione lineare 425x200 con isola frontale. Colorazione bianco opaco con top marmo. Inclusi nel prezzo poker di elettrodomestici (fuochi, forno, frigo e lavastoviglie) Hotpoint Ariston., title: Cucina}]

      this.optional = List<PropertyOptionalFeature>.from(
          doc.data()!['optional'].map((config) {
        return new PropertyOptionalFeature.fromMap(config);
      }));
    }

    this.startDate = doc.data()!['startDate'];
    this.endDate = doc.data()!['endDate'];
    this.limitDate = doc.data()!['limitDate'];
    this.virtualTour = doc.data()!['virtualTour'] == null ? '' : doc.data()!['virtualTour'];
    this.currentVirtualTour = doc.data()!['currentVirtualTour'] == null ? '' : doc.data()!['currentVirtualTour'];
    

    this.insertUid = doc.data()!['insert_uid'];
    this.updateUid = doc.data()!['update_uid'];
    this.insertTimestamp = doc.data()!['insertTimestamp'];
    this.updateTimestamp = doc.data()!['updateTimestamp'];
    this.archivedTimestamp = doc.data()!['archivedTimestamp'];
    this.basePrice = doc.data()!['basePrice']??0;
    
    this.activateNightTimePicture = doc.data()!['activateNightTimePicture']??true;
    this.activateVirtualTourProject = doc.data()!['activateVirtualTourProject']??true;
    this.activateVideoRender = doc.data()!['activateVideoRender']??true;
    this.activateVirtualTourCurrent = doc.data()!['activateVirtualTourCurrent']??true;
    
    this.code = doc.data()!['code']??'';
    this.codeCounter = doc.data()!['codeCounter']??0;
    this.year = doc.data()!['year']??0;
    this.clicks = doc.data()!['clicks']??0;
    this.sentToAgencyCounter = doc.data()!['sentToAgencyCounter']??0;
    this.brochurePdfPath = doc.data()!['brochurePdfPath'] ?? {};

    this.materialStandardFullMin = doc.data()!['materialStandardFullMin']??'';
    this.materialStandardFullMax = doc.data()!['materialStandardFullMax']??'';
    this.materialPremiumFullMin = doc.data()!['materialPremiumFullMin']??'';
    this.materialPremiumFullMax = doc.data()!['materialPremiumFullMax']??'';
    this.materialFixtureFullMin = doc.data()!['materialFixtureFullMin']??'';
    this.materialFixtureFullMax = doc.data()!['materialFixtureFullMax']??'';
    
    this.materialStandardLightMin = doc.data()!['materialStandardLightMin']??'';
    this.materialStandardLightMax = doc.data()!['materialStandardLightMax']??'';
    this.materialPremiumLightMin = doc.data()!['materialPremiumLightMin']??'';
    this.materialPremiumLightMax = doc.data()!['materialPremiumLightMax']??'';
    
    this.cutsCount = doc.data()!['cutsCount'] ?? '';
    this.children = doc.data()!['children'] != null
        ? List<String>.from(doc.data()!['children'])
        : [];
    this.buildingPictures = doc.data()!['buildingPictures'] != null
      ? List<String>.from(doc.data()!['buildingPictures'])
      : [];
    // this.optional =
    //     doc.data()!['optional'] == null ? {} : doc.data()!['optional'];

    this.projectStatus = doc.data()!['projectStatus'] ?? '';
  }

  setFromObject(Property doc) {

    /* Update the website ad data! */
    updateWebsiteAd(doc.firebaseId!);

    this.firebaseId = doc.firebaseId;
    this.propertyName = doc.propertyName != null ? doc.propertyName : '';
    this.publicationDate = doc.publicationDate != null ? doc.publicationDate : DateTime.now().millisecondsSinceEpoch;
    this.zone = doc.zone;
    this.type = doc.type;
    this.projectType = doc.projectType;
    this.location = doc.location;
    this.addressInfo =
        doc.addressInfo != null ? doc.addressInfo : BaseAddressInfo.empty();
    this.civic = doc.civic??'';
    this.city = doc.city;
    this.description = doc.description;
    this.mq = doc.mq;
    this.baths = doc.baths;
    this.locals = doc.locals;
    /*this.bathsAfter = doc.bathsAfter;
    this.localsAfter = doc.localsAfter;
    this.InternalDoorsAfter = doc.InternalDoorsAfter;
    this.fixtureAfter = doc.fixtureAfter;*/
    
    this.bedrooms = doc.bedrooms;
    this.floors = doc.floors;
    this.picturePaths = doc.picturePaths != null ? doc.picturePaths : [];

    this.bathsIns = doc.bathsIns??'';
    this.localsIns = doc.localsIns??'';
    this.fixtureIns = doc.fixtureIns??'';
    this.InternalDoorsIns = doc.InternalDoorsIns??'';
    this.WalkableIns = doc.WalkableIns??'';
    this.qualityOfTheArea = doc.qualityOfTheArea??'';
    
    /*this.DemolitionIns = doc.DemolitionIns??'';
    this.ConstructionIns = doc.ConstructionIns??'';
    this.LinearMeterIns = doc.LinearMeterIns??'';
    this.GeneralWallsIns = doc.GeneralWallsIns??'';
    this.BathroomWallsIns = doc.BathroomWallsIns??'';*/
    this.LighRenoIns = doc.LighRenoIns??'';
    this.FullRenoIns = doc.FullRenoIns??'';

    this.LighRenoInsMin = doc.LighRenoInsMin??'';
    this.LighRenoInsMax = doc.LighRenoInsMax??'';
    this.FullRenoInsMin = doc.FullRenoInsMin??'';
    this.FullRenoInsMax = doc.FullRenoInsMax??'';

    this.materialStandardLight = doc.materialStandardLight??'';
    this.materialPremiumLight = doc.materialPremiumLight??'';
    this.materialStandardFull = doc.materialStandardFull??'';
    this.materialPremiumFull = doc.materialPremiumFull??'';
    this.materialFixtureFull = doc.materialFixtureFull??'';

    this.photoDayTimePaths = doc.photoDayTimePaths != null ? doc.photoDayTimePaths : [];
    this.photoNightTimePaths = doc.photoNightTimePaths != null ? doc.photoNightTimePaths : [];
    this.photoBeforeAfterPaths = doc.photoBeforeAfterPaths != null ? doc.photoBeforeAfterPaths : [];

    
    this.videoRenderPaths = doc.videoRenderPaths != null ? doc.videoRenderPaths : [];
    this.qrPaths = doc.qrPaths != null ? doc.qrPaths : [];

    this.photoBrochureCoverPaths = doc.photoBrochureCoverPaths != null ? doc.photoBrochureCoverPaths : [];
    this.photoBrochureCoverActualPaths = doc.photoBrochureCoverActualPaths != null ? doc.photoBrochureCoverActualPaths : [];
    this.photoBrochureRenderPaths = doc.photoBrochureRenderPaths != null ? doc.photoBrochureRenderPaths : [];
    this.videoBrochureVTPaths = doc.videoBrochureVTPaths != null ? doc.videoBrochureVTPaths : [];
    this.brochureProjectDescription = doc.brochureProjectDescription != null ? doc.brochureProjectDescription : '';
    this.brochureActualDescription = doc.brochureActualDescription != null ? doc.brochureActualDescription : '';

    this.projectEnergyClass = doc.projectEnergyClass != null ? doc.projectEnergyClass : '';
    this.actualEnergyClass = doc.actualEnergyClass != null ? doc.actualEnergyClass : '';

    this.photographs = doc.photographs != null ? doc.photographs : [];
    this.currentPlan = doc.currentPlan != null ? doc.currentPlan : [];

    this.propertyFeatures = doc.propertyFeatures != null ? doc.propertyFeatures : [];
    this.currentFeatures = doc.currentFeatures != null ? doc.currentFeatures : [];
    this.amenities = doc.amenities;
    this.amenitiesCount = doc.amenitiesCount;
    this.isActive = doc.isActive == null ? true : doc.isActive;
    this.isArchived = doc.isArchived == null ? false : doc.isArchived;
    this.publicStatus = doc.publicStatus != null
        ? doc.publicStatus as Map<String, dynamic>
        : {};

    if (doc.styles != null) {
      this.styles = doc.styles;
    }

    if (doc.optional != null) {
      this.optional = doc.optional;
      
    }

    this.startDate = doc.startDate;
    this.endDate = doc.endDate;
    this.limitDate = doc.limitDate;
    this.virtualTour = doc.virtualTour;
    this.currentVirtualTour = doc.currentVirtualTour;
    

    this.insertUid = doc.insertUid;
    this.updateUid = doc.updateUid;
    this.insertTimestamp = doc.insertTimestamp;
    this.updateTimestamp = doc.updateTimestamp;
    this.archivedTimestamp = doc.archivedTimestamp;
    
    this.basePrice = doc.basePrice??0;
    
    this.activateNightTimePicture = doc.activateNightTimePicture??true;
    this.activateVirtualTourProject = doc.activateVirtualTourProject??true;
    this.activateVideoRender = doc.activateVideoRender??true;
    this.activateVirtualTourCurrent = doc.activateVirtualTourCurrent??true;

    this.code = doc.code??'';
    this.codeCounter = doc.codeCounter??0;
    this.year = doc.year??0;
    this.clicks = doc.clicks??0;
    this.sentToAgencyCounter = doc.sentToAgencyCounter??0;
    this.brochurePdfPath = doc.brochurePdfPath ?? {};

    this.materialStandardFullMin = doc.materialStandardFullMin??'';
    this.materialStandardFullMax = doc.materialStandardFullMax??'';
    this.materialPremiumFullMin = doc.materialPremiumFullMin??'';
    this.materialPremiumFullMax = doc.materialPremiumFullMax??'';
    this.materialFixtureFullMin = doc.materialFixtureFullMin??'';
    this.materialFixtureFullMax = doc.materialFixtureFullMax??'';
    
    this.materialStandardLightMin = doc.materialStandardLightMin??'';
    this.materialStandardLightMax = doc.materialStandardLightMax??'';
    this.materialPremiumLightMin = doc.materialPremiumLightMin??'';
    this.materialPremiumLightMax = doc.materialPremiumLightMax??'';
    
    this.cutsCount = doc.cutsCount ?? '';
    this.children = doc.children ?? [];
    this.buildingPictures = doc.buildingPictures ?? [];

    this.projectStatus = doc.projectStatus ?? '';
  
    // this.optional =
    //     doc.data()!['optional'] == null ? {} : doc.data()!['optional'];
  }

  Property.fromDocument(DocumentSnapshot<Map<String, dynamic>> doc) {

    this.firebaseId = doc.id;
    this.propertyName =
        doc.data()!['propertyName'] != null ? doc.data()!['propertyName'] : '';
    this.zone = doc.data()!['zone']??'';
    this.publicationDate = doc.data()!['publicationDate'] ?? DateTime.now().millisecondsSinceEpoch;
    this.type = doc.data()!['type']??'';
    this.projectType = doc.data()!['projectType']??'';
    
    this.location = doc.data()!['location'];
    this.addressInfo = (doc.data()!.containsKey('addressInfo') && doc.data()!['addressInfo'] != null) ? BaseAddressInfo.fromMap(doc.data()!['addressInfo']) : null;
    this.civic = doc.data()!['civic']??'';
    this.city = doc.data()!['city']??'';
    this.description = doc.data()!['description']??'';
    this.mq = doc.data()!['mq']??'';
    this.baths = doc.data()!['baths'].toString();
    this.locals = doc.data()!['locals']??'';
    /*this.bathsAfter = doc.data()!['bathsAfter']??'';
    this.localsAfter = doc.data()!['localsAfter']??'';
    this.InternalDoorsAfter = doc.data()!['InternalDoorsAfter']??'';
    this.fixtureAfter = doc.data()!['fixtureAfter']??'';*/

    this.bathsIns = doc.data()!['bathsIns']??'';
    this.localsIns = doc.data()!['localsIns']??'';
    this.fixtureIns = doc.data()!['fixtureIns']??'';
    this.InternalDoorsIns = doc.data()!['InternalDoorsIns']??'';
    this.WalkableIns = doc.data()!['WalkableIns']??'';
    this.qualityOfTheArea = doc.data()!['qualityOfTheArea']??'';
    
    /*this.DemolitionIns = doc.data()!['DemolitionIns']??'';
    this.ConstructionIns = doc.data()!['ConstructionIns']??'';
    this.LinearMeterIns = doc.data()!['LinearMeterIns']??'';
    this.GeneralWallsIns = doc.data()!['GeneralWallsIns']??'';
    this.BathroomWallsIns = doc.data()!['BathroomWallsIns']??'';*/
    this.LighRenoIns = doc.data()!['LighRenoIns']??'';
    this.FullRenoIns = doc.data()!['FullRenoIns']??'';
    this.LighRenoInsMin = doc.data()!['LighRenoInsMin']??'';
    this.LighRenoInsMax = doc.data()!['LighRenoInsMax']??'';
    this.FullRenoInsMin = doc.data()!['FullRenoInsMin']??'';
    this.FullRenoInsMax = doc.data()!['FullRenoInsMax']??'';

    this.materialStandardLight = doc.data()!['materialStandardLight']??'';
    this.materialPremiumLight = doc.data()!['materialPremiumLight']??'';
    this.materialStandardFull = doc.data()!['materialStandardFull']??'';
    this.materialPremiumFull = doc.data()!['materialPremiumFull']??'';
    this.materialFixtureFull = doc.data()!['materialFixtureFull']??'';

    this.bedrooms = doc.data()!['bedrooms']??'';
    this.floors = doc.data()!['floors']??'';
    this.picturePaths = doc.data()!['picturePaths'] != null
        ? doc.data()!['picturePaths'].cast<String>()
        : [];

    this.photoDayTimePaths = doc.data()!['photoDayTimePaths'] != null
        ? doc.data()!['photoDayTimePaths']
        : [];
    this.photoNightTimePaths = doc.data()!['photoNightTimePaths'] != null
        ? doc.data()!['photoNightTimePaths']
        : [];
    this.photoBeforeAfterPaths = doc.data()!['photoBeforeAfterPaths'] != null
        ? doc.data()!['photoBeforeAfterPaths']
        : [];
    
    this.videoRenderPaths = doc.data()!['videoRenderPaths'] != null
        ? doc.data()!['videoRenderPaths'].cast<String>()
        : [];

    this.photoBrochureCoverPaths = doc.data()!['photoBrochureCoverPaths'] != null
        ? doc.data()!['photoBrochureCoverPaths']
        : [];
    this.photoBrochureCoverActualPaths = doc.data()!['photoBrochureCoverActualPaths'] != null
        ? doc.data()!['photoBrochureCoverActualPaths']
        : [];
    this.photoBrochureRenderPaths = doc.data()!['photoBrochureRenderPaths'] != null
        ? doc.data()!['photoBrochureRenderPaths']
        : [];
    this.videoBrochureVTPaths = doc.data()!['videoBrochureVTPaths'] != null
        ? doc.data()!['videoBrochureVTPaths']
        : [];
    this.brochureProjectDescription = doc.data()!['brochureProjectDescription']??'';
    this.brochureActualDescription = doc.data()!['brochureActualDescription']??''; 

    this.projectEnergyClass = doc.data()!['projectEnergyClass']??''; 
    this.actualEnergyClass = doc.data()!['actualEnergyClass']??''; 
        
    this.qrPaths = doc.data()!['qrPaths'] != null
        ? doc.data()!['qrPaths'].cast<String>()
        : [];

    this.photographs = doc.data()!['photographs'] != null
        ? doc.data()!['photographs'].cast<String>()
        : [];
    this.currentPlan = doc.data()!['currenPlan'] != null
        ? doc.data()!['currenPlan'].cast<String>()
        : [];

    this.propertyFeatures = doc.data()!['propertyFeatures'] != null
        ? doc.data()!['propertyFeatures'].cast<String>()
        : [];
    this.currentFeatures = doc.data()!['currentFeatures'] != null
        ? doc.data()!['currentFeatures'].cast<String>()
        : [];
    this.amenities = doc.data()!['amenities']??[];
    this.amenitiesCount = doc.data()!['amenitiesCount'];
    this.isActive = doc.data()!['isActive'] == null ? true : doc.data()!['isActive'];
    this.isArchived = doc.data()!['isArchived'] == null ? false : doc.data()!['isArchived'];
    this.publicStatus = doc.data()!['publicStatus'] != null
        ? doc.data()!['publicStatus'] as Map<String, dynamic>
        : {};

    if (doc.data()!['styles'] != null && doc.data()!['styles'].length > 0) {
      this.styles =
          List<PropertyConfigStyles>.from(doc.data()!['styles'].map((config) {
          return new PropertyConfigStyles.fromMap(config);
        }));
    }

    if (doc.data()!['optional'] != null && doc.data()!['optional'].length > 0 ) {
    
      this.optional = List<PropertyOptionalFeature>.from(
          doc.data()!['optional'].map((config) {
        return new PropertyOptionalFeature.fromMap(config);
      }));
    }

    this.startDate = doc.data()!['startDate'];
    this.endDate = doc.data()!['endDate'];
    this.limitDate = doc.data()!['limitDate'];

    this.insertUid = doc.data()!['insert_uid'];
    this.updateUid = doc.data()!['update_uid'];
    this.insertTimestamp = doc.data()!['insertTimestamp'];
    this.updateTimestamp = doc.data()!['updateTimestamp'];
    this.archivedTimestamp = doc.data()!['archivedTimestamp'];
    
    this.virtualTour = doc.data()!['virtualTour'] == null ? '' : doc.data()!['virtualTour'];
    this.currentVirtualTour = doc.data()!['currentVirtualTour'] == null ? '' : doc.data()!['currentVirtualTour'];
    
    this.activateNightTimePicture = doc.data()!['activateNightTimePicture']??false;
    this.activateVirtualTourProject = doc.data()!['activateVirtualTourProject']??false;
    this.activateVideoRender = doc.data()!['activateVideoRender']??false;
    this.activateVirtualTourCurrent = doc.data()!['activateVirtualTourCurrent']??false;
    
    this.code = doc.data()!['code']??'no code';
    this.codeCounter = doc.data()!['codeCounter']??0;
    this.year = doc.data()!['year']??0;
    this.clicks = doc.data()!['clicks']??0;
    this.sentToAgencyCounter = doc.data()!['sentToAgencyCounter']??0;
    this.brochurePdfPath = doc.data()!['brochurePdfPath'] ?? {};

    this.materialStandardFullMin = doc.data()!['materialStandardFullMin']??'';
    this.materialStandardFullMax = doc.data()!['materialStandardFullMax']??'';
    this.materialPremiumFullMin = doc.data()!['materialPremiumFullMin']??'';
    this.materialPremiumFullMax = doc.data()!['materialPremiumFullMax']??'';
    this.materialFixtureFullMin = doc.data()!['materialFixtureFullMin']??'';
    this.materialFixtureFullMax = doc.data()!['materialFixtureFullMax']??'';
    
    this.materialStandardLightMin = doc.data()!['materialStandardLightMin']??'';
    this.materialStandardLightMax = doc.data()!['materialStandardLightMax']??'';
    this.materialPremiumLightMin = doc.data()!['materialPremiumLightMin']??'';
    this.materialPremiumLightMax = doc.data()!['materialPremiumLightMax']??'';
    
    this.cutsCount = doc.data()!['cutsCount'] ?? '';
    this.children = doc.data()!['children'] != null
      ? List<String>.from(doc.data()!['children'])
      : [];
    this.buildingPictures = doc.data()!['buildingPictures'] != null
      ? List<dynamic>.from(doc.data()!['buildingPictures'])
      : [];

    this.projectStatus = doc.data()!['projectStatus'] ?? '';

  }

  Map<String, dynamic> toMap() {
    return {
      'firebaseId': firebaseId,
      'publicationDate': publicationDate,
      'propertyName': propertyName,
      'zone': zone,
      'type': type,
      'projectType': projectType,
      'location': location,
      'addressInfo': addressInfo!.toMap(),
      'civic': civic,
      'city': city,
      'description': description,
      'mq': mq,
      'baths': baths,
      'locals': locals,
      'bathsIns': bathsIns,
      'localsIns': localsIns,
      'fixtureIns': fixtureIns,
      'InternalDoorsIns': InternalDoorsIns,
      'WalkableIns': WalkableIns,
      'qualityOfTheArea': qualityOfTheArea,
      /*'DemolitionIns': DemolitionIns,
      'ConstructionIns': ConstructionIns,
      'LinearMeterIns': LinearMeterIns,
      'GeneralWallsIns': GeneralWallsIns,
      'BathroomWallsIns': BathroomWallsIns,*/
      'LighRenoIns': LighRenoIns,
      'FullRenoIns': FullRenoIns,
      'LighRenoInsMin': LighRenoInsMin,
      'LighRenoInsMax': LighRenoInsMax,
      'FullRenoInsMin': FullRenoInsMin,
      'FullRenoInsMax': FullRenoInsMax,

      'materialStandardLight': materialStandardLight,
      'materialPremiumLight': materialPremiumLight,
      'materialStandardFull': materialStandardFull,
      'materialPremiumFull': materialPremiumFull,
      'materialFixtureFull': materialFixtureFull,

      'bedrooms': bedrooms,
      'floors': floors,
      'propertyFeatures': propertyFeatures,
      'currentFeatures': currentFeatures,
      'styles': styles != null ? styles!.map((e) => e.toMap()) : {},
      'startDate': startDate,
      'endDate': endDate,
      'limitDate': limitDate,
      'insertUid': insertUid,
      'updateUid': updateUid,
      'insertTimestamp': insertTimestamp,
      'updateTimestamp': updateTimestamp,
      'archivedTimestamp': archivedTimestamp,
      
      'picturePaths': picturePaths,

      'photoDayTimePaths': photoDayTimePaths,
      'photoNightTimePaths': photoNightTimePaths,
      'photoBeforeAfterPaths': photoBeforeAfterPaths,
      'videoRenderPaths': videoRenderPaths,
      'qrPaths': qrPaths,
      'photographs': photographs,
      'currenPlan': currentPlan,

      'photoBrochureCoverPaths': photoBrochureCoverPaths,
      "photoBrochureCoverActualPaths": photoBrochureCoverActualPaths,
      'photoBrochureRenderPaths': photoBrochureRenderPaths,
      'videoBrochureVTPaths': videoBrochureVTPaths,
      'brochureProjectDescription': brochureProjectDescription,
      'brochureActualDescription': brochureActualDescription,

      'projectEnergyClass': projectEnergyClass,
      'actualEnergyClass': actualEnergyClass,

      'amenities': amenities,
      'amenitiesCount': amenitiesCount,
      'optional': optional != null ? optional!.map((e) => e.toMap()) : {},
      'isActive': isActive,
      'isArchived': isArchived,
      'publicStatus': publicStatus,
      'virtualTour': virtualTour,
      'currentVirtualTour': currentVirtualTour,
      'basePrice': basePrice,
      'activateNightTimePicture': activateNightTimePicture,
      'activateVirtualTourProject': activateVirtualTourProject,
      'activateVideoRender': activateVideoRender,
      'activateVirtualTourCurrent': activateVirtualTourCurrent,
      
      'code': code,
      'codeCounter': codeCounter,
      'year': year,
      'clicks': clicks,
      'sentToAgencyCounter': sentToAgencyCounter,
      'brochurePdfPath': brochurePdfPath,
      'materialStandardFullMin': materialStandardFullMin,
      'materialStandardFullMax': materialStandardFullMax,
      'materialPremiumFullMin': materialPremiumFullMin,
      'materialPremiumFullMax': materialPremiumFullMax,
      'materialFixtureFullMin': materialFixtureFullMin,
      'materialFixtureFullMax': materialFixtureFullMax,
      
      'materialStandardLightMin': materialStandardLightMin,
      'materialStandardLightMax': materialStandardLightMax,
      'materialPremiumLightMin': materialPremiumLightMin,
      'materialPremiumLightMax': materialPremiumLightMax,
      'cutsCount': cutsCount,
      'children': children,
      'buildingPictures': buildingPictures,
      'projectStatus': projectStatus,

    };
  }

  Future<List<String>> uploadPropertyPictures(String docId,
      List<XFile> pictures, List<dynamic> existingPictures) async {
    List<String> files = [];

    // print('I am here');
    if (existingPictures.length > 0) {
      existingPictures.forEach((existingImage) {
        files.add(existingImage);
      });
    }

    // print(files);
    // print(pictures);

    for (var i = 0; i < pictures.length; i++) {
      if (pictures[i].runtimeType == XFile) {
        final String filename =
            Timestamp.now().millisecondsSinceEpoch.toString() +
                '-' +
                pictures[i].name;

        await uploadFile('newarcHomes/' + docId, filename, pictures[i])
            .then((uploadTask) {
          try {
            files.add(filename);
          } catch (e) {
            // print(e);
          }
        });
      }
    }

    // print(files);
    try {
      this.updatePictures(docId, files);
      return files;
    } catch (e) {
      // print(e);
      return [];
    }
  }

  Future uploadStylePictures(
      String docId, List<PropertyConfigStyles> styles) async {
    final List<PropertyConfigStyles>? updatedStyle = [];

    if (styles.length > 0) {
      for (var i = 0; i < styles.length; i++) {
        List<XFile> pictures = styles[i].pictures!;
        if (pictures.length > 0) {
          List<String> files = [];

          if (styles[i].picturePaths != null &&
              styles[i].picturePaths!.length > 0) {
            styles[i].picturePaths!.forEach((existingImage) {
              files.add(existingImage);
            });
          }

          for (var j = 0; j < pictures.length; j++) {
            if (pictures[j].runtimeType == XFile) {
              final String filename =
                  Timestamp.now().millisecondsSinceEpoch.toString() +
                      '-' +
                      pictures[j].name;

              await uploadFile('newarcHomes/' + docId, filename, pictures[j])
                  .then((uploadTask) {
                try {
                  files.add(filename);
                } catch (e) {
                  // print(e);
                }
              });
            }
          }

          updatedStyle!.add(PropertyConfigStyles(
            // styleId: styles[i].styleId,
            styleName: styles[i].styleName,
            description: styles[i].description,
            price: styles[i].price,
            picturePaths: files,
          ));

          this.styles![i] = PropertyConfigStyles(
            // styleId: styles[i].styleId,
            styleName: styles[i].styleName,
            description: styles[i].description,
            price: styles[i].price,
            picturePaths: files,
          );
        }
      }

      if (updatedStyle!.length > 0) {
        this.updateStylesPictures(docId, updatedStyle);
      }
    }
  }

  Future<String> addProperty(Property propertyData) async {
    // print(propertyData);
    return await _db
        .collection(this.collectionName!)
        .add(this.toMap())
        .then((value) async {
      // Store firebase id
      this.firebaseId = value.id;

      // upload images

      // if (this.pictures!.length > 0) {
      //   // print({'adpictures', value.id, this.pictures});
      //   List<String> fileList = await this.uploadPropertyPictures(
      //       value.id, this.pictures!, this.picturePaths!);
      //   this.picturePaths = fileList;
      // }

      if (this.styles!.length > 0) {
        // print({'adstyle', this.styles});
        await this.uploadStylePictures(value.id, this.styles!);
        // this.styles = updatedStyles;
      }

      return value.id;
    }).onError((error, stackTrace) {
      // print({error, stackTrace});
      return error.toString();
    });
  }

  Future<void> updateProperty() async {
    await _db
        .collection(this.collectionName!)
        .doc(this.firebaseId)
        .update(this.toMap());

    // if (this.pictures!.length > 0) {
    //   List<String> fileList = await this.uploadPropertyPictures(
    //       this.firebaseId!, this.pictures!, this.picturePaths!);
    //   this.picturePaths = fileList;
    // }

    if (this.styles!.length > 0) {
      await this.uploadStylePictures(this.firebaseId!, this.styles!);
      // this.styles = updatedStyles;
    }
  }

  Future<void> updatePictures(docId, List<String> files) async {
    return await _db
        .collection(this.collectionName!)
        .doc(docId)
        .update({'picturePaths': files, 'firebaseId': docId});
  }

  Future<void> updateStylesPictures(
      docId, List<PropertyConfigStyles> styles) async {
    // print(styles.map((e) => e.toMap() ));

    return await _db
        .collection(this.collectionName!)
        .doc(docId)
        .update({'styles': styles.map((e) => e.toMap())}).then((value) {});
  }

  Future<void> deleteProperty() async {
    await _db.collection(this.collectionName!).doc(this.firebaseId).delete();
  }

  Future<List<Property>> retrieveProperties() async {
    QuerySnapshot<Map<String, dynamic>> snapshot =
        await _db.collection(this.collectionName!).get();
    return snapshot.docs
        .map((docSnapshot) => Property.fromDocument(docSnapshot))
        .toList();
  }
}

class PropertyConfigStyles {
  String? styleId; // predefined, i.e. newarc-2022
  String? styleName;
  String? description; //predefined
  List<dynamic>? picturePaths;

  List<XFile>? pictures;
  String? price; // TODO da rendere int
  bool? isDefault;

  PropertyConfigStyles(
      {this.styleName,
      this.picturePaths,
      this.description,
      this.price,
      this.styleId,
      this.isDefault,
      this.pictures});

  Map<String, Object?> toJson() {
    return {
      'styleName': this.styleName,
      'description': this.description,
      'picturePaths': this.picturePaths,
      'pictures': this.pictures,
      'price': this.price,
      'styleId': this.styleId,
      'isDefault': this.isDefault
    };
  }

  Map<String, dynamic> toMap() {
    return {
      'styleName': styleName ?? '',
      'description': description ?? '',
      'picturePaths': picturePaths ?? [],
      // 'pictures': pictures ?? [],
      'price': price ?? 0,
      'styleId': styleId ?? '',
      'isDefault': isDefault ?? ''
    };
  }

  PropertyConfigStyles.fromMap(Map<String, dynamic> config)
      : styleName = config["styleName"],
        picturePaths = config["picturePaths"],
        price = config["price"],
        styleId = config["styleId"];
}

class PropertyAmenities {
  String? styleId; // predefined, i.e. newarc-2022
  String? styleName;
  String? description; //predefined
  List<dynamic>? picturePaths;

  List<XFile>? pictures;
  String? price; // TODO da rendere int
  bool? isDefault;

  PropertyAmenities(
      {this.styleName,
      this.picturePaths,
      this.description,
      this.price,
      this.styleId,
      this.isDefault,
      this.pictures});

  Map<String, Object?> toJson() {
    return {
      'styleName': this.styleName,
      'description': this.description,
      'picturePaths': this.picturePaths,
      'price': this.price,
      'styleId': this.styleId,
      'isDefault': this.isDefault
    };
  }

  Map<String, dynamic> toMap() {
    return {
      'styleName': styleName ?? '',
      'description': description ?? '',
      'picturePaths': picturePaths ?? [],
      'price': price ?? 0,
      'styleId': styleId ?? '',
      'isDefault': isDefault ?? ''
    };
  }

  PropertyAmenities.fromMap(Map<String, dynamic> config)
      : styleName = config["styleName"],
        picturePaths = config["picturePaths"],
        price = config["price"],
        styleId = config["styleId"];
}

class PropertyOptionalFeature {
  String? title;
  int? price;
  String? description;

  PropertyOptionalFeature({this.title, this.price, this.description});

  Map<String, Object?> toJson() {
    return {
      'title': this.title,
      'price': this.price,
      'description': this.description
    };
  }

  Map<String, dynamic> toMap() {
    return {
      'title': title ?? '',
      'price': price ?? 0,
      'description': description ?? ''
    };
  }

  PropertyOptionalFeature.fromMap(Map<String, dynamic> config)
      : title = config["title"],
        price = config["price"],
        description = config["description"];
}