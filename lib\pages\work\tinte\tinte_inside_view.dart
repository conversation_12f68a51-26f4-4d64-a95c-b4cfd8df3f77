import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:image_compression_flutter/image_compression_flutter.dart';
import 'package:newarc_platform/classes/NAMaterial.dart';
import 'package:newarc_platform/classes/NewarcMaterialSupplier.dart';
import 'package:newarc_platform/pages/work/tinte/tinte_controller.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:path/path.dart' as path;
import '../../../classes/NewarcMaterialManufacturer.dart';
import '../../../classes/NewarcMaterialDimension.dart';
import '../../../classes/NewarcMaterialDimensionMeta.dart';
import '../../../classes/NewarcMaterialManufacturerCollection.dart';
import '../../../classes/NewarcMaterialSubcategory.dart';
import '../../../classes/NewarcMaterialVariantSupplierPrice.dart';
import '../../../utils/storage.dart';
import '../../../widget/UI/base_newarc_popup.dart';
import '../../../widget/UI/custom_textformfield.dart';
import '../../../widget/UI/file-picker.dart';
import '../../../widget/UI/form-label.dart';
import '../../../widget/UI/link.dart';
import '../../../widget/UI/multi-select-dropdown.dart';
import '../../../widget/UI/tab/common_icon_button.dart';
import '../../../widget/UI/tab/status_widget.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;

class TinteInsideView extends StatefulWidget {
  final String? id;
  const TinteInsideView({super.key,required this.id});

  @override
  State<TinteInsideView> createState() => _TinteInsideViewState();
}

class _TinteInsideViewState extends State<TinteInsideView> {
  final controller = Get.put<TinteController>(TinteController());

  final List<dynamic> coverImages = [];
  final List<dynamic> applicationImages = [];

  bool isAvailable = false;
  bool isAnySupplierPriceAvailable = false;
  bool isMateriotechAvailable = false;
  List<Map<String, dynamic>>? selectedSubCategory = [];
  String naMaterialFirebaseId = '';

  Future<void>? fetchNewarcMaterialSupplierPricesFuture;
  String? selectedManufacturer = null;
  String? selectedCollection = null;
  Future<List>? collectionFuture;
  Future<List>? fetchNewarcMaterialManufacturerFuture;
  Future<List>? fetchNewarcMaterialSubCategoryFuture;

  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  bool isLoading = false;
  NAMaterial? naMaterialModel;

  @override
  void initState() {
    super.initState();
    initMaterial();
  }

  initMaterial()async{
    isLoading = true;
    try{
      if((widget.id?.isNotEmpty ?? false) && widget.id != "new"){

        DocumentSnapshot<Map<String, dynamic>> documentSnapshot = await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_NEWARCHMATERIAL)
            .doc(widget.id)
            .get();

        if(documentSnapshot.exists && documentSnapshot.data() != null){
          naMaterialModel = NAMaterial.fromDocument(documentSnapshot.data()!, widget.id!);
        }

        naMaterialFirebaseId = naMaterialModel?.firebaseId ?? "";
        selectedCollection = naMaterialModel?.newarcProductCollectionID ?? "";
        selectedManufacturer = naMaterialModel?.newarcManufacturerID ?? "";
        isAvailable = naMaterialModel?.configurationStatus ?? false;
        isMateriotechAvailable = naMaterialModel?.isMateriotechAvailable ?? false;


        controller.collectionController.text = naMaterialModel?.newarcProductCollectionID ?? "";
        controller.manufacturerController.text = naMaterialModel?.newarcManufacturerID ?? "";
        controller.productNameController.text = naMaterialModel?.name ?? "";

        if (controller.manufacturerController.text.isNotEmpty) {
          collectionFuture = fetchNewarcProductCollection(manufacturerId: controller.manufacturerController.text);
        }

        coverImages.clear();
        applicationImages.clear();
        controller.formProgressMessage = '';

        if (naMaterialModel?.coverImagePath?["filename"] != null && naMaterialModel?.coverImagePath?["filename"] != "") {
          coverImages.add(naMaterialModel!.coverImagePath!["filename"]);
        }

        if (naMaterialModel?.applicationImagesPath?.isNotEmpty ?? false) {
          applicationImages.addAll(
              naMaterialModel!.applicationImagesPath!
                  .map((image) => image["filename"]?.toString() ?? "")
                  .where((filename) => filename.isNotEmpty)
          );
        }

        fetchNewarcMaterialSupplierPricesFuture = fetchSupplierPrices();
        fetchNewarcMaterialManufacturerFuture = fetchNewarcManufacturer();
        fetchNewarcMaterialSubCategoryFuture = fetchNewarcSubCategory();

      }else{
        DocumentReference docRefNewarchmaterial = await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_NEWARCHMATERIAL)
            .doc();
        naMaterialFirebaseId = docRefNewarchmaterial.id;



        selectedManufacturer = controller.manufacturerController.text.isNotEmpty
            ? controller.manufacturerController.text
            : "";
        selectedCollection = controller.collectionController.text.isNotEmpty
            ? controller.collectionController.text
            : null;

        fetchNewarcMaterialSupplierPricesFuture = fetchSupplierPrices();
        fetchNewarcMaterialManufacturerFuture = fetchNewarcManufacturer();
        fetchNewarcMaterialSubCategoryFuture = fetchNewarcSubCategory();
      }
    }catch(e){
      log("Error while fetching material ===> ${e.toString()}");
    }finally{
      isLoading = false;
    }
  }

  @override
  void dispose() {
    super.dispose();
    controller.clearController();
  }

  @override
  Widget build(BuildContext context) {
    return isLoading ? Center(
        child: CircularProgressIndicator(
          color: Theme.of(context).primaryColor,
        )) : Column(
      children: [
        Expanded(
          child: ListView(
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.max,
                children: [
                  Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        IconButton(
                          hoverColor: Colors.transparent,
                          focusColor: Colors.transparent,
                          onPressed: () {
                            context.pop();
                          },
                          icon: SvgPicture.asset('assets/icons/arrow_left.svg',
                              height: 20, color: Colors.black),
                        ),
                        SizedBox(
                          width: 10,
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            NarFormLabelWidget(
                              label: "Tinte",
                              fontSize: 10,
                              fontWeight: '600',
                              textColor: AppColor.greyColor,
                            ),
                            SizedBox(
                              width: 5,
                            ),
                            NarFormLabelWidget(
                              label: naMaterialModel?.name?.isNotEmpty ?? false ?  "${naMaterialModel?.name}" : "Aggiungi nuovo",
                              fontSize: 22,
                              fontWeight: '700',
                              textColor: Colors.black,
                            ),
                          ],
                        )
                      ]),
                  SizedBox(height: 20),
                  Container(
                    padding: EdgeInsets.all(15),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(13),
                      border: Border.all(width: 1, color: Color(0Xffe7e7e7)),
                    ),
                    width: double.infinity,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [

                        Form(
                          key: formKey,
                          child: IntrinsicHeight(
                              child: Row(
                                children: [
                                  Opacity(
                                    child: AbsorbPointer(
                                      absorbing: (naMaterialModel?.firebaseId?.isNotEmpty ?? false),
                                      child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          //?----------------------------------- Produttore Section Start ------------------------------
                                          NarFormLabelWidget(
                                            label: "Produttore",
                                            fontSize: 18,
                                            fontWeight: '700',
                                            textColor: AppColor.black,
                                          ),
                                          SizedBox(height: 10),
                                          SizedBox(
                                            width: 350,
                                            child: Row(
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              children: [
                                                NarFormLabelWidget(
                                                  label: "Seleziona produttore",
                                                  fontSize: 14,
                                                  fontWeight: '600',
                                                  textColor: AppColor.greyColor,
                                                ),
                                                NarLinkWidget(
                                                  fontSize: 12,
                                                  fontWeight: '600',
                                                  textColor: Theme.of(context).primaryColor,
                                                  text: 'Aggiungi produttore',
                                                  textDecoration: TextDecoration.underline,
                                                  onClick: () {
                                                    showProduttorePopup();
                                                  },
                                                )
                                              ],
                                            ),
                                          ),
                                          SizedBox(height: 10),
                                          SizedBox(
                                            width: 350,
                                            height: 51,
                                            child: FutureBuilder<List>(
                                                future: fetchNewarcMaterialManufacturerFuture,
                                                builder: (context, snapshot) {
                                                  if (snapshot.hasData) {
                                                    return DropdownButtonFormField<String>(
                                                      isExpanded: true,
                                                      initialValue: selectedManufacturer?.isNotEmpty ?? false ? selectedManufacturer : null,
                                                      icon: Padding(
                                                        padding: EdgeInsets.only(right: 8),
                                                        child: SvgPicture.asset(
                                                          'assets/icons/arrow_down.svg',
                                                          width: 12,
                                                          color: Color(0xff7e7e7e),
                                                        ),
                                                      ),
                                                      style: TextStyle(
                                                        overflow: TextOverflow.ellipsis,
                                                        color: Colors.black,
                                                        fontSize: 12.0,
                                                        fontWeight: FontWeight.bold,
                                                        fontStyle: FontStyle.normal,
                                                      ),
                                                      borderRadius: BorderRadius.circular(8),
                                                      decoration: InputDecoration(
                                                        border: OutlineInputBorder(
                                                          borderRadius: BorderRadius.all(
                                                              Radius.circular(8)),
                                                          borderSide: BorderSide(
                                                            color:
                                                            Color.fromRGBO(227, 227, 227, 1),
                                                            width: 1,
                                                          ),
                                                        ),
                                                        hintStyle: TextStyle(
                                                          color: Colors.grey,
                                                          fontSize: 15.0,
                                                          fontWeight: FontWeight.w800,
                                                          fontStyle: FontStyle.normal,
                                                          letterSpacing: 0,
                                                        ),
                                                        focusedBorder: OutlineInputBorder(
                                                          borderRadius: BorderRadius.all(
                                                              Radius.circular(8)),
                                                          borderSide: BorderSide(
                                                            color:
                                                            Color.fromRGBO(227, 227, 227, 1),
                                                            width: 1,
                                                          ),
                                                        ),
                                                        contentPadding: EdgeInsets.symmetric(
                                                            horizontal: 12, vertical: 8),
                                                        fillColor: Colors.white,
                                                      ),
                                                      onChanged: (String? value) async{
                                                        formKey.currentState!.validate();
                                                        if (value != null && value.isNotEmpty) {
                                                          setState(() {
                                                            controller.manufacturerController.text = value;
                                                            controller.collectionController.clear();
                                                            selectedCollection = null;
                                                            collectionFuture = Future.value([]);
                                                          });
                                                          final newCollectionData = await fetchNewarcProductCollection(manufacturerId: value);

                                                          // Step 3: Update the state with the fetched data
                                                          setState(() {
                                                            collectionFuture = Future.value(newCollectionData);
                                                          });
                                                        }

                                                      },
                                                      validator: (value) {
                                                        if (value == null || value.isEmpty) {
                                                          return "Required!";
                                                        }
                                                        return null;
                                                      },
                                                      dropdownColor: Colors.white,
                                                      items: snapshot.data
                                                          ?.map<DropdownMenuItem<String>>((item) {
                                                        return DropdownMenuItem<String>(
                                                          value: item['value'],
                                                          enabled: !(naMaterialModel?.firebaseId?.isNotEmpty ?? false),
                                                          child: NarFormLabelWidget(
                                                            label: item['label']!,
                                                            textColor: Colors.black,
                                                            fontSize: 14,
                                                            fontWeight: '600',
                                                          ),
                                                        );
                                                      }).toList(),
                                                    );
                                                  } else if (snapshot.hasError) {
                                                    return Container(
                                                      width: 30,
                                                      height: 30,
                                                      alignment: Alignment.center,
                                                      decoration: BoxDecoration(
                                                        borderRadius: BorderRadius.circular(100),
                                                        color:
                                                        const Color.fromARGB(255, 19, 17, 17),
                                                      ),
                                                    );
                                                  }

                                                  return Center(
                                                    child: CircularProgressIndicator(
                                                      color: Theme.of(context).primaryColor,
                                                    ),
                                                  );
                                                }),
                                          ),
                                          //?----------------------------------- Produttore Section end ------------------------------

                                          SizedBox(height: 30),

                                          //?----------------------------------- Collezione Section Start ------------------------------
                                          NarFormLabelWidget(
                                            label: "Collezione",
                                            fontSize: 18,
                                            fontWeight: '700',
                                            textColor: AppColor.black,
                                          ),
                                          SizedBox(height: 10),
                                          SizedBox(
                                            width: 350,
                                            child: Row(
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              children: [
                                                NarFormLabelWidget(
                                                  label: "Seleziona collezione",
                                                  fontSize: 14,
                                                  fontWeight: '600',
                                                  textColor: AppColor.greyColor,
                                                ),
                                                controller.manufacturerController.text.isNotEmpty
                                                    ?
                                                NarLinkWidget(
                                                  fontSize: 12,
                                                  fontWeight: '600',
                                                  textColor: Theme.of(context).primaryColor,
                                                  text: 'Aggiungi collezione',
                                                  textDecoration: TextDecoration.underline,
                                                  onClick: () {
                                                    showCollectionPopup();
                                                  },
                                                )
                                                    : SizedBox.shrink()
                                              ],
                                            ),
                                          ),
                                          SizedBox(height: 10),
                                          SizedBox(
                                            width: 350,
                                            height: 51,
                                            child: FutureBuilder<List>(
                                              future: collectionFuture,
                                              builder: (context, snapshot) {
                                                if (snapshot.connectionState == ConnectionState.waiting) {
                                                  return Center(
                                                    child: CircularProgressIndicator(
                                                      color: Theme.of(context).primaryColor,
                                                    ),
                                                  );
                                                }

                                                if (snapshot.hasError) {
                                                  return Center(
                                                    child: Text(
                                                      "Error loading collections",
                                                      style: TextStyle(color: Colors.red),
                                                    ),
                                                  );
                                                }

                                                List<DropdownMenuItem<String>> dropdownItems = [];
                                                if (snapshot.hasData && snapshot.data!.isNotEmpty) {
                                                  dropdownItems = snapshot.data!
                                                      .map<DropdownMenuItem<String>>((item) => DropdownMenuItem<String>(
                                                    value: item['value'],
                                                    enabled: !(naMaterialModel?.firebaseId?.isNotEmpty ?? false),
                                                    child: NarFormLabelWidget(
                                                      label: item['label']!,
                                                      textColor: Colors.black,
                                                      fontSize: 14,
                                                      fontWeight: '600',
                                                    ),
                                                  ))
                                                      .toList();
                                                }

                                                return DropdownButtonFormField<String>(
                                                  isExpanded: true,
                                                  initialValue: selectedCollection?.isNotEmpty == true ? selectedCollection : null,
                                                  icon: Padding(
                                                    padding: EdgeInsets.only(right: 8),
                                                    child: SvgPicture.asset(
                                                      'assets/icons/arrow_down.svg',
                                                      width: 12,
                                                      color: Color(0xff7e7e7e),
                                                    ),
                                                  ),
                                                  style: TextStyle(
                                                    overflow: TextOverflow.ellipsis,
                                                    color: Colors.black,
                                                    fontSize: 12.0,
                                                    fontWeight: FontWeight.bold,
                                                    fontStyle: FontStyle.normal,
                                                  ),
                                                  borderRadius: BorderRadius.circular(8),
                                                  decoration: InputDecoration(
                                                    border: OutlineInputBorder(
                                                      borderRadius: BorderRadius.all(Radius.circular(8)),
                                                      borderSide: BorderSide(
                                                        color: Color.fromRGBO(227, 227, 227, 1),
                                                        width: 1,
                                                      ),
                                                    ),
                                                    hintStyle: TextStyle(
                                                      color: Colors.grey,
                                                      fontSize: 15.0,
                                                      fontWeight: FontWeight.w800,
                                                      fontStyle: FontStyle.normal,
                                                      letterSpacing: 0,
                                                    ),
                                                    focusedBorder: OutlineInputBorder(
                                                      borderRadius: BorderRadius.all(Radius.circular(8)),
                                                      borderSide: BorderSide(
                                                        color: Color.fromRGBO(227, 227, 227, 1),
                                                        width: 1,
                                                      ),
                                                    ),
                                                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                                    fillColor: Colors.white,
                                                  ),
                                                  onChanged: (String? value) {
                                                    setState(() {
                                                      selectedCollection = value ?? "";
                                                      controller.collectionController.text = value ?? "";
                                                    });
                                                    formKey.currentState!.validate();
                                                  },
                                                  validator: (value) {
                                                    if (value == null || value.isEmpty) {
                                                      return "Required!";
                                                    }
                                                    return null;
                                                  },
                                                  dropdownColor: Colors.white,
                                                  items: dropdownItems,
                                                );
                                              },
                                            ),
                                          ),
                                          SizedBox(height: 30),
                                          NarFormLabelWidget(
                                            label: "Identificazione prodotto",
                                            fontSize: 18,
                                            fontWeight: '700',
                                            textColor: AppColor.black,
                                          ),
                                          SizedBox(height: 10),
                                          Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            children: [
                                              Container(
                                                child: NarFormLabelWidget(
                                                  label: "Nome prodotto",
                                                  fontSize: 14,
                                                  fontWeight: '600',
                                                  textColor: AppColor.greyColor,
                                                ),
                                                alignment: Alignment.center,
                                                height: 33,
                                              ),
                                              SizedBox(
                                                height: 75,
                                                width: 350,
                                                child: CustomTextFormField(
                                                  isExpanded: false,
                                                  label: "",
                                                  minLines: 1,
                                                  controller: controller.productNameController,
                                                  enabled: !(naMaterialModel?.firebaseId?.isNotEmpty ?? false),
                                                  validator: (value) {
                                                    if (value == null || value.isEmpty) {
                                                      return "Required!";
                                                    }
                                                    return null;
                                                  },
                                                ),
                                              ),
                                            ],
                                          ),
                                          //?----------------------------------- Collezione Section end ----------------------------------



                                          //?----------------------------------- Identification Section start ----------------------------------
                                        ],
                                      ),
                                    ),
                                    opacity: (naMaterialModel?.firebaseId?.isNotEmpty ?? false) ? 0.5 : 1.0,
                                  ),
                                  SizedBox(width: 20,),
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    mainAxisSize: MainAxisSize.max,
                                    children: [
                                      Spacer(flex: 3,),
                                      (naMaterialModel?.firebaseId?.isNotEmpty ?? false) ? Container(
                                        height: 25,
                                        width: 110,
                                        child: NarLinkWidget(
                                          fontSize: 12,
                                          fontWeight: '600',
                                          textColor: AppColor.greyColor,
                                          text: 'Modifica collezione',
                                          textDecoration: TextDecoration.underline,
                                          onClick: () {
                                            NewarcMaterialManufacturerCollection selectedCollection = controller.newarcProductCollection.firstWhere(
                                                  (cal) => cal.collectionFirebaseId == naMaterialModel?.newarcProductCollectionID,
                                            );
                                            showCollectionModificationPopup(selectedCollection);
                                          },
                                        ),
                                      ) : SizedBox.shrink(),
                                      Spacer(),
                                      Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          SizedBox(
                                            width: 350,
                                            child: Row(
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              children: [
                                                NarFormLabelWidget(
                                                  label: "Sottocategoria",
                                                  fontSize: 14,
                                                  fontWeight: '600',
                                                  textColor: AppColor.greyColor,
                                                ),
                                                NarLinkWidget(
                                                  fontSize: 12,
                                                  fontWeight: '600',
                                                  textColor: Theme.of(context).primaryColor,
                                                  text: 'Aggiungi sottocategoria',
                                                  textDecoration: TextDecoration.underline,
                                                  onClick: () {
                                                    showAddSubCategoryPopup();
                                                  },
                                                )
                                              ],
                                            ),
                                          ),
                                          SizedBox(
                                            width: 350,
                                            height: 75,
                                            child: FutureBuilder<List>(
                                                future: fetchNewarcMaterialSubCategoryFuture,
                                                builder: (context, snapshot) {
                                                  if (snapshot.hasData) {
                                                    return MultiSelectDropdownWidget(
                                                      dialogTitle: "Seleziona Sottocategoria",
                                                      options: snapshot.data?.map((item) => item).toList() ?? [],
                                                      initialValue: selectedSubCategory,
                                                      onChanged: (List<dynamic> selectedValues) {
                                                        setState(() {
                                                          selectedSubCategory = selectedValues
                                                              .map((item) => Map<String, dynamic>.from(item))
                                                              .toList();
                                                        });
                                                      },
                                                    );
                                                  } else if (snapshot.hasError) {
                                                    return Container(
                                                      width: 30,
                                                      height: 30,
                                                      alignment: Alignment.center,
                                                      decoration: BoxDecoration(
                                                        borderRadius: BorderRadius.circular(100),
                                                        color:
                                                        const Color.fromARGB(255, 19, 17, 17),
                                                      ),
                                                    );
                                                  }

                                                  return Center(
                                                    child: CircularProgressIndicator(
                                                      color: Theme.of(context).primaryColor,
                                                    ),
                                                  );
                                                }),
                                          ),
                                        ],
                                      ),
                                    ],
                                  )

                                ],
                              )
                          ),
                        ),

                        SizedBox(height: 30),

                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              width: 350,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(13),
                                border: Border.all(
                                  width: 1,
                                  color: Color(0xffE1E1E1),
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(20.0),
                                child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          NarFormLabelWidget(
                                            label: 'Cover',
                                            fontSize: 14,
                                            fontWeight: '700',
                                            textColor: AppColor.black,
                                          ),
                                          NarFilePickerWidget(
                                            allowMultiple: false,
                                            filesToDisplayInList: 0,
                                            removeButton: true,
                                            isDownloadable: false,
                                            removeButtonText: 'Elimina',
                                            removeButtonTextColor: Color(0xff797979),
                                            uploadButtonPosition: 'back',
                                            showMoreButtonText: '+ espandi',
                                            actionButtonPosition: 'bottom',
                                            displayFormat: 'inline-button',
                                            containerWidth: 65,
                                            containerHeight: 65,
                                            containerBorderRadius: 8,
                                            borderRadius: 7,
                                            fontSize: 11,
                                            fontWeight: '600',
                                            text: 'Carica',
                                            borderSideColor: Theme.of(context).primaryColor,
                                            hoverColor: Color.fromRGBO(133, 133, 133, 1),
                                            allFiles: coverImages,
                                            isResizeImage: true,
                                            compressionQuality: 80,
                                            resizeImageSize: [[200,200,"thumbnail"]],
                                            pageContext: context,
                                            storageDirectory: "newArcMaterial/cover/$naMaterialFirebaseId",
                                            removeExistingOnChange: true,
                                            progressMessage: [''],
                                            notAccent: true,
                                            splashColor: Color(0xffE5E5E5),
                                            height: 35,
                                            buttonWidth: 125,
                                            buttonTextColor: Colors.black,
                                            onUploadCompleted: () {
                                              if (mounted) {
                                                setState(() {});
                                              }
                                            },
                                          )
                                        ],
                                      ),
                                      SizedBox(
                                        height: 10,
                                      ),
                                      NarFilePickerWidget(
                                        allowMultiple: false,
                                        filesToDisplayInList: 0,
                                        removeButton: true,
                                        isDownloadable: false,
                                        removeButtonText: 'Elimina',
                                        removeButtonTextColor: Color(0xff797979),
                                        uploadButtonPosition: 'back',
                                        showMoreButtonText: '+ espandi',
                                        actionButtonPosition: 'bottom',
                                        displayFormat: 'inline-widget',
                                        containerWidth: 86,
                                        containerHeight: 86,
                                        containerBorderRadius: 8,
                                        borderRadius: 7,
                                        fontSize: 11,
                                        fontWeight: '600',
                                        text: 'Carica Cover',
                                        borderSideColor: Theme.of(context).primaryColor,
                                        hoverColor: Color.fromRGBO(133, 133, 133, 1),
                                        allFiles: coverImages.map((filename) {
                                          final ext = path.extension(filename);
                                          final base = path.basenameWithoutExtension(filename);
                                          return '${base}_thumbnail$ext';
                                        }).toList(),
                                        pageContext: context,
                                        storageDirectory: "newArcMaterial/cover/$naMaterialFirebaseId",
                                        removeExistingOnChange: true,
                                        progressMessage: [''],
                                        notAccent: true,
                                        showTitle: false,
                                        onUploadCompleted: () {
                                          if (mounted) {
                                            setState(() {});
                                          }
                                        },
                                      ),
                                    ]),
                              ),
                            ),
                            SizedBox(width: 20),
                            Expanded(
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(13),
                                  border: Border.all(
                                    width: 1,
                                    color: Color(0xffE1E1E1),
                                  ),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(20.0),
                                  child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            NarFormLabelWidget(
                                              label: 'Immagini applicazioni',
                                              fontSize: 14,
                                              fontWeight: '700',
                                              textColor: AppColor.black,
                                            ),
                                            NarFilePickerWidget(
                                              allowMultiple: true,
                                              filesToDisplayInList: 0,
                                              removeButton: true,
                                              isDownloadable: false,
                                              removeButtonText: 'Elimina',
                                              removeButtonTextColor: Color(0xff797979),
                                              uploadButtonPosition: 'back',
                                              showMoreButtonText: '+ espandi',
                                              actionButtonPosition: 'bottom',
                                              displayFormat: 'inline-button',
                                              containerWidth: 65,
                                              containerHeight: 65,
                                              containerBorderRadius: 8,
                                              borderRadius: 7,
                                              fontSize: 11,
                                              fontWeight: '600',
                                              text: 'Carica',
                                              borderSideColor: Theme.of(context).primaryColor,
                                              hoverColor: Color.fromRGBO(133, 133, 133, 1),
                                              allFiles: applicationImages,
                                              pageContext: context,
                                              storageDirectory: "newArcMaterial/applicationImages/$naMaterialFirebaseId",
                                              removeExistingOnChange: true,
                                              progressMessage: [''],
                                              notAccent: true,
                                              splashColor: Color(0xffE5E5E5),
                                              height: 35,
                                              buttonWidth: 125,
                                              buttonTextColor: Colors.black,
                                              onUploadCompleted: () {
                                                if (mounted) {
                                                  setState(() {});
                                                }
                                              },
                                            )
                                          ],
                                        ),
                                        SizedBox(
                                          height: 15,
                                        ),
                                        NarFilePickerWidget(
                                          allowMultiple: true,
                                          filesToDisplayInList: 0,
                                          removeButton: true,
                                          isDownloadable: false,
                                          removeButtonText: 'Elimina',
                                          removeButtonTextColor: Color(0xff797979),
                                          uploadButtonPosition: 'back',
                                          showMoreButtonText: '+ espandi',
                                          actionButtonPosition: 'bottom',
                                          displayFormat: 'inline-widget',
                                          containerWidth: 86,
                                          containerHeight: 86,
                                          containerBorderRadius: 8,
                                          borderRadius: 7,
                                          fontSize: 11,
                                          fontWeight: '600',
                                          text: 'Carica Application',
                                          borderSideColor: Theme.of(context).primaryColor,
                                          hoverColor: Color.fromRGBO(133, 133, 133, 1),
                                          allFiles: applicationImages,
                                          pageContext: context,
                                          storageDirectory: "newArcMaterial/applicationImages/$naMaterialFirebaseId",
                                          removeExistingOnChange: true,
                                          progressMessage: [''],
                                          notAccent: true,
                                          showTitle: false,
                                          onUploadCompleted: () {
                                            if (mounted) {
                                              setState(() {});
                                            }
                                          },
                                        )
                                      ]),
                                ),
                              ),
                            ),
                          ],
                        ),
                        //?----------------------------------- Identification Section end ----------------------------------

                        SizedBox(height: 30),

                        //?----------------------------------- Suppliers and prices Section start ----------------------------------
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            NarFormLabelWidget(
                              label: "Fornitori e prezzi",
                              fontSize: 18,
                              fontWeight: '700',
                              textColor: AppColor.black,
                            ),
                            NarLinkWidget(
                              fontSize: 12,
                              fontWeight: '600',
                              textColor: Theme.of(context).primaryColor,
                              text: 'Seleziona fornitori',
                              textDecoration: TextDecoration.underline,
                              onClick: () {
                                controller.supplierNameDropdownController.clear();
                                controller.codeAtSupplierController.clear();
                                controller.supplierPriceController.clear();
                                controller.newarcPriceController.clear();
                                Future.delayed(Duration(milliseconds: 100));
                                showSelectSupplierPopup(isEdit: false);
                              },
                            )
                          ],
                        ),
                        SizedBox(height: 20),
                        FutureBuilder(
                          future: fetchNewarcMaterialSupplierPricesFuture,
                          builder: (context, snapshot) {
                            if (snapshot.connectionState == ConnectionState.waiting) {
                              return Center(
                                child: CircularProgressIndicator(
                                  color: Theme.of(context).primaryColor,
                                ),
                              );
                            }
                            if (snapshot.connectionState == ConnectionState.done) {
                              if (controller.newarcMaterialSupplierPrices.isNotEmpty) {
                                return ListView.separated(
                                  shrinkWrap: true,
                                  itemBuilder: (context, index) {
                                    final item = controller.newarcMaterialSupplierPrices[index];
                                    return Container(
                                      width: double.infinity,
                                      padding: EdgeInsets.all(15),
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(10),
                                          border: Border.all(
                                              color: Color(0xffD5D5D5))),
                                      child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          NarFormLabelWidget(
                                            label: controller.productNameController.text,
                                            fontWeight: '700',
                                            textColor: AppColor.black,
                                            fontSize: 15,
                                          ),
                                          SizedBox(
                                            height: 10,
                                          ),
                                          Container(
                                              width: double.infinity,
                                              margin: EdgeInsets.only(bottom: 10),
                                              padding: EdgeInsets.all(10),
                                              decoration: BoxDecoration(
                                                  color: Color(0xffF4F4F4),
                                                  borderRadius: BorderRadius.circular(10)),
                                              child: Row(
                                                mainAxisAlignment: MainAxisAlignment.start,
                                                children: [
                                                  Expanded(
                                                    flex: 2,
                                                    child: Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .start,
                                                      children: [
                                                        StatusWidget(
                                                          statusColor: (item.status ?? false) ? Color(0xff39C14F) : Color(0xffDD0000),
                                                        ),
                                                        SizedBox(
                                                          width: 10,
                                                        ),
                                                        Column(
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .start,
                                                          children: [
                                                            NarFormLabelWidget(
                                                              label:
                                                                  'Fornitore',
                                                              fontWeight: '500',
                                                              textColor: AppColor
                                                                  .greyColor,
                                                              fontSize: 12,
                                                            ),
                                                            SizedBox(
                                                              height: 5,
                                                            ),
                                                            NarFormLabelWidget(
                                                              label: item.supplierName,
                                                              fontWeight: '600',
                                                              textColor: AppColor.black,
                                                              fontSize: 13,
                                                            ),
                                                          ],
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  Expanded(
                                                    flex: 1,
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        NarFormLabelWidget(
                                                          label: 'Cod. Forn.',
                                                          fontWeight: '500',
                                                          textColor: AppColor
                                                              .greyColor,
                                                          fontSize: 12,
                                                        ),
                                                        SizedBox(
                                                          height: 5,
                                                        ),
                                                        NarFormLabelWidget(
                                                          label: item.code,
                                                          fontWeight: '600',
                                                          textColor:
                                                              AppColor.black,
                                                          fontSize: 13,
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  Expanded(
                                                    flex: 1,
                                                    child: Column(
                                                      crossAxisAlignment: CrossAxisAlignment.start,
                                                      children: [
                                                        NarFormLabelWidget(
                                                          label: 'Prezzo Forn.',
                                                          fontWeight: '500',
                                                          textColor: AppColor
                                                              .greyColor,
                                                          fontSize: 12,
                                                        ),
                                                        SizedBox(
                                                          height: 5,
                                                        ),
                                                        NarFormLabelWidget(
                                                          label: "${controller.localCurrencyFormatMain.format(item.basePrice)} €/mq",
                                                          fontWeight: '600',
                                                          textColor:
                                                              AppColor.black,
                                                          fontSize: 13,
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  Expanded(
                                                    flex: 1,
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        NarFormLabelWidget(
                                                          label:
                                                              'Prezzo Newarc',
                                                          fontWeight: '500',
                                                          textColor: AppColor
                                                              .greyColor,
                                                          fontSize: 12,
                                                        ),
                                                        SizedBox(
                                                          height: 5,
                                                        ),
                                                        NarFormLabelWidget(
                                                          label: "${controller.localCurrencyFormatMain.format(item.retailPrice)} €/mq",
                                                          fontWeight: '600',
                                                          textColor:
                                                              AppColor.black,
                                                          fontSize: 13,
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  Expanded(
                                                    flex: 1,
                                                    child: Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment.end,
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      children: [
                                                        IconButtonWidget(
                                                          onTap: () {
                                                            setState(() {
                                                              controller.supplierNameDropdownController.text = item.newarcMaterialSupplierID ?? "";
                                                              controller.codeAtSupplierController.text = item.code ?? "";
                                                              controller.supplierPriceController.text = item.basePrice.toString();
                                                              controller.supplierPrice = double.tryParse(item.basePrice.toString()) ?? 0.0;
                                                              controller.newArcPrice = double.tryParse(item.retailPrice.toString()) ?? 0.0;
                                                              controller.newarcPriceController.text = item.retailPrice.toString();
                                                            });
                                                            Future.delayed(
                                                                Duration(
                                                                    milliseconds:
                                                                        100));
                                                            showSelectSupplierPopup(
                                                                isEdit: true,
                                                                selectedSupplierName: item.newarcMaterialSupplierID,
                                                                supplierFirebaseId: item.firebaseId,
                                                                selectedVariantProduct: item.newarcMaterialVariantID,
                                                                isAvailableStatus: item.status);
                                                          },
                                                          iconPadding:
                                                              EdgeInsets.all(8),
                                                          isSvgIcon: false,
                                                          backgroundColor:
                                                              AppColor.white,
                                                          icon:
                                                              'assets/icons/edit.png',
                                                          iconColor: AppColor
                                                              .greyColor,
                                                        ),
                                                        SizedBox(width: 4),
                                                        IconButtonWidget(
                                                          onTap: () async {
                                                            await FirebaseFirestore
                                                                .instance
                                                                .collection(appConfig.COLLECT_NEWARCMATERIALVARIANTSUPPLIERPRICE)
                                                                .doc(item.firebaseId)
                                                                .delete();
                                                            setState(() {
                                                              fetchNewarcMaterialSupplierPricesFuture =
                                                                  fetchSupplierPrices();
                                                            });
                                                          },
                                                          iconPadding:
                                                              EdgeInsets.all(6),
                                                          backgroundColor:
                                                              AppColor.white,
                                                          isSvgIcon: false,
                                                          icon:
                                                              'assets/icons/trash-process.png',
                                                          iconColor: AppColor
                                                              .greyColor,
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            )
                                        ],
                                      ),
                                    );
                                  },
                                  separatorBuilder: (context, index) {
                                    return SizedBox(
                                      height: 10,
                                    );
                                  },
                                  itemCount: controller.newarcMaterialSupplierPrices.length,
                                );
                              } else {
                                return Center(
                                  child: NarFormLabelWidget(
                                    label: 'No Data',
                                    fontWeight: '600',
                                    textColor: AppColor.black,
                                    fontSize: 15,
                                  ),
                                );
                              }
                            }
                            if (snapshot.hasError) {
                              return Center(child: Text("Error loading data"));
                            }
                            return Center(
                              child: NarFormLabelWidget(
                                label: 'No Data',
                                fontWeight: '600',
                                textColor: AppColor.black,
                                fontSize: 15,
                              ),
                            );
                          },
                        ),

                        //?----------------------------------- Suppliers and prices Section end ----------------------------------

                        //?----------------------------------- Variants Section end ---------------------------------------

                        SizedBox(height: 30),

                        //?----------------------------------- Configurator Section start ----------------------------------
                        NarFormLabelWidget(
                          label: "Materioteca",
                          fontSize: 18,
                          fontWeight: '700',
                          textColor: AppColor.black,
                        ),
                        SizedBox(height: 20),

                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Switch(
                              value: isMateriotechAvailable,
                              activeThumbColor: AppColor.white,
                              activeTrackColor: Theme.of(context).primaryColor,
                              onChanged: (bool value) async {
                                setState(() {
                                  isMateriotechAvailable = !isMateriotechAvailable;
                                });
                              },
                            ),
                            SizedBox(
                              width: 10,
                            ),
                            NarFormLabelWidget(
                              label: 'Disponibile',
                              fontSize: 14,
                              fontWeight: '600',
                              textColor: AppColor.greyColor,
                            ),
                          ],
                        ),
                        SizedBox(height: 20),
                        NarFormLabelWidget(
                          label: "Configuratore",
                          fontSize: 18,
                          fontWeight: '700',
                          textColor: AppColor.black,
                        ),
                        SizedBox(height: 20),

                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Switch(
                              value: isAvailable,
                              activeThumbColor: AppColor.white,
                              activeTrackColor: Theme.of(context).primaryColor,
                              onChanged: (bool value) async {
                                setState(() {
                                  isAvailable = !isAvailable;
                                });
                              },
                            ),
                            SizedBox(
                              width: 10,
                            ),
                            NarFormLabelWidget(
                              label: 'Disponibile',
                              fontSize: 14,
                              fontWeight: '600',
                              textColor: AppColor.greyColor,
                            ),
                          ],
                        ),

                        SizedBox(height: 20),

                        Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            MouseRegion(
                              cursor: SystemMouseCursors.click,
                              child: GestureDetector(
                                onTap: () async {
                                  if (formKey.currentState!.validate()) {
                                    setState(() {
                                      controller.formProgressMessage = "Salvataggio in corso...";
                                    });
                                   try {
                                      User? user = FirebaseAuth.instance.currentUser;
                                      if (user == null) return;

                                      final String uid = user.uid;
                                      final collectionRef = FirebaseFirestore.instance.collection(appConfig.COLLECT_NEWARCHMATERIAL);

                                      QuerySnapshot snapshot = await collectionRef
                                          .where("newarcProductCollectionID", isEqualTo: controller.collectionController.text.trim())
                                          .where("materialType", isEqualTo: 'tinte')
                                          .orderBy("codeCounter", descending: true)
                                          .limit(1)
                                          .get();


                                      int codeCounter = snapshot.docs.isNotEmpty ? (((snapshot.docs.first.data() as Map<String, dynamic>)['codeCounter']  ?? 0) + 1) : 1;

                                      
                                      DocumentSnapshot manufacturerSnapshot = await FirebaseFirestore
                                          .instance
                                          .collection(appConfig.COLLECT_NEWARCMATERIALMANUFACTURER)
                                          .doc(controller.manufacturerController.text)
                                          .get();

                                      DocumentSnapshot collectionSnapshot = await FirebaseFirestore.instance
                                          .collection(appConfig.COLLECT_NEWARCMATERIALMANUFACTURERCOLLECTION)
                                          .doc(controller.collectionController.text)
                                          .get();

                                      int collectionCodeCounterInt = collectionSnapshot.exists ? (await collectionSnapshot.data() as Map<String, dynamic>)['codeCounter'] : 1;
                                      int manufacturerCodeCounterInt = manufacturerSnapshot.exists ? (await manufacturerSnapshot.data() as Map<String, dynamic>)['codeCounter'] : 1;


                                      String manufacturerCodeCounter = formatCodeCounter(manufacturerCodeCounterInt, totalDigits: 2);
                                      String collectionCodeCounter = formatCodeCounter(collectionCodeCounterInt, totalDigits: 2);

                                      String materialCodeCounter = formatCodeCounter(codeCounter, totalDigits: 3);


                                      String _code = "F_TI_${manufacturerCodeCounter}${collectionCodeCounter}${materialCodeCounter}";


                                      if((naMaterialModel?.firebaseId?.isNotEmpty ?? false)){
                                        //? ---- for edit
                                        Map<String, dynamic>  data = {
                                          "modificationTimestamp": DateTime.now().millisecondsSinceEpoch,
                                          "modificationUid": uid,
                                          "coverImagePath": {
                                            'filename': coverImages.isNotEmpty ? coverImages[0] : "",
                                            'location': coverImages.isNotEmpty ? "newArcMaterial/cover/$naMaterialFirebaseId" : ""
                                          },
                                          "applicationImagesPath": applicationImages.map((image) => {
                                            'filename': image,
                                            'location': "newArcMaterial/applicationImages/$naMaterialFirebaseId"
                                          }).toList(),
                                          "configurationStatus": isAvailable,
                                          "isMateriotechAvailable": isMateriotechAvailable,
                                          "isAnySupplierPriceAvailable": isAnySupplierPriceAvailable,
                                          "newarcSubCategoryID": selectedSubCategory?.map((item)=>item["value"]).toList(),
                                        };
                                        await collectionRef
                                            .doc(naMaterialFirebaseId)
                                            .update(data);
                                      }else{
                                        //? ------ for add
                                        Map<String, dynamic>  data = {
                                          "insertTimestamp": DateTime.now().millisecondsSinceEpoch,
                                          "modificationTimestamp": DateTime.now().millisecondsSinceEpoch,
                                          "creationUid": uid,
                                          "isArchive": false,
                                          "modificationUid": "",
                                          "newarcManufacturerID": controller.manufacturerController.text.trim(),
                                          "newarcProductCollectionID": controller.collectionController.text.trim(),
                                          "name": controller.productNameController.text.trim(),
                                          "coverImagePath": {
                                            'filename': coverImages.isNotEmpty ? coverImages[0] : "",
                                            'location': coverImages.isNotEmpty ? "newArcMaterial/cover/$naMaterialFirebaseId" : ""
                                          },
                                          "applicationImagesPath": applicationImages.map((image) => {
                                            'filename': image,
                                            'location': "newArcMaterial/applicationImages/$naMaterialFirebaseId"
                                          }).toList(),
                                          "configurationStatus": isAvailable,
                                          "materialType": 'tinte',
                                          "codeCounter": codeCounter,
                                          "code": _code,
                                          "isAnySupplierPriceAvailable": isAnySupplierPriceAvailable,
                                          "isMateriotechAvailable": isMateriotechAvailable,
                                          "newarcSubCategoryID": selectedSubCategory?.map((item)=>item["value"]).toList(),
                                        };

                                        await collectionRef
                                            .doc(naMaterialFirebaseId)
                                            .set(data);
                                      }


                                      setState(() {
                                        controller.formProgressMessage = "Salvato";
                                      });
                                    } catch (e) {
                                      setState(() {
                                        controller.formProgressMessage = "Si è verificato un errore.";
                                      });
                                      log("Error While Adding NAMaterial TINTE -----> ${e.toString()}");
                                    }
                                  }

                                },
                                child: Container(
                                  height: 38,
                                  width: 140,
                                  padding: const EdgeInsets.symmetric(horizontal: 20.0),
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    color: Theme.of(context).primaryColor.withOpacity(controller.formProgressMessage == "Salvataggio in corso..." ? 0.5 : 1.0),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Text(
                                    "Salva",
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 13,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 5),
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            NarFormLabelWidget(
                              label: controller.formProgressMessage,
                              fontSize: 14,
                              fontWeight: '600',
                              textColor: AppColor.black,
                            ),
                          ],
                        ),

                        SizedBox(height: 20),

                        //?----------------------------------- Configurator Section end ----------------------------------
                      ],
                    ),
                  )
                ],
              )
            ],
          ),
        )
      ],
    );
  }

  Future<List> fetchNewarcSubCategory() async {
    try {
      List<NewarcMaterialSubCategory> _newarcMaterialSubCategory = [];
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance
          .collection(appConfig.COLLECT_NEWARCMATERIALSUBCATEGORY);

      collectionSnapshot = await collectionSnapshotQuery
          .orderBy('insertTimestamp', descending: true)
          .get();

      if (collectionSnapshot.docs.length > 0) {
        for (var element in collectionSnapshot.docs) {
          try {
            NewarcMaterialSubCategory _tmp =
            NewarcMaterialSubCategory.fromDocument(
                element.data(), element.id);
            _newarcMaterialSubCategory.add(_tmp);
          } catch (e) {
            print("ERROR fetchNewarcSubCategory ---> $e");
          }
        }
      }

      List<Map<String, dynamic>> allOptions = _newarcMaterialSubCategory
          .map((e) => {'value': e.firebaseId, 'label': e.name})
          .toList();
      List<Map<String, dynamic>>? preSelected;

      if(naMaterialModel?.newarcSubCategoryID?.isNotEmpty ?? false){
        preSelected = allOptions
            .where((option) =>
            naMaterialModel!.newarcSubCategoryID!.contains(option['value'])) // Saved IDs
            .toList();
      }


      if(mounted) {
        setState(() {
          controller.newarcMaterialSubCategory = _newarcMaterialSubCategory;
          selectedSubCategory = preSelected;
        });
      };

      return allOptions.isNotEmpty ? allOptions : [{'value': '', 'label': ''}];
    } catch (e, s) {
      print('error: $e');
      print(s);
      return [
        {'value': '', 'label': ''}
      ];
    }
  }

  Future<void> showAddSubCategoryPopup() async {
    TextEditingController subCategoryNameController = TextEditingController();
    List<String> formErrorMessage = [];
    await showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (__context, _setState) {
            return Center(
                child: BaseNewarcPopup(
                  title: "Aggiungi sottocategoria",
                  buttonText: "Aggiungi",
                  formErrorMessage: formErrorMessage,
                  onPressed: () async {
                    _setState((){
                      formErrorMessage.clear();
                      formErrorMessage.add("Salvataggio in corso...");
                    });
                    try {
                      User? user = FirebaseAuth.instance.currentUser;
                      if (user == null) return;

                      final String uid = user.uid;
                      final subCategoryName =
                      subCategoryNameController.text.trim();
                      final collectionRef = FirebaseFirestore.instance.collection(appConfig.COLLECT_NEWARCMATERIALSUBCATEGORY);

                      int codeCounter = 1;

                      QuerySnapshot snapshot = await collectionRef
                          .orderBy("codeCounter", descending: true)
                          .limit(1)
                          .get();

                      if (snapshot.docs.isNotEmpty) {
                        codeCounter = (snapshot.docs.first.data()
                        as Map<String, dynamic>)['codeCounter'] +
                            1;
                      }

                      // Add a new document with the incremented codeCounter
                      await collectionRef.add({
                        "name": subCategoryName,
                        "insertTimestamp": DateTime.now().millisecondsSinceEpoch,
                        "uid": uid,
                        "codeCounter": codeCounter,
                      });
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Salvato");
                      });
                      setState(() {
                        fetchNewarcMaterialSubCategoryFuture = fetchNewarcSubCategory();
                      });
                    } catch (e) {
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Si è verificato un errore.");
                      });
                      log("Error While Adding NEWARCSUBCATEGORY Pavimenti -----> ${e.toString()}");
                    }
                  },
                  buttonColor: Theme.of(context).primaryColor.withOpacity(controller.formProgressMessage == "Salvataggio in corso..." ? 0.5 : 1.0),
                  column: Container(
                    width: 400,
                    child: CustomTextFormField(
                      isExpanded: false,
                      label: "Digita il nome del nuovo sottocategoria",
                      validator: (value) {
                        if (value == '') {
                          return 'Required!';
                        }
                        return null;
                      },
                      controller: subCategoryNameController,
                    ),
                  ),
                ));
          });
        });
  }

  Future<List> fetchNewarcManufacturer() async {
    try {
      List<NewarcMaterialManufacturer> _newarcManufacturer = [];
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance
          .collection(appConfig.COLLECT_NEWARCMATERIALMANUFACTURER);

      collectionSnapshot = await collectionSnapshotQuery
          .orderBy('insertTimestamp', descending: true)
          .get();

      if (collectionSnapshot.docs.length > 0) {
        for (var element in collectionSnapshot.docs) {
          try {
            NewarcMaterialManufacturer _tmp =
                NewarcMaterialManufacturer.fromDocument(
                    element.data(), element.id);
            _newarcManufacturer.add(_tmp);
          } catch (e) {
            print("ERROR fetchNewarcManufacturer ---> $e");
          }
        }
      }

      if(mounted) {
        setState(() {
          controller.newarcManufacturer = _newarcManufacturer;
        });
      };

      if (_newarcManufacturer.length > 0) {
        return _newarcManufacturer.map((e) {
          return {'value': e.firebaseId, 'label': e.name};
        }).toList();
      } else {
        return [
          {'value': '', 'label': ''}
        ];
      }
    } catch (e, s) {
      print('error: $e');
      print(s);
      return [
        {'value': '', 'label': ''}
      ];
    }
  }

  Future<List> fetchNewarcSupplier() async {
    try {
      List<NewarcMaterialSupplier> _newarcMaterialSupplier = [];
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance
          .collection(appConfig.COLLECT_NEWARCMATERIALSUPPLIER);

      collectionSnapshot = await collectionSnapshotQuery
          .orderBy('insertTimestamp', descending: true)
          .get();

      if (collectionSnapshot.docs.length > 0) {
        for (var element in collectionSnapshot.docs) {
          try {
            NewarcMaterialSupplier _tmp =
                NewarcMaterialSupplier.fromDocument(element.data(), element.id);
            _newarcMaterialSupplier.add(_tmp);
          } catch (e) {
            print("ERROR fetchNewarcSupplier ---> $e");
          }
        }
      }

      setState(() {
        controller.newarcMaterialSupplier = _newarcMaterialSupplier;
      });

      if (_newarcMaterialSupplier.length > 0) {
        return _newarcMaterialSupplier.map((e) {
          return {'value': e.firebaseId, 'label': e.name};
        }).toList();
      } else {
        return [
          {'value': '', 'label': ''}
        ];
      }
    } catch (e, s) {
      print('error: $e');
      print(s);
      return [
        {'value': '', 'label': ''}
      ];
    }
  }

  Future<void> fetchSupplierPrices() async {
    setState(() {
      isAnySupplierPriceAvailable = false;
    });
    try {
      List<NewarcMaterialVariantSupplierPrice> _supplierPrices = [];

      // Fetch supplier prices
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot =
          await FirebaseFirestore.instance
              .collection(appConfig.COLLECT_NEWARCMATERIALVARIANTSUPPLIERPRICE)
              .where("naMaterialId", isEqualTo: naMaterialFirebaseId)
              .orderBy('insertTimestamp', descending: true)
              .get();

      if (collectionSnapshot.docs.isNotEmpty) {
        for (var element in collectionSnapshot.docs) {
          try {
            if (element.data().isNotEmpty) {
              NewarcMaterialVariantSupplierPrice supplierPrice = NewarcMaterialVariantSupplierPrice.fromDocument(element.data(), element.id);
              // **Fetch Supplier Name using newarcMaterialSupplierID**
              if (supplierPrice.newarcMaterialSupplierID?.isNotEmpty ?? false) {
                DocumentSnapshot<Map<String, dynamic>> supplierSnapshot =
                    await FirebaseFirestore.instance
                        .collection(appConfig.COLLECT_NEWARCMATERIALSUPPLIER)
                        .doc(supplierPrice.newarcMaterialSupplierID)
                        .get();

                if (supplierSnapshot.exists) {
                  supplierPrice.supplierName = supplierSnapshot.data()?['name'] ?? "";
                }
              }

              _supplierPrices.add(supplierPrice);
            }
          } catch (e) {
            print("Error processing supplier price ${element.id} ---> $e");
          }
        }
      }

      for (var price in _supplierPrices) {
        if (price.status == true) {
          setState(() {
            isAnySupplierPriceAvailable = true;
          });
        }
      }

      setState(() {
        controller.newarcMaterialSupplierPrices = _supplierPrices;
      });
    } catch (e) {
      print("ERROR fetching supplier prices ---> $e");
    }
  }

  Future<List> fetchNewarcMaterialDimensionMeta() async {
    try {
      List<NewarcMaterialDimensionMeta> _newarcMaterialDimensionMeta = [];
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance
          .collection(appConfig.COLLECT_NEWARCMATERIALDIMENSIONMETA);

      collectionSnapshot = await collectionSnapshotQuery
          .orderBy('insertTimestamp', descending: true)
          .get();

      if (collectionSnapshot.docs.length > 0) {
        for (var element in collectionSnapshot.docs) {
          try {
            NewarcMaterialDimensionMeta _tmp =
                NewarcMaterialDimensionMeta.fromDocument(
                    element.data(), element.id);
            _newarcMaterialDimensionMeta.add(_tmp);
          } catch (e) {
            print("ERROR fetchNewarcManufacturer ---> $e");
          }
        }
      }

      setState(() {
        controller.newarcMaterialDimensionMeta = _newarcMaterialDimensionMeta;
      });

      if (_newarcMaterialDimensionMeta.length > 0) {
        return _newarcMaterialDimensionMeta.map((e) {
          return {'value': e.firebaseId, 'label': "${e.value} ${e.unit}"};
        }).toList();
      } else {
        return [
          {'value': '', 'label': ''}
        ];
      }
    } catch (e, s) {
      print('error: $e');
      print(s);
      return [
        {'value': '', 'label': ''}
      ];
    }
  }

  Future<List> fetchNewarcMaterialDimension() async {
    try {
      List<NewarcMaterialDimension> _newarcMaterialDimension = [];

      QuerySnapshot<Map<String, dynamic>> collectionSnapshot = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_NEWARCMATERIALDIMENSION)
          .orderBy('insertTimestamp', descending: true)
          .get();

      // Filter out documents where "height" is missing or null
      List<QueryDocumentSnapshot<Map<String, dynamic>>> filteredDocs = collectionSnapshot.docs
          .where((doc) => !doc.data().containsKey("height") || doc["height"] == null)
          .toList();

      _newarcMaterialDimension = filteredDocs
          .map((doc) => NewarcMaterialDimension.fromDocument(doc.data(), doc.id))
          .toList();

      setState(() {
        controller.newarcMaterialDimension = _newarcMaterialDimension;
      });

      return _newarcMaterialDimension.isNotEmpty
          ? _newarcMaterialDimension.map((e) => {
        'value': e.firebaseId,
        'label':
        "${controller.localCurrencyFormatMain.format(e.width)} x ${controller.localCurrencyFormatMain.format(e.length)} ${e.dimensionUnit}"
      }).toList()
          : [
        {'value': '', 'label': ''}
      ];
    } catch (e, s) {
      print('ERROR: $e\nSTACKTRACE: $s');
      return [
        {'value': '', 'label': ''}
      ];
    }
  }

  Future<List> fetchNewarcProductCollection({required String manufacturerId}) async {
    try {
      List<NewarcMaterialManufacturerCollection> _newarcProductCollection = [];
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance
          .collection(appConfig.COLLECT_NEWARCMATERIALMANUFACTURERCOLLECTION);

      collectionSnapshot = await collectionSnapshotQuery
          .orderBy('insertTimestamp', descending: true)
          .where('newarcManufacturerId', isEqualTo: manufacturerId)
          .get();

      if (collectionSnapshot.docs.length > 0) {
        for (var element in collectionSnapshot.docs) {
          try {
            NewarcMaterialManufacturerCollection _tmp =
                NewarcMaterialManufacturerCollection.fromDocument(
                    element.data(), element.id);
            _newarcProductCollection.add(_tmp);
          } catch (e) {
            print("ERROR fetchNewarcProductCollection ---> $e");
          }
        }
      }

      if (!mounted) return [];

      setState(() {
        controller.newarcProductCollection = _newarcProductCollection;
      });

      if (_newarcProductCollection.length > 0) {
        return _newarcProductCollection.map((e) {
          return {'value': e.firebaseId, 'label': e.name};
        }).toList();
      } else {
        return [];
      }
    } catch (e, s) {
      print('error: $e');
      print(s);
      return [];
    }
  }

  Future<void> showProduttorePopup() async {
    TextEditingController manufacturerNameController = TextEditingController();
    List<String> formErrorMessage = [];
    await showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (__context, _setState) {
            return Center(
                child: BaseNewarcPopup(
              title: "Aggiungi produttore",
              buttonText: "Aggiungi",
              formErrorMessage: formErrorMessage,
              onPressed: () async {
                _setState((){
                  formErrorMessage.clear();
                  formErrorMessage.add("Salvataggio in corso...");
                });
                try {
                  User? user = FirebaseAuth.instance.currentUser;
                  if (user == null) return;

                  final String uid = user.uid;
                  final manufacturerName =
                      manufacturerNameController.text.trim();
                  final collectionRef = FirebaseFirestore.instance
                      .collection(appConfig.COLLECT_NEWARCMATERIALMANUFACTURER);

                  int codeCounter = 1;

                  // Fetch the latest document with the highest codeCounter for the current uid
                  QuerySnapshot snapshot = await collectionRef
                      .orderBy("codeCounter", descending: true)
                      .limit(1)
                      .get();

                  if (snapshot.docs.isNotEmpty) {
                    codeCounter = (snapshot.docs.first.data()
                            as Map<String, dynamic>)['codeCounter'] +
                        1;
                  }

                  // Add a new document with the incremented codeCounter
                  await collectionRef.add({
                    "name": manufacturerName,
                    "insertTimestamp": DateTime.now().millisecondsSinceEpoch,
                    "status": true,
                    "uid": uid,
                    "codeCounter": codeCounter,
                  });
                  _setState((){
                    formErrorMessage.clear();
                    formErrorMessage.add("Salvato");
                  });
                  setState(() {
                    fetchNewarcMaterialManufacturerFuture = fetchNewarcManufacturer();
                  });
                } catch (e) {
                  _setState((){
                    formErrorMessage.clear();
                    formErrorMessage.add("Si è verificato un errore.");
                  });
                  log("Error While Adding NEWARCMANUFACTURER Rivestimenti -----> ${e.toString()}");
                }
              },
              buttonColor: Theme.of(context).primaryColor.withOpacity(controller.formProgressMessage == "Salvataggio in corso..." ? 0.5 : 1.0),
              column: Container(
                width: 400,
                child: CustomTextFormField(
                  isExpanded: false,
                  label: "Digita il nome del nuovo produttore",
                  validator: (value) {
                    if (value == '') {
                      return 'Required!';
                    }
                    return null;
                  },
                  controller: manufacturerNameController,
                ),
              ),
            ));
          });
        });
  }

  Future<void> showAddSupplierPopup({required VoidCallback onFormatAdded}) async {
    TextEditingController supplierNameController = TextEditingController();
    List<String> formErrorMessage = [];
    await showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (__context, _setState) {
            return Center(
                child: BaseNewarcPopup(
              title: "Aggiungi fornitore",
              buttonText: "Aggiungi",
              formErrorMessage: formErrorMessage,
              onPressed: () async {
                _setState((){
                  formErrorMessage.clear();
                  formErrorMessage.add("Salvataggio in corso...");
                });
                try {
                  User? user = FirebaseAuth.instance.currentUser;
                  if (user == null) return;

                  final String uid = user.uid;
                  final supplierName = supplierNameController.text.trim();
                  final collectionRef = FirebaseFirestore.instance
                      .collection(appConfig.COLLECT_NEWARCMATERIALSUPPLIER);

                  await collectionRef.add({
                    "name": supplierName,
                    "insertTimestamp": DateTime.now().millisecondsSinceEpoch,
                    "status": true,
                    "uid": uid,
                  });
                  _setState((){
                    formErrorMessage.clear();
                    formErrorMessage.add("Salvato");
                  });
                  onFormatAdded();
                } catch (e) {
                  _setState((){
                    formErrorMessage.clear();
                    formErrorMessage.add("Si è verificato un errore.");
                  });
                  log("Error While Adding NEWARCMATERIALSUPPLIER Rivestimenti -----> ${e.toString()}");
                }
              },
              buttonColor: Theme.of(context).primaryColor.withOpacity(controller.formProgressMessage == "Salvataggio in corso..." ? 0.5 : 1.0),
              column: Container(
                width: 400,
                child: CustomTextFormField(
                  isExpanded: false,
                  label: "Digita il nome del nuovo produttore",
                  validator: (value) {
                    if (value == '') {
                      return 'Required!';
                    }
                    return null;
                  },
                  controller: supplierNameController,
                ),
              ),
            ));
          });
        });
  }

  Future<void> showSelectSupplierPopup({required bool isEdit, String? selectedSupplierName, String? supplierFirebaseId, String? selectedVariantProduct, bool? isAvailableStatus}) async {
    bool isAvailable = isAvailableStatus ?? false;
    List<String> formErrorMessage = [];
    await showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (__context, _setState) {
            Future<List>? futureNewarcMaterialSupplier;
            void refreshFetchNewarcMaterialDimension() {
              _setState(() {
                futureNewarcMaterialSupplier = fetchNewarcSupplier();
              });
            }

            futureNewarcMaterialSupplier ??= fetchNewarcSupplier();

            return Center(
                child: BaseNewarcPopup(
              title: "Seleziona fornitore",
              buttonText: "Aggiungi",
              formErrorMessage: formErrorMessage,
              onPressed: () async {
                _setState((){
                  formErrorMessage.clear();
                  formErrorMessage.add("Salvataggio in corso...");
                });
                try {
                  User? user = FirebaseAuth.instance.currentUser;
                  if (user == null) return;

                  final String uid = user.uid;
                  final collectionRef = FirebaseFirestore.instance.collection(
                      appConfig.COLLECT_NEWARCMATERIALVARIANTSUPPLIERPRICE);

                  if (isEdit) {
                    Map<String, dynamic> data = {
                      "modificationTimestamp": DateTime.now().millisecondsSinceEpoch,
                      "code": controller.codeAtSupplierController.text.trim(),
                      "isArchive": false,
                      "modificationUid": uid,
                      "newarcMaterialSupplierID": controller.supplierNameDropdownController.text,
                      "basePrice": controller.supplierPrice,
                      "retailPrice": controller.newArcPrice,
                      "status": isAvailable,
                    };

                    await collectionRef.doc(supplierFirebaseId).update(data);

                    setState(() {
                      fetchNewarcMaterialSupplierPricesFuture =
                          fetchSupplierPrices();
                    });
                    _setState((){
                      formErrorMessage.clear();
                      formErrorMessage.add("Salvato");
                    });
                  } else {
                    Map<String, dynamic> data = {
                      "insertTimestamp": DateTime.now().millisecondsSinceEpoch,
                      "modificationTimestamp": DateTime.now().millisecondsSinceEpoch,
                      "creationUid": uid,
                      "code": controller.codeAtSupplierController.text.trim(),
                      "isArchive": false,
                      "modificationUid": "",
                      "naMaterialId": naMaterialFirebaseId,
                      "newarcMaterialSupplierID": controller.supplierNameDropdownController.text,
                      "newarcMaterialVariantID": null,
                      "newarcMaterialVariant": null,
                      "basePrice": controller.supplierPrice,
                      "retailPrice": controller.newArcPrice,
                      "status": isAvailable,
                    };

                    await collectionRef.add(data);

                    _setState((){
                      formErrorMessage.clear();
                      formErrorMessage.add("Salvato");
                    });

                    setState(() {
                      fetchNewarcMaterialSupplierPricesFuture =
                          fetchSupplierPrices();
                    });
                  }
                } catch (e) {
                  _setState((){
                    formErrorMessage.clear();
                    formErrorMessage.add("Si è verificato un errore.");
                  });
                  log("Error While Adding Supplier Price TINTE -----> ${e.toString()}");
                }
              },
              buttonColor: Theme.of(context).primaryColor.withOpacity(controller.formProgressMessage == "Salvataggio in corso..." ? 0.5 : 1.0),
              column: Container(
                width: 350,
                child: Column(
                  children: [
                    //-------Supplier Name--------

                    SizedBox(
                      width: 300,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          NarFormLabelWidget(
                            label: "Nome fornitore",
                            fontSize: 14,
                            fontWeight: '600',
                            textColor: AppColor.greyColor,
                          ),
                          NarLinkWidget(
                            fontSize: 12,
                            fontWeight: '600',
                            textColor: Theme.of(context).primaryColor,
                            text: 'Aggiungi fornitore',
                            textDecoration: TextDecoration.underline,
                            onClick: () {
                              showAddSupplierPopup(
                                  onFormatAdded:
                                      refreshFetchNewarcMaterialDimension);
                            },
                          )
                        ],
                      ),
                    ),
                    SizedBox(height: 10),
                    SizedBox(
                      width: 300,
                      height: 51,
                      child: FutureBuilder<List>(
                          future: futureNewarcMaterialSupplier,
                          builder: (context, snapshot) {
                            if (snapshot.hasData) {
                              return DropdownButtonFormField<String>(
                                isExpanded: true,
                                initialValue: selectedSupplierName?.isNotEmpty ?? false
                                    ? selectedSupplierName
                                    : null,
                                icon: Padding(
                                  padding: EdgeInsets.only(right: 8),
                                  child: SvgPicture.asset(
                                    'assets/icons/arrow_down.svg',
                                    width: 12,
                                    color: Color(0xff7e7e7e),
                                  ),
                                ),
                                style: TextStyle(
                                  overflow: TextOverflow.ellipsis,
                                  color: Colors.black,
                                  fontSize: 12.0,
                                  fontWeight: FontWeight.bold,
                                  fontStyle: FontStyle.normal,
                                ),
                                borderRadius: BorderRadius.circular(8),
                                decoration: InputDecoration(
                                  border: OutlineInputBorder(
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(8)),
                                    borderSide: BorderSide(
                                      color: Color.fromRGBO(227, 227, 227, 1),
                                      width: 1,
                                    ),
                                  ),
                                  hintStyle: TextStyle(
                                    color: Colors.grey,
                                    fontSize: 15.0,
                                    fontWeight: FontWeight.w800,
                                    fontStyle: FontStyle.normal,
                                    letterSpacing: 0,
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(8)),
                                    borderSide: BorderSide(
                                      color: Color.fromRGBO(227, 227, 227, 1),
                                      width: 1,
                                    ),
                                  ),
                                  contentPadding: EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 8),
                                  fillColor: Colors.white,
                                ),
                                onChanged: (String? value) {
                                  setState(() {
                                    controller.supplierNameDropdownController
                                        .text = value ?? "";
                                  });
                                },
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return "Required!";
                                  }
                                  return null;
                                },
                                dropdownColor: Colors.white,
                                items: snapshot.data
                                    ?.map<DropdownMenuItem<String>>((item) {
                                  return DropdownMenuItem<String>(
                                    value: item['value'],
                                    child: NarFormLabelWidget(
                                      label: item['label']!,
                                      textColor: Colors.black,
                                      fontSize: 14,
                                      fontWeight: '600',
                                    ),
                                  );
                                }).toList(),
                              );
                            } else if (snapshot.hasError) {
                              return Container(
                                width: 30,
                                height: 30,
                                alignment: Alignment.center,
                              );
                            }
                            return Center(
                              child: CircularProgressIndicator(
                                color: Theme.of(context).primaryColor,
                              ),
                            );
                          }),
                    ),

                    SizedBox(height: 10),

                    SizedBox(
                      width: 300,
                      // height: 51,
                      child: CustomTextFormField(
                        isExpanded: false,
                        label: "Codice presso il fornitore",
                        validator: (value) {
                          if (value == '') {
                            return 'Required!';
                          }
                          return null;
                        },
                        controller: controller.codeAtSupplierController,
                      ),
                    ),

                    SizedBox(height: 10),

                    SizedBox(
                      width: 300,
                      // height: 51,
                      child: CustomTextFormField(
                        isExpanded: false,
                        label: "Prezzo fornitore",
                        suffixIcon: Container(
                          width: 50,
                          padding: const EdgeInsets.only(right: 5),
                          child: Align(
                              alignment: Alignment.centerRight,
                              child: NarFormLabelWidget(
                                label: "€/mq",
                                textColor: AppColor.greyColor,
                                fontWeight: "500",
                              )),
                        ),
                        isMoney: true,
                        validator: (value) {
                          if (value == '') {
                            return 'Required!';
                          }
                          return null;
                        },
                        onChangedCallback: (String value) {
                          double supplierPriceOnchange = double.tryParse(controller.supplierPriceController.text.replaceAll('.', '').replaceAll(',', '.').toString()) ?? 0.0;
                          setState((){
                            controller.supplierPrice = supplierPriceOnchange;
                          });
                        },
                        controller: controller.supplierPriceController,
                      ),
                    ),

                    SizedBox(height: 10),

                    SizedBox(
                      width: 300,
                      child: CustomTextFormField(
                        isExpanded: false,
                        label: "Prezzo Newarc",
                        suffixIcon: Container(
                          width: 50,
                          padding: const EdgeInsets.only(right: 5),
                          child: Align(
                              alignment: Alignment.centerRight,
                              child: NarFormLabelWidget(
                                label: "€/mq",
                                textColor: AppColor.greyColor,
                                fontWeight: "500",
                              )),
                        ),
                        isMoney: true,
                        isPercentage: false,
                        onChangedCallback: (String value) {
                          double newArcPriceOnchange = double.tryParse(controller.newarcPriceController.text.replaceAll('.', '').replaceAll(',', '.').toString()) ?? 0.0;
                          setState((){
                            controller.newArcPrice = newArcPriceOnchange;
                          });
                        },
                        validator: (value) {
                          if (value == '') {
                            return 'Required!';
                          }
                          return null;
                        },
                        controller: controller.newarcPriceController,
                      ),
                    ),

                    SizedBox(height: 10),

                    SizedBox(
                      width: 300,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Switch(
                            value: isAvailable,
                            activeThumbColor: AppColor.white,
                            activeTrackColor: Theme.of(context).primaryColor,
                            onChanged: (bool value) async {
                              _setState(() {
                                isAvailable = !isAvailable;
                              });
                            },
                          ),
                          SizedBox(
                            width: 10,
                          ),
                          NarFormLabelWidget(
                            label: 'Disponibile',
                            fontSize: 14,
                            fontWeight: '600',
                            textColor: AppColor.black,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ));
          });
        });
  }

  Future<void> showCollectionPopup() async {
    TextEditingController collectionNameController = TextEditingController();
    TextEditingController descriptionController = TextEditingController();
    XFile? dataSheetFile;
    String? dataSheetFileFileExt;
    List<String> formErrorMessage = [];

    await showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (__context, _setState) {
            return Center(
                child: BaseNewarcPopup(
              title: "Aggiungi collezione",
              buttonText: "Aggiungi",
              formErrorMessage: formErrorMessage,
              onPressed: () async {
                _setState((){
                  formErrorMessage.clear();
                  formErrorMessage.add("Salvataggio in corso...");
                });
                try {


                  User? user = FirebaseAuth.instance.currentUser;

                  String uid = user?.uid ?? "";

                  // Reference to Firestore collection
                  CollectionReference collectionRef = FirebaseFirestore.instance
                      .collection(appConfig
                          .COLLECT_NEWARCMATERIALMANUFACTURERCOLLECTION);

                  // Generate a new document ID
                  DocumentReference docRef = collectionRef.doc();
                  String firebaseId = docRef.id;

                  // Fetch the latest codeCounter for the current uid
                  int codeCounter = 1;
                  QuerySnapshot snapshot = await collectionRef
                      .where("newarcManufacturerId", isEqualTo: controller.manufacturerController.text.trim())
                      .orderBy("codeCounter", descending: true)
                      .limit(1)
                      .get();

                  if (snapshot.docs.isNotEmpty) {
                    codeCounter = (snapshot.docs.first.data()
                            as Map<String, dynamic>)['codeCounter'] +
                        1;
                  }

                  // Upload file if available
                  if (dataSheetFile != null) {
                    await uploadFileDelayed(
                        'newArcMaterial/collection/$firebaseId/',
                        dataSheetFile!.name,
                        dataSheetFile);
                  }

                  // Save the document with the incremented codeCounter
                  await docRef.set({
                    "name": collectionNameController.text.trim(),
                    "description": descriptionController.text.trim(),
                    "insertTimestamp": DateTime.now().millisecondsSinceEpoch,
                    "modificationTimestamp": DateTime.now().millisecondsSinceEpoch,
                    "isArchive": false,
                    "creationUid": uid,
                    "modificationUid": "",
                    "collectionFirebaseId": firebaseId,
                    "newarcManufacturerId": controller.manufacturerController.text.trim(),
                    "filePath": {
                      'filename':
                          dataSheetFile != null ? dataSheetFile!.name : "",
                      'location': dataSheetFile != null
                          ? 'newArcMaterial/collection/$firebaseId/'
                          : ""
                    },
                    "codeCounter": codeCounter
                  });
                  _setState((){
                    formErrorMessage.clear();
                    formErrorMessage.add("Salvato");
                  });
                  setState(() {
                    selectedCollection = null;
                    controller.collectionController.clear();
                    collectionFuture = fetchNewarcProductCollection(manufacturerId: controller.manufacturerController.text.trim());
                  });
                } catch (e) {
                  _setState((){
                    formErrorMessage.clear();
                    formErrorMessage.add("Si è verificato un errore.");
                  });
                  log("Error While Adding NEWARCMANUFACTURER TINTE -----> ${e.toString()}");
                }
              },
              buttonColor: Theme.of(context).primaryColor.withOpacity(controller.formProgressMessage == "Salvataggio in corso..." ? 0.5 : 1.0),
              column: Container(
                width: 400,
                child: ListView(
                  shrinkWrap: true,
                  children: [
                    CustomTextFormField(
                      isExpanded: false,
                      label: "Digita il nome della nuova collezione",
                      validator: (value) {
                        if (value == '') {
                          return 'Required!';
                        }
                        return null;
                      },
                      controller: collectionNameController,
                    ),
                    SizedBox(
                      height: 10,
                    ),
                    CustomTextFormField(
                      isExpanded: false,
                      minLines: 5,
                      label: "Descrizione",
                      controller: descriptionController,
                    ),
                    SizedBox(
                      height: 10,
                    ),
                    Container(
                      padding: EdgeInsets.all(15),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(7),
                        border: Border.all(color: Color(0xffDBDBDB)),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              NarFormLabelWidget(
                                label: 'Scheda tecnica',
                                textColor: Colors.black,
                                fontWeight: '700',
                                fontSize: 14,
                              ),
                              MouseRegion(
                                cursor: SystemMouseCursors.click,
                                child: GestureDetector(
                                  onTap: () async {
                                    final pickedFile =
                                        await FilePicker.platform.pickFiles(
                                      type: FileType.custom,
                                      allowMultiple: false,
                                      allowedExtensions: ['pdf'],
                                    );

                                    if (pickedFile != null) {
                                      _setState(() {
                                        dataSheetFile =
                                            pickedFile.files[0].xFile;
                                        dataSheetFileFileExt = path.extension(
                                            pickedFile.files[0].name);
                                      });
                                    }
                                  },
                                  child: Container(
                                    height: 30,
                                    width: 80,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(7),
                                      color: Color(0xffE6E6E6),
                                    ),
                                    child: Center(
                                      child: NarFormLabelWidget(
                                        label: 'carica',
                                        fontSize: 12,
                                        fontWeight: "600",
                                        textColor: Colors.black,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(
                            height: 15,
                          ),
                          (dataSheetFileFileExt?.isNotEmpty ?? false)
                              ? Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Container(
                                      height: 86,
                                      width: 86,
                                      clipBehavior: Clip.none,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(7),
                                        border: Border.all(
                                            color: Color(0xffD9D9D9), width: 1),
                                        color: Color(0xffD9D9D9),
                                      ),
                                      child: Image.asset(
                                        'assets/icons/pdf.png',
                                        color: Color(0xffa6a6a6),
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                    Center(
                                      child: NarLinkWidget(
                                        textAlign: TextAlign.center,
                                        text: dataSheetFile?.name ?? "",
                                        fontSize: 10,
                                        fontWeight: '600',
                                        textColor: Color(0xff838383),
                                        onClick: () {
                                          _setState(() {
                                            dataSheetFileFileExt = '';
                                            dataSheetFile = null;
                                          });
                                        },
                                      ),
                                    ),
                                    Center(
                                      child: SizedBox(
                                        width: 86,
                                        child: NarLinkWidget(
                                          textAlign: TextAlign.center,
                                          text: "Elimina",
                                          fontSize: 12,
                                          fontWeight: '600',
                                          textColor: Color(0xff838383),
                                          onClick: () {
                                            _setState(() {
                                              dataSheetFileFileExt = '';
                                              dataSheetFile = null;
                                            });
                                          },
                                        ),
                                      ),
                                    ),
                                  ],
                                )
                              : SizedBox.shrink(),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ));
          });
        });
  }

  Future<void> showCollectionModificationPopup(NewarcMaterialManufacturerCollection? collection) async {
    TextEditingController descriptionController = TextEditingController(text: collection?.description ?? '');
    XFile? dataSheetFile;
    String? dataSheetFileFileExt;
    List<String> formErrorMessage = [];

    await showDialog(
      context: context,
      builder: (BuildContext _context) {
        return StatefulBuilder(
          builder: (__context, _setState) {
            return Center(
              child: BaseNewarcPopup(
                title: "Modifica collezione",
                buttonText: "Salva",
                formErrorMessage: formErrorMessage,
                onPressed: () async {
                  _setState(() {
                    formErrorMessage.clear();
                    formErrorMessage.add("Salvataggio in corso...");
                  });

                  try {
                    User? user = FirebaseAuth.instance.currentUser;
                    String uid = user?.uid ?? "";

                    // Use existing document ID or generate a new one
                    String firebaseId = collection!.firebaseId!;

                    // Upload file if available
                    if (dataSheetFile != null) {
                      await uploadFileDelayed('newArcMaterial/collection/$firebaseId/', dataSheetFile!.name, dataSheetFile);
                    }

                    // Save the updated data
                    await FirebaseFirestore.instance
                        .collection(appConfig.COLLECT_NEWARCMATERIALMANUFACTURERCOLLECTION)
                        .doc(firebaseId)
                        .update({
                      "description": descriptionController.text.trim(),
                      "modificationTimestamp": DateTime.now().millisecondsSinceEpoch,
                      "modificationUid": uid,
                      "filePath": {
                        'filename': dataSheetFile != null ? dataSheetFile!.name : collection.filePath?['filename'] ?? "",
                        'location': dataSheetFile != null ? 'newArcMaterial/collection/$firebaseId/' : collection.filePath?['location'] ?? "",
                      },
                    });

                    _setState(() {
                      formErrorMessage.clear();
                      formErrorMessage.add("Salvato");
                    });

                  } catch (e) {
                    _setState(() {
                      formErrorMessage.clear();
                      formErrorMessage.add("Si è verificato un errore.");
                    });
                    log("Error While Updating Collection TINTE -----> ${e.toString()}");
                  }
                },
                buttonColor: Theme.of(context).primaryColor.withOpacity(
                  formErrorMessage.contains("Salvataggio in corso...") ? 0.5 : 1.0,
                ),
                column: Container(
                  width: 400,
                  child: ListView(
                    shrinkWrap: true,
                    children: [
                      NarFormLabelWidget(
                        label: collection?.name ?? '',
                        fontSize: 14,
                        fontWeight: '700',
                        textColor: AppColor.black,
                      ),
                      SizedBox(height: 10),
                      CustomTextFormField(
                        isExpanded: false,
                        minLines: 5,
                        label: "Descrizione",
                        controller: descriptionController,
                      ),
                      SizedBox(height: 10),
                      Container(
                        padding: EdgeInsets.all(15),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(7),
                          border: Border.all(color: Color(0xffDBDBDB)),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                NarFormLabelWidget(
                                  label: 'Scheda tecnica',
                                  textColor: Colors.black,
                                  fontWeight: '700',
                                  fontSize: 14,
                                ),
                                MouseRegion(
                                  cursor: SystemMouseCursors.click,
                                  child: GestureDetector(
                                    onTap: () async {
                                      final pickedFile =
                                      await FilePicker.platform.pickFiles(
                                        type: FileType.custom,
                                        allowMultiple: false,
                                        allowedExtensions: ['pdf'],
                                      );

                                      if (pickedFile != null) {
                                        _setState(() {
                                          dataSheetFile =
                                              pickedFile.files[0].xFile;
                                          dataSheetFileFileExt = path.extension(
                                              pickedFile.files[0].name);
                                        });
                                      }
                                    },
                                    child: Container(
                                      height: 30,
                                      width: 80,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(7),
                                        color: Color(0xffE6E6E6),
                                      ),
                                      child: Center(
                                        child: NarFormLabelWidget(
                                          label: 'Carica',
                                          fontSize: 12,
                                          fontWeight: "600",
                                          textColor: Colors.black,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 15),
                            (dataSheetFileFileExt?.isNotEmpty ?? false) ||
                                (collection?.filePath?['filename']?.isNotEmpty ?? false)
                                ? Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Container(
                                  height: 86,
                                  width: 86,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(7),
                                    border: Border.all(color: Color(0xffD9D9D9), width: 1),
                                    color: Color(0xffD9D9D9),
                                  ),
                                  child: Image.asset(
                                    'assets/icons/pdf.png',
                                    color: Color(0xffa6a6a6),
                                    fit: BoxFit.cover,
                                  ),
                                ),
                                Center(
                                  child: NarLinkWidget(
                                    textAlign: TextAlign.center,
                                    text: dataSheetFile?.name ?? collection?.filePath?['filename'] ?? "",
                                    fontSize: 10,
                                    fontWeight: '600',
                                    textColor: Color(0xff838383),
                                    onClick: () {
                                      _setState(() {
                                        dataSheetFileFileExt = '';
                                        dataSheetFile = null;
                                      });
                                    },
                                  ),
                                ),
                                Center(
                                  child: SizedBox(
                                    width: 86,
                                    child: NarLinkWidget(
                                      textAlign: TextAlign.center,
                                      text: "Elimina",
                                      fontSize: 12,
                                      fontWeight: '600',
                                      textColor: Color(0xff838383),
                                      onClick: () {
                                        _setState(() {
                                          collection?.filePath?['filename'] = "";
                                          collection?.filePath?['location'] = "";
                                          dataSheetFileFileExt = '';
                                          dataSheetFile = null;
                                        });
                                      },
                                    ),
                                  ),
                                ),
                              ],
                            )
                                : SizedBox.shrink(),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }


  String formatCodeCounter(int codeCounter, {int totalDigits = 3}) {
    return codeCounter.toString().padLeft(totalDigits, '0');
  }
}
