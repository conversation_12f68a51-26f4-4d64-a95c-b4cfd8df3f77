import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/classes/property.dart';
import 'package:newarc_platform/widget/work/project/ads/ad.dart';
import 'package:newarc_platform/widget/work/project/ads/configuration.dart';

class AddPropertyForm extends StatefulWidget {
  NewarcProject? project;
  Property? property;
  final Function? initialFetchProperties;
  List<bool>? isInputChangeDetected = [];

  AddPropertyForm(
      {Key? key,
      this.project,
      this.property,
      this.initialFetchProperties,
      this.isInputChangeDetected })
      : super(key: key);

  static const String route = '/property/add';

  @override
  _AddPropertyFormState createState() => _AddPropertyFormState();
}

class _AddPropertyFormState extends State<AddPropertyForm> {
  bool loading = false;
  
  bool isAdSectionActive = true;

  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  final _formKey = GlobalKey<FormState>();
  
  @override
  Widget build(BuildContext context) {
    
    return SingleChildScrollView(
      child: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // tabletSize ? Container() : CustomDrawer(),
                  Container(
                    decoration: BoxDecoration(
                      color: Color(0xffECECEC),
                      borderRadius: BorderRadius.only( 
                        topLeft: Radius.circular(13), 
                        topRight: Radius.circular(13)
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: MouseRegion(
                            cursor: SystemMouseCursors.click,
                            child: GestureDetector(
                              onTap: () => setState((){isAdSectionActive = true;}),
                              child: Container(
                                height: 40,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.only( 
                                    topLeft: Radius.circular(13), 
                                    topRight: isAdSectionActive ? Radius.circular(13) : Radius.circular(0) 
                                  ),
                                  // border: Border(
                                  //   top: BorderSide( width: 1, color: Color(0xffE6E6E6) ),
                                  //   left: BorderSide( width: 1, color: Color(0xffE6E6E6) ),
                                  // ),
                                  color: isAdSectionActive ? Colors.white : Colors.transparent
                                  
                                ),
                                child: Center(
                                  child: NarFormLabelWidget(
                                    label: 'Annuncio',
                                    fontSize: 15,
                                    textColor: Colors.black
                                  ),
                                )
                              ),
                            ),
                          ),
                        ),

                        Expanded(
                          child: MouseRegion(
                            cursor: SystemMouseCursors.click,
                            child: GestureDetector(
                              onTap: () => setState((){isAdSectionActive = false;}),
                              child: Container(
                                height: 40,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.only( 
                                    topLeft: Radius.circular(13), 
                                    topRight: Radius.circular(13)
                                  ),
                                  // border: Border(
                                  //   top: BorderSide( width: 1, color: Color(0xffE6E6E6) ),
                                  //   right: BorderSide( width: 1, color: Color(0xffE6E6E6) ),
                                  // ),
                                  color: !isAdSectionActive ? Colors.white : Colors.transparent
                                  
                                ),
                                child: Center(
                                  child: NarFormLabelWidget(
                                    label: 'Configuratore',
                                    fontSize: 15,
                                    textColor: Colors.black
                                  ),
                                )
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Expanded(
                        flex: 1,
                        child: Container(
                          //width: MediaQuery.of(context).size.width * 0.75,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 30, vertical: 15),
                          decoration: BoxDecoration(color: Colors.white),
                          child: isAdSectionActive
                          ? AdForm(
                            project: widget.project,
                            property: widget.property,
                            initialFetchProperties: widget.initialFetchProperties,
                            isInputChangeDetected: widget.isInputChangeDetected
                          )
                          : ConfigurationForm()
              
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
          
        ],
      ),
    );
  }

  
}
