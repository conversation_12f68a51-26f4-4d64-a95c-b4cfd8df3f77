import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/acquiredContact.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/classes/wpformSubmission.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/firestore.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/tab/common_dropdown_widget.dart';
import 'package:newarc_platform/widget/UI/tab/status_widget.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/widget/UI/tab/tag_widget.dart';
import 'package:newarc_platform/widget/agency/agency_comment_popup.dart';
import 'package:newarc_platform/widget/agency/assegna_immobile_popup.dart';

class WebLeadsDataSource extends DataTableSource {
  List<WpFormsSubmission> displayContacts;
  BuildContext context;
  Function()? initialFetchContacts;
  var selectedStatus;
  String query;
  String? agencyId;
  String? tag;
  NewarcUser? newarcUser;

  WebLeadsDataSource({
    required this.displayContacts,
    required this.context,
    this.initialFetchContacts,
    this.selectedStatus,
    this.newarcUser,
    required this.query,
    this.tag = 'agency',
    this.agencyId
  });

  @override
  DataRow? getRow(int index) {
    if (index < displayContacts.length) {
      WpFormsSubmission webLead = displayContacts[index];
      var address = "";

      int millisecondsSinceEpoch = webLead.insertTimestamp!;
      var date = DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch).day.toString() +
          '/' +
          DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch).month.toString() +
          '/' +
          DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch).year.toString();

      

      return DataRow(
        cells: [
          DataCell(
            // NarLinkWidget(
            //   text: webLead.adData!.toFullAddress(),
            //   textColor: Colors.black,
            //   fontWeight: '700',
            //   overflow: TextOverflow.ellipsis,
            //   fontSize: 12,
            //   onClick: () {
                
            //   },
            // ),
            Stack(
              clipBehavior: Clip.none,
              children: [
                if( isToday(millisecondsSinceEpoch) ) Positioned(
                  top: -25,
                  child: TagWidget(
                    text: 'Novità!',
                    textColor: Colors.white,
                    statusColor: Color(0xff39C14F),
                    borderRadius: 15,

                    textStyle: TextStyle(
                      fontSize: 10,
                      fontFamily: 'Raleway-bold',

                    ),
                  ),
                ),
                NarFormLabelWidget(
                  label: webLead.adData!.addressInfo!.toFullAddress(),
                  fontSize: 12,
                  fontWeight: '600',
                  textColor: AppColor.black,
                ),
              ],
            ),
          ),
          DataCell(
            NarFormLabelWidget(
              label: webLead.basePersonInfo!.toFullName(),
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),
          ),
          DataCell(
            
            NarFormLabelWidget(
              label: webLead.basePersonInfo!.phone,
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),
          ),
          DataCell(
            NarFormLabelWidget(
              label: webLead.basePersonInfo!.email,
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),
          ),
          DataCell(
            NarFormLabelWidget(
              label: date,
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),
          ),
          if(tag == 'agency') DataCell(
            // StatusWidget(
            //   status: webLead.status,
            //   statusColor: getColor(webLead.status ?? ""),
            // )
            agencyId == ''
            ? StatusWidget(
              status: webLead.status,
              statusColor: getColor(webLead.status ?? ""),
            )
            : CustomDropdownButton(
              selectedValue: webLead.status,
              
              items: [
                'Da contattare',
                'Contattato',
                'Interessato',
                'Non interessato',
                'Acquirente'
              ],
              hintText: 'Status',
              onChanged: (value) async {
                webLead.status = value;
                displayContacts[index].status = value;
                await updateDocument(appConfig.COLLECT_WP_FORM_SUBMISSION, webLead.id!, webLead.toMap());
                notifyListeners();
              },
              getColor: (status) {
                switch (status) {
                  case 'Da contattare':
                    return Colors.transparent;
                  case 'Contattato':
                    return Color(0xffFEC600);
                  case 'Interessato':
                    return Color(0xffFF7B00);
                  case 'Non interessato':
                    return Color(0xffDD0000);
                  case 'Acquirente':
                    return Color(0xff39C14F);
                }
              }
            )
          ),
          
        ],
      );
    }

    return null;
  }

  getColor(String status) {
    switch (status) {
      case 'Da contattare':
        return Colors.transparent;
      case 'Contattato':
        return Color(0xffFEC600);
      case 'Interessato':
        return Color(0xffFF7B00);
      case 'Non interessato':
        return Color(0xffDD0000);
      case 'Acquirente':
        return Color(0xff39C14F);
    }
  }

  void showCommentPopup(AcquiredContact acquiredContact) {
    showGeneralDialog(
      context: context,
      barrierLabel: "Barrier",
      barrierDismissible: false,
      barrierColor: Colors.black.withOpacity(0.5),
      pageBuilder: (_, __, ___) {
        return Center(
          child: AgencyCommentPopup(acquiredContact: acquiredContact, newarcUser: newarcUser),
        );
      },
    );
  }

  void showAgencyStatusPopup(AcquiredContact acquiredContact) {
    showGeneralDialog(
      context: context,
      barrierLabel: "Barrier",
      barrierDismissible: true,
      barrierColor: Colors.black.withOpacity(0.5),
      useRootNavigator: true,
      pageBuilder: (_, __, ___) {
        return Center(
          child: AssegnaImmobilePopup(
            acquiredContact: acquiredContact,
            initialFetchContacts: initialFetchContacts,
          ),
        );
      },
    );
  }

  
  bool filterFunction(WpFormsSubmission webLead) {
    bool _show = true;

    return true; 
  }

  updateContactStatus(WpFormsSubmission webLead, String stage) async {
    webLead.status = stage;
    
    await updateDocument(appConfig.COLLECT_WP_FORM_SUBMISSION, webLead.id!, webLead.toMap());
    // setState(() {
    displayContacts.where(filterFunction).elementAt(displayContacts.where(filterFunction).toList().indexOf(webLead)).status = stage;
    // });
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => displayContacts.length;

  @override
  int get selectedRowCount => 0;
}
