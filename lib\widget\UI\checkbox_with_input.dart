import 'package:flutter/material.dart';
import 'package:newarc_platform/widget/UI/input.dart';

/// *  textInputType - The type of information for which to optimize the text input control.
/// *  hintText - Text that suggests what sort of input the field accepts.
/// *  prefixIcon - Pass Icon if required
/// *  defaultText - If there is predefined value is there for a text field
/// *  focusNode - Currently focus node
/// *  obscureText - Is Password field?
/// *  controller - Text controller
/// *  functionValidate - Validation function(currently I have used to check empty field)
/// *  parametersValidate - Value to validate
/// *  actionKeyboard - Keyboard action eg. next, done, search, etc
/// *  onSubmitField - Done click action
/// *  onFieldTap - On focus on TextField
class NarCheckboxWithInputWidget extends StatefulWidget {
  final String? label;
  final Map<String, bool>? values;
  final int? columns;
  final Widget? appendText;
  final Map<String, TextEditingController>? additionalValue;
  final double? childAspectRatio;

  const NarCheckboxWithInputWidget(
      {required this.label,
      required this.values,
      this.columns,
      this.appendText,
      this.additionalValue,
      this.childAspectRatio});

  @override
  _NarCheckboxWithInputWidgetState createState() =>
      _NarCheckboxWithInputWidgetState();
}

class _NarCheckboxWithInputWidgetState
    extends State<NarCheckboxWithInputWidget> {
  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(
        primaryColor: Theme.of(context).primaryColor,
        unselectedWidgetColor: Color.fromRGBO(219, 219, 219, 1),
        checkboxTheme: CheckboxThemeData(
          fillColor: WidgetStateProperty.resolveWith<Color>(
              (Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return Color.fromRGBO(219, 219, 219, 1);
            } else if (states.contains(WidgetState.selected)) {
              return Theme.of(context).primaryColor;
            }

            return Color.fromRGBO(219, 219, 219, 1);
          }),
          overlayColor: WidgetStateProperty.resolveWith<Color>(
              (Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return Colors.transparent;
            } else if (states.contains(WidgetState.selected)) {
              return Colors.transparent;
            }

            return Colors.transparent;
          }),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(5),
          ),
        ),
      ),
      child: new GridView.count(
        // scrollDirection: Axis.horizontal,
        crossAxisCount: widget.columns ?? 3,
        shrinkWrap: true,
        childAspectRatio: widget.childAspectRatio ?? 8,
        mainAxisSpacing: 5,

        children: widget.values!.keys.map((String key) {
          return SizedBox(
            height: 24,
            width: 24,
            child: new CheckboxListTile(
              isThreeLine:
                  widget.appendText != null && widget.values![key] == true
                      ? true
                      : false,
              dense: true,
              controlAffinity: ListTileControlAffinity.leading,
              title: new Text(key,
                  style: TextStyle(
                    // color: colorRed,
                    fontSize: 15.0,
                    fontWeight: FontWeight.w800,
                    fontStyle: FontStyle.normal,
                    //fontFamily: 'Visby800'
                  )),
              subtitle: widget.appendText != null && widget.values![key] == true
                  ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Expanded(
                            flex: 50,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(
                                  height: 5,
                                ),
                                widget.appendText!,
                                NarInputWidget(
                                    hintText: '',
                                    controller: widget.additionalValue![key])
                              ],
                            )),
                        Expanded(flex: 50, child: SizedBox(height: 0))
                      ],
                    )
                  : SizedBox(
                      height: 0,
                    ),
              value: widget.values![key],
              onChanged: (bool? value) {
                setState(() {
                  widget.values![key] = value!;
                  if (widget.appendText != null) {
                    print(widget.additionalValue![key]!.text);
                  }
                });
              },
            ),
          );
        }).toList(),
      ),
    );
  }
}
