import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:newarc_platform/classes/supplier.dart';
import 'package:newarc_platform/utils/color_schema.dart';


class CustomDrawer extends StatefulWidget {
  CustomDrawer(
      {Key? key, this.updateViewCallback, this.supplierUser, this.selectedView})
      : super(key: key);

  final Function? updateViewCallback;
  final SupplierUser? supplierUser;
  String? selectedView;

  @override
  State<CustomDrawer> createState() => _CustomDrawerState();
}

class _CustomDrawerState extends State<CustomDrawer> {
  bool immaginaExpanded = false;
  ExpansibleController immaginaTile = new ExpansibleController();
  bool contattiTileExpand = false;
  ExpansibleController contattiTileKey = new ExpansibleController();
  bool reportTileExpand = false;
  ExpansibleController reportTileKey = new ExpansibleController();
  bool strumentiTileExpand = false;
  ExpansibleController strumentiTileKey = new ExpansibleController();

  bool isDrawerOpen = true;

  void toggleDrawer() {
    setState(() {
      isDrawerOpen = !isDrawerOpen;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        AnimatedContainer(
          duration: Duration(milliseconds: 300),
          width: isDrawerOpen ? 257 : 80,
          color: Theme.of(context).primaryColorDark,
          child: Container(
            child: !isDrawerOpen
                ? Column(
                    children: [
                      Align(
                        alignment: Alignment.centerLeft,
                        child: Padding(
                          padding:
                              EdgeInsets.only(left: 20, right: 20, top: 42),
                          child: Image.asset(
                            "assets/home-agencies.png",
                            height: 25,
                          ),
                        ),
                      ),
                    ],
                  )
                : Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: ListView(
                          shrinkWrap: true,
                          padding: EdgeInsets.all(0),
                          children: [
                            Align(
                              alignment: Alignment.centerLeft,
                              child: Padding(
                                padding: EdgeInsets.only(
                                    left: 20, right: 20, top: 30),
                                child: Image.asset(
                                  "assets/logo.png",
                                  height: 50,
                                ),
                              ),
                            ),
                            SizedBox(height: 40),
                          Theme(
                              data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
                              child: ExpansionTile(
                                key: Key('dashboard'),
                                controller: immaginaTile,
                                initiallyExpanded: immaginaExpanded,
                                onExpansionChanged: ((value) => setState(
                                      () {
                                        immaginaExpanded = value;
                                      },
                                    )),
                                collapsedIconColor: Colors.white,
                                iconColor: Colors.white,
                                minTileHeight: 0,
                                childrenPadding: EdgeInsets.symmetric(horizontal: 15),
                                tilePadding: EdgeInsets.only(left: 15, right: 15, top: 5),
                                title: Text(
                                  'Dashboard',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontFamily: 'semi-bold',
                                    color: Colors.white,
                                  ),
                                ),
                                trailing: SvgPicture.asset(
                                  immaginaExpanded ? 'assets/icons/arrow_up.svg' : 'assets/icons/arrow_down.svg',
                                  height: 6,
                                  width: 10,
                                  color: Colors.white,
                                ),
                                children: <Widget>[
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      InkWell(
                                        onTap: () {
                                          widget.selectedView = 'dashboard';
                                          widget.updateViewCallback!(widget.selectedView);
                                        },
                                        child: Row(
                                          children: [
                                            Text(
                                              'Dashboard',
                                              style: TextStyle(
                                                fontSize: 14,
                                                fontFamily: 'Raleway-400',
                                                color: getColor('dashboard'),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
          ),
        ),
        Positioned(
          right: -10,
          top: 42,
          child: InkWell(
            onTap: toggleDrawer,
            child: Container(
              height: 28,
              width: 28,
              child: Center(
                child: SvgPicture.asset(
                  isDrawerOpen
                      ? "assets/icons/arrow_left.svg"
                      : "assets/icons/arrow_right.svg",
                  color: AppColor.drawerIconButtonColor,
                  height: 10,
                ),
              ),
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                      offset: Offset(0, 10),
                      blurRadius: 10,
                      color: AppColor.black.withOpacity(0.1))
                ],
                color: AppColor.drawerButtonColor,
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        )
      ],
    );
  }

  Color? getColor(String view) {
    if (widget.selectedView == view) {
      return Theme.of(context).primaryColor; //Color(0xff489B79);
    } else {
      return Colors.white; //Color(0xff7D7D7D);
    }
  }
}

