import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

/*
getColor(String status) {
  switch (status) {
    case 'Da sbloccare':
      return Color.fromRGBO(166, 166, 166, 1);
    case 'Da contattare':
      return Color.fromRGBO(245, 198, 32, 1);
    case 'Contattato':
      return Color.fromRGBO(86, 195, 229, 1);
    case 'Completato':
      return Color.fromRGBO(72, 155, 121, 1);
    case 'Preventivazione':
      return Color(0xff5DB1E3);
    case 'In analisi':
      return Color(0xffFEC600);
    case 'Bloccata':
      return Color(0xffDD0000);
    case 'Confermata':
      return Color(0xff39C14F);
    case 'completato':
      return Color(0xff39C14F);
    case 'In lavorazione':
      return Color(0xffFF7B00);
    default:
      return Colors.transparent;
  }
}
*/

class CustomDropdownButton extends StatefulWidget {
  final String? selectedValue;
  final List<String> items;
  final String hintText;
  final Color? containerColor;
  final bool showCircle;
  final bool onlyShowCircle;
  final TextStyle? textStyle;
  final Function(String?)? onChanged;
  final Function(String?)? getColor;
  final double? circleWidth;
  final double? circleHeight;
  final double? width;
  final double? height;
  final bool? disabled;
  const CustomDropdownButton({
    Key? key,
    this.selectedValue,
    required this.items,
    this.onChanged,
    required this.hintText,
    required this.getColor,
    this.containerColor,
    this.showCircle = true,
    this.onlyShowCircle = false,
    this.textStyle, this.width,this.height,this.circleWidth,this.circleHeight,
    this.disabled = false
  }) : super(key: key);

  @override
  State<CustomDropdownButton> createState() => _CustomDropdownButtonState();
}

class _CustomDropdownButtonState extends State<CustomDropdownButton> {
  double defaultWidth = 140;
  @override
  Widget build(BuildContext context) {
    if (widget.items.isEmpty) {
      return Container(
        width: widget.width ?? defaultWidth,
        height: widget.height ?? null,
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
        decoration: BoxDecoration(
          border: Border.all(
            width: 1,
            color: AppColor.borderColor,
          ),
          color: AppColor.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            if (widget.showCircle)
              Container(
                height: widget.circleHeight ?? 12,
                width: widget.circleWidth ?? 12,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: widget.getColor!(widget.selectedValue ?? "") ?? Colors.red,
                ),
              ),
            if (widget.showCircle) SizedBox(width: 8),
            !(widget.onlyShowCircle) ?
            NarFormLabelWidget(
              label: widget.selectedValue ?? widget.hintText,
              fontSize: 11,
              fontWeight: '600',
              textColor: AppColor.black,
            ) : SizedBox.shrink(),
          ],
        ),
      );
    }

    return Container(
      width: widget.width ?? defaultWidth,
      decoration: BoxDecoration(
        border: Border.all(
          width: 1,
          color: AppColor.borderColor,
        ),
        color: AppColor.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButtonHideUnderline(
        
        child: DropdownButton2<String>(
          isExpanded: true,
          hint: NarFormLabelWidget(
            label: widget.hintText,
            fontSize: 11,
            fontWeight: '600',
            textColor: AppColor.greyColor,
          ),
          style: TextStyle(
            fontSize: 11,
            fontFamily: 'Raleway-600',
            color: AppColor.black,
          ),
          enableFeedback: true,
          dropdownStyleData: DropdownStyleData(
            elevation: 0,
            padding: EdgeInsets.zero,
            decoration: BoxDecoration(
              color: AppColor.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                width: 1,
                color: AppColor.borderColor,
              ),
              boxShadow: [
                BoxShadow(
                  offset: Offset(0, 7),
                  color: AppColor.black.withOpacity(.08),
                  blurRadius: 17,
                )
              ],
            ),
          ),
          items: widget.items.map((String item) {
            return DropdownMenuItem<String>(
              value: item,
              child: Row(
                children: [
                  if (widget.showCircle)
                    Container(
                      height: widget.circleHeight ?? 12,
                      width: widget.circleWidth ?? 12,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: widget.getColor!(item) ?? Colors.red,
                      ),
                    ),
                  if (widget.showCircle) SizedBox(width: 8),

                  !(widget.onlyShowCircle) ?
                  Expanded(child: Text(item)) : SizedBox.shrink(),
                ],
              ),
            );
          }).toList(),
          value: widget.selectedValue,
          onChanged: widget.onChanged,
          buttonStyleData: ButtonStyleData(
            decoration: BoxDecoration(
              color: AppColor.white,
              borderRadius: BorderRadius.circular(8),
            ),
            elevation: 0,
            padding: EdgeInsets.symmetric(horizontal: 10),
            height:  widget.height ?? 30,
            width: 148,
          ),
          menuItemStyleData: MenuItemStyleData(
            height: 40,
          ),
          iconStyleData: IconStyleData(
            iconSize: 15,
            icon: Icon(
              Icons.keyboard_arrow_down,
              color: AppColor.iconGreyColor,
            ),
          ),
        ),
      ),
    );
  }
}
