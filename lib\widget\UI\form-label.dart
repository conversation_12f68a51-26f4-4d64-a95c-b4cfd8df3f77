import 'package:flutter/material.dart';

/// *  textInputType - The type of information for which to optimize the text input control.
/// *  hintText - Text that suggests what sort of input the field accepts.
/// *  prefixIcon - Pass Icon if required
/// *  defaultText - If there is predefined value is there for a text field
/// *  focusNode - Currently focus node
/// *  obscureText - Is Password field?
/// *  controller - Text controller
/// *  functionValidate - Validation function(currently I have used to check empty field)
/// *  parametersValidate - Value to validate
/// *  actionKeyboard - Keyboard action eg. next, done, search, etc
/// *  onSubmitField - Done click action
/// *  onFieldTap - On focus on TextField
class NarFormLabelWidget extends StatefulWidget {
  final String? label;
  final Color? textColor;
  final String? fontWeight;
  final double? fontSize;
  final TextAlign? textAlign;
  final TextOverflow? overflow;
  final double? height;
  final TextDecoration? textDecoration;
  double? letterSpacing = 0;
  int? maxLines;
  bool? selectable;

  NarFormLabelWidget(
      {Key? key,
      required this.label,
      this.textColor,
      this.fontWeight,
      this.fontSize,
      this.textAlign,
      this.overflow,
      this.height,
      this.textDecoration,
      this.letterSpacing = 0, 
      this.maxLines,
      this.selectable,
      })
      : super(key: key);

  @override
  _NarFormLabelWidgetState createState() => _NarFormLabelWidgetState();
}

class _NarFormLabelWidgetState extends State<NarFormLabelWidget> {
  String? label;
  Color? textColor;
  String? fontWeight;
  double? fontSize;
  TextAlign? textAlign;
  TextOverflow? overflow;
  double? height;
  TextDecoration? textDecoration;
  int? maxLines;
  bool selectable = false;

  @override
  void initState() {
    label = widget.label != null ? widget.label : '';
    textColor = widget.textColor != null ? widget.textColor : Colors.black;
    fontWeight = widget.fontWeight != null ? widget.fontWeight : 'bold';
    fontSize = widget.fontSize != null ? widget.fontSize : 14;
    textAlign = widget.textAlign != null ? widget.textAlign : TextAlign.left;
    overflow =
        widget.overflow != null ? widget.overflow : TextOverflow.ellipsis;
    height = widget.height != null ? widget.height : 1.2;
    textDecoration = widget.textDecoration != null
        ? widget.textDecoration
        : TextDecoration.none;
    maxLines = widget.maxLines ?? null;
    selectable = widget.selectable ?? false;
    super.initState();
  }

  @protected
  void didUpdateWidget(NarFormLabelWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    label = widget.label != null ? widget.label : '';
    textColor = widget.textColor != null ? widget.textColor : Colors.black;
    fontWeight = widget.fontWeight != null ? widget.fontWeight : 'bold';
    fontSize = widget.fontSize != null ? widget.fontSize : 14;
    textAlign = widget.textAlign != null ? widget.textAlign : TextAlign.left;
    overflow =
        widget.overflow != null ? widget.overflow : TextOverflow.ellipsis;
    height = widget.height != null ? widget.height : 1.2;
    textDecoration = widget.textDecoration != null
        ? widget.textDecoration
        : TextDecoration.none;
    maxLines = widget.maxLines ?? null;
    selectable = widget.selectable ?? false;
    // getMasterFilter();
  }

  @override
  Widget build(BuildContext context) {
    if (selectable) {
      return SelectableText(
        label!,
        maxLines: maxLines,
        textAlign: textAlign ?? TextAlign.start,
        style: TextStyle(
          color: textColor ?? Color.fromRGBO(105, 105, 105, 1),
          overflow: overflow,
          fontFamily: 'Raleway-' + fontWeight!,
          fontSize: fontSize ?? 14,
          letterSpacing: widget.letterSpacing,
          height: height,
          decoration: textDecoration,
          decorationColor: textColor ?? Color.fromRGBO(105, 105, 105, 1),
        ),
      );
    } else {
      return Text(
        label!,
        overflow: overflow,
        maxLines: maxLines,
        textAlign: textAlign ?? TextAlign.start,
        softWrap: true,
        style: TextStyle(
          color: textColor ?? Color.fromRGBO(105, 105, 105, 1),
          fontFamily: 'Raleway-' + fontWeight!,
          fontSize: fontSize ?? 14,
          letterSpacing: widget.letterSpacing,
          height: height,
          decoration: textDecoration,
          decorationColor: textColor ?? Color.fromRGBO(105, 105, 105, 1),
        ),
      );
    }
  }
}
