import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/routes/work_routes.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/classes/property.dart';
import 'package:newarc_platform/widget/work/ads/active_ad.dart';
import 'package:newarc_platform/widget/work/project/ads/configuration.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import '../../classes/immaginaProject.dart';
import '../../pages/work/newarc_active_ads.dart';
import '../../utils/color_schema.dart';

class ActiveAdAdd extends StatefulWidget {
  // var project;
  // Property? property;
  String? projectId;
  String? propertyId;
  String? type;

  List<bool>? isInputChangeDetected = [];
  bool? wasArchived;

  ActiveAdAdd(
      {Key? key,
      // this.project,
      // this.property,
      this.projectId,
      this.propertyId,
      this.type,
      this.wasArchived,
      this.isInputChangeDetected })
      : super(key: key);

  @override
  _ActiveAdAddState createState() => _ActiveAdAddState();
}

class _ActiveAdAddState extends State<ActiveAdAdd> {
  
  bool loading = false;
  bool isAdSectionActive = true;
  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  final _formKey = GlobalKey<FormState>();

  String _address = '';

  String pageTitle = '';

  
  String projectType = '';
  Color? projectTypeColor;
  Property? property;
  var project;
  

  @override
  initState(){
    // if( widget.project.runtimeType == NewarcProject ) {
    //   NewarcProject _pro = widget.project;
    //   _address = _pro.addressInfo!.streetName! +' '+ _pro.addressInfo!.streetNumber! +', '+_pro.addressInfo!.city!;
    // } else {
      
    //   Immag _pro = widget.project;
    //   _address = _pro.addressInfo!.streetName! +' '+ _pro.addressInfo!.streetNumber! +', '+_pro.addressInfo!.city!;
    // }
    super.initState();

    initActiveAd();

  }

  initActiveAd()async{
    try{
      if (!mounted) return;
      setState(() {
        loading = true;
      });
      DocumentSnapshot<Map<String, dynamic>> _prop = await FirebaseFirestore.instance.collection(appConfig.COLLECT_NEWARC_HOME).doc(widget.propertyId).get();
      if(_prop.exists && _prop.data() != null){
        property =  Property.fromDocument(_prop);
      }
      if(widget.type == "newarc"){
        DocumentSnapshot<Map<String, dynamic>> _proj = await FirebaseFirestore.instance.collection(appConfig.COLLECT_NEWARC_PROJECTS).doc(widget.projectId).get();
        if(_proj.exists && _proj.data() != null) {
          project = NewarcProject.fromDocument(_proj.data()!, _proj.id);
          projectType = project!.type;
        }
      }else{
        DocumentSnapshot<Map<String, dynamic>> _proj = await FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).doc(widget.projectId).get();
        if(_proj.exists && _proj.data() != null) {
          project = NewarcProject.fromDocument(_proj.data()!, _proj.id);
          if (property!.projectType == 'Immagina for Professionals') {
            projectType = 'Professionals';
          } else {
            projectType = property!.projectType!;
          }
        }
      }

      _address = project.addressInfo!.toShortAddress();

      String rooms = '';
      if(property!.projectType == 'cut' ) {
          switch (int.tryParse(property?.locals?.toString() ?? '')) {
            case 1:
              rooms = 'Monolocale';
              break;
            case 2:
              rooms = 'Bilocale';
              break;
            case 3:
              rooms = 'Trilocale';
              break;
            case 4:
              rooms = 'Quadrilocale';
              break;
            default:
              rooms = 'Plurilocale';
              break;
          }
      }
      if(property!.projectType == 'cut' ) {
        pageTitle = rooms +' in '+ property!.addressInfo!.toShortAddress();;
      } else {
        pageTitle = property!.addressInfo!.toShortAddress();
      }
    }catch(e){
      log("Error while fetching project and properties ===> ${e.toString()}");

    }finally{
      if (!mounted) return;
      setState(() {
        loading = false;
      });
    }

    log("projectType INIT ===> ${projectType}");
  }
  
  @override
  Widget build(BuildContext context) {
    log("projectType ===> ${projectType}");
    return loading? Scaffold(
      backgroundColor: Colors.white,
      body: Center(child: CircularProgressIndicator(color: Theme.of(context).primaryColor,),),
    )  : Stack(
      children: [
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: Stack(
              clipBehavior: Clip.none,
              // mainAxisSize: MainAxisSize.max,
              // mainAxisAlignment: MainAxisAlignment.spaceBetween,
              // crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Positioned(
                  left: 0,
                  child: MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: () async {
                        if(property!.projectType == 'cut' ) {

                          DocumentSnapshot<Map<String, dynamic>> cutResponse =
                          await FirebaseFirestore.instance
                              .collection(appConfig.COLLECT_NEWARC_HOME)
                              .doc(project.propertyId)
                              .get();
                          Property building = Property.fromDocument(cutResponse);

                          context.go(WorkRoutes.workAnnunciAttiviInside(projectId: project.id!, propertyId: building.firebaseId!,type: normalizeName(projectType)));
                          // widget.updateViewCallback!(
                          //   'active-ad-single',
                          //   projectArguments: {
                          //     'property': building,
                          //     'project': widget.project,
                          //     'wasArchived': building.isArchived,
                          //     'isInputChangeDetected': [false]
                          //   }
                          // );
                        } else {
                          context.go(WorkRoutes.workAnnunciAttivi);
                        }
                        
                      },
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          // IconButton(
                          //   hoverColor: Colors.transparent,
                          //   focusColor: Colors.transparent,
                          //   onPressed: () {
                          //     widget.updateViewCallback!(
                          //         'active-ads', projectArguments: {
                          //       'isArchived': widget.wasArchived
                          //     });
                          //     widget.initialFetchProperties;
                          //   },
                          //   icon: SvgPicture.asset(
                          //       'assets/icons/arrow_left.svg',
                          //       height: 20,
                          //       color: Colors.black),
                          // ),
                          SvgPicture.asset(
                              'assets/icons/arrow_left.svg',
                              height: 15,
                              color: Colors.black),
                          SizedBox(width: 10,),
                          SizedBox(width: 5,),
                          property!.projectType == 'cut'
                          ? NarFormLabelWidget(
                            label:property!.addressInfo!.toShortAddress(),
                            fontSize: 15,
                            fontWeight: '600',
                            textDecoration: TextDecoration.underline,
                            textColor: AppColor.black,
                          )
                          : NarFormLabelWidget(
                            label: 'Tutti gli annunci',
                            fontSize: 15,
                            fontWeight: '600',
                            textDecoration: TextDecoration.underline,
                            textColor: AppColor.black,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    NarFormLabelWidget(
                      label: pageTitle,
                      fontSize: 18,
                      fontWeight: '700',
                      textColor: Colors.black,
                    ),
                  ],
                ),
                

                property!.projectType != 'cut'
                ? Positioned(
                  right: 0,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      NarFormLabelWidget(
                        label: 'Tipologia annuncio:',
                        fontSize: 13,
                        fontWeight: '600',
                        textColor: AppColor.black,
                      ),
                      SizedBox(
                        width: 10,
                      ),
                      Container(
                        padding: EdgeInsets.symmetric( vertical: 5, horizontal: 10),
                        decoration: BoxDecoration(
                          color: property!.projectType! == 'Immagina for Professionals' ? Colors.black : Theme.of(context).primaryColor,
                          borderRadius:
                          BorderRadius.circular(5),
                          border: Border.all( color: Theme.of(context).primaryColor),
                        ),
                        child: NarFormLabelWidget(
                          label: projectType,
                          fontSize: 15,
                          fontWeight: 'bold',
                          textColor: Colors.white,
                        ),
                      )
                    ],
                  ),
                )
                : Container(),
              ]
          ),
        ),
        Padding(
          padding: EdgeInsets.only(top: 50),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: Color(0xffECECEC),
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(13),
                        topRight: Radius.circular(13)
                    ),
                    border: Border(
                      top: BorderSide(
                          width: 1, color: Color(0xffECECEC)),
                      left: BorderSide(
                          width: 1, color: Color(0xffECECEC)),
                      right: BorderSide(
                          width: 1, color: Color(0xffECECEC)),
                    ),
                  ),
                  margin: EdgeInsets.only(top: 20),
                  child: 
                 property!.projectType != 'cut'
                  ? Row(
                    children: [
                      Expanded(
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () => setState((){isAdSectionActive = true;}),
                            child: Container(
                                height: 40,
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(13),
                                        topRight: isAdSectionActive ? Radius.circular(13) : Radius.circular(0)
                                    ),
                                    color: isAdSectionActive ? Colors.white : Colors.transparent
                                ),
                                child: Center(
                                  child: NarFormLabelWidget(
                                      label: 'Annuncio',
                                      fontSize: 15,
                                      textColor: Colors.black
                                  ),
                                )
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () => setState((){isAdSectionActive = false;}),
                            child: Container(
                                height: 40,
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(13),
                                        topRight: Radius.circular(13)
                                    ),
                                    color: !isAdSectionActive ? Colors.white : Colors.transparent
                                ),
                                child: Center(
                                  child: NarFormLabelWidget(
                                      label: 'Configuratore',
                                      fontSize: 15,
                                      textColor: Colors.black
                                  ),
                                )
                            ),
                          ),
                        ),
                      ),
                    ],
                  )
                  : Container( 
                    height: 15, 
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(13),
                            topRight: isAdSectionActive ? Radius.circular(13) : Radius.circular(0)
                        ),
                        color: isAdSectionActive ? Colors.white : Colors.transparent
                    ),
                  ),
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 1,
                      child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
                          decoration: BoxDecoration(
                            border: Border(
                              top: BorderSide(width: 1, color: Colors.white),
                              bottom: BorderSide(width: 1, color: Color(0xffECECEC)),
                              left: BorderSide(width: 1, color: Color(0xffECECEC)),
                              right: BorderSide(width: 1, color: Color(0xffECECEC)),
                            ),
                          ),
                          child: isAdSectionActive
                              ? ActiveAd (
                              project: project,
                              property: property,
                              isInputChangeDetected: widget.isInputChangeDetected,

                          )
                              : ConfigurationForm(
                            property: property,
                          )

                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),

      ],
    );
  }

  
}
