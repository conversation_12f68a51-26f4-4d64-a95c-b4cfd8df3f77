//
// class FontFamily {
//   static const raleway300 = 'Raleway-300';
//   static const raleway400 = 'Raleway-400';
//   static const raleway500 = 'Raleway-500';
//   static const raleway600 = 'Raleway-600';
//   static const raleway700 = 'Raleway-700';
//   static const ralewayBold = 'Raleway-bold';
//   static const raleway800 = 'Raleway-800';
//   static const raleway900 = 'Raleway-900';
// }
//
// extension TextStyleExtensions on TextStyle {
//   TextStyle get bold => weight(FontWeight.w600);
//
//   TextStyle get text10w700 => customStyle(
//         fontSize: 10,
//         weight: FontWeight.w700,
//         letterSpacing: 0.0,
//       );
//
//   TextStyle get text11w600 => customStyle(
//         fontSize: 11,
//         weight: FontWeight.w600,
//         letterSpacing: 0.0,
//       );
//
//   TextStyle get text12w600 => customStyle(
//         fontSize: 12,
//         weight: FontWeight.w600,
//         letterSpacing: 0.0,
//       );
//
//   TextStyle get text14w500 => customStyle(
//         fontSize: 14,
//         weight: FontWeight.w500,
//         letterSpacing: 0.0,
//       );
//   TextStyle get text14w800 => customStyle(
//     fontSize: 14,
//     weight: FontWeight.w800,
//     letterSpacing: 0.0,
//   );
//   TextStyle get text12w700 => customStyle(
//         fontSize: 12,
//         weight: FontWeight.w700,
//         letterSpacing: 0.0,
//       );
//
//   TextStyle get text15w500 => customStyle(
//         fontSize: 15,
//         weight: FontWeight.w500,
//         letterSpacing: 0.0,
//       );
//
//   TextStyle get text22w700 => customStyle(
//         fontSize: 22,
//         weight: FontWeight.w700,
//         letterSpacing: 0.0,
//       );
//
//   TextStyle get text22w800 => customStyle(
//     fontSize: 22,
//     weight: FontWeight.w800,
//     letterSpacing: 0.0,
//   );
//   TextStyle textColor(Color v) => copyWith(color: v);
//
//   TextStyle weight(FontWeight v) => copyWith(fontWeight: v);
//
//   TextStyle size(double v) => copyWith(fontSize: v);
//
//   TextStyle letterSpace(double v) => copyWith(letterSpacing: v);
//
//   TextStyle underline() => copyWith(
//         decoration: TextDecoration.underline,
//
//       );
//
//   String fontFamily(FontWeight weight) {
//     if (weight == FontWeight.w300) {
//       return FontFamily.raleway300;
//     } else if (weight == FontWeight.w400) {
//       return FontFamily.raleway400;
//     } else if (weight == FontWeight.w500) {
//       return FontFamily.raleway500;
//     } else if (weight == FontWeight.w600) {
//       return FontFamily.raleway600;
//     } else if (weight == FontWeight.w700) {
//       return FontFamily.raleway700;
//     } else if (weight == FontWeight.w800) {
//       return FontFamily.raleway800;
//     } else if (weight == FontWeight.w900) {
//       return FontFamily.raleway900;
//     } else {
//       return FontFamily.raleway500;
//     }
//   }
//
//   TextStyle customStyle({
//     required double letterSpacing,
//     required double fontSize,
//     required FontWeight weight,
//   }) =>
//       copyWith(
//         letterSpacing: letterSpacing,
//         fontSize: fontSize,
//         fontWeight: weight,
//         fontFamily: fontFamily(weight),
//       );
// }
