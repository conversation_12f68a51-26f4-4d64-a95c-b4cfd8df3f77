import 'dart:js_interop';
import 'dart:typed_data';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/moodboard.dart';
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/classes/renovationContact.dart';
import 'package:newarc_platform/classes/renovationQuotation.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/pages/work/moodboard/moodboard_controller.dart';
import 'package:newarc_platform/pages/work/moodboard/moodboard_data_source.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/firestore.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/image-select-box.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/widget/UI/newarc_data_table.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/widget/UI/tab/common_icon_button.dart';
import 'package:newarc_platform/widget/UI/tab/text_style.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:pdf/pdf.dart';
import 'package:intl/intl.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:flutter/services.dart' show rootBundle;
import 'package:web/web.dart' as web;

class MoodboardView extends StatefulWidget {
  final responsive;
  final Function? updateViewCallback;

  const MoodboardView({Key? key, required this.responsive, this.updateViewCallback}) : super(key: key);

  @override
  State<MoodboardView> createState() => _MoodboardViewState();
}

class _MoodboardViewState extends State<MoodboardView> {
  final controller = Get.put<MoodboardViewController>(MoodboardViewController());
  Key? paddingKey;

  @override
  void initState() {
    controller.contSearchRef.text = '';

    initialFetchMoodboards();
    fetchClients();

    super.initState();
  }

  List<Map> searchRef = [];
  TextEditingController contSearchQuotation = new TextEditingController();

  Future<List> fetchClients() async {
    try {
      List<RenovationContact> _renovationContacts = [];

      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      if (controller.connectClientSelectedCityController.text != '') {
        collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_RENOVATION_CONTACTS).where('city', isEqualTo: controller.connectClientSelectedCityController.text);
      } else {
        collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_RENOVATION_CONTACTS);
      }

      collectionSnapshot = await collectionSnapshotQuery.get();

      _renovationContacts.add(RenovationContact.empty());

      if (collectionSnapshot.docs.length > 0) {
        for (var element in collectionSnapshot.docs) {
          try {
            RenovationContact _tmp = RenovationContact.fromDocument(element.data(), element.id);
            _renovationContacts.add(_tmp);
          } catch (e) {
            print("error in contact document ${element.id}");
          }
        }
      }
      setState(() {
        controller.renovationContacts = _renovationContacts;
      });

      if (controller.renovationContacts.length > 1) {
        return controller.renovationContacts.map((e) {
          return {'value': e.id, 'label': e.name! + " " + e.city!};
        }).toList();
      } else {
        return [];
      }
    } catch (e) {
      return [];
    }
  }

  Future<List> fetchProjects() async {
    try {
      List<NewarcProject> _projects = [];

      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      if (controller.connectProjectSelectedTypeController.text != '') {
        collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_NEWARC_PROJECTS).where('type', isEqualTo: controller.connectProjectSelectedTypeController.text);
      } else {
        collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_NEWARC_PROJECTS);
      }

      collectionSnapshot = await collectionSnapshotQuery.get();

      _projects.add(NewarcProject.empty());

      if (collectionSnapshot.docs.length > 0) {
        for (var element in collectionSnapshot.docs) {
          try {
            NewarcProject _tmp = NewarcProject.fromDocument(element.data(), element.id);
            _projects.add(_tmp);
          } catch (e) {}
        }
      }

      controller.newarcProjects = _projects;
      if (controller.newarcProjects.length > 1) {
        return controller.newarcProjects.map((e) {
          return {'value': e.id, 'label': e.name! + " " + e.city!};
        }).toList();
      } else {
        return [];
      }
    } catch (e, s) {
      print({e, s});
      return [];
    }
  }

  // @override
  // void dispose() {
  //   setState(() {
  //     controller.moodboards = [];
  //   });
  //   super.dispose();
  // }

  // @protected
  // void didUpdateWidget(MoodboardView oldWidget) {
  //   super.didUpdateWidget(oldWidget);
  //   initialFetchMoodboards();
  // }

  Widget dataTablePagination() {
    return Visibility(
      visible: controller.currentlyShowing.isNotEmpty,
      child: Padding(
        key: paddingKey,
        padding: EdgeInsets.symmetric(vertical: 10),
        child: IconTheme.merge(
          data: const IconThemeData(opacity: 0.54),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                "${controller.currentlyShowing} di  ${controller.totalRecords.toString()}",
                style: TextStyle(
                  fontFamily: '',
                  fontSize: 12.0,
                  color: Colors.black.withOpacity(0.54),
                ),
              ),
              SizedBox(width: 32.0),
              IconButton(
                padding: EdgeInsets.zero,
                icon: const Icon(Icons.chevron_left),
                onPressed: () {
                  if (controller.disablePreviousButton == true) return;
                  if (controller.loadingMoodboards == true) return;
                  fetchPrevContacts();
                },
              ),
              SizedBox(width: 24.0),
              IconButton(
                icon: const Icon(Icons.chevron_right),
                padding: EdgeInsets.zero,
                onPressed: () {
                  if (controller.disableNextButton == true) return;
                  if (controller.loadingMoodboards == true) return;

                  fetchNextContacts();
                },
              ),
              SizedBox(width: 14.0),
            ],
          ),
        ),
      ),
    );
  }

  Future<List<String>> getRenovatorAssociatedContacts() async {
    Query<Map<String, dynamic>> collectionSnapshotQuery;
    QuerySnapshot<Map<String, dynamic>> collectionSnapshot;

    collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_MOODBOARDS);

    collectionSnapshot = await collectionSnapshotQuery.get();
    List<String> renovationContactIds = [];

    if (collectionSnapshot.docs.length > 0) {
      for (var i = 0; i < collectionSnapshot.docs.length; i++) {
        renovationContactIds.add(collectionSnapshot.docs[i].id);
      }
    }

    return renovationContactIds;
  }

  Future<void> initialFetchMoodboards({bool force = false}) async {
    if (controller.moodboards.isNotEmpty && !force) return;

    controller.pageCounter = 1;

    setState(() {
      controller.moodboards = [];
      controller.loadingMoodboards = true;
    });

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshotCounter;
      Query<Map<String, dynamic>> collectionSnapshotCounterQuery;

      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_MOODBOARDS);
      collectionSnapshotCounterQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_MOODBOARDS);

      // collectionSnapshot = await collectionSnapshotQuery.orderBy('created', descending: true).limit(recordsPerPage).get();
      collectionSnapshot = await collectionSnapshotQuery.limit(controller.recordsPerPage).get();

      collectionSnapshotCounter = await collectionSnapshotCounterQuery.get();

      controller.totalRecords = collectionSnapshotCounter.docs.length;

      controller.totalPages = (controller.totalRecords / controller.recordsPerPage).ceil();

      if (controller.totalRecords > controller.recordsPerPage) {
        controller.currentlyShowing = '1-' + controller.recordsPerPage.toString();
      } else {
        controller.currentlyShowing = '1-' + controller.recordsPerPage.toString();
      }

      controller.documentList = collectionSnapshot.docs;

      await generateQuotationRow(collectionSnapshot);

      setState(() {
        controller.loadingMoodboards = false;
      });
    } catch (e, s) {
      setState(() {
        controller.loadingMoodboards = false;
      });
      print({e, s});
    }
  }

  fetchNextContacts() async {
    setState(() {
      controller.loadingMoodboards = true;
    });

    controller.pageCounter++;

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;
      int indexOfSnapshot = isRecordExists(controller.pageCounter);

      if (indexOfSnapshot > -1) {
        collectionSnapshot = controller.cacheFirestore[indexOfSnapshot]['snapshot'];
      } else {
        collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_MOODBOARDS);

        /*if (filters.length > 0) {
          for (var i = 0; i < filters.length; i++) {
            collectionSnapshotQuery = collectionSnapshotQuery
                .where('isArchived', isEqualTo: false)
                .where(filters[i]['field'], isEqualTo: filters[i]['value']);
          }
        }*/

        collectionSnapshot = await collectionSnapshotQuery
            //.where('isArchived', isEqualTo: false)
            .orderBy('created', descending: true)
            .limit(controller.recordsPerPage)
            .startAfterDocument(controller.documentList[controller.documentList.length - 1])
            .get();
      }

      // List<DocumentSnapshot> newDocumentList = collectionSnapshot.docs;
      controller.documentList = collectionSnapshot.docs;

      // documentList.addAll(newDocumentList);

      generateQuotationRow(collectionSnapshot);
    } catch (e) {
      setState(() {
        controller.loadingMoodboards = false;
      });
    }
  }

  fetchPrevContacts() async {
    setState(() {
      controller.loadingMoodboards = true;
    });

    controller.pageCounter--;

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      int indexOfSnapshot = isRecordExists(controller.pageCounter);
      if (indexOfSnapshot > -1) {
        collectionSnapshot = controller.cacheFirestore[indexOfSnapshot]['snapshot'];
      } else {
        collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_MOODBOARDS);

        if (controller.filters.length > 0) {
          for (var i = 0; i < controller.filters.length; i++) {
            collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']).where('isArchived', isEqualTo: false);
          }
        }

        collectionSnapshot = await collectionSnapshotQuery
            .where('isArchived', isEqualTo: false)
            .orderBy('created', descending: true)
            .limit(controller.recordsPerPage)
            .endBeforeDocument(controller.documentList[controller.documentList.length - 1])
            .get();

        // List<DocumentSnapshot> newDocumentList = collectionSnapshot.docs;
      }

      // updateIndicator(true);

      // documentList.addAll(newDocumentList);
      controller.documentList = collectionSnapshot.docs;
      generateQuotationRow(collectionSnapshot);
    } catch (e) {
      setState(() {
        controller.loadingMoodboards = false;
      });
    }
  }

  Future<Uint8List> loadImage(String path) async {
    final ByteData data = await rootBundle.load(path);
    return data.buffer.asUint8List();
  }

  pw.Widget dataRow(String category, Map rowData, ralewayMedium, ralewayBold) {
    return pw.Container(
      margin: pw.EdgeInsets.only(bottom: 5),
      padding: const pw.EdgeInsets.only(left: 15, right: 15, bottom: 5, top: 5),
      child: pw.Row(
        mainAxisSize: pw.MainAxisSize.max,
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Expanded(
            flex: 6,
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(rowData['title'], style: pw.TextStyle(fontSize: 10, color: PdfColor.fromHex('000'))
                    // fontSize: 12,
                    // textColor: Colors.black,
                    ),
                pw.SizedBox(
                  height: 5,
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.only(right: 15),
                  child: pw.Text(rowData['description'], style: pw.TextStyle(fontSize: 8, color: PdfColor.fromHex('6e6e6e'))),
                ),
              ],
            ),
          ),
          pw.SizedBox(
            width: 5,
          ),
          pw.Expanded(
            flex: 1,
            child: pw.Text(rowData['measurementUnit'], textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 10, color: PdfColor.fromHex('000'))),
          ),
          pw.SizedBox(
            width: 5,
          ),
          pw.Expanded(flex: 1, child: pw.Text(rowData['quantity'].toString(), textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 10, color: PdfColor.fromHex('000')))),
          pw.SizedBox(
            width: 5,
          ),
          pw.Expanded(
              flex: 1,
              child: pw.Text(controller.localCurrencyFormatMain.format(rowData['unitPrice']) + '€', textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 10, color: PdfColor.fromHex('000')))),
          pw.SizedBox(
            width: 5,
          ),
          pw.Expanded(
              flex: 1,
              child: pw.Text(controller.localCurrencyFormatMain.format(rowData['total']) + '€',
                  textAlign: pw.TextAlign.right, style: pw.TextStyle(font: ralewayBold, fontSize: 10, color: PdfColor.fromHex('000')))),
        ],
      ),
    );
  }

  pdfDesign(RenovationQuotation quotation, Map<String, Map<String, List<Map<dynamic, dynamic>>>> renovationData, Map categoryTotal) async {
    try {
      double total = 0;

      final ByteData fontMediumData = await rootBundle.load('assets/fonts/Raleway-Medium.ttf');
      final ralewayMedium = pw.Font.ttf(fontMediumData.buffer.asByteData());

      final ByteData fontLightData = await rootBundle.load('assets/fonts/Raleway-Light.ttf');
      final ralewayLight = pw.Font.ttf(fontLightData.buffer.asByteData());

      final ByteData fontBoldData = await rootBundle.load('assets/fonts/Raleway-Bold.ttf');
      final ralewayBold = pw.Font.ttf(fontBoldData.buffer.asByteData());

      final Uint8List coverbgImageData = await loadImage('assets/renovation-quotation-pdf-bg.jpg');
      final coverbg = pw.MemoryImage(coverbgImageData);

      final Uint8List coverLogoImageData = await loadImage('assets/rq-pdf-cover-logo.png');
      final coverLogo = pw.MemoryImage(coverLogoImageData);

      final Uint8List cover2Image1LogoImageData = await loadImage('assets/cover-2-1.jpg');
      final cover2Image1 = pw.MemoryImage(cover2Image1LogoImageData);

      final Uint8List cover2Image2LogoImageData = await loadImage('assets/cover-2-2.jpg');
      final cover2Image2 = pw.MemoryImage(cover2Image2LogoImageData);

      final Uint8List cover2Image3LogoImageData = await loadImage('assets/cover-2-3.jpg');
      final cover2Image3 = pw.MemoryImage(cover2Image3LogoImageData);

      final Uint8List cover2Image4LogoImageData = await loadImage('assets/cover-2-4.jpg');
      final cover2Image4 = pw.MemoryImage(cover2Image4LogoImageData);

      List<String> payments = ['#% Accettazione preventivo', '#% Fine progetto', '#% Fine impianti', '#% Fine massetti', '#% Consegna chiavi'];
      if (quotation.paymentMode == 'Da definire') {
        payments.clear();
        payments.add('Da definire');
      } else if (quotation.paymentMode != null) {
        List<String> tmp = quotation.paymentMode.toString().split('-');
        if (tmp.length > 0) {
          for (var i = 0; i < tmp.length; i++) {
            payments[i] = payments[i].replaceFirst('#', tmp[i].toString());
          }
        }
      } else {
        payments.clear();
      }

      // Create PDF
      final pdf = pw.Document();

      NewarcUser renovatorData;
      renovatorData = controller.renovators.firstWhere((e) => e.id == quotation.renovationContact!.assignedRenovatorId!);

      List<pw.Widget> pdfDataWidgets = [];
      pdfDataWidgets.add(pw.Text('Quotazione', style: pw.TextStyle(font: ralewayBold, fontSize: 18, color: PdfColor.fromHex('000'))));

      pdfDataWidgets.add(
        pw.SizedBox(height: 20),
      );

      renovationData.keys.map((category) {
        // print({ 'renovationRow', renovationRow});
        total += categoryTotal[category];

        pdfDataWidgets.add(pw.Container(
          // margin: pw.EdgeInsets.only(top: 15),
          padding: pw.EdgeInsets.only(left: 15, right: 15, bottom: 10, top: 10),
          decoration: pw.BoxDecoration(
            color: PdfColor.fromHex('F2FAF7'),
            borderRadius: pw.BorderRadius.circular(7),
            // border: pw.Border(
            //   bottom: pw.BorderSide(width: 1.0, color: PdfColor.fromHex('E4E4E4')),

            // )
          ),
          child: pw.Row(
            mainAxisSize: pw.MainAxisSize.max,
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            crossAxisAlignment: pw.CrossAxisAlignment.center,
            children: [
              pw.Text(category, style: pw.TextStyle(font: ralewayBold, fontSize: 13, color: PdfColor.fromHex('489B79'))),
              pw.Text(controller.localCurrencyFormatMain.format(categoryTotal[category]) + ' €', style: pw.TextStyle(font: ralewayBold, fontSize: 13, color: PdfColor.fromHex('489B79')))
            ],
          ),
        ));
        // pdfDataWidgets.add(headerRow());
        pdfDataWidgets.add(pw.SizedBox(
          height: 10,
        ));
        renovationData[category]!.keys.map((subcategory) {
          pdfDataWidgets.add(pw.Container(
            padding: pw.EdgeInsets.only(left: 15, right: 15, bottom: 5, top: 3),
            child: pw.Text(subcategory, style: pw.TextStyle(font: ralewayBold, fontSize: 11, color: PdfColor.fromHex('489B79'))),
          ));

          renovationData[category]![subcategory]!.map((entry) {
            pdfDataWidgets.add(dataRow(category, entry, ralewayMedium, ralewayBold));
          }).toList();
        }).toList();

        pdfDataWidgets.add(pw.SizedBox(
          height: 10,
        ));
      }).toList();

      pdfDataWidgets.add(
        pw.Container(
            padding: pw.EdgeInsets.symmetric(horizontal: 15, vertical: 15),
            decoration: pw.BoxDecoration(
              color: PdfColor.fromHex('489B79'),
              borderRadius: pw.BorderRadius.circular(10),
              // border: pw.Border(
              //   bottom: pw.BorderSide(width: 1.0, color: PdfColor.fromHex('E4E4E4')),

              // )
            ),
            child: pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text('Totale', style: pw.TextStyle(font: ralewayBold, fontSize: 13, color: PdfColor.fromHex('fff'))),
                pw.Text(controller.localCurrencyFormatMain.format(total) + ' €', style: pw.TextStyle(font: ralewayBold, fontSize: 13, color: PdfColor.fromHex('fff')))
              ],
            )),
      );

      pdfDataWidgets.add(
        pw.Container(
            margin: pw.EdgeInsets.only(top: 10),
            padding: pw.EdgeInsets.symmetric(horizontal: 15, vertical: 15),
            decoration: pw.BoxDecoration(
              color: PdfColor.fromHex('F1F1F1'),
              borderRadius: pw.BorderRadius.circular(10),
              // border: pw.Border(
              //   bottom: pw.BorderSide(width: 1.0, color: PdfColor.fromHex('E4E4E4')),

              // )
            ),
            child: pw.Row(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text('Modalità di pagamento', style: pw.TextStyle(font: ralewayBold, fontSize: 13, color: PdfColor.fromHex('489B79'))),
                pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: payments.map((e) {
                      return pw.Column(children: [
                        pw.Text(e, style: pw.TextStyle(font: ralewayMedium, fontSize: 11, color: PdfColor.fromHex('489B79'))),
                        pw.SizedBox(height: 5),
                      ]);
                    }).toList())
              ],
            )),
      );

      pdfDataWidgets.add(
        pw.SizedBox(height: 25),
      );

      List<pw.Widget> pdfDataWidgets2 = [];

      pdfDataWidgets2.add(
        pw.Text('DURATA PRESUNTA CANTIERE:', style: pw.TextStyle(font: ralewayBold, fontSize: 9, color: PdfColor.fromHex('000'))),
      );
      pdfDataWidgets2.add(
        pw.Text(quotation.constructionDuration! == 'Da definire' ? 'Da definire' : quotation.constructionDuration! + ' dalla data di inizio lavori.',
            style: pw.TextStyle(font: ralewayMedium, fontSize: 9, color: PdfColor.fromHex('000'))),
      );
      pdfDataWidgets2.add(
        pw.SizedBox(height: 25),
      );
      pdfDataWidgets2.add(
        pw.Text('ESCLUSIONI:', style: pw.TextStyle(font: ralewayBold, fontSize: 9, color: PdfColor.fromHex('000'))),
      );
      pdfDataWidgets2.add(
        pw.Text('• Ogni tipo di lavorazione effettuata sotto richiesta dalla proprietà e non compresa in questo computo verrà conteggiata a fine lavori.',
            style: pw.TextStyle(font: ralewayMedium, fontSize: 9, color: PdfColor.fromHex('000'))),
      );
      pdfDataWidgets2.add(
        pw.SizedBox(height: 5),
      );

      pdfDataWidgets2.add(
        pw.Text('• Fornitura e consumi di acqua ed energia elettrica.', style: pw.TextStyle(font: ralewayMedium, fontSize: 9, color: PdfColor.fromHex('000'))),
      );

      pdfDataWidgets2.add(
        pw.SizedBox(height: 25),
      );
      pdfDataWidgets2.add(
        pw.Text('VALIDITÀ OFFERTA:', style: pw.TextStyle(font: ralewayBold, fontSize: 9, color: PdfColor.fromHex('000'))),
      );
      pdfDataWidgets2.add(
        pw.Text('30 giorni.', style: pw.TextStyle(font: ralewayMedium, fontSize: 9, color: PdfColor.fromHex('000'))),
      );

      /**
       * Cover page
       */
      pdf.addPage(
        pw.Page(
          theme: pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(0),
          build: (pw.Context context) => pw.Center(
              child: pw.Stack(children: [
            pw.Positioned(
                child: pw.Image(
                  coverbg,
                  fit: pw.BoxFit.cover,
                ),
                left: 0,
                top: 0,
                right: 0,
                bottom: 0),
            pw.Container(
                width: double.infinity,
                height: double.infinity,
                padding: pw.EdgeInsets.only(top: 100, left: 50, right: 50, bottom: 30),
                child: pw.Column(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Container(
                        child: pw.Column(crossAxisAlignment: pw.CrossAxisAlignment.start, children: [
                      pw.Text('RISTRUTTURAZIONE', style: pw.TextStyle(fontSize: 10, letterSpacing: 1, color: PdfColor.fromHex('ffffff'))),
                      pw.SizedBox(height: 10),
                      pw.Text(quotation.renovationContact!.streetAddress!, style: pw.TextStyle(font: ralewayBold, fontSize: 40, color: PdfColor.fromHex('ffffff'))),
                      pw.SizedBox(height: 60),
                      pw.Text('IL TUO ARCHITETTO', style: pw.TextStyle(fontSize: 10, letterSpacing: 1, color: PdfColor.fromHex('ffffff'))),
                      pw.SizedBox(height: 10),
                      pw.Text(renovatorData.firstName! + ' ' + renovatorData.lastName!, style: pw.TextStyle(font: ralewayBold, fontSize: 20, color: PdfColor.fromHex('ffffff')))
                    ])),
                    pw.Row(crossAxisAlignment: pw.CrossAxisAlignment.start, children: [
                      pw.Expanded(child: pw.Image(coverLogo, height: 56)),
                      pw.SizedBox(width: 80),
                      pw.Expanded(
                          child: pw.Column(crossAxisAlignment: pw.CrossAxisAlignment.start, children: [
                        pw.Text('SPETT.LE ', style: pw.TextStyle(fontSize: 8, letterSpacing: 1, color: PdfColor.fromHex('000'))),
                        pw.SizedBox(height: 5),
                        pw.Text(quotation.renovationContact!.name! + ' ' + quotation.renovationContact!.surname!, style: pw.TextStyle(font: ralewayBold, fontSize: 14, color: PdfColor.fromHex('000'))),
                        pw.SizedBox(height: 10),
                        pw.Row(children: [
                          pw.Expanded(
                              child: pw.Column(mainAxisAlignment: pw.MainAxisAlignment.start, crossAxisAlignment: pw.CrossAxisAlignment.start, children: [
                            pw.Text('DATA', style: pw.TextStyle(fontSize: 8, letterSpacing: 1, color: PdfColor.fromHex('000'))),
                            pw.SizedBox(height: 5),
                            pw.Text(timestampToUtcDate(quotation.renovationContact!.created!), style: pw.TextStyle(font: ralewayBold, fontSize: 14, color: PdfColor.fromHex('000'))),
                          ])),
                          pw.SizedBox(width: 10),
                          pw.Expanded(
                              child: pw.Column(mainAxisAlignment: pw.MainAxisAlignment.start, crossAxisAlignment: pw.CrossAxisAlignment.start, children: [
                            pw.Text('CODICE', style: pw.TextStyle(fontSize: 8, letterSpacing: 1, color: PdfColor.fromHex('000'))),
                            pw.SizedBox(height: 5),
                            pw.Text(quotation.code!, style: pw.TextStyle(font: ralewayBold, fontSize: 14, color: PdfColor.fromHex('000'))),
                          ]))
                        ]),
                      ]))
                    ])
                  ],
                )),
          ])),
        ),
      );

      List<pw.Widget> cover2 = [];

      cover2.add(pw.Text('La differenza Newarc', style: pw.TextStyle(font: ralewayBold, fontSize: 18, color: PdfColor.fromHex('000'))));

      cover2.add(
        pw.SizedBox(height: 20),
      );

      cover2.add(pw.Row(mainAxisAlignment: pw.MainAxisAlignment.start, crossAxisAlignment: pw.CrossAxisAlignment.start, children: [
        pw.Expanded(
            child: pw.Container(
                width: 221,
                height: 132,
                decoration: pw.BoxDecoration(
                  borderRadius: pw.BorderRadius.circular(10),
                  image: pw.DecorationImage(
                    image: cover2Image1,
                    fit: pw.BoxFit.cover,
                  ),
                ))),
        pw.SizedBox(
          width: 40,
        ),
        pw.Expanded(
            child: pw.Container(
                child: pw.Column(mainAxisAlignment: pw.MainAxisAlignment.start, crossAxisAlignment: pw.CrossAxisAlignment.start, children: [
          pw.Text('Newarc Immagina', style: pw.TextStyle(font: ralewayBold, fontSize: 13, color: PdfColor.fromHex('000'))),
          pw.SizedBox(height: 5),
          pw.Text('Newarc Immagina è il servizio che ti permette di vedere il futuro della tua nuova casa in maniera iper realistica, grazie ai prodotti digitali realizzati dai nostri renderist.',
              style: pw.TextStyle(font: ralewayMedium, fontSize: 10, color: PdfColor.fromHex('000'), lineSpacing: 5))
        ]))),
      ]));

      cover2.add(pw.SizedBox(
        height: 40,
      ));

      cover2.add(pw.Row(mainAxisAlignment: pw.MainAxisAlignment.start, crossAxisAlignment: pw.CrossAxisAlignment.start, children: [
        pw.Expanded(
            child: pw.Container(
                width: 221,
                height: 132,
                decoration: pw.BoxDecoration(
                  borderRadius: pw.BorderRadius.circular(10),
                  image: pw.DecorationImage(
                    image: cover2Image2,
                    fit: pw.BoxFit.cover,
                  ),
                ))),
        pw.SizedBox(
          width: 40,
        ),
        pw.Expanded(
            child: pw.Container(
                child: pw.Column(mainAxisAlignment: pw.MainAxisAlignment.start, crossAxisAlignment: pw.CrossAxisAlignment.start, children: [
          pw.Text('App del cantiere', style: pw.TextStyle(font: ralewayBold, fontSize: 13, color: PdfColor.fromHex('000'))),
          pw.SizedBox(height: 5),
          pw.Text('L’applicazione Newarc Ristrutturazioni ti permetterà di rimanere costantemente aggiornato sullo stato dei lavori, comodamente da casa tua.',
              style: pw.TextStyle(font: ralewayMedium, fontSize: 10, color: PdfColor.fromHex('000'), lineSpacing: 5))
        ]))),
      ]));

      cover2.add(pw.SizedBox(
        height: 40,
      ));

      cover2.add(pw.Row(mainAxisAlignment: pw.MainAxisAlignment.start, crossAxisAlignment: pw.CrossAxisAlignment.start, children: [
        pw.Expanded(
            child: pw.Container(
                width: 221,
                height: 132,
                decoration: pw.BoxDecoration(
                  borderRadius: pw.BorderRadius.circular(10),
                  image: pw.DecorationImage(
                    image: cover2Image3,
                    fit: pw.BoxFit.cover,
                  ),
                ))),
        pw.SizedBox(
          width: 40,
        ),
        pw.Expanded(
            child: pw.Container(
                child: pw.Column(mainAxisAlignment: pw.MainAxisAlignment.start, crossAxisAlignment: pw.CrossAxisAlignment.start, children: [
          pw.Text('Architetto dedicato', style: pw.TextStyle(font: ralewayBold, fontSize: 13, color: PdfColor.fromHex('000'))),
          pw.SizedBox(height: 5),
          pw.Text('Uno dei nostri architetti sarà al tuo fianco per progettare insieme a te la tua futura casa e per seguire il cantiere durante tutte le fasi.',
              style: pw.TextStyle(font: ralewayMedium, fontSize: 10, color: PdfColor.fromHex('000'), lineSpacing: 5))
        ]))),
      ]));

      cover2.add(pw.SizedBox(
        height: 40,
      ));

      cover2.add(pw.Row(mainAxisAlignment: pw.MainAxisAlignment.start, crossAxisAlignment: pw.CrossAxisAlignment.start, children: [
        pw.Expanded(
            child: pw.Container(
                width: 221,
                height: 132,
                decoration: pw.BoxDecoration(
                  borderRadius: pw.BorderRadius.circular(10),
                  image: pw.DecorationImage(
                    image: cover2Image4,
                    fit: pw.BoxFit.cover,
                  ),
                ))),
        pw.SizedBox(
          width: 50,
        ),
        pw.Expanded(
            child: pw.Container(
                child: pw.Column(mainAxisAlignment: pw.MainAxisAlignment.start, crossAxisAlignment: pw.CrossAxisAlignment.start, children: [
          pw.Text('Ditte selezionate', style: pw.TextStyle(font: ralewayBold, fontSize: 13, color: PdfColor.fromHex('000'))),
          pw.SizedBox(height: 5),
          pw.Text('Ci avvaliamo solo di professionisti con cui instauriamo lunghe collaborazioni. Un rapporto di fiducia basato sulla qualità.',
              style: pw.TextStyle(font: ralewayMedium, fontSize: 10, color: PdfColor.fromHex('000'), lineSpacing: 5))
        ]))),
      ]));
      /**
       * Cover 2
       */
      pdf.addPage(pw.MultiPage(
        theme: pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
        pageFormat: PdfPageFormat.a4,
        footer: (context) => pw.Container(
            // color: PdfColors.amber,
            height: 35,
            child: pw.Column(
              mainAxisAlignment: pw.MainAxisAlignment.start,
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.SizedBox(height: 10),
                pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceBetween, crossAxisAlignment: pw.CrossAxisAlignment.center, children: [
                  pw.Expanded(
                    flex: 1,
                    child: pw.Image(coverLogo, height: 25),
                  ),
                  pw.Expanded(
                    flex: 5,
                    child: pw.Padding(
                        padding: pw.EdgeInsets.symmetric(horizontal: 20),
                        child: pw.Text("NEWARC SRL - Sede Legale Via Vittorio Emanuele II 29, Chieri - Sede Operativa Corso Ferrucci 36, Torino - P.Iva 12533550013 - <EMAIL> - 011.02.63.850",
                            overflow: pw.TextOverflow.visible, style: pw.TextStyle(font: ralewayMedium, fontSize: 7, color: PdfColor.fromHex('6E6E6E')))),
                  ),
                  pw.Expanded(flex: 1, child: pw.Text('${context.pageNumber}', textAlign: pw.TextAlign.right, style: pw.TextStyle(font: ralewayBold, fontSize: 13, color: PdfColor.fromHex('000'))))
                ]),
              ],
            )),
        margin: const pw.EdgeInsets.all(25),
        build: (pw.Context context) => cover2,
      ));

      /**
       * Content
       */
      pdf.addPage(pw.MultiPage(
        theme: pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
        pageFormat: PdfPageFormat.a4,
        footer: (context) => pw.Container(
            // color: PdfColors.amber,
            height: 110,
            child: pw.Column(
              mainAxisAlignment: pw.MainAxisAlignment.start,
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.SizedBox(height: 10),
                pw.Column(crossAxisAlignment: pw.CrossAxisAlignment.start, mainAxisAlignment: pw.MainAxisAlignment.start, children: [
                  pw.Text("Firma per accettazione", overflow: pw.TextOverflow.visible, style: pw.TextStyle(font: ralewayBold, fontSize: 11, color: PdfColors.black)),
                  pw.Container(
                      height: 1,
                      width: 250,
                      margin: pw.EdgeInsets.only(top: 30),
                      decoration: pw.BoxDecoration(
                        color: PdfColors.black,
                      ))
                ]),
                pw.SizedBox(height: 30),
                pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceBetween, crossAxisAlignment: pw.CrossAxisAlignment.center, children: [
                  pw.Expanded(
                    flex: 1,
                    child: pw.Image(coverLogo, height: 25),
                  ),
                  pw.Expanded(
                    flex: 5,
                    child: pw.Padding(
                        padding: pw.EdgeInsets.symmetric(horizontal: 20),
                        child: pw.Text("NEWARC SRL - Sede Legale Via Vittorio Emanuele II 29, Chieri - Sede Operativa Corso Ferrucci 36, Torino - P.Iva 12533550013 - <EMAIL> - 011.02.63.850",
                            overflow: pw.TextOverflow.visible, style: pw.TextStyle(font: ralewayMedium, fontSize: 7, color: PdfColor.fromHex('6E6E6E')))),
                  ),
                  pw.Expanded(flex: 1, child: pw.Text('${context.pageNumber}', textAlign: pw.TextAlign.right, style: pw.TextStyle(font: ralewayBold, fontSize: 13, color: PdfColor.fromHex('000'))))
                ]),
              ],
            )),
        margin: const pw.EdgeInsets.all(25),
        build: (pw.Context context) => pdfDataWidgets,
      ));

      /**
          Last page
       */

      pdf.addPage(pw.MultiPage(
        theme: pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
        pageFormat: PdfPageFormat.a4,
        footer: (context) => pw.Container(
            // color: PdfColors.amber,
            height: 110,
            child: pw.Column(
              mainAxisAlignment: pw.MainAxisAlignment.start,
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.SizedBox(height: 10),
                pw.Column(crossAxisAlignment: pw.CrossAxisAlignment.start, mainAxisAlignment: pw.MainAxisAlignment.start, children: [
                  pw.Text("Firma per accettazione", overflow: pw.TextOverflow.visible, style: pw.TextStyle(font: ralewayBold, fontSize: 11, color: PdfColors.black)),
                  pw.Container(
                      height: 1,
                      width: 250,
                      margin: pw.EdgeInsets.only(top: 30),
                      decoration: pw.BoxDecoration(
                        color: PdfColors.black,
                      ))
                ]),
                pw.SizedBox(height: 30),
                pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceBetween, crossAxisAlignment: pw.CrossAxisAlignment.center, children: [
                  pw.Expanded(
                    flex: 1,
                    child: pw.Image(coverLogo, height: 25),
                  ),
                  pw.Expanded(
                    flex: 5,
                    child: pw.Padding(
                        padding: pw.EdgeInsets.symmetric(horizontal: 20),
                        child: pw.Text("NEWARC SRL - Sede Legale Via Vittorio Emanuele II 29, Chieri - Sede Operativa Corso Ferrucci 36, Torino - P.Iva 12533550013 - <EMAIL> - 011.02.63.850",
                            overflow: pw.TextOverflow.visible, style: pw.TextStyle(font: ralewayMedium, fontSize: 7, color: PdfColor.fromHex('6E6E6E')))),
                  ),
                  pw.Expanded(flex: 1, child: pw.Text('${context.pageNumber}', textAlign: pw.TextAlign.right, style: pw.TextStyle(font: ralewayBold, fontSize: 13, color: PdfColor.fromHex('000'))))
                ]),
              ],
            )),
        margin: const pw.EdgeInsets.all(25),
        build: (pw.Context context) => pdfDataWidgets2,
      ));
      
      // Save PDF to bytes
      final pdfBytes = await pdf.save();

      await downloadPdf(pdfBytes: pdfBytes, fileName: '${quotation.code!}.pdf');
    } catch (e, s) {
      print({e, s});
    }
  }

  int isRecordExists(int pageCounter) {
    String filtersKey = controller.filters.map((f) => '${f['field']}:${f['value']}').join('|');
    return controller.cacheFirestore.indexWhere(
      (record) => record['key'] == pageCounter && record['filtersKey'] == filtersKey,
    );
  }

  generateQuotationRow(collectionSnapshot) async {
    // if (isRecordExists(pageCounter) < 0) {
    //   cacheFirestore.add({'key': pageCounter, 'snapshot': collectionSnapshot});
    // }
    if (isRecordExists(controller.pageCounter) < 0) {
      String filtersKey = controller.filters.map((f) => '${f['field']}:${f['value']}').join('|');
      controller.cacheFirestore.add({
        'key': controller.pageCounter,
        'snapshot': collectionSnapshot,
        'filtersKey': filtersKey,
      });
    }
    if (controller.pageCounter == 1) {
      controller.disablePreviousButton = true;
    } else {
      controller.disablePreviousButton = false;
    }

    if (controller.pageCounter == controller.totalPages) {
      controller.disableNextButton = true;
    } else {
      controller.disableNextButton = false;
    }

    List<Moodboard> _moodboards = [];
    for (var element in collectionSnapshot.docs) {
      try {
        var _tmp = Moodboard.fromDocument(element.data(), element.id);

        //Questo codice serve a fetchare informazioni da un altro documento associato alla moodboard, da modificare per fetchare info necessarie
        //da discutere con Ste quali
        /*DocumentSnapshot<Map<String, dynamic>> renoContactSnapshot =
            await FirebaseFirestore.instance
                .collection(appConfig.COLLECT_MOODBOARDS)
                .doc(_tmp.renovationContactId)
                .get();
        _tmp.renovationContact = Moodboard.fromDocument(
            renoContactSnapshot.data()!, renoContactSnapshot.id);*/

        _moodboards.add(_tmp);
      } catch (e, s) {
        print({e, s});
      }
    }

    int lastRecordNumber = controller.pageCounter * controller.recordsPerPage;
    if (_moodboards.length == controller.recordsPerPage) {
      controller.currentlyShowing = (lastRecordNumber - (controller.recordsPerPage - 1)).toString() + '-' + lastRecordNumber.toString();
    } else if (_moodboards.length > 0 && _moodboards.length < controller.recordsPerPage) {
      int prevLastRecordNumber = (controller.pageCounter - 1) * controller.recordsPerPage;

      controller.currentlyShowing = (lastRecordNumber - (controller.recordsPerPage - 1)).toString() + '-' + (prevLastRecordNumber + _moodboards.length).toString();
    }

    setState(() {
      controller.moodboards = _moodboards;
      controller.displayQuotations = _moodboards;
      controller.loadingMoodboards = false;
    });
  }

  Future<void> showMoodboard(Moodboard moodboard) async {
    controller.contactNameController.clear();
    controller.contactNameController.clear();
    controller.contactSurnameController.clear();
    controller.contactEmailController.clear();
    controller.contactPhoneController.clear();
    controller.streetAddresController.clear();
    controller.contCity.clear();

    controller.formProgressMessage = '';
    await showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (__context, setState) {
            return Center(
                child: BaseNewarcPopup(
                    title: moodboard.name,
                    noButton: true,
                    column: Container(
                      width: 700,
                      height: 250,
                      child: Column(
                        children: [
                          Expanded(
                            child: SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: DataTable(
                                dataRowMinHeight: 70,
                                dataRowMaxHeight: 90,
                                columns: [
                                  DataColumn(
                                      label: NarFormLabelWidget(
                                    label: 'Img',
                                    textColor: Color(0xff838383),
                                  )),
                                  DataColumn(
                                      label: NarFormLabelWidget(
                                    label: 'Materiale',
                                    textColor: Color(0xff838383),
                                  )),
                                  DataColumn(
                                      label: NarFormLabelWidget(
                                    label: 'Codice',
                                    textColor: Color(0xff838383),
                                  )),
                                  DataColumn(
                                      label: NarFormLabelWidget(
                                    label: 'Produttore',
                                    textColor: Color(0xff838383),
                                  )),
                                ],
                                rows: List<DataRow>.generate(
                                  moodboard.newarcMaterial.length,
                                  (int index) {
                                    final material = moodboard.newarcMaterial.elementAt(index);
                                    return DataRow(cells: [
                                      DataCell(
                                        ClipRRect(
                                          borderRadius: BorderRadius.circular(8),
                                          child: Container(
                                            width: 70,
                                            height: 70,
                                            color: Colors.grey,
                                            child: material.imageUrl == null ? Container() : Image.network(material.imageUrl!),
                                          ),
                                        ),
                                      ),
                                      DataCell(Text(material.materialName!)),
                                      DataCell(Text(material.code!)),
                                      DataCell(Text(material.supplier!)),
                                    ]);
                                  },
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    )));
          });
        });
  }

  showConnectToProject(BuildContext context, Moodboard moodboard) async {
    controller.formProgressMessage = '';
    return showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (context, _setState) {
            return Center(
              child: BaseNewarcPopup(
                  title: 'Collega a progetto',
                  buttonText: "Collega",
                  onPressed: () async {
                    _setState(() {
                      controller.formProgressMessage = 'Salvataggio in corso..';
                    });
                    try {
                      await updateDocument(appConfig.COLLECT_MOODBOARDS, moodboard.id!, {'newarcProjectId': controller.newarcProjectsController.text});

                      initialFetchMoodboards(force: true);
                      _setState(() {
                        controller.formProgressMessage = 'Salvataggio completato';
                      });
                    } catch (e) {
                      _setState(() {
                        controller.formProgressMessage = 'Errore durante salvataggio';
                      });
                    }
                  },
                  column: Container(
                    width: 400,
                    child: Column(
                      children: [
                        Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            NarFormLabelWidget(
                              label: "Città",
                              textColor: Color(0xff696969),
                              fontSize: 14,
                              fontWeight: '600',
                            ),
                            SizedBox(height: 4),
                            Container(
                                child: NarSelectBoxWidget(
                              options: ['Torino'],
                              controller: controller.connectProjectSelectedCityController,
                              validationType: 'required',
                              parametersValidate: 'Required!',
                            ))
                          ],
                        ),
                        SizedBox(height: 20),
                        Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            NarFormLabelWidget(
                              label: "Tipologia",
                              textColor: Color(0xff696969),
                              fontSize: 14,
                              fontWeight: '600',
                            ),
                            SizedBox(height: 4),
                            Container(
                                child: NarSelectBoxWidget(
                              options: ['Immagina', 'Subito', 'Ristrutturazione'],
                              controller: controller.connectProjectSelectedTypeController,
                              validationType: 'required',
                              parametersValidate: 'Required!',
                              onChanged: (value) {
                                _setState(
                                  () {},
                                );
                              },
                            ))
                          ],
                        ),
                        SizedBox(
                          height: 20,
                        ),
                        Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            NarFormLabelWidget(
                              label: "Seleziona progetto",
                              textColor: Color(0xff696969),
                              fontSize: 14,
                              fontWeight: '600',
                            ),
                            SizedBox(height: 4),
                            controller.connectProjectSelectedTypeController.text == ''
                                ? Container()
                                : FutureBuilder<List>(
                                    future: fetchProjects(),
                                    builder: (context, snapshot) {
                                      if (snapshot.hasData) {
                                        return Container(
                                            child: NarImageSelectBoxWidget(
                                          options: snapshot.data!,
                                          controller: controller.newarcProjectsController,
                                          validationType: 'required',
                                          parametersValidate: 'Required!',
                                          onChanged: (val) {
                                            _setState(() {});
                                          },
                                        ));
                                      } else if (snapshot.hasError) {
                                        return Container(
                                          width: 30,
                                          height: 30,
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(100),
                                            color: const Color.fromARGB(255, 19, 17, 17),
                                          ),
                                        );
                                      }

                                      return CircularProgressIndicator(
                                        color: Theme.of(context).primaryColor,
                                      );
                                    })
                          ],
                        ),
                        Row(
                          children: [NarFormLabelWidget(label: controller.formProgressMessage)],
                        )
                      ],
                    ),
                  )),
            );
          });
        });
  }

  showConnectToClient(BuildContext context, Moodboard moodboard) async {
    controller.formProgressMessage = '';
    return showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (context, _setState) {
            return Center(
              child: BaseNewarcPopup(
                  title: 'Collega cliente',
                  buttonText: "Collega",
                  onPressed: () async {
                    setState(() {
                      controller.formProgressMessage = 'Salvataggio in corso..';
                    });
                    try {
                      await updateDocument(appConfig.COLLECT_MOODBOARDS, moodboard.id!, {'clientId': controller.newarcClientsController.text});

                      initialFetchMoodboards(force: true);

                      setState(() {
                        controller.formProgressMessage = 'Salvataggio completato';
                      });
                    } catch (e) {
                      setState(() {
                        controller.formProgressMessage = 'Errore durante salvataggio';
                      });
                    }
                  },
                  column: Container(
                    width: 400,
                    child: Column(
                      children: [
                        Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            NarFormLabelWidget(
                              label: "Città",
                              textColor: Color(0xff696969),
                              fontSize: 14,
                              fontWeight: '600',
                            ),
                            SizedBox(height: 4),
                            Container(
                                child: NarSelectBoxWidget(
                              options: ['Torino'],
                              controller: controller.connectClientSelectedCityController,
                              validationType: 'required',
                              parametersValidate: 'Required!',
                              onChanged: (value) {
                                _setState(() {});
                              },
                            ))
                          ],
                        ),
                        SizedBox(
                          height: 20,
                        ),
                        Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            NarFormLabelWidget(
                              label: "Seleziona cliente",
                              textColor: Color(0xff696969),
                              fontSize: 14,
                              fontWeight: '600',
                            ),
                            SizedBox(height: 4),
                            controller.connectClientSelectedCityController.text == ''
                                ? Container()
                                : FutureBuilder<List>(
                                    future: fetchClients(),
                                    builder: (context, snapshot) {
                                      if (snapshot.hasData) {
                                        return Container(
                                            child: NarImageSelectBoxWidget(
                                          options: snapshot.data!,
                                          controller: controller.newarcClientsController,
                                          validationType: 'required',
                                          parametersValidate: 'Required!',
                                          onChanged: (val) {
                                            _setState(() {});
                                          },
                                        ));
                                      } else if (snapshot.hasError) {
                                        return Container(
                                          width: 30,
                                          height: 30,
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(100),
                                            color: const Color.fromARGB(255, 19, 17, 17),
                                          ),
                                        );
                                      }

                                      return CircularProgressIndicator(
                                        color: Theme.of(context).primaryColor,
                                      );
                                    }),
                            Text(controller.newarcClientsController.text),
                            Text(controller.selectedClientId ?? "puppa")
                          ],
                        ),
                        Row(
                          children: [NarFormLabelWidget(label: controller.formProgressMessage)],
                        )
                      ],
                    ),
                  )),
            );
          });
        });
  }

  @override
  Widget build(BuildContext context) {
    double pageWidth = MediaQuery.of(context).size.width - 260;
    if (pageWidth < 1800) {
      pageWidth = 1800;
    }

    return LayoutBuilder(builder: (context, constraints) {
      return Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              NarFormLabelWidget(
                label: 'Moodboard clienti',
                fontSize: 19,
                fontWeight: '700',
                textColor: Colors.black,
              ),
              Expanded(
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    //   Row(
                    //   children: [
                    //     Container(
                    //       margin: EdgeInsets.only(right:5),
                    //       width: 200,
                    //       child: CustomTextFormField(
                    //         label: ' ',
                    //         hintText: 'Cerca contatto',
                    //         controller: contSearchQuotation,
                    //       ),
                    //     ),
                    //
                    //     Container(
                    //       margin: EdgeInsets.only(top:3),
                    //       child: IconButton(
                    //         onPressed: (){
                    //
                    //         },
                    //         icon: Icon(Icons.search, size: 25, color: Colors.white),
                    //         splashRadius: 1,
                    //         style: ButtonStyle(
                    //           backgroundColor: WidgetStateProperty.all( Theme.of(context).primaryColor ),
                    //           shape: WidgetStateProperty.all( RoundedRectangleBorder( borderRadius: BorderRadius.all(Radius.circular(7)) ) )
                    //         ),
                    //         padding: EdgeInsets.symmetric( vertical: 10, horizontal: 10 ),
                    //       ),
                    //     ),
                    //
                    //   ],
                    // ),
                    //
                    // SizedBox(width: 20,),
                    //
                    //   Container(
                    //     width: 200,
                    //     child: Column(
                    //       mainAxisSize: MainAxisSize.max,
                    //       crossAxisAlignment: CrossAxisAlignment.start,
                    //       mainAxisAlignment: MainAxisAlignment.start,
                    //       children: [
                    //         NarFormLabelWidget(
                    //           label: "Assegnazione",
                    //           textColor: Color(0xff696969),
                    //           fontSize: 11,
                    //           fontWeight: '600',
                    //         ),
                    //         loadingRenovators
                    //             ? Container()
                    //             : NarImageSelectBoxWidget(
                    //                 options: searchRef,
                    //                 controller: contSearchRef,
                    //                 validationType: 'required',
                    //                 parametersValidate: 'Required!',
                    //                 onChanged: (val) {
                    //                   initialFetchMoodboards();
                    //                 },
                    //               ),
                    //         SizedBox(height: 10),
                    //       ],
                    //     ),
                    //   ),
                    //   SizedBox(
                    //     width: 20,
                    //   ),
                    // BaseNewarcButton(
                    //   buttonText: "Nuovo preventivo",
                    //   onPressed: () {
                    //     // showNewQuotation(context);
                    //     return true;
                    //   },
                    // )
                  ],
                ),
              )
            ],
          ),
          SizedBox(height: 10),
          Container(
            height: constraints.maxHeight / 1.2,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: AppColor.white,
              border: Border.all(width: 1.5, color: AppColor.borderColor),
            ),
            child: Column(
              children: [
                Expanded(
                  child: Stack(
                    children: [
                      Opacity(
                        opacity: controller.loadingMoodboards ? 0.5 : 1,
                        child: _dataTable(context),
                      ),
                      if (controller.loadingMoodboards)
                        Positioned.fill(
                          child: Center(
                            child: CircularProgressIndicator(
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                Align(
                  alignment: Alignment.centerRight,
                  child: dataTablePagination(),
                ),
              ],
            ),
          ),
        ],
      );
    });
  }

  NewarcDataTable _dataTable(BuildContext context) {
    return NewarcDataTable(
      rowsPerPage: 20,
      isHasDecoration: false,
      source: MoodBoardDataSource(
        onCustomerTap: (moodboard) {
          if (moodboard.clientId == "") {
            showConnectToClient(context, moodboard);
          } else {
            showDialog(
                context: context,
                builder: (BuildContext _bc1) {
                  return StatefulBuilder(builder: (BuildContext _bc2, StateSetter setState) {
                    return Center(
                        child: BaseNewarcPopup(
                      buttonText: 'Ok',
                      onPressed: () async {
                        setState(() {
                          controller.loadingMoodboards = true;
                        });
                        await updateDocument(appConfig.COLLECT_MOODBOARDS, moodboard.id!, {'clientId': ""});

                        initialFetchMoodboards(force: true);

                        return true;
                      },
                      title: "Attenzione!",
                      column: Container(
                        width: 600,
                        margin: EdgeInsets.symmetric(vertical: 30),
                        child: Center(
                          child: ListView(
                            shrinkWrap: true,
                            children: [
                              NarFormLabelWidget(
                                label: "Vuoi davvero scollegare questa moodboard dal suo cliente?",
                                fontSize: 18,
                                textAlign: TextAlign.center,
                                fontWeight: '600',
                              )
                            ],
                          ),
                        ),
                      ),
                    ));
                  });
                });
          }
        },
        onLinkProject: (moodboard) {
          if (moodboard.newarcProjectId == "") {
            showConnectToProject(context, moodboard);
          } else {
            showDialog(
                context: context,
                builder: (BuildContext _bc1) {
                  return StatefulBuilder(builder: (BuildContext _bc2, StateSetter setState) {
                    return Center(
                        child: BaseNewarcPopup(
                      buttonText: 'Ok',
                      onPressed: () async {
                        setState(() {
                          controller.loadingMoodboards = true;
                        });
                        await updateDocument(appConfig.COLLECT_MOODBOARDS, moodboard.id!, {'newarcProjectId': ""});

                        initialFetchMoodboards(force: true);

                        return true;
                      },
                      title: "Attenzione!",
                      column: Container(
                        width: 600,
                        margin: EdgeInsets.symmetric(vertical: 30),
                        child: Center(
                          child: ListView(
                            shrinkWrap: true,
                            children: [
                              NarFormLabelWidget(
                                label: "Vuoi davvero scollegare questa moodboard dal suo progetto?",
                                fontSize: 18,
                                textAlign: TextAlign.center,
                                fontWeight: '600',
                              )
                            ],
                          ),
                        ),
                      ),
                    ));
                  });
                });
          }
        },
        displayQuotations: controller.displayQuotations,
        renovationContacts: controller.renovationContacts,
        onMaterialTap: (moodboard) {
          showMoodboard(moodboard);
        },
        onDeleteTap: (moodboard) {
          List<String> formMessages = [''];

          showDialog(
              context: context,
              builder: (BuildContext _bc1) {
                return StatefulBuilder(builder: (BuildContext _bc2, StateSetter setState) {
                  return Center(
                      child: BaseNewarcPopup(
                    formErrorMessage: formMessages,
                    buttonText: 'Ok',
                    onPressed: () async {
                      await FirebaseFirestore.instance.collection(appConfig.COLLECT_MOODBOARDS).doc(moodboard.id).update({'isArchived': true});

                      /*setState(() {
                                    moodboard.isArchived = true;
                                  });*/

                      initialFetchMoodboards(force: true);

                      return true;
                    },
                    title: "Attenzione!",
                    column: Container(
                      width: 600,
                      margin: EdgeInsets.symmetric(vertical: 30),
                      child: Center(
                        child: ListView(
                          shrinkWrap: true,
                          children: [
                            NarFormLabelWidget(
                              label: "Vuoi davvero eliminare questa moodboard?",
                              fontSize: 18,
                              textAlign: TextAlign.center,
                              fontWeight: '600',
                            )
                          ],
                        ),
                      ),
                    ),
                  ));
                });
              });
        },
        context: context,
      ),
      hidePaginator: true,
      onPageChanged: (val) {
        print("page : $val");
      },
      columns: [
        DataColumn2(label: Text('Indirizzo')),
        DataColumn2(label: Text('Cliente')),
        DataColumn2(label: Text('Moodboard')),
        DataColumn2(label: Text('Data di creazione')),
        DataColumn2(label: Text('Azioni')),
      ],
    );
  }
}

class MoodBoardDataSource extends DataTableSource {
  BuildContext context;
  List<Moodboard> displayQuotations;
  List<RenovationContact> renovationContacts;
  Function(Moodboard val) onMaterialTap;
  Function(Moodboard val) onDeleteTap;
  Function(Moodboard val) onLinkProject;
  Function(Moodboard val) onCustomerTap;

  MoodBoardDataSource({
    required this.context,
    required this.displayQuotations,
    required this.renovationContacts,
    required this.onMaterialTap,
    required this.onDeleteTap,
    required this.onLinkProject,
    required this.onCustomerTap,
  });

  @override
  DataRow? getRow(int index) {
    if (index < displayQuotations.length) {
      final moodboard = displayQuotations[index];
      RenovationContact _ren = renovationContacts.firstWhere((contact) => contact.id == moodboard.clientId, orElse: () => RenovationContact.empty());
      int millisecondsSinceEpoch = moodboard.created!;
      var date = DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch).day.toString() +
          '/' +
          DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch).month.toString() +
          '/' +
          DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch).year.toString();
      return DataRow(
        cells: [
          DataCell(
            NarFormLabelWidget(
              label: moodboard.name ?? "",
              fontSize: 12,
              fontWeight: '600',
              overflow: TextOverflow.ellipsis,
              textColor: Colors.black,
            ),
          ),
          DataCell(
            NarFormLabelWidget(
              label: _ren.id == '' ? "MISSINGNO" : _ren.name ?? "",
              fontSize: 12,
              fontWeight: '600',
              overflow: TextOverflow.ellipsis,
              textColor: Colors.black,
            ),
          ),
          DataCell(
            Row(
              children: [
                NarLinkWidget(
                  text: "Materiali",
                  textColor: Colors.black,
                  fontWeight: '700',
                  fontSize: 12,
                  onClick: () {
                    onMaterialTap(moodboard);
                  },
                ),
                SizedBox(width: 8),
                moodboard.newarcProjectId != ""
                    ? Container()
                    : IconButtonWidget(
                        onTap: () {
                          onDeleteTap(moodboard);
                        },
                        iconPadding: EdgeInsets.all(4),
                        isSvgIcon: true,
                        isOnlyBorder: true,
                        backgroundColor: Colors.transparent,
                        borderColor: AppColor.redColor,
                        iconColor: AppColor.redColor,
                        icon: 'assets/icons/trash.svg',
                      ),
              ],
            ),
          ),
          DataCell(
            NarFormLabelWidget(
              label: date,
              fontSize: 12,
              fontWeight: '600',
              overflow: TextOverflow.ellipsis,
              textColor: Colors.black,
            ),
          ),
          DataCell(
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    child: Row(
                      children: [
                        Image.asset(
                          moodboard.newarcProjectId == "" ? "assets/icons/link-project.png" : "assets/icons/unlink.png",
                          color: moodboard.newarcProjectId == "" ? Theme.of(context).primaryColor : Color(0xff999999),
                          height: 10,
                        ),
                        SizedBox(width: 4),
                        NarFormLabelWidget(
                          label: moodboard.newarcProjectId == "" ? "Collega ad un progetto" : "Scollega da un progetto",
                          fontSize: 12,
                          fontWeight: '600',
                          overflow: TextOverflow.ellipsis,
                          textColor: moodboard.newarcProjectId == "" ? Theme.of(context).primaryColor : Color(0xff999999),
                        ),
                      ],
                    ),
                    onTap: () {
                      onLinkProject(moodboard);
                    },
                  ),
                ),
                SizedBox(width: 12),
                MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    child: Row(
                      children: [
                        Image.asset(
                          moodboard.clientId == "" ? "assets/icons/link-project.png" : "assets/icons/unlink.png",
                          color: moodboard.clientId == "" ? Theme.of(context).primaryColor : Color(0xff999999),
                          height: 10,
                        ),
                        SizedBox(width: 4),
                        NarFormLabelWidget(
                          label: moodboard.clientId == "" ? "Collega cliente" : "Scollega cliente",
                          fontSize: 12,
                          fontWeight: '600',
                          overflow: TextOverflow.ellipsis,
                          textColor: moodboard.clientId == "" ? Theme.of(context).primaryColor : Color(0xff999999),
                        ),
                      ],
                    ),
                    onTap: () {
                      onCustomerTap(moodboard);
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    }

    return null;
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => displayQuotations.length;

  @override
  int get selectedRowCount => 0;
}
