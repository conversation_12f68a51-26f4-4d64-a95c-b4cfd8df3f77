import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:csv/csv.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'dart:convert';
import 'package:newarc_platform/classes/projectEconomic.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/pages/work/cep_view/cep_view_controller.dart';
import 'package:newarc_platform/routes/work_routes.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/widget/UI/newarc_data_table.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/widget/UI/address_completion_google.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/widget/UI/tab/common_icon_button.dart';
import 'package:newarc_platform/widget/UI/tab/tag_widget.dart';
import 'package:newarc_platform/widget/custom_drawer.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/utils/various.dart';
import '../../../widget/UI/filter.dart';
import 'dart:js_interop';
import 'package:web/web.dart' as web;

class NewarcCEPView extends StatefulWidget {
  final bool isArchived;
  final NewarcUser? newarcUser;

  const NewarcCEPView({super.key,
    required this.isArchived,
    this.newarcUser,
  }
  );

  @override
  State<NewarcCEPView> createState() => _NewarcCEPViewState();
}

class _NewarcCEPViewState extends State<NewarcCEPView> {
  final controller = Get.put<NewarcCEPController>(NewarcCEPController());
  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    controller.clearFilter();
    initialFetch();
    super.initState();
  }

  void reloadAfterPop() {
    controller.cacheFirestore.clear();
    initialFetch();
  }

  getColor(String status) {
    switch (status) {
      case 'Da contattare':
        return Color(0xff5FBCEC);
      case 'Contattato':
        return Color(0xffFFC633);
      case 'Non interessato':
        return Color(0xffFF5E53);
      case 'Acquisito':
        return Theme.of(context).primaryColor;
    }
  }

  Widget dataTablePagination() {
    return Visibility(
      visible: controller.currentlyShowing.isNotEmpty,
      child: Padding(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: IconTheme.merge(
            data: const IconThemeData(opacity: 0.54),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "${controller.currentlyShowing} di  ${controller.totalRecords.toString()}",
                  style: TextStyle(
                    fontFamily: '',
                    fontSize: 12.0,
                    color: Colors.black.withOpacity(0.54),
                  ),
                ),
                SizedBox(width: 32.0),

                IconButton(
                  icon: const Icon(Icons.chevron_left),
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    if (controller.disablePreviousButton == true) return;
                    if (controller.loadingProperties == true) return;
                    fetchPrev();
                  },
                ),
                SizedBox(width: 24.0),
                IconButton(
                  padding: EdgeInsets.zero,
                  icon: const Icon(Icons.chevron_right),
                  onPressed: () {
                    if (controller.disableNextButton == true) return;
                    if (controller.loadingProperties == true) return;

                    fetchNext();
                  },
                ),
                SizedBox(width: 14.0),

                // TextButton(
                //   child: Icon(Icons.refresh, size: 20, color: disableNextButton ? Colors.grey : Colors.black),
                //   onPressed: () {
                //     cacheFirestore.clear();
                //     initialFetch();
                //   },
                // )
              ],
            ),
          )),
    );
  }

  Future<void> initialFetch({bool force = false,bool reloadAll = false}) async {
    if (controller.projects.isNotEmpty && !force && !reloadAll) return;

    controller.pageCounter = 1;

    setState(() {
      controller.projects = [];
      controller.loadingProperties = true;
    });

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshotCounter;
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_PROVISIONAL_ECONOMIC_ACCOUNT)
          .where('isArchived', isEqualTo: widget.isArchived)
          .orderBy('created', descending: true);

      if (controller.filters.length > 0) {
        for (var i = 0; i < controller.filters.length; i++) {
          if (controller.filters[i]['search'] == 'equal') {
            collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }
      }

      if(reloadAll){
        collectionSnapshot = await collectionSnapshotQuery.get();
      }else{
        collectionSnapshot = await collectionSnapshotQuery
            .limit(controller.recordsPerPage)
            .get();
      }

      collectionSnapshotCounter =
          await FirebaseFirestore.instance.collection(appConfig.COLLECT_PROVISIONAL_ECONOMIC_ACCOUNT).where('isArchived', isEqualTo: widget.isArchived).orderBy('created', descending: true).get();

      controller.totalRecords = collectionSnapshotCounter.docs.length;

      controller.totalPages = (controller.totalRecords / controller.recordsPerPage).ceil();

      if (controller.totalRecords > controller.recordsPerPage) {
        controller.currentlyShowing = '1-' + controller.recordsPerPage.toString();
      } else {
        controller.currentlyShowing = '1-' + controller.recordsPerPage.toString();
      }

      controller.documentList = collectionSnapshot.docs;
      await generateDataRows(collectionSnapshot);

      setState(() {
        controller.loadingProperties = false;
      });
    } catch (e) {
      setState(() {
        controller.loadingProperties = false;
      });
      print(e.toString());
    }
  }

  fetchNext() async {
    setState(() {
      controller.loadingProperties = true;
    });

    controller.pageCounter++;

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      int indexOfSnapshot = isRecordExists(controller.pageCounter);

      if (indexOfSnapshot > -1) {
        collectionSnapshot = controller.cacheFirestore[indexOfSnapshot]['snapshot'];
      } else {
        collectionSnapshot = await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_PROVISIONAL_ECONOMIC_ACCOUNT)
            .orderBy('created', descending: true)
            .startAfterDocument(controller.documentList[controller.documentList.length - 1])
            .limit(controller.recordsPerPage)
            .get();
      }

      controller.documentList = collectionSnapshot.docs;
      generateDataRows(collectionSnapshot);
    } catch (e) {
      setState(() {
        controller.loadingProperties = false;
      });
    }
  }

  fetchPrev() async {
    setState(() {
      controller.loadingProperties = true;
    });

    controller.pageCounter--;
    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      int indexOfSnapshot = isRecordExists(controller.pageCounter);
      if (indexOfSnapshot > -1) {
        collectionSnapshot = controller.cacheFirestore[indexOfSnapshot]['snapshot'];
      } else {
        collectionSnapshot = await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_PROVISIONAL_ECONOMIC_ACCOUNT)
            .orderBy('created', descending: true)
            .endBeforeDocument(controller.documentList[controller.documentList.length - 1])
            .limit(controller.recordsPerPage)
            .get();
      }

      controller.documentList = collectionSnapshot.docs;
      generateDataRows(collectionSnapshot);
    } catch (e) {
      setState(() {
        controller.loadingProperties = false;
      });
    }
  }

  int isRecordExists(int pageCounter) {
    String filtersKey = controller.filters.map((f) => '${f['field']}:${f['value']}').join('|');
    return controller.cacheFirestore.indexWhere(
      (record) => record['key'] == pageCounter && record['filtersKey'] == filtersKey,
    );
  }

  generateDataRows(QuerySnapshot<Map<String, dynamic>> collectionSnapshot) {
    // If a record already doesn't exists then store
    // if (isRecordExists(pageCounter) < 0) {
    //   cacheFirestore.add({'key': pageCounter, 'snapshot': collectionSnapshot});
    // }
    if (isRecordExists(controller.pageCounter) < 0) {
      String filtersKey = controller.filters.map((f) => '${f['field']}:${f['value']}').join('|');
      controller.cacheFirestore.add({
        'key': controller.pageCounter,
        'snapshot': collectionSnapshot,
        'filtersKey': filtersKey,
      });
    }
    if (controller.pageCounter == 1) {
      controller.disablePreviousButton = true;
    } else {
      controller.disablePreviousButton = false;
    }

    if (controller.pageCounter == controller.totalPages) {
      controller.disableNextButton = true;
    } else {
      controller.disableNextButton = false;
    }

    List<ProjectEconomic> _projects = [];
    for (var element in collectionSnapshot.docs) {
      ProjectEconomic _tmp = ProjectEconomic.fromDocument(element.data(), element.id);
      _projects.add(_tmp);
    }

    int lastRecordNumber = controller.pageCounter * controller.recordsPerPage;
    if (_projects.length == controller.recordsPerPage) {
      controller.currentlyShowing = (lastRecordNumber - (controller.recordsPerPage - 1)).toString() + '-' + lastRecordNumber.toString();
    } else if (_projects.length > 0 && _projects.length < controller.recordsPerPage) {
      int prevLastRecordNumber = (controller.pageCounter - 1) * controller.recordsPerPage;

      controller.currentlyShowing = (lastRecordNumber - (controller.recordsPerPage - 1)).toString() + '-' + (prevLastRecordNumber + _projects.length).toString();
    }

    setState(() {
      controller.projects = _projects;
      controller.loadingProperties = false;
    });
  }

  projectArchivePopup(ProjectEconomic economic) {
    return BaseNewarcPopup(
      formErrorMessage: controller.formMessages,
      buttonText: 'Ok',
      onPressed: () async {
        setState(() {
          controller.formMessages.clear();
          controller.formMessages.add('Termina in corso');
        });

        final FirebaseFirestore _db = FirebaseFirestore.instance;

        economic.isArchived = true;
        await _db.collection(appConfig.COLLECT_PROVISIONAL_ECONOMIC_ACCOUNT).doc(economic.firebaseId).update(economic.toMap());

        initialFetch();

        setState(() {
          controller.formMessages.clear();
          // formMessages.add('Deleted!');
        });

        return true;
      },
      title: "Attenzione!",
      column: Container(
        width: 600,
        margin: EdgeInsets.symmetric(vertical: 30),
        child: Center(
          child: ListView(
            shrinkWrap: true,
            children: [
              NarFormLabelWidget(
                label: "Vuoi davvero eliminare questo C.E.P.?",
                fontSize: 18,
                textAlign: TextAlign.center,
                fontWeight: '600',
              )
            ],
          ),
        ),
      ),
    );
  }

  projectDisabledDeletePopup(ProjectEconomic economic) {
    return BaseNewarcPopup(
      formErrorMessage: controller.formMessages,
      buttonText: 'Ok',
      onPressed: () async {
        return true;
      },
      title: "Attenzione!",
      column: Container(
        width: 600,
        margin: EdgeInsets.symmetric(vertical: 30),
        child: Center(
          child: ListView(
            shrinkWrap: true,
            children: [
              NarFormLabelWidget(
                label: "Non puoi eliminare questo conto economico \nperchè è stato trasformato in progetto.",
                fontSize: 18,
                textAlign: TextAlign.center,
                fontWeight: '600',
              )
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    double pageWidth = MediaQuery.of(context).size.width - 260;

    return Scaffold(
      backgroundColor: Colors.transparent,
      key: scaffoldKey,
      drawer: CustomDrawer(),
      body: LayoutBuilder(builder: (BuildContext context, BoxConstraints constraints) {
        return SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  NarFormLabelWidget(
                    label: 'Conti economici previsionali',
                    fontSize: 19,
                    fontWeight: '700',
                    textColor: Colors.black,
                  ),
                  _create(context),
                ],
              ),
              _filter(),
              SizedBox(height: 10),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: AppColor.white,
                  border: Border.all(width: 1.5, color: AppColor.borderColor),
                ),
                height: constraints.maxHeight / 1.2,
                child: Column(
                  children: [
                    Expanded(
                      child: Stack(
                        children: [
                          Opacity(
                            opacity: controller.loadingProperties ? 0.5 : 1,
                            child: NewarcDataTable(
                              rowsPerPage: 20,
                              hidePaginator: true,
                              onPageChanged: (val) {
                                print("page : $val");
                              },
                              columns: [
                                DataColumn2(
                                  label: Text(
                                    'Indirizzo',
                                  ),
                                ),
                                DataColumn2(
                                  label: Text(
                                    'Data di creazione',
                                  ),
                                ),
                                DataColumn2(
                                  label: Text(
                                    'Azioni',
                                  ),
                                ),
                              ],
                              isHasDecoration: false,
                              minWidth: pageWidth,
                              source: CEPViewDataSource(
                                onAddressTap: (economic) {
                                  context.go(WorkRoutes.workCEprevisionaliInside(economic.firebaseId!));
                                },
                                onTrashTap: (economic) {
                                  if (economic.isAssigned == true) {
                                    showDialog(
                                        context: context,
                                        builder: (BuildContext _bc1) {
                                          return StatefulBuilder(builder: (BuildContext _bc2, StateSetter setState) {
                                            return Center(
                                              child: projectDisabledDeletePopup(economic),
                                            );
                                          });
                                        });
                                  } else {
                                    showDialog(
                                        context: context,
                                        builder: (BuildContext _bc1) {
                                          return StatefulBuilder(builder: (BuildContext _bc2, StateSetter setState) {
                                            return Center(
                                              child: projectArchivePopup(economic),
                                            );
                                          });
                                        });
                                  }
                                },
                                onDownloadTap: (economic) {
                                  List<List<dynamic>> rows = [];

                                  // Adding headers to the CSV
                                  rows.add(["Category", "Subcategory", "Description", "Value"]);

                                  Map tmpEconomic = economic.toMap();

                                  // Iterating through the map to generate rows
                                  controller.costBreakdown.forEach((category, subcategories) {
                                    subcategories.forEach((subcategory, items) {
                                      if (tmpEconomic[subcategory].length > 0) {
                                        for (var item in items) {
                                          List rowData = [
                                            convertFromCamelCase(item[0]),
                                            double.tryParse(tmpEconomic[subcategory][item[0]]),
                                          ];

                                          rows.add([category, convertFromCamelCase(subcategory), ...rowData]);
                                        }
                                      }
                                    });
                                  });

                                  // Converting rows to CSV string
                                  String csv = const ListToCsvConverter().convert(rows);

                                  // Encoding the CSV string to bytes
                                  final bytes = utf8.encode(csv);

                                  // Convert Uint8List -> JSUint8Array -> BlobPart[]
                                  final blobParts = <JSAny>[bytes.toJS].toJS;

                                  // Create Blob
                                  final blob = web.Blob(blobParts, web.BlobPropertyBag(type: 'text/csv'));

                                  // Create Object URL
                                  final url = web.URL.createObjectURL(blob);

                                  // Create anchor for download
                                  final anchor = web.HTMLAnchorElement()
                                    ..href = url
                                    ..download = "cost_breakdown.csv";

                                  anchor.click();

                                  // Clean up
                                  web.URL.revokeObjectURL(url);
                                },
                                onEditTap: (val) {
                                  context.go(WorkRoutes.workCEprevisionaliInside(val.firebaseId!));
                                },
                                context: context,
                                projects: controller.projects,
                              ),
                            ),
                            // child: DataTable2(
                            //   dataRowHeight: loadingProperties ? 300 : 50,
                            //   minWidth: constraints.maxWidth,
                            //   columns: getColumns(constraints),
                            //   // columnSpacing: 20,
                            //   empty: NarFormLabelWidget(
                            //     label: 'Nessun record trovato!',
                            //     fontWeight: '800',
                            //     fontSize: 15,
                            //     textColor: Colors.black,
                            //   ),
                            //   rows: List.generate(projects.length, (int index) {
                            //     return DataRow(
                            //       cells: getDataRow(
                            //         projects.elementAt(index),
                            //       ),
                            //     );
                            //   }),
                            // ),
                          ),
                          if (controller.loadingProperties)
                            Positioned.fill(
                              child: Center(
                                child: CircularProgressIndicator(
                                  color: Theme.of(context).primaryColor,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                    Align(
                      alignment: Alignment.centerRight,
                      child: dataTablePagination(),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  MouseRegion _create(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () async {
          controller.cepAddressInfo = BaseAddressInfo.empty();
          return await showDialog(
            context: context,
            barrierDismissible: false,
            builder: (BuildContext context) {
              return StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
                return Center(
                  child: BaseNewarcPopup(
                    formErrorMessage: controller.formMessages,
                    buttonText: 'Crea C.E.P.',
                    onPressed: () async {
                      setState(() {
                        controller.formMessages.clear();
                        controller.formMessages.add('Creazione in corso');
                      });

                      if (!controller.cepAddressInfo.isValidAddress()) {
                        setState(() {
                          controller.formMessages.clear();
                          controller.formMessages.add('Inserisci un indirizzo valido');
                        });
                        return false;
                      }

                      final FirebaseFirestore _db = FirebaseFirestore.instance;
                      ProjectEconomic projectEconomic = new ProjectEconomic({
                        'address': "${controller.cepAddressInfo.toShortAddress()}",
                        'city': controller.cepAddressInfo.city,
                        'addressInfo': controller.cepAddressInfo.toMap(),
                        'created': DateTime.now().millisecondsSinceEpoch,
                        'isArchived': false,
                        'creatorUserId': widget.newarcUser!.id
                      });

                      DocumentReference<Map<String, dynamic>> response = await _db.collection(appConfig.COLLECT_PROVISIONAL_ECONOMIC_ACCOUNT).add(projectEconomic.toMap());

                      setState(() {
                        controller.formMessages.clear();
                        controller.formMessages.add('CEP created!');
                      });

                      if (response.id != '') {
                        context.go(WorkRoutes.workCEprevisionaliInside(response.id));
                        return true;
                      }
                      return false;
                    },
                    title: "Crea C.E.P.",
                    column: Container(
                      width: 400,
                      margin: EdgeInsets.symmetric(vertical: 30),
                      child: AddressSearchBar(
                        label: "Inserisci Indirizzo", 
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Inserisci indirizzo completo di numero civico';
                          }
                          return null;
                        },
                        onPlaceSelected: (selectedPlace){
                          debugPrint('Selected place: \n$selectedPlace');
                          BaseAddressInfo selectedAddress = BaseAddressInfo.fromMap(selectedPlace["place"]);
                          if (selectedAddress.isValidAddress()){
                            controller.cepAddressInfo = selectedAddress;
                          } else {
                            controller.cepAddressInfo = BaseAddressInfo.empty();
                          }
                        }
                      ),
                    ),
                  ),
                );
              });
            }
          );
        },
        child: Container(
          height: 32,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20.0),
            child: Text(
              "Crea C.E.P.",
              style: TextStyle(
                color: Colors.white,
                fontSize: 13,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }

  NarFilter _filter() {
    return NarFilter(
      showSearchInput: true,
      searchHintText: "Cerca per indirizzo...",
      onChanged: (String? searchQuery)async{
        if(searchQuery?.isNotEmpty ?? false){
          if (searchQuery != null && searchQuery.isNotEmpty) {
            List<ProjectEconomic> filtered = controller.projects.where((project) {
              final address = project.addressInfo;
              final city = address?.city?.toLowerCase() ?? project.city?.toLowerCase() ?? "";
              final fullAddress = address?.fullAddress?.toLowerCase() ?? project.address?.toLowerCase() ?? "";
              return
                fullAddress.contains(searchQuery.toLowerCase()) ||
                city.contains(searchQuery.toLowerCase());
            }).toList();

            setState(() {
              controller.projects = filtered;
            });
          }
        }else{
          await initialFetch(force: true);
        }
      },
      suffixIconOnTap: ()async{
        await initialFetch(force: true,reloadAll: true);
        if(controller.searchTextController.text.trim().isNotEmpty){
          List<ProjectEconomic> filtered = controller.projects.where((project) {
            final address = project.addressInfo;
            final city = address?.city?.toLowerCase() ?? project.city?.toLowerCase() ?? "";
            final fullAddress = address?.fullAddress?.toLowerCase() ?? project.address?.toLowerCase() ?? "";
            return
              fullAddress.contains(controller.searchTextController.text.toLowerCase()) ||
                  city.contains(controller.searchTextController.text.toLowerCase());
          }).toList();

          setState(() {
            controller.projects = filtered;
          });
        }else{
          await initialFetch(force: true);
        }
      },
      searchTextEditingControllers: controller.searchTextController,
      selectedFilters: [controller.progettoSelectedFilter],
      textEditingControllers: [controller.progettoFilterTextController],
      filterFields: [
        {
          'Progetto': NarSelectBoxWidget(
            options:  ["Progetto", "Non progetto"],
            onChanged: (value) {
              controller.progettoSelectedFilter = controller.progettoFilterTextController.text;

              if(value == "Progetto"){
                controller.filters = [
                  {
                    'field': 'isAssigned',
                    'value': true,
                    'search': 'equal',
                  }
                ];
              }
              if(value == "Non progetto"){
                controller.filters = [
                  {
                    'field': 'isAssigned',
                    'value': false,
                    'search': 'equal',
                  }
                ];
              }


              setState(() {});
            },
            controller: controller.progettoFilterTextController,
          ),
        },
      ],
      onSubmit: () async {
        await initialFetch(force: true);
      },
      onReset: () async {
        controller.clearFilter();
        await initialFetch(force: true);
      },
    );
  }
}

class CEPViewDataSource extends DataTableSource {
  List<ProjectEconomic> projects;
  BuildContext context;
  Function(ProjectEconomic p) onEditTap;
  Function(ProjectEconomic p) onDownloadTap;
  Function(ProjectEconomic p) onTrashTap;
  Function(ProjectEconomic p) onAddressTap;

  CEPViewDataSource({
    required this.projects,
    required this.context,
    required this.onEditTap,
    required this.onDownloadTap,
    required this.onTrashTap,
    required this.onAddressTap,
  });

  @override
  DataRow? getRow(int index) {
    if (index < projects.length) {
      final economic = projects[index];

      String createdDate = '';
      if (economic.created! > 0) {
        DateTime commentedOn = DateTime.fromMillisecondsSinceEpoch(economic.created!);
        createdDate = (commentedOn.day > 9 ? commentedOn.day.toString() : '0' + commentedOn.day.toString()) +
            '/' +
            (commentedOn.month > 9 ? commentedOn.month.toString() : '0' + commentedOn.month.toString()) +
            '/' +
            commentedOn.year.toString();
      }
      var address = economic.address ?? "";
      if (economic.addressInfo != null) {
        address = economic.addressInfo!.toShortAddress();
      }
      return DataRow(
        cells: [
          DataCell(
            Stack(
              clipBehavior: Clip.none,
              children: [
                if (economic.isAssigned == true)
                  Positioned(
                    top: -23,
                    child: TagWidget(
                      statusColor: Theme.of(context).primaryColor,
                      text: "Progetto",
                    ),
                  ),
                NarLinkWidget(
                  text: address,
                  textColor: Colors.black,
                  fontWeight: '700',
                  fontSize: 12,
                  onClick: () {
                    onAddressTap(economic);
                  },
                ),
              ],
            ),
          ),
          DataCell(
            NarFormLabelWidget(
              label:createdDate,
              fontSize: 12,
              fontWeight: '600',
              overflow: TextOverflow.ellipsis,
              textColor: Colors.black,
            ),

          ),
          DataCell(
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButtonWidget(
                  onTap: () {
                    onEditTap(economic);
                  },
                  iconPadding: EdgeInsets.all(8),
                  isSvgIcon: false,
                  icon: 'assets/icons/edit.png',
                  iconColor: AppColor.greyColor,
                ),
                SizedBox(width: 4),
                IconButtonWidget(
                  iconPadding: EdgeInsets.all(8),
                  onTap: () {
                    onDownloadTap(economic);
                  },
                  isSvgIcon: false,
                  icon: 'assets/icons/download.png',
                  iconColor: AppColor.greyColor,
                ),
                SizedBox(width: 4),
                IconButtonWidget(
                  iconPadding: EdgeInsets.all(6),
                  onTap: () {
                    onTrashTap(economic);
                  },
                  isSvgIcon: false,
                  icon: 'assets/icons/trash-process.png',
                  iconColor: AppColor.redColor,
                ),
              ],
            ),
          ),
        ],
      );
    }

    return null;
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => projects.length;

  @override
  int get selectedRowCount => 0;
}
