import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/acquiredContact.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

class ContactPopup extends StatefulWidget {
  const ContactPopup({
    Key? key,
    required this.acquiredContact,
  }) : super(key: key);

  final AcquiredContact acquiredContact;

  @override
  State<ContactPopup> createState() => _ContactPopupState();
}

class _ContactPopupState extends State<ContactPopup> {
  TextStyle sectionTitleStyle =
      TextStyle(fontSize: 16, fontWeight: FontWeight.bold);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 400,
      child: Column(
        children: [
          NarFormLabelWidget(
            label:
                "${widget.acquiredContact.address} ${widget.acquiredContact.streetNumber}",
            fontSize: 15,
            fontWeight: '800',
            textColor: Color.fromRGBO(113, 113, 113, 1),
          ),
          Si<PERSON><PERSON><PERSON>(height: 20),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisSize: MainAxisSize.max,
                children: [
                  CustomTextFormField(
                    initialValue: widget.acquiredContact.contactFullName,
                    label: "Nome e cognome",
                    labelColor: Color.fromRGBO(149, 149, 149, 1),
                    isPercentage: false,
                    isMoney: false,
                    isNullable: true,
                  ),
                ],
              ),
            ],
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisSize: MainAxisSize.max,
                children: [
                  CustomTextFormField(
                    initialValue: widget.acquiredContact.contactEmail,
                    label: "E-mail",
                    labelColor: Color.fromRGBO(149, 149, 149, 1),
                    isPercentage: false,
                    isMoney: false,
                    isNullable: true,
                  ),
                ],
              )
            ],
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisSize: MainAxisSize.max,
                children: [
                  CustomTextFormField(
                    initialValue: widget.acquiredContact.contactPhone,
                    label: "Telefono",
                    labelColor: Color.fromRGBO(149, 149, 149, 1),
                    isPercentage: false,
                    isMoney: false,
                    isNullable: true,
                  ),
                ],
              ),
            ],
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisSize: MainAxisSize.max,
                children: [
                  CustomTextFormField(
                    initialValue: widget.acquiredContact.userAgeRange,
                    label: "Fascia d'età",
                    labelColor: Color.fromRGBO(149, 149, 149, 1),
                    isPercentage: false,
                    isMoney: false,
                    isNullable: true,
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}
