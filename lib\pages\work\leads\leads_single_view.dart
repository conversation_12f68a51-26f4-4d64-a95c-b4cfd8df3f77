import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:newarc_platform/classes/acquiredContact.dart';
import 'package:newarc_platform/classes/lead.dart';
import 'package:newarc_platform/classes/renovationContact.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/pages/login_page.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/pages/work/leads/tabs/account_information.dart';
import 'package:newarc_platform/pages/work/leads/tabs/quotation.dart';
import 'package:newarc_platform/pages/work/leads/tabs/valuation_submission.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/tab/tag_widget.dart';
import 'package:newarc_platform/widget/agency/immobile_popup.dart';

class LeadsSingleView extends StatefulWidget {
  final String id;
  LeadsSingleView({
    Key? key,
    required this.id,
  }) : super(key: key);

  @override
  State<LeadsSingleView> createState() => _LeadsSingleViewState();
}

class _LeadsSingleViewState extends State<LeadsSingleView>
    with WidgetsBindingObserver {
  
  late Timer _timer;
  DateTime _lastUserInteraction = DateTime.now();
  Duration _inactivityDuration = Duration(minutes: 10);

  bool loading = false;
  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  Widget? tabContent;
  List<Map> dataList = [];
  final List<Map> userList = [];
  int tmpCurrentIndex = 0;
  final List<bool> isInputChangeDetected = [false];
  NewarcUser? currentLead;
  RenovationContact? currentContact = new RenovationContact.empty();
  AcquiredContact? acquiredContact = new AcquiredContact.empty();
  String userType = '';

  String origin = 'diretto';
  Color tagColor = Colors.black;
  Widget? originTag;

      
  @override
  void initState() {

    super.initState();
    loading = true;

    WidgetsBinding.instance.addObserver(this);
    // currentLead = widget.projectArguments!['lead'];

    WidgetsBinding.instance.addPostFrameCallback((_){
      initialFetch();
      // _startInactivityTimer();
    });

    /*DocumentReference sourceReference = currentLead!.sourceReference!;
    
    if( sourceReference.path.contains('renovationContacts') ) {
      userType = 'renovationContact';
      
    } else if( sourceReference.path.contains('valuatorSubmissions') ) {
      userType = 'valuatorSubmissions';
    } else if( sourceReference.path.contains('wpformsSubmissions') ) {
      userType = 'wpformsSubmissions';
    } else if( sourceReference.path.contains('users') ) {
      userType = 'users';
    }*/




    
  }

  @override
  void didUpdateWidget(oldWidget) {
    super.didUpdateWidget(oldWidget);

  }

  updateContact(NewarcUser updatedData) {
    currentLead = updatedData;
    setState(() {
      
    });
  }

  void _handleUserInteraction() {
    _lastUserInteraction = DateTime.now();
  }

  void _startInactivityTimer() {
    _timer = Timer.periodic(Duration(seconds: 1), (_) {
      final now = DateTime.now();
      final inactivityDuration = now.difference(_lastUserInteraction);
      if (inactivityDuration >= _inactivityDuration) {
        _logoutUser();
      }
    });
  }

  void _logoutUser() {
    context.go("/work/login");
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _timer.cancel();
    super.dispose();
  }

  initialFetch() async {
    
    /*DocumentReference sourceReference = currentLead!.sourceReference!;
    if( userType == 'renovationContact' ) {
      DocumentSnapshot referenceData = await sourceReference.get();
      final data = referenceData.data() as Map<String, dynamic>;
      currentContact = RenovationContact.fromDocument( data, referenceData.id);
    } else if( userType == 'valuatorSubmissions' ) {
      DocumentSnapshot referenceData = await sourceReference.get();
      final data = referenceData.data() as Map<String, dynamic>;
      acquiredContact = AcquiredContact.fromDocument( data, referenceData.id);
    }*/
    DocumentSnapshot<Map<String, dynamic>> doc = await FirebaseFirestore.instance.collection(appConfig.COLLECT_USERS).doc(widget.id).get();

    currentLead = NewarcUser.fromDocument(doc.data()!, doc.id);

    String origin = 'diretto';
    Color tagColor = Colors.black;

    if( currentLead!.type == 'agency' ) {
      origin = 'agenzia';
      tagColor = Color(0xff5DB1E3);

    } else if( currentLead!.type == 'wordpress' || currentLead!.type == 'valuation' || currentLead!.type == 'web' ) {
      origin = 'web';
      tagColor = Color(0xff499B79);
    }

    originTag = TagWidget(
      text: origin,
      borderRadius: 10,
      statusColor: tagColor,
      textColor: Colors.white,
    );

    setTabContent();

    setState(() {
      loading = false;
    });
  }

  String loadingMessage = 'Caricamento progetto';

  unselectAllTabs() {
    for (var i = 0; i < dataList.length; i++) {
      dataList[i]['isExpanded'] = false;
      for (var j = 0; j < dataList[i]['subMenu'].length; j++) {
        dataList[i]['subMenu'][j]['selected'] = false;
      }
    }
  }

  setTabContent() async {
   

    

    dataList = [
      {
        "hasTitle": false,
        "name": "",
        "isExpanded": true,
        "subMenu": [
          {"name": 'Dati account', "selected": true, "modal": LeadAccountInformation(lead: currentLead, updateContact: updateContact) }
        ]
      },
      {
        "hasTitle": false,
        "name": "",
        "isExpanded": true,
        "subMenu": [
          {"name": 'Preventivi', "selected": false, "modal": RQQuotation(user: currentLead, updateContact: updateContact)}
        ]
      },
      {
        "hasTitle": false,
        "name": "",
        "isExpanded": true,
        "subMenu": [
          {"name": 'Ristrutturazioni', "selected": false, "modal": null}
        ]
      },
      {
        "hasTitle": false,
        "name": "",
        "isExpanded": true,
        "subMenu": [
          {"name": 'Richieste di visita', "selected": false, "modal": null}
        ]
      },
      {
        "hasTitle": false,
        "name": "",
        "isExpanded": true,
        "subMenu": [
          {"name": 'Valutazioni', "selected": false, "modal": LeadValuationSubmission( acquiredContact: acquiredContact! )}
        ]
      },
      {
        "hasTitle": false,
        "name": "",
        "isExpanded": true,
        "subMenu": [
          {"name": 'Configurazioni', "selected": false, "modal": null}
        ]
      },
      {
        "hasTitle": false,
        "name": "",
        "isExpanded": true,
        "subMenu": [
          {"name": 'Moodboard', "selected": false, "modal": null}
        ]
      },
      {
        "hasTitle": false,
        "name": "",
        "isExpanded": true,
        "subMenu": [
          {"name": 'Newarc Ping!', "selected": false, "modal": null}
        ]
      },
      {
        "hasTitle": false,
        "name": "",
        "isExpanded": true,
        "subMenu": [
          {"name": 'Segnala e Guadagna', "selected": false, "modal": null}
        ]
      },
      /* {
        "hasTitle": true,
        "name": "GENERALI",
        "isExpanded": true,
        "subMenu": [
          {
            'name': 'Dashboard',
            'selected': true,
            "modal": ProjectDashboard(
                project: newarcProject, updateProject: updateProject)
          },
          {
            'name': 'Immobile',
            'selected': false,
            "modal": ProjectFixedAssetDetails(
                project: newarcProject,
                isInputChangeDetected: isInputChangeDetected)
          },
          {
            'name': 'Team Newarc',
            'selected': false,
            "modal": ProjectAssignTeam(
                project: newarcProject,
                userList: userList,
                isInputChangeDetected: isInputChangeDetected)
          },
        ]
      },
       */
    ];

    for (var i = 0; i < dataList.length; i++) {
      for (var j = 0; j < dataList[i]['subMenu'].length; j++) {
        if (dataList[i]['subMenu'][j]['selected'] == true) {
          // setState(() {
          if (dataList[i]['subMenu'][j]['modal'] != null) {
            tabContent = dataList[i]['subMenu'][j]['modal'];
          } else {
            tabContent = Container(
                height: double.infinity,
                child: Center(
                    child: NarFormLabelWidget(label: 'Nothing to display')));
          }
          // });
        }
      }
    }
  }

  switchTab(currentIndex, tabData, i) {
    setState(() {
      tmpCurrentIndex = currentIndex;
      isInputChangeDetected[0] = false;

      if (tabData['subMenu'][i]['modal'] != null) {
        tabContent = tabData['subMenu'][i]['modal'];
      } else {
        tabContent = Container(
            height: double.infinity,
            child:
                Center(child: NarFormLabelWidget(label: 'Nothing to display')));
      }
    });
  }

  int activeTabIndex = 0;
  int activeItemIndex = 0;

  _buildExpandableContent(
      BuildContext _buildContext, tabData, int currentIndex) {
    List<Widget> columnContent = [];

    for (var i = 0; i < tabData['subMenu'].length; i++) {
      // print({ vehicle['subMenu'][i] });

      columnContent.add(
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () {
              unselectAllTabs();
              tabData['subMenu'][i]['selected'] = true;

              activeTabIndex = currentIndex;
              activeItemIndex = i;

              if (isInputChangeDetected[0] == true) {
                showDialog(
                    context: _buildContext,
                    builder: (_context) {
                      return StatefulBuilder(
                          builder: (BuildContext _bc2, StateSetter setState) {
                        return Center(
                          child: BaseNewarcPopup(
                            title: 'Attenzione!',
                            buttonText: 'Esci senza salvare',
                            onPressed: () async {
                              switchTab(currentIndex, tabData, i);
                              // Navigator.pop(_bc2);
                              return true;
                            },
                            column: Container(
                                height: 150,
                                width: 465,
                                child: Center(
                                  child: Column(
                                    children: [
                                      NarFormLabelWidget(
                                          overflow: TextOverflow.visible,
                                          label:
                                              "Stai uscendo dalla pagina senza aver\nsalvato le modifiche.",
                                          textAlign: TextAlign.center,
                                          fontSize: 18,
                                          fontWeight: '600',
                                          height: 1.5,
                                          textColor: Color(0xFF696969)),
                                      SizedBox(
                                        height: 10,
                                      ),
                                      NarFormLabelWidget(
                                          overflow: TextOverflow.visible,
                                          label: "Vuoi uscire senza salvare?",
                                          textAlign: TextAlign.center,
                                          fontSize: 18,
                                          fontWeight: 'bold',
                                          height: 1.5,
                                          textColor: Color(0xFF696969)),
                                    ],
                                  ),
                                )),
                          ),
                        );
                      });
                    });
              } else {
                switchTab(currentIndex, tabData, i);
              }
              // tmpCurrentIndex = currentIndex;
            },
            child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: tabData['subMenu'][i]['selected']
                      ? Color.fromRGBO(72, 155, 121, 1)
                      : Color.fromRGBO(240, 240, 240, 1),
                ),
                width: double.infinity,
                padding: EdgeInsets.symmetric(vertical: 15, horizontal: 10),
                margin: EdgeInsets.only(bottom: 8),
                child: NarFormLabelWidget(
                  label: tabData['subMenu'][i]['name'],
                  fontSize: 13,
                  fontWeight: 'bold',
                  textColor: tabData['subMenu'][i]['selected']
                      ? Colors.white
                      : Colors.black,
                )),
          ),
        ),
      );
    }

    return columnContent;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: _handleUserInteraction,
      onPanDown: (_) => _handleUserInteraction(),
      onPanUpdate: (_) => _handleUserInteraction(),
      child: Listener(
        onPointerDown: (_) => _handleUserInteraction(),
        onPointerMove: (_) => _handleUserInteraction(),
        onPointerUp: (_) => _handleUserInteraction(),
        onPointerHover: (_) => _handleUserInteraction(),
        child: Scaffold(
          backgroundColor: Colors.white,
          key: scaffoldKey,
          body: LayoutBuilder(
              builder: (BuildContext context, BoxConstraints constraints) {
            return loading
                ? Center(
                    child: Column(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(
                        color: Theme.of(context).primaryColor,
                      ),
                      SizedBox(height: 5),
                      NarFormLabelWidget(label: loadingMessage)
                    ],
                  ))
                : Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  IconButton(
                                    hoverColor: Colors.transparent,
                                    focusColor: Colors.transparent,
                                    onPressed: () {
                                      context.pop();
                                    },
                                    icon: SvgPicture.asset(
                                        'assets/icons/arrow_left.svg',
                                        height: 20,
                                        color: Colors.black),
                                  ),
                                  SizedBox(
                                    width: 10,
                                  ),
                                  Column(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          NarFormLabelWidget(
                                            label: '${currentLead!.toFullName()}',
                                            fontSize: 20,
                                            fontWeight: 'bold',
                                            textColor: Colors.black,
                                          ),
                                          SizedBox(width: 10,),
                                          originTag!
                                        ],
                                      ),
                                      
                                    ],
                                  )
                                ]),
                            SizedBox(height: 20),
                            Expanded(
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    width: 275,
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 10, vertical: 20),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(13),
                                      border: Border.all(
                                          color: Color(0xffe7e7e7)),
                                    ),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Expanded(
                                            child: ListView.builder(
                                                itemCount: dataList.length,
                                                itemBuilder:
                                                    (context, index) {
                                                  return Theme(
                                                    data: ThemeData().copyWith(
                                                        dividerColor: Colors
                                                            .transparent),
                                                    child: dataList[index]['hasTitle'] == true
                                                        ? ExpansionTile(
                                                            initiallyExpanded:
                                                                dataList[index][ 'isExpanded'],
                                                            trailing:
                                                                Image.asset(
                                                              dataList[index][ 'isExpanded']
                                                                  ? 'assets/icons/arrow_up.png'
                                                                  : 'assets/icons/arrow_down.png',
                                                              width: 12,
                                                              color: Color(
                                                                  0xff838383),
                                                            ),
                                                            onExpansionChanged:
                                                                (value) {
                                                              setState(() {
                                                                dataList[index]['isExpanded'] = value;
                                                              });
                                                            },
                                                            tilePadding:
                                                                EdgeInsets.only(
                                                                    left: 5),
                                                            title:
                                                                NarFormLabelWidget(
                                                              label: dataList[index]['name'].toString().toUpperCase(),
                                                              fontSize: 13,
                                                              textColor: Color
                                                                  .fromRGBO(
                                                                      117,
                                                                      117,
                                                                      117,
                                                                      1),
                                                            ),
                                                            children: <Widget>[
                                                                Column(
                                                                  children: _buildExpandableContent(
                                                                      context,
                                                                      dataList[index],
                                                                      index),
                                                                ),
                                                              ])
                                                        : Column(
                                                            children:
                                                                _buildExpandableContent(
                                                                    context,
                                                                    dataList[index],
                                                                    index)),
                                                  );
                                                })),
                                      ],
                                    ),
                                  ),
                                  SizedBox(width: 10),
                                  Expanded(
                                    child: Container(
                                      padding: EdgeInsets.symmetric( horizontal: 15, vertical: 10),
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius:
                                            BorderRadius.circular(13),
                                        border: Border.all(color: Color(0xffe7e7e7)),
                                      ),
                                      child: tabContent,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
          }),
        ),
      ),
    );
  }
}
