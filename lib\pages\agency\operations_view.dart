import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/operation.dart';
import 'package:newarc_platform/widget/agency/add_edit_operation_popup.dart';
import 'package:newarc_platform/widget/agency/newarc_operation_entry.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;

class OperationsView extends StatefulWidget {
  const OperationsView(
      {Key? key,
      required this.agency,
      required this.agencyUser,
      required this.responsive})
      : super(key: key);

  final bool responsive;
  final Agency agency;
  final AgencyUser agencyUser;

  @override
  State<OperationsView> createState() => OperationsViewState();
}

class OperationsViewState extends State<OperationsView> {
  bool loading = true;
  List<Operation> operations = [];

  Future<void> fetchOperations() async {
    QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
    if (widget.agencyUser.role != 'master') {
      collectionSnapshot = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_NEWARC_OPERATIONS)
          .where('assignedAgencyId', isEqualTo: widget.agency.id)
          .get();
    } else {
      collectionSnapshot = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_NEWARC_OPERATIONS)
          .get();
    }

    List<Operation> _operations = [];
    for (var element in collectionSnapshot.docs) {
      try {
        //var _tmp = Operation.fromDocument(element.data(), element.id);
        var _tmp = element;
        _operations.add(Operation.fromJson(element.data(), element.id));
      } catch (e) {
        print("error in document ${element.id}");
        print(e);
      }
    }
    //_operations.addAll(List.generate(25, (_) => Operation.mock()));
    setState(() {
      operations = _operations;
      loading = false;
    });
  }

  Future<bool> showAddEditOperationPopup(
      Operation operation, bool isEdit) async {
    var result = await showGeneralDialog(
      context: context,
      barrierLabel: "Barrier",
      barrierDismissible: true,
      barrierColor: Colors.black.withOpacity(0.5),
      pageBuilder: (_, __, ___) {
        return Center(
          child: AddEditOperationPopup(
              agency: widget.agency,
              agencyUser: widget.agencyUser,
              operation: operation,
              isEdit: isEdit,
              refetch: refetch),
        );
      },
    );
    if (result != null && result == true) {
      return true;
    }
    return false;
  }

  refetch() async {
    setState(() {
      loading = true;
    });
    print("refetching");
    await fetchOperations();
  }

  @override
  void initState() {
    fetchOperations();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return loading
        ? Center(
            child: CircularProgressIndicator(
              color: Theme.of(context).primaryColor,
            ),
          )
        : Column(
            children: [
              Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Operazioni Newarc',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 22),
                  ),
                  widget.agencyUser.role == "master"
                      ? MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () async {
                              bool popupResult =
                                  await showAddEditOperationPopup(
                                      Operation.empty(), false);

                              if (popupResult) {
                                refetch();
                              }
                            },
                            child: Container(
                              height: 40,
                              width: 180,
                              decoration: BoxDecoration(
                                color: Theme.of(context).primaryColor,
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 8.0),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      "Aggiungi operazione",
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Icon(
                                      Icons.add,
                                      color: Colors.white,
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ),
                        )
                      : Container(),
                ],
              ),
              SizedBox(height: 20),
              Expanded(
                child: operations.isEmpty
                    ? Text(
                        "Non sono ancora presenti operazioni visualizzabili",
                        style: TextStyle(
                            fontSize: 18, fontWeight: FontWeight.bold),
                      )
                    : Container(
                        constraints: BoxConstraints(maxWidth: 1000),
                        child: ListView(
                          children: List<NewarcOperationEntry>.generate(
                            operations.length,
                            (int index) => NewarcOperationEntry(
                              operation: operations.elementAt(index),
                              agencyUser: widget.agencyUser,
                              showAddEditOperationPopup:
                                  showAddEditOperationPopup,
                            ),
                          ),
                        ),
                      ),
              ),
            ],
          );
  }
}
