import 'dart:convert';
import 'dart:js_interop';
import 'dart:typed_data';
import 'package:csv/csv.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/classes/process.dart';
import 'package:newarc_platform/classes/projectEconomic.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:intl/intl.dart';
import 'package:web/web.dart' as web;

class ProjectManageEconomicAccount extends StatefulWidget {
  final NewarcProject? project;
  final List<bool>? isInputChangeDetected;

  const ProjectManageEconomicAccount({Key? key, this.project, this.isInputChangeDetected})
      : super(key: key);

  @override
  State<ProjectManageEconomicAccount> createState() =>
      _ProjectManageEconomicAccountState();
}

class _ProjectManageEconomicAccountState
    extends State<ProjectManageEconomicAccount> {
  NumberFormat localCurrencyFormat =
      NumberFormat.currency(locale: 'it_IT', symbol: '\€', decimalDigits: 2);
  NumberFormat localCurrencyFormatMain =
      NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 2);

  bool isAccount = true;
  bool loading = false;

  String progressMessage = '';
  double cost = 0;
  double revenue = 0;

  Map<String, Map<String, List<List>>> costBreakdown = {
    'Entrate': {},
    'Uscite': {}
  };
  List<String> activities = [
    'Demolizioni/Costruzioni',
    'Elettricista',
    'Idraulico',
    'Palchettista',
    'Piastrellista',
    'Decoratore'
  ];

  Map breakdownLabels = {
    "revenueExpected": 'venditaImmobile',
    "acquisitionCosts": 'costiDiAcquisizione',
    "agencyCost": 'costiDiAgenzia',
    "restorationAndMaterail": 'ristrutturazioneEMateriali',
    "variableCost": "costiVari"
  };

  Map<String, dynamic> existingPEA = {};
  ProjectEconomic? tempEconomic;

  @override
  void initState() {
    super.initState();

    double realRevenue = 0;

    if (widget.project!.economicAccount!['realRevenue'] != null) {
      realRevenue = widget.project!.economicAccount!['realRevenue']!;
    }
    costBreakdown["Entrate"]!["revenueExpected"] = [
      ["vendita", realRevenue, 0],
    ];

    costBreakdown["Uscite"]!["acquisitionCosts"] = [
      [
        "acquisto",
        widget.project!.fixedProperty!.purchaseCost != null
            ? widget.project!.fixedProperty!.purchaseCost
            : 0,
        0
      ],
      [
        "costiNotarili",
        widget.project!.fixedProperty!.notaryCost != null
            ? widget.project!.fixedProperty!.notaryCost
            : 0,
        0
      ],
      [
        "impostaDiRegistro",
        widget.project!.fixedProperty!.registrationTax != null
            ? widget.project!.fixedProperty!.registrationTax
            : 0,
        0
      ]
    ];

    costBreakdown["Uscite"]!["agencyCost"] = [
      [
        "commissioneIn",
        widget.project!.assignedAgency!['commissionIn'] != null
            ? double.parse(widget.project!.assignedAgency!['commissionIn'])
            : 0,
        0
      ],
      [
        "commissioneOut",
        widget.project!.assignedAgency!['commissionOut'] != null
            ? double.parse(widget.project!.assignedAgency!['commissionOut'])
            : 0,
        0
      ],
      [
        "bonusObiettivo",
        widget.project!.assignedAgency!['bonusObi'] != null
            ? double.parse(widget.project!.assignedAgency!['bonusObi'])
            : 0,
        0
      ],
      [
        "bonusInsieme",
        widget.project!.assignedAgency!['bonusIns'] != null
            ? double.parse(widget.project!.assignedAgency!['bonusIns'])
            : 0,
        0
      ],
    ];

    List<Process> vendorAndProfessionals =
        widget.project!.vendorAndProfessionals!;
    double elettricistaCost = 0;
    double materialCost = 0;

    costBreakdown["Uscite"]!["restorationAndMaterail"] = [];

    if (widget.project!.newarcMaterial!.length > 0) {
      for (var i = 0; i < widget.project!.newarcMaterial!.length; i++) {
        materialCost += widget.project!.newarcMaterial![i].cost as double;
      }
    }

    costBreakdown["Uscite"]!["restorationAndMaterail"]!
        .add(["materiali", 0, null, false]);
    costBreakdown["Uscite"]!["restorationAndMaterail"]!
        .add(["lavoriRistrutturazione", 0, null, false]);
    
    costBreakdown["Uscite"]!["restorationAndMaterail"]!
        .add(["ristrutturazioneEMateriali", materialCost, null]);
    

    try {
      for (var i = 0; i < activities.length; i++) {
        double _cost = 0;
        _cost = vendorAndProfessionals
            .firstWhere((process) => process.activity == activities[i],
                orElse: Process.empty)
            .installments!
            .fold<double>(
                _cost, (_cost, installment) => _cost + installment.amount!);

        if (_cost > 0) {
          costBreakdown["Uscite"]!["restorationAndMaterail"]!
              .add([activities[i], _cost, null]);
        }
      }
    } catch (e, s) {
      print({e, s});
    }

    costBreakdown["Uscite"]!["variableCost"] = [
      [
        "ipotesiCostiVai",
        0,
        null,
        false
      ],
      [
        "APE",
        widget.project!.fixedProperty!.ape != null
            ? widget.project!.fixedProperty!.ape
            : 0,
        null
      ],
      [
        "Spese condominiali",
        widget.project!.fixedProperty!.condominiumFees != null
            ? widget.project!.fixedProperty!.condominiumFees
            : 0,
        null
      ],
      [
        "Carico/scarico impianti",
        widget.project!.fixedProperty!.loadUnloadInstallation != null
            ? widget.project!.fixedProperty!.loadUnloadInstallation
            : 0,
        null
      ],
      [
        "Elettricista condominiale",
        widget.project!.fixedProperty!.electricalCond != null
            ? widget.project!.fixedProperty!.electricalCond
            : 0,
        null
      ],
      [
        "Luce",
        widget.project!.fixedProperty!.light != null
            ? widget.project!.fixedProperty!.light
            : 0,
        null
      ],
      [
        "Spostamento contatore",
        widget.project!.fixedProperty!.moveGasCont != null
            ? widget.project!.fixedProperty!.moveGasCont
            : 0,
        null
      ],
      [
        "Riscaldamento",
        widget.project!.fixedProperty!.heating != null
            ? widget.project!.fixedProperty!.heating
            : 0,
        null
      ],
    ];

    // Iterating through the map to generate rows and calculate costs and revenue
    setCostAndRevenue();

    if (widget.project!.provisionalAccountId != '') {
      fetchProvisional();
      loading = true;
    }
    // set up cost, revenue
  }

  fetchProvisional() async {
    if (widget.project!.provisionalAccountId == '') {
      return;
    }

    setState(() {
      loading = true;
    });

    DocumentSnapshot<Map<String, dynamic>> collectionSnapshot =
        await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_PROVISIONAL_ECONOMIC_ACCOUNT)
            .doc(widget.project!.provisionalAccountId)
            .get();

    tempEconomic = ProjectEconomic.empty();

    if (collectionSnapshot.exists) {
      tempEconomic = ProjectEconomic.fromDocument(
          collectionSnapshot.data()!, collectionSnapshot.id);
    }

    existingPEA = tempEconomic!.toMap();

    costBreakdown.keys.forEach((key) {
      if (costBreakdown.containsKey(key)) {
        costBreakdown[key]!.forEach((category, items) {
          for (var i = 0; i < items.length; i++) {
            String title = costBreakdown[key]![category]![i][0];

            if (existingPEA.length > 0 && existingPEA[category] != null && existingPEA[category].length > 0) {
              if (existingPEA[category][title] != null && existingPEA[category][title] != '') {
                costBreakdown[key]![category]![i][2] = double.tryParse(existingPEA[category][title]);
              }
            }
          }
        });
      }
    });

    setState(() {
      loading = false;
    });
  }

  void setCostAndRevenue() {
    double _cost = 0;
    double _revenue = 0;
    costBreakdown.forEach((category, subcategories) {
      subcategories.forEach((subcategory, items) {
        for (var item in items) {
          // Summing amounts for "Uscite" and "Entrate"
          if (category == "Uscite") {
            _cost += (item[1] is num) ? item[1] : 0;
          } else if (category == "Entrate") {
            _revenue += (item[1] is num) ? item[1] : 0;
          }
        }
      });
    });

    setState(() {
      cost = _cost;
      revenue = _revenue;
      
    });
  }

  Color getCostBoxColor(double amount) {
    if (revenue - cost > 0) {
      return Color(0xff4E9A7A);
    } else {
      return Color(0xffE82525);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.max,
        children: [
          Expanded(
            child: ListView(
              children: [
                SizedBox(height: 20),
                Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    NarFormLabelWidget(
                      label: 'Conto Economico',
                      fontSize: 20,
                      fontWeight: 'bold',
                    ),
                    BaseNewarcButton(
                      onPressed: () {
                        List<List<dynamic>> rows = [];

                        // Adding headers to the CSV
                        rows.add([
                          "Category",
                          "Subcategory",
                          "Description",
                          "Amount",
                          "Value"
                        ]);

                        // Iterating through the map to generate rows
                        costBreakdown.forEach((category, subcategories) {
                          subcategories.forEach((subcategory, items) {
                            for (var item in items) {
                              item[0] = convertFromCamelCase(item[0]);
                              item[2] = item[2] == null ? 0 : item[2];
                              rows.add([
                                category,
                                convertFromCamelCase(subcategory),
                                ...item
                              ]);
                            }
                          });
                        });

                        // Converting rows to CSV string
                        String csv = const ListToCsvConverter().convert(rows);

                        final bytes = utf8.encode(csv);

                        final uint8 = Uint8List.fromList(bytes).toJS;

                        final blob = web.Blob(
                          [uint8].toJS,
                          web.BlobPropertyBag(type: 'text/csv'),
                        );

                        // Create Object URL
                        final url = web.URL.createObjectURL(blob);

                        // Create and click anchor
                        web.HTMLAnchorElement()
                          ..href = url
                          ..download = "cost_breakdown.csv"
                          ..click();

                        // Clean up
                        web.URL.revokeObjectURL(url);
                      },
                      buttonText: 'Scarica csv',
                      notAccent: true,
                    )
                  ],
                ),
                SizedBox(height: 30),
                Container(
                  child: ListView(
                    //should be a column
                    shrinkWrap: true,
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: EdgeInsets.symmetric(
                                vertical: 7, horizontal: 15),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                width: 1,
                                color: Color.fromRGBO(214, 214, 214, 1),
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                NarFormLabelWidget(
                                    label: 'Costi',
                                    fontSize: 12,
                                    fontWeight: '600',
                                    textColor: Colors.black),
                                SizedBox(
                                  height: 10,
                                ),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    NarFormLabelWidget(
                                        label: localCurrencyFormatMain
                                            .format(cost),
                                        fontSize: 25,
                                        fontWeight: 'bold',
                                        textColor: Colors.black),
                                    SizedBox(
                                      width: 20,
                                    ),
                                    NarFormLabelWidget(
                                        label: '€',
                                        fontSize: 17,
                                        fontWeight: 'bold',
                                        textColor: Colors.black),
                                  ],
                                )
                              ],
                            ),
                          ),
                          SizedBox(
                            width: 25,
                          ),
                          Container(
                            padding: EdgeInsets.symmetric(
                                vertical: 7, horizontal: 15),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                width: 1,
                                color: Color.fromRGBO(214, 214, 214, 1),
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                NarFormLabelWidget(
                                  label: 'Ricavi',
                                  fontSize: 12,
                                  fontWeight: '600',
                                  textColor: Colors.black,
                                ),
                                SizedBox(
                                  height: 10,
                                ),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    NarFormLabelWidget(
                                      label: localCurrencyFormatMain
                                          .format(revenue),
                                      fontSize: 25,
                                      fontWeight: 'bold',
                                      textColor: Colors.black,
                                    ),
                                    SizedBox(
                                      width: 20,
                                    ),
                                    NarFormLabelWidget(
                                      label: '€',
                                      fontSize: 17,
                                      fontWeight: 'bold',
                                      textColor: Colors.black,
                                    ),
                                  ],
                                )
                              ],
                            ),
                          ),
                          SizedBox(
                            width: 25,
                          ),
                          Container(
                            padding: EdgeInsets.symmetric(
                                vertical: 7, horizontal: 15),
                            decoration: BoxDecoration(
                              color: getCostBoxColor(revenue - cost),
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                width: 1,
                                color: getCostBoxColor(revenue - cost),
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                NarFormLabelWidget(
                                  label: 'Guadagno',
                                  fontSize: 12,
                                  fontWeight: '600',
                                  textColor: Colors.white,
                                ),
                                SizedBox(
                                  height: 10,
                                ),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    NarFormLabelWidget(
                                      label: localCurrencyFormatMain
                                          .format(revenue - cost),
                                      fontSize: 25,
                                      fontWeight: 'bold',
                                      textColor: Colors.white,
                                    ),
                                    SizedBox(
                                      width: 20,
                                    ),
                                    NarFormLabelWidget(
                                      label: '€',
                                      fontSize: 17,
                                      fontWeight: 'bold',
                                      textColor: Colors.white,
                                    ),
                                  ],
                                )
                              ],
                            ),
                          )
                        ],
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(height: 50),
                          NarFormLabelWidget(
                            label: 'ENTRATE',
                            fontSize: 18,
                          ),
                          SizedBox(height: 10),
                        ],
                      ),
                      loading == true
                          ? Center(child: NarFormLabelWidget(label: 'Loading'))
                          : Column(
                              children: costBreakdown['Entrate']!
                                  .keys
                                  .map((paymentCategory) {
                              return processWrapper(context, paymentCategory,
                                  costBreakdown['Entrate']![paymentCategory]!,
                                  isEntrata: true);
                            }).toList()),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(height: 50),
                          NarFormLabelWidget(
                            label: 'USCITE',
                            fontSize: 18,
                          ),
                          SizedBox(height: 10),
                        ],
                      ),
                      loading == true
                          ? Center(child: NarFormLabelWidget(label: 'Loading'))
                          : Column(
                              children: costBreakdown['Uscite']!
                                  .keys
                                  .map((paymentCategory) {
                              return processWrapper(context, paymentCategory,
                                  costBreakdown['Uscite']![paymentCategory]!);
                            }).toList()),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          NarFormLabelWidget(label: progressMessage),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              BaseNewarcButton(
                                buttonText: "Salva",
                                onPressed: () async {
                                  setState(() {
                                    progressMessage = 'Salvataggio in corso...';
                                  });

                                  try {
                                    widget.project!.economicAccount = {
                                      'realRevenue': costBreakdown['Entrate']![
                                          'revenueExpected']![0][1]
                                    };

                                    final FirebaseFirestore _db =
                                        FirebaseFirestore.instance;

                                    await _db
                                        .collection(
                                            appConfig.COLLECT_NEWARC_PROJECTS)
                                        .doc(widget.project!.id)
                                        .update(widget.project!.toMap());

                                    setState(() {
                                      widget.isInputChangeDetected![0] = false;
                                      progressMessage = 'Saved!';
                                    });
                                  } catch (e, s) {
                                    print({e, s});
                                    setState(() {
                                      progressMessage = 'Error';
                                    });
                                  }
                                },
                              )
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget processWrapper(
      BuildContext context, String paymentCategory, List<List> paymentList,
      {bool isEntrata = false}) {
    // contStatus.text =

    double initialValue = 0;
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
              color: Color(0xfff9f9f9),
              borderRadius: BorderRadius.circular(14),
              border: Border.all(color: Color(0xffe7e7e7))),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.only(
                    top: 10, bottom: 20, right: 20, left: 20),
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(right: 20.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                              flex: 2,
                              child: NarFormLabelWidget(
                                label: breakdownLabels[paymentCategory] != null
                                    ? convertFromCamelCase(
                                        breakdownLabels[paymentCategory])
                                    : convertFromCamelCase(paymentCategory),
                                fontSize: 18,
                              )),
                          Expanded(
                            flex: 1,
                            child: getTotalWidget(
                                'Tot. Reale',
                                double.tryParse(paymentList
                                    .fold<double>(
                                        initialValue,
                                        (previousElement, element) =>
                                            previousElement + element[1])
                                    .toString())!,
                                checkColor: true,
                                comparingAmount: double.tryParse(paymentList
                                    .fold<double>(
                                        initialValue,
                                        (previousElement, element) =>
                                            previousElement +
                                            (element[2] != null
                                                ? element[2]
                                                : 0))
                                    .toString())!,
                                flipColor: isEntrata),
                          ),
                          SizedBox(
                            width: 10,
                          ),
                          Expanded(
                              flex: 1,
                              child: paymentList[0][2] == null
                                  ? Container()
                                  : getTotalWidget(
                                      'Tot. Previs.',
                                      double.tryParse(paymentList
                                          .fold<double>(
                                              initialValue,
                                              (previousElement, element) =>
                                                  previousElement +
                                                  (element[2] != null
                                                      ? element[2]
                                                      : 0))
                                          .toStringAsFixed(2))!,
                                    ))
                        ],
                      ),
                    ),
                    SizedBox(height: 10),
                    Container(
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(color: Color(0xffe7e7e7))),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20.0, vertical: 20),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                    flex: 2,
                                    child: NarFormLabelWidget(
                                        label: isEntrata
                                            ? "Tipo di entrata"
                                            : "Tipo di costo")),
                                Expanded(
                                    flex: 1,
                                    child: NarFormLabelWidget(
                                        label: isEntrata
                                            ? "Entrata reale"
                                            : "Costo reale")),
                                Expanded(
                                    flex: 1,
                                    child: paymentList[0][2] == null ||
                                            paymentList[0][0] ==
                                                'ristrutturazioneEMateriali' || paymentList[0][0] ==
                                                'ipotesiCostiVai'
                                        ? Container()
                                        : NarFormLabelWidget(
                                            label: isEntrata
                                                ? "Entrata previsionale"
                                                : "Costo previsionale"))
                              ],
                            ),
                            Column(
                              children: paymentList
                                  .map(
                                    (payment) { 
                                      
                                      if( payment.length == 4 && payment[3] != null && payment[3] == false )
                                        return SizedBox(height: 0,);
                                      return Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Expanded(
                                            flex: 2,
                                            child: NarFormLabelWidget(
                                              label: payment[0] ==
                                                      'ristrutturazioneEMateriali'
                                                  ? 'Materiali'
                                                  : convertFromCamelCase(
                                                      payment[0]),
                                              textColor: Color(0xff454545),
                                              fontWeight: '500',
                                            )),
                                        CustomTextFormField(
                                          key: Key(payment[0]),
                                          label: "",
                                          hintText: "",
                                          initialValue: payment[1].toString(),
                                          suffixIcon: Column(
                                            mainAxisSize: MainAxisSize.max,
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  NarFormLabelWidget(
                                                    label: '€',
                                                    fontSize: 14,
                                                    textColor: Colors.black,
                                                  ),
                                                  SizedBox(
                                                    width: 5,
                                                  )
                                                ],
                                              ),
                                            ],
                                          ),
                                          //controller: revenueController,
                                          enabled: payment[0] == 'vendita',
                                          fillColor: payment[0] == 'vendita'
                                              ? Color(0xffffffff)
                                              : Color(0xffeaeaea),
                                          onChangedCallback:
                                              (String newValue) async {
                                            if (payment[0] != 'vendita') {
                                              return;
                                            }



                                            if (newValue == '') {
                                              costBreakdown['Entrate']!['revenueExpected']![0][1] = 0;
                                            } else {
                                              costBreakdown['Entrate']!['revenueExpected']![0][1] = double.parse(newValue);
                                            }

                                            widget.isInputChangeDetected![0] = true;

                                            setCostAndRevenue();
                                          },
                                        ),
                                        SizedBox(
                                          width: 10,
                                        ),
                                        payment[2] == null ||
                                                payment[0] ==
                                                    'ristrutturazioneEMateriali'
                                            ? Expanded(
                                                child: SizedBox(height: 0))
                                            : CustomTextFormField(
                                                key: Key(payment[0] + 'dis'),
                                                label: "",
                                                hintText: "",
                                                fillColor: Color(0xffeaeaea),
                                                initialValue:
                                                    payment[2].toString(),
                                                //controller: contPrice,
                                                suffixIcon: Column(
                                                  mainAxisSize:
                                                      MainAxisSize.max,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    Row(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      children: [
                                                        NarFormLabelWidget(
                                                          label: '€',
                                                          fontSize: 14,
                                                          textColor:
                                                              Color.fromRGBO(
                                                                  123,
                                                                  123,
                                                                  123,
                                                                  1),
                                                        ),
                                                        SizedBox(
                                                          width: 5,
                                                        )
                                                      ],
                                                    ),
                                                  ],
                                                ),
                                                enabled: false,
                                              )
                                      ],
                                    );

                                    }
                                  )
                                  .toList(),
                            )
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 8)
      ],
    );
  }

  Widget getTotalWidget(String title, double amount,
      {bool? checkColor, double? comparingAmount, bool flipColor = false}) {
    //Color _spendColor = Colors.red; //supera previsionale
    //verde se inferiore o uguale
    //se è zero deve essere grigio
    Color _spendColor = Color(0xffEAEAEA);
    Color _fontColor = Colors.black;

    if (checkColor != null &&
        checkColor &&
        comparingAmount != null &&
        amount != 0) {
      _fontColor = Colors.white;
      if (amount > comparingAmount) {
        _spendColor = flipColor ? Theme.of(context).primaryColor : Colors.red;
      } else {
        _spendColor = flipColor ? Colors.red : Theme.of(context).primaryColor;
      }
    }
    _spendColor = checkColor != null ? _spendColor : Color(0xffEAEAEA);
    return Container(
      //width: 150,
      height: 50,
      padding: EdgeInsets.symmetric(vertical: 7, horizontal: 15),
      decoration: BoxDecoration(
        color: checkColor != null ? _spendColor : Color(0xffEAEAEA),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.max,
        children: [
          NarFormLabelWidget(
            overflow: TextOverflow.clip,
            label: title.toUpperCase(),
            fontSize: 9,
            fontWeight: 'bold',
            textColor: _fontColor,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.max,
            children: [
              SizedBox(width: 8),
              NarFormLabelWidget(
                  label: localCurrencyFormatMain.format(amount),
                  fontSize: 15,
                  fontWeight: 'bold',
                  textColor: _fontColor),
              SizedBox(
                width: 10,
              ),
              NarFormLabelWidget(
                label: '€',
                fontSize: 15,
                fontWeight: 'bold',
                textColor: _fontColor,
              ),
            ],
          )
        ],
      ),
    );
  }
}
