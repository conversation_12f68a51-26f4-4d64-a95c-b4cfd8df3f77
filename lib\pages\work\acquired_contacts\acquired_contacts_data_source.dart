import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:newarc_platform/classes/acquiredContact.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/firestore.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/widget/UI/tab/common_icon_button.dart';
import 'package:newarc_platform/widget/UI/tab/status_widget.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/widget/agency/agency_comment_popup.dart';
import 'package:newarc_platform/widget/agency/assegna_immobile_popup.dart';
import 'package:newarc_platform/widget/agency/contact_popup.dart';
import 'package:newarc_platform/widget/agency/immobile_popup.dart';

class AcquiredContactsDataSource extends DataTableSource {
  List<AcquiredContact> displayContacts;
  BuildContext context;
  Function()? initialFetchContacts;
  var selectedStatus;
  String query;
  NewarcUser? newarcUser;

  AcquiredContactsDataSource({
    required this.displayContacts,
    required this.context,
    this.initialFetchContacts,
    this.selectedStatus,
    this.newarcUser,
    required this.query,
  });

  @override
  DataRow? getRow(int index) {
    if (index < displayContacts.length) {
      final contact = displayContacts[index];
      var address = contact.address! + " " + contact.streetNumber + "," + contact.city;

      int millisecondsSinceEpoch = contact.insertionTimestamp!;
      var date = DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch).day.toString() +
          '/' +
          DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch).month.toString() +
          '/' +
          DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch).year.toString();

      String? tipo;
      if (contact.newarcType != null) {
        tipo = contact.newarcType;
      } else {
        tipo = contact.valutaType ?? contact.vendiType;
      }
      if (tipo == 'nok' || tipo == 'agency') {
        tipo = 'Agenzia'; //vendi
      } else if (tipo == 'ok' || tipo == 'subito/insieme') {
        tipo = 'Subito/Insieme'; //vendi
      } else if (tipo == 'curiosity') {
        tipo = 'Curiosità'; //valuta
      } else if (tipo == 'compra' || tipo == 'buy') {
        tipo = 'Compra'; //valuta
      }

      String professione = contact.sellerProfession ?? 'Privato';
      if (professione == 'Agente immobiliare') {
        professione = 'Agente';
      }
      return DataRow(
        cells: [
          DataCell(
            NarLinkWidget(
              text: address,
              textColor: Colors.black,
              fontWeight: '700',
              overflow: TextOverflow.ellipsis,
              fontSize: 12,
              onClick: () {
                showHousePopup(contact);
              },
            ),
          ),
          DataCell(
            NarFormLabelWidget(
              label: date,
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),
          ),
          DataCell(
            NarFormLabelWidget(
              label: tipo ?? "",
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),
          ),
          DataCell(
            NarFormLabelWidget(
              label: professione,
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),
          ),
          DataCell(
            IconButtonWidget(
              onTap: () {
                showContactPopup(contact);
              },
              isSvgIcon: true,
              icon: 'assets/icons/account.svg',
              iconColor: AppColor.greyColor,
            ),
          ),
          DataCell(
            StatusWidget(
              statusColor: contact.wantsAgencyValuation == false ? Colors.grey : Colors.green[700],
            ),
          ),
          DataCell(
            TextButton(
              child: Row(
                children: [
                  Container(
                    height: 30,
                    width: 30,
                    child: SvgPicture.asset('assets/icons/plane.svg', height: 20, color: contact.assignedAgencyId == null ? Color(0xff5b5b5b) : Colors.white),
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: contact.assignedAgencyId == null
                          ? Colors.transparent
                          : contact.dateUnlocked != 'Not unlocked'
                          ? Color.fromRGBO(72, 155, 121, 1)
                          : Color.fromRGBO(147, 147, 147, 1),
                      borderRadius: BorderRadius.circular(7.0),
                    ),
                  ),
                ],
              ),
              style: ButtonStyle(overlayColor: WidgetStateProperty.all(Colors.transparent)),
              onPressed: () {
                showAgencyStatusPopup(contact);
              },
            ),
          ),
          // DataCell(
          //   contact.assignedAgencyId == null
          //       ? Container()
          //       : CustomDropdownButton(
          //           selectedValue: contact.contactStage,
          //           items: [],
          //           showCircle: true,
          //           hintText: 'Select Status',
          //           containerColor: Colors.green,
          //           onChanged: (value) {},
          //         ),
          // ),
          DataCell(
            contact.assignedAgencyId == null
                ? Container()
                : StatusWidget(
              status: contact.contactStage,
              statusColor: getColor(contact.contactStage ?? ""),
            ),
          ),
          DataCell(
            Stack(
              children: [
                Align(
                  alignment: AlignmentDirectional.centerStart,
                  child: TextButton(
                    child: Container(
                      height: 30,
                      width: 30,
                      padding: EdgeInsets.all(4),
                      child: SvgPicture.asset('assets/icons/comment.svg', height: 12, color: Color(0xff5b5b5b)),
                      decoration: BoxDecoration(
                        color: Color.fromRGBO(231, 231, 231, 1),
                        borderRadius: BorderRadius.circular(7.0),
                      ),
                    ),
                    onPressed: () {
                      showCommentPopup(contact);
                    },
                    style: ButtonStyle(overlayColor: WidgetStateProperty.all(Colors.transparent)),
                  ),
                ),
                Positioned(
                  bottom: 15,
                  left: 35,
                  child: Container(
                    height: 15,
                    width: 15,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(15.0), border: Border.all(color: Color.fromRGBO(197, 197, 197, 1), width: 1)),
                    child: StreamBuilder(
                      stream: FirebaseFirestore.instance.collection(appConfig.COLLECT_VALUATOR_SUBMISSION_COMMENTS).where('contactId', isEqualTo: contact.firebaseId).snapshots(),
                      builder: (BuildContext context, AsyncSnapshot<QuerySnapshot> snapshot) {
                        if (!snapshot.hasData) {
                          return Center(
                            child: Container(),
                          );
                        }

                        return NarFormLabelWidget(
                          label: snapshot.data!.docs.length.toString(),
                          fontWeight: 'bold',
                          fontSize: 10,
                          height: 1.3,
                          textColor: Colors.black,
                          textAlign: TextAlign.center,
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    }

    return null;
  }

  getColor(String status) {
    switch (status) {
      case 'Da sbloccare':
        return Color.fromRGBO(166, 166, 166, 1);
      case 'Da contattare':
        return Color.fromRGBO(245, 198, 32, 1);
      case 'Contattato':
        return Color.fromRGBO(86, 195, 229, 1);
      case 'Completato':
        return Color.fromRGBO(72, 155, 121, 1);
      default:
        return Colors.transparent;
    }
  }

  void showCommentPopup(AcquiredContact acquiredContact) {
    showGeneralDialog(
      context: context,
      barrierLabel: "Barrier",
      barrierDismissible: false,
      barrierColor: Colors.black.withOpacity(0.5),
      pageBuilder: (_, __, ___) {
        return Center(
          child: AgencyCommentPopup(acquiredContact: acquiredContact, newarcUser: newarcUser),
        );
      },
    );
  }

  void showAgencyStatusPopup(AcquiredContact acquiredContact) {
    showGeneralDialog(
      context: context,
      barrierLabel: "Barrier",
      barrierDismissible: true,
      barrierColor: Colors.black.withOpacity(0.5),
      useRootNavigator: true,
      pageBuilder: (_, __, ___) {
        return Center(
          child: AssegnaImmobilePopup(
            acquiredContact: acquiredContact,
            initialFetchContacts: initialFetchContacts,
          ),
        );
      },
    );
  }

  void showContactPopup(AcquiredContact acquiredContact) {
    showGeneralDialog(
      context: context,
      barrierLabel: "Barrier",
      barrierDismissible: false,
      barrierColor: Colors.black.withOpacity(0.5),
      pageBuilder: (_, __, ___) {
        return Center(
          child: BaseNewarcPopup(
            title: "Info Contatto",
            column: ContactPopup(acquiredContact: acquiredContact),
          ),
        );
      },
    );
  }

  void showHousePopup(AcquiredContact acquiredContact) {
    showGeneralDialog(
      context: context,
      barrierLabel: "Barrier",
      barrierDismissible: true,
      barrierColor: Colors.black.withOpacity(0.5),
      pageBuilder: (_, __, ___) {
        return Center(
            child: ImmobilePopup(
              acquiredContact: acquiredContact,
            ));
      },
    );
  }

  bool filterFunction(AcquiredContact contact) {
    bool _show = true;

    if (!contact.contactFullName!.toLowerCase().contains(query) &&
        !(contact.address! + " " + contact.streetNumber + "," + contact.city).toLowerCase().contains(query) &&
        !contact.contactEmail!.toLowerCase().contains(query)) {
      _show = false;
    }

    if (_show && selectedStatus['keyword'] != "Filtra per stato") {
      if (contact.contactStage! != selectedStatus['keyword']) {
        _show = false;
      }
    }

    return _show;
  }

  updateContactStatus(AcquiredContact acquiredContact, String stage) async {
    acquiredContact.contactStage = stage;
    // print(acquiredContact.contactStage);
    // print(acquiredContact.firebaseId!);
    await updateDocument(appConfig.COLLECT_VALUATOR_SUBMISSIONS, acquiredContact.firebaseId!, acquiredContact.toMap());
    // setState(() {
    displayContacts.where(filterFunction).elementAt(displayContacts.where(filterFunction).toList().indexOf(acquiredContact)).contactStage = stage;
    // });
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => displayContacts.length;

  @override
  int get selectedRowCount => 0;
}
