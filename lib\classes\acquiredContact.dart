// Te<PERSON>raneo, da spostare in classe adeguata
// import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/basePersonInfo.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/classes/lead.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/utils/various.dart';
// import 'package:cloud_firestore/cloud_firestore.dart';

class AcquiredContact {
  AcquiredContact(
      {this.contactFullName,
      this.contactEmail,
      this.contactPhone,
      this.address});

  String? firebaseId;
  String? userId;
  //address object
  String? address = "unknown";
  String postalCode = "unknown";
  String streetNumber = "unknown";
  String city = "unknown";
  String latitude = "unknown";
  String longitude = "unknown";
  String? contactStage;
  //additionalInfo
  String? contactFullName;
  String? contactPhone;
  String? contactEmail;
  bool? isPropertyOfSubmitter;
  String? desiredSellTime;
  bool? wantsAgencyValuation;
  String? currentAvailability;
  String? explicitConversionSource;
  String? submitterIsLookingForAHouse;
  String? whyAskEvaluation;
  String? whatKindOfProperty;
  String? sellerProfession;
  String? newarcType;
  String? requestType; //valuta or compra DEPRECATO
  String? valutaType; // comprare o curiosita DEPRECATO
  String? vendiType; // subito /insieme o agenzie DEPRECATO
  String? userAgeRange;

  //planimetry
  String? bathrooms;
  String? energyClass;
  dynamic floor;
  dynamic locali;
  dynamic surface;
  bool? hasGarage;
  int? garageCount;
  bool? hasBalcony;
  bool? hasCantina;
  bool? hasCloset;
  bool? hasConcierge;
  bool? hasTerrace;
  int? numBalcony;
  String? heating;
  bool? hasSharedGarden;
  dynamic yearBuilt;
  dynamic termovalvole;
  bool? knowExposition;
  List<dynamic>? exposition;
  bool? hasElevator;

  String? typology;

  String? status;
  double prezzoAcquistoPrevisto = 0;
  double prezzoMqPrevisto = 0;

  int? insertionTimestamp;
  bool? unlocked;
  dynamic dateUnlocked;
  String? assignedAgencyId;
  int? assignedAgencyTimestamp;

  bool? display;

  Agency? agencyUser;

  Map<String, Object?> toJson() {
    return {
      'contactFullName': contactFullName,
      'contactEmail': contactEmail,
      'contactPhone': contactPhone,
      'address': address
    };
  }

  AcquiredContact.empty() {
    this.firebaseId = '';
  }

  AcquiredContact.fromJson(Map<String, dynamic> json)
      : this(
            contactFullName: json['additionalInfo']['submitterName'] +
                ' ' +
                json['additionalInfo']['submitterSurname'],
            contactPhone: json['additionalInfo']['submitterPhone'],
            contactEmail: json['additionalInfo']['submitterEmail'],
            address: json['addressObject']['address']);

  AcquiredContact.fromDocument(Map<String, dynamic> data, String id) {
    this.firebaseId = id;

    try {
      Map<String, dynamic> additionalInfoMap = data['additionalInfo'];
      this.userId = data['userId'] ?? "";
      this.contactFullName = (additionalInfoMap['submitterName'] == null ? '' : additionalInfoMap['submitterName']) +
          ' ' +
          (additionalInfoMap['submitterSurname'] == null ? '' : additionalInfoMap['submitterSurname']);

      this.contactPhone = additionalInfoMap['submitterPhone'];
      this.contactEmail = additionalInfoMap['submitterEmail'];
      this.isPropertyOfSubmitter =
          additionalInfoMap['isPropertyOfSubmitter'] ?? false;
      this.desiredSellTime =
          additionalInfoMap['desiredSellTime'] ?? "Non specificato";
      this.currentAvailability = additionalInfoMap['currentAvailability'];
      this.wantsAgencyValuation = data['additionalInfo']['wantsAgencyValuation'] == null ? false : data['additionalInfo']['wantsAgencyValuation'];
      this.whyAskEvaluation = data['additionalInfo']['whyAskEvaluation'];

      this.whatKindOfProperty = data['additionalInfo']['whatKindOfProperty'];
      this.sellerProfession = data['additionalInfo']['sellerProfession'];
      this.explicitConversionSource =
          data['additionalInfo']['explicitConversionSource'];
      this.submitterIsLookingForAHouse =
          data['additionalInfo']['submitterIsLookingForAHouse'];
      this.userAgeRange =
          data['additionalInfo']['userAgeRange'] ?? 'Non disponibile';

      this.newarcType = data['newarcType'];
      //DEPRECATO
      this.requestType = additionalInfoMap['requestType'] ?? 'vendi';
      this.valutaType = additionalInfoMap['valutaType'];

      //address
      if (data['addressObject'] != null) {
        this.address = data['addressObject']['address'] ?? "unknown";
        this.postalCode = data['addressObject']['postalCode'] == null ? '' : data['addressObject']['postalCode'].toString();
        this.streetNumber = data['addressObject']['streetNumber'] == null ? '' : data['addressObject']['streetNumber'].toString();
        this.city = data['addressObject']['city'] ?? "unknown";
        this.latitude = data['addressObject']['latitude'] == null ? '' : data['addressObject']['latitude'].toString();
        this.longitude = data['addressObject']['longitude'] == null ? '' : data['addressObject']['longitude'].toString();
      }

      //planimetry
      this.surface = data['planimetry']['surface'];
      this.floor = data['planimetry']['floor'];
      this.hasElevator = data['planimetry']['hasElevator'];

      this.locali = data['planimetry']['locali'];
      this.hasGarage = data['planimetry']['hasGarage'] ?? false;
      this.garageCount = data['planimetry']['garageCount'] ?? 0;
      this.hasBalcony = data['planimetry']['hasBalcony'] ?? false;
      this.hasCantina = data['planimetry']['hasCantina'] ?? false;
      this.hasCloset = data['planimetry']['hasCloset'] ?? false;
      this.hasConcierge = data['planimetry']['hasConcierge'] ?? false;
      this.hasTerrace = data['planimetry']['hasTerrace'] ?? false;
      this.numBalcony = data['planimetry']['numBalcony'] ?? 0;
      this.heating = data['planimetry']['heating'] ?? 'Non so';
      this.hasSharedGarden = data['planimetry']['hasSharedGarden'] ?? false;

      this.yearBuilt = data['planimetry']['yearBuilt'] ?? 'Non so';
      this.termovalvole = data['planimetry']['termovalvole'];

      this.knowExposition = data['planimetry']['knownExposition'];
      this.exposition = data['planimetry']['exposition'];

      this.typology = data['typology'];
      this.status = data['status'];

      // DEPRECATO
      if (data['status'] == 'Ottimo/Ristrutturato' ||
          data['status'] == 'Nuovo / In costruzione' ||
          !data['planimetry']['hasElevator']) {
        this.vendiType = 'nok'; // Only for Agenzia
      } else if (data['status'] == 'Buono / Abitabile' ||
          data['status'] == 'Da ristrutturare') {
        this.vendiType = 'ok'; // Good for us
        if (data['addressObject']['postalCode'] != null &&
            (int.parse(data['addressObject']['postalCode']) < 10120 ||
                int.parse(data['addressObject']['postalCode']) > 10147)) {
          this.vendiType = 'nok';
        }
      }

      this.prezzoAcquistoPrevisto = data['prezzo_acquisto_previsto'] ?? 0;
      this.prezzoMqPrevisto = data['prezzo_mq_previsto'] ?? 0;

      this.insertionTimestamp = data['insertion_timestamp'] ?? 0;
      this.unlocked = data['unlocked'] ?? false;

      //if (data['contact_stage'] == null || data['contact_stage'] == "Acquisita") {

      this.dateUnlocked = data['dateUnlocked'] ?? "Not unlocked";

      if (data['contact_stage'] != null) {
        this.contactStage = data['contact_stage'];
        /*if (this.unlocked == true) {
          this.contactStage = 'Da contattare';
        } else {
          
        }*/
      } else if (this.unlocked == true) {
        this.contactStage = 'Da contattare';
      } else if (data['contact_stage'] == null) {
        this.contactStage = 'Da sbloccare';
      }

      this.assignedAgencyId = data['assignedAgencyId'];
      this.assignedAgencyTimestamp = data['assignedAgencyTimestamp'] ??
          DateTime.now().millisecondsSinceEpoch;

      this.display = true;
      // fallbackUserId();
      // fallbackAddToLead();
      
    } catch (e) {
      // print({ 'ac error', e, s});
      // print(id);
    }
  }

  // fetchAgency(agencyId) async  {
  //   DocumentSnapshot<Map<String, dynamic>> collectionSnapshot;

  //   return collectionSnapshot = await FirebaseFirestore.instance
  //           .collection('agencies')
  //           .doc(agencyId)
  //           .get()
  //           .then((value){
  //              return  Agency.fromDocument(value.data()!, value.id );
  //            });
  //           // print(collectionSnapshot.data()!);
  //   // return Agency.fromDocument(collectionSnapshot.data()!, collectionSnapshot.id );

  // }

  Map<String, dynamic> toMap() {
    return {
      'userId': this.userId,
      'contact_stage': this.contactStage,
      'assignedAgencyId': this.assignedAgencyId,
      'assignedAgencyTimestamp': this.assignedAgencyTimestamp,
      'unlocked': this.unlocked,
      'dateUnlocked': this.dateUnlocked,
      'bathrooms': bathrooms,
      'energyClass;': energyClass,
      'floor': floor,
      'locali': locali,
      'surface': surface,
      'hasGarage': hasGarage,
      'arageCount': garageCount,
      'hasBalcony': hasBalcony,
      'hasCantina': hasCantina,
      'hasCloset': hasCloset,
      'hasConcierge': hasConcierge,
      'hasTerrace': hasTerrace,
      'numBalcony': numBalcony,
      'heating': heating,
      'hasSharedGarden': hasSharedGarden,
      'yearBuilt': yearBuilt,
      'termovalvole': termovalvole,
      'knowExposition': knowExposition,
      'exposition': exposition,
      'hasElevator': hasElevator
    };
  }

  // fallbackUserId() async {
  //   ''' fallback to change user_id to userId ''';
  //   await FirebaseFirestore.instance
  //     .collection(appConfig.COLLECT_VALUATOR_SUBMISSIONS)
  //     .doc(this.firebaseId)
  //     .update(this.toMap());
  // }
  // fallbackAddToLead() async {

  //   /*    

  //   QuerySnapshot<Map<String, dynamic>> checkLeads = await FirebaseFirestore.instance
  //   .collection(appConfig.COLLECT_LEADS)
  //   .where('basePersonInfo.email', isEqualTo: personInfo.email)
  //   .get();

  //   if( checkLeads.docs.length > 0 ) return;

  //   DocumentReference valuationRef = FirebaseFirestore.instance.collection(appConfig.COLLECT_VALUATOR_SUBMISSIONS).doc(this.firebaseId);

  //   Lead leadData = new Lead({
  //     'basePersonInfo': personInfo,
  //     'insertTimestamp': Timestamp.now().millisecondsSinceEpoch,
  //     'source': 'valuation',
  //     'sourceReference': valuationRef
  //   });

  //   DocumentReference<Map<String, dynamic>> leadRef = await FirebaseFirestore.instance
  //   .collection(appConfig.COLLECT_LEADS)
  //   .add(leadData.toMap());*/

  //   if( this.user_id != '' ) return;

  //   List fullName = this.contactFullName!.split(' ');
  //   BasePersonInfo personInfo = new BasePersonInfo({
  //     'name': fullName[0]??'',
  //     'surname': fullName[1]??'',
  //     'phone': this.contactPhone,
  //     'email': this.contactEmail

  //   });

  //   /* Get users with the email address */
  //   QuerySnapshot<Map<String, dynamic>> checkUser = await FirebaseFirestore.instance
  //   .collection(appConfig.COLLECT_USERS)
  //   .where('email', isEqualTo: personInfo.email)
  //   .get();

  //   /* If a user exists with the email address then update the user_id in the Valuation Submission */
  //   if( checkUser.docs.length > 0 ) {
  //     // NewarcUser nu = new NewarcUser.fromDocument(checkUser.docs.first.data(), checkUser.docs.first.id);
  //     // if( nu. )
  //     await FirebaseFirestore.instance
  //     .collection(appConfig.COLLECT_VALUATOR_SUBMISSIONS)
  //     .doc(this.firebaseId)
  //     .update({ 
  //       'user_id': checkUser.docs.first.id
  //     });

  //     return;

  //   } 

  //   registerAcquiredContact(this).then(( uid ) async {

  //     // createLead(this, 'direct', uid);
  //     if( uid == false ) {
  //       QuerySnapshot<Map<String, dynamic>> userRef =  await FirebaseFirestore.instance
  //       .collection(
  //       appConfig.COLLECT_USERS)
  //       .where('email', isEqualTo: this.contactEmail)
  //       .get();

  //       print({'userRef', this.contactEmail, userRef.docs.length});
  //       if( userRef.docs.length == 0 ) {
  //         uid = 'randomuid';
  //       } else {
  //         print({'had user document', userRef.docs[0].id });
  //         return;
  //       }
        
  //       // return;               
  //     }

  //     print({'uid', uid});

  //     try {
  //       // print({
  //       //   'id': uid == false ? 'randomuid' : uid,
  //       //   'name': personInfo.name,
  //       //   'surname': personInfo.surname,
  //       //   'email': personInfo.email,
  //       //   'type': 'web-client',
  //       //   'role': 'web-client',
  //       //   'source': 'valuation',
  //       //   'isActive': false,
  //       //   'isArchived': true,
  //       //   'isHiredInternally': false,
  //       //   'menuAccess': {},
  //       //   'projectTabAccess': {},
  //       //   'isFilterPerAccountEnabled': false,
  //       //   'phone': personInfo.phone,
  //       //   'insertTimestamp': this.insertionTimestamp
  //       // });
  //       NewarcUser newarcUser = new NewarcUser({
  //         'id': uid,
  //         'name': personInfo.name,
  //         'surname': personInfo.surname,
  //         'email': personInfo.email,
  //         'type': 'web-client',
  //         'role': 'web-client',
  //         'source': 'valuation',
  //         'isActive': false,
  //         'isArchived': true,
  //         'isHiredInternally': false,
  //         'menuAccess': {},
  //         'projectTabAccess': {},
  //         'isFilterPerAccountEnabled': false,
  //         'phone': personInfo.phone,
  //         'insertTimestamp': this.insertionTimestamp
  //       });
  //       DocumentReference<Map<String, dynamic>> userRef =  await FirebaseFirestore.instance
  //       .collection(
  //       appConfig.COLLECT_USERS)
  //       .add(newarcUser.toMap());

  //       await FirebaseFirestore.instance
  //       .collection(appConfig.COLLECT_VALUATOR_SUBMISSIONS)
  //       .doc(this.firebaseId)
  //       .update({ 
  //         'user_id': userRef.id
  //       });
  //     } catch (e,s) {
  //       print({e,s});
  //     }
      
      
      
      

  //   });


  // }

  
}

// class TableRow extends DataTableSource {
//   @override
//   DataRow? getRow(AcquiredContact contact) {
//     return DataCell(child);
//   }
// }
