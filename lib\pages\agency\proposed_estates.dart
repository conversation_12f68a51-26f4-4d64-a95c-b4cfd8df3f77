import 'package:newarc_platform/widget/UI/alert.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/proposedEstate.dart';
import 'package:newarc_platform/functions/various.dart';
import 'package:newarc_platform/utils/firestore.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/widget/agency/suggest_immobile_popup.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;

class ProposedEstatesView extends StatefulWidget {
  final Agency agency;
  final AgencyUser agencyUser;
  final responsive;

  const ProposedEstatesView({Key? key, required this.agency, required this.agencyUser, required this.responsive}) : super(key: key);

  @override
  State<ProposedEstatesView> createState() => _ProposedEstatesViewState();
}

class _ProposedEstatesViewState extends State<ProposedEstatesView> {
  bool loading = true;
  List<ProposedEstate> estates = [];
  String query = "";

  TextEditingController filterCity = new TextEditingController();
  TextEditingController agencyController = new TextEditingController();
  List<Map> filters = [];
  List<dynamic> cacheFirestore = [];
  List<Agency> agencyList = [];

  @override
  void initState() {
    fetchEstates(widget.agency);
    getAgencies();

    super.initState();
  }

  getAgencies() async {
    QuerySnapshot<Map<String, dynamic>> collectionSnapshot;

    collectionSnapshot = await FirebaseFirestore.instance.collection('agencies').get();

    //List<DocumentSnapshot> documentList = collectionSnapshot.docs;
    // print({ 'documentlist', documentList });

    //agencyList.add('Mostra tutti');
    for (var element in collectionSnapshot.docs) {
      Agency agency = Agency.fromDocument(element.data(), element.id);

      agencyList.add(agency);

      /*var _tmp = element.data();

      if (_tmp['name'] != null &&
          _tmp['name'] != '' &&
          agencyList.indexOf(_tmp['name']) == -1){
            agencyList.add(_tmp['name']);
          } */
    }

    setState(() {});
  }

  getColor(String status) {
    switch (status) {
      case 'Da contattare':
        return Color(0xff5FBCEC);
      case 'Contattato':
        return Color(0xffFFC633);
      case 'Non interessato':
        return Color(0xffFF5E53);
      case 'Acquisito':
        return Color(0xff489B79);
    }
  }

  List<DropdownMenuItem> buildDropdownTestItems(List _testList) {
    List<DropdownMenuItem> items = [];
    for (var i in _testList) {
      items.add(
        DropdownMenuItem(
          value: i,
          child: Container(
            decoration: BoxDecoration(
              color: getColor(i['keyword']),
              borderRadius: BorderRadius.circular(8),
            ),
            padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
            child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
              Text(
                i['keyword'],
                style: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
              ),
            ]),
          ),
        ),
      );
    }
    return items;
  }

  Future<void> fetchEstates(Agency agency) async {
    QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
    Query<Map<String, dynamic>> collectionSnapshotQuery;

    if (widget.agencyUser.role != 'master') {
      collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_PROPOSED_ESTATES).where('agencyId', isEqualTo: agency.id);
      ;
    } else {
      collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_PROPOSED_ESTATES);
    }

    if (filters.length > 0) {
      for (var i = 0; i < filters.length; i++) {
        collectionSnapshotQuery = collectionSnapshotQuery.where(filters[i]['field'], isEqualTo: filters[i]['value']);
      }
    }

    collectionSnapshot = await collectionSnapshotQuery.orderBy('insertion_timestamp', descending: true).limit(20).get();

    List<ProposedEstate> _estates = [];
    for (QueryDocumentSnapshot<Map<String, dynamic>> element in collectionSnapshot.docs) {
      try {
        ProposedEstate _tmp = ProposedEstate.fromDocument(element.data(), element.id);
        _estates.add(_tmp);
      } catch (e) {
        print("error in document ${element.id}");
        print(e);
      }
    }

    _estates.sort((a, b) => b.insertionTimestamp!.compareTo(a.insertionTimestamp!));

    setState(() {
      estates = _estates;
      loading = false;
    });
  }

  Future<bool> showSuggestImmobilePopup(bool isEdit, ProposedEstate? proposedEstate) async {
    var result = await showGeneralDialog(
      context: context,
      barrierLabel: "Barrier",
      barrierDismissible: true,
      barrierColor: Colors.black.withOpacity(0.5),
      pageBuilder: (_, __, ___) {
        return Center(
            child: SuggestImmobilePopup(
          agency: widget.agency,
          agencyUser: widget.agencyUser,
          isEdit: isEdit,
          originalProposedEstate: proposedEstate,
        ));
      },
    );
    if (result != null && result == true) {
      return true;
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    double pageWidth = MediaQuery.of(context).size.width - 260;
    if (pageWidth < 1800) {
      pageWidth = 1800;
    }
    if (widget.agencyUser.role != 'master') {
      pageWidth *= 1.1;
    }
    return Column(
      children: [
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            NarFormLabelWidget(
              label: 'Immobili proposti',
              fontSize: 22,
              fontWeight: 'bold',
              textColor: Colors.black,
            ),
            widget.agencyUser.role == 'master'
                ? Row(children: [
                    Container(
                      width: 165,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          NarFormLabelWidget(
                            label: 'Città',
                            fontSize: 10,
                            fontWeight: 'bold',
                            height: 1.5,
                          ),
                          NarSelectBoxWidget(
                            options: [
                              "Tutte",
                              "Torino",
                              "Milano",
                              "Roma",
                            ],
                            controller: filterCity,
                            onChanged: (value) {
                              if (filterCity.text == 'Tutte') {
                                filters.removeWhere((element) {
                                  return element['field'] == 'city';
                                });
                              } else {
                                // var indexoffilter = filters.indexOf({ 'field': 'additionalInfo.submitterName' });

                                filters.removeWhere((element) {
                                  return element['field'] == 'city';
                                });

                                filters.add({'field': 'city', 'value': filterCity.text, 'search': 'equal'});
                              }

                              setState(() {
                                loading = true;
                                cacheFirestore.clear();
                                fetchEstates(widget.agency);
                              });
                            },
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 5),
                    Container(
                      width: 180,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          NarFormLabelWidget(
                            label: 'Agenzia',
                            fontSize: 10,
                            fontWeight: 'bold',
                            height: 1.5,
                          ),
                          NarSelectBoxWidget(
                            options: ['Tutte']..addAll(agencyList.map((e) => e.name!)),
                            controller: agencyController,
                            onChanged: (value) {
                              if (agencyController.text == 'Tutte') {
                                filters.removeWhere((element) {
                                  return element['field'] == 'agencyId';
                                });
                              } else {
                                Agency _selectedAgency = agencyList.where((agency) => agency.name! == agencyController.text).first;
                                filters.removeWhere((element) {
                                  return element['field'] == 'agencyId';
                                });

                                filters.add({'field': 'agencyId', 'value': _selectedAgency.id, 'search': 'equal'});
                              }

                              setState(() {
                                loading = true;
                                cacheFirestore.clear();
                                fetchEstates(widget.agency);
                              });
                            },
                          ),
                        ],
                      ),
                    ),
                  ])
                : MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: () async {
                        if (await showSuggestImmobilePopup(false, null)) {
                          setState(() {
                            loading = true;
                          });
                          fetchEstates(widget.agency);
                        }
                      },
                      child: Container(
                        height: 40,
                        width: 160,
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor,
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                "Proponi nuovo",
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Icon(
                                Icons.add,
                                color: Colors.white,
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
          ],
        ),
        SizedBox(height: 20),
        loading
            ? Center(
                child: CircularProgressIndicator(
                  color: Theme.of(context).primaryColor,
                ),
              )
            : Expanded(
                child: estates.isEmpty
                    ? Text(
                        "Non sono ancora presenti immobili proposti",
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      )
                    : Container(
                        padding: EdgeInsets.symmetric(vertical: 10),
                        decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(10), border: Border.all(width: 1, color: Color.fromRGBO(219, 219, 219, 1))),
                        child: DataTable2(
                          dataRowHeight: 70,
                          isHorizontalScrollBarVisible: true,
                          minWidth: 1800,
                          columnSpacing: 10,
                          horizontalMargin: 0,
                          headingRowHeight: 25,
                          columns: getColumns(pageWidth),
                          empty: Text('Nessun record trovato!'),
                          rows: List.generate(estates.length, (int index) {
                            return DataRow(
                              cells: getDataRow(
                                estates.elementAt(index),
                              ),
                            );
                          }),
                        ),
                      ),
              ),
      ],
    );
  }

  List<DataCell> getDataRow(ProposedEstate estate) {
    List<DataCell> list = [];

    list.add(DataCell(Padding(
      padding: const EdgeInsets.only(left: 10),
      child: GestureDetector(
        child: Text(
          estate.address!,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            decoration: widget.agencyUser.role != 'master' ? TextDecoration.none : TextDecoration.underline,
            color: Colors.black,
          ),
        ),
        onTap: () async {
          if (widget.agencyUser.role != 'master') {
            return;
          }
          await showSuggestImmobilePopup(true, estate);
        },
      ),
    )));
    /*list.add(DataCell(Text(
      estate.zona!,
      style: TextStyle(fontWeight: FontWeight.bold),
    )));*/

    if (estate.link == null) {
      list.add(DataCell(Text(
        "",
        style: TextStyle(fontWeight: FontWeight.bold),
      )));
    } else {
      list.add(DataCell(GestureDetector(
        onTap: () {
          launchCustomUrl(estate.link!);
        },
        child: Text(
          'Vai all\'annuncio',
          style: TextStyle(fontWeight: FontWeight.bold, decoration: TextDecoration.underline, color: Colors.black),
        ),
      )));
    }

    /*list.add(DataCell(Text(
      estate.margineTrattativa!,
      style: TextStyle(fontWeight: FontWeight.bold),
    )));

    list.add(DataCell(Text(
      estate.possibilityCessionePreliminare!,
      style: TextStyle(fontWeight: FontWeight.bold),
    )));*/
    if (widget.agencyUser.role == 'master') {
      list.add(DataCell(Text(
        estate.agencyName ?? '-',
        style: TextStyle(fontWeight: FontWeight.bold),
      )));
    }

    list.add(DataCell(Text(
      DateTime.fromMillisecondsSinceEpoch(estate.insertionTimestamp!).day.toString() +
          '/' +
          DateTime.fromMillisecondsSinceEpoch(estate.insertionTimestamp!).month.toString() +
          '/' +
          DateTime.fromMillisecondsSinceEpoch(estate.insertionTimestamp!).year.toString(),
      style: TextStyle(fontWeight: FontWeight.bold),
    )));

    if (widget.agencyUser.role != 'master') {
      list.add(DataCell(
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () async {
              await showSuggestImmobilePopup(true, estate);
              setState(() {
                loading = true;
              });
              fetchEstates(widget.agency);
            },
            child: Container(
              height: 30,
              width: 30,
              child: SvgPicture.asset(
                'assets/icons/edit.svg',
                height: 20,
                color: Color(0xff5b5b5b),
              ),
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Color.fromRGBO(231, 231, 231, 1),
                borderRadius: BorderRadius.circular(7.0),
              ),
            ),
          ),
        ),
      ));
    }

    if (widget.agencyUser.role != 'master') {
      list.add(DataCell(
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: (() async {
              NarAlertDialog(context, 'Eliminazione annuncio', 'Sei sicuro di voler cancellare l\'annuncio?', _buildDeleteConfirmPopupAction());
              await deleteDocument(appConfig.COLLECT_PROPOSED_ESTATES, estate.id!);
              setState(() {
                loading = true;
              });
              fetchEstates(widget.agency);
            }),
            child: Container(
              height: 30,
              width: 30,
              child: SvgPicture.asset('assets/icons/trash.svg', height: 20, color: Color(0xff5b5b5b)),
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Color.fromRGBO(231, 231, 231, 1),
                borderRadius: BorderRadius.circular(7.0),
              ),
            ),
          ),
        ),
      ));
    }

    return list;
  }

  List<Widget> _buildDeleteConfirmPopupAction() {
    return [
      GestureDetector(
        child: Container(
            padding: EdgeInsets.symmetric(horizontal: 14, vertical: 10),
            decoration: BoxDecoration(
              color: Color(0xffB32F2B),
              borderRadius: BorderRadius.circular(6),
            ),
            child: NarFormLabelWidget(
              label: 'Annulla',
              fontWeight: '800',
              textColor: Colors.white,
            )),
        onTap: () {
          Navigator.of(context).pop(false);
        },
      ),
      GestureDetector(
        child: Container(
            padding: EdgeInsets.symmetric(horizontal: 14, vertical: 10),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
              borderRadius: BorderRadius.circular(6),
            ),
            child: NarFormLabelWidget(label: 'Procedi', fontWeight: '800', textColor: Colors.white)),
        onTap: () async {
          Navigator.of(context).pop(true);
        },
      )
    ];
  }

  List<DataColumn> getColumns(double pageWidth) {
    List<DataColumn> list = [];

    list.add(DataColumn2(
        fixedWidth: 0.18 * pageWidth,
        label: Padding(
          padding: const EdgeInsets.only(left: 10.0),
          child: NarFormLabelWidget(
            label: 'Indirizzo',
            fontSize: 13,
            fontWeight: 'bold',
            textColor: Color.fromRGBO(131, 131, 131, 1),
            height: 1.15,
          ),
        )));

    /*list.add(DataColumn2(
      size: ColumnSize.S,
      label: SelectableText(
        'Zona',
        style: TextStyle(),
      ),
    ));*/

    list.add(DataColumn2(
        fixedWidth: 0.15 * pageWidth,
        label: NarFormLabelWidget(
          label: 'Link',
          fontSize: 13,
          fontWeight: 'bold',
          textColor: Color.fromRGBO(131, 131, 131, 1),
          height: 1.15,
        )));

    if (widget.agencyUser.role == 'master') {
      list.add(DataColumn2(
          fixedWidth: 0.16 * pageWidth,
          label: NarFormLabelWidget(
            label: 'Agenzia',
            fontSize: 13,
            fontWeight: 'bold',
            textColor: Color.fromRGBO(131, 131, 131, 1),
            height: 1.15,
          )));
    }

    list.add(DataColumn2(
        fixedWidth: 0.16 * pageWidth,
        label: NarFormLabelWidget(
          label: 'Data',
          fontSize: 13,
          fontWeight: 'bold',
          textColor: Color.fromRGBO(131, 131, 131, 1),
          height: 1.15,
        )));

    if (widget.agencyUser.role != 'master') {
      list.add(DataColumn2(
          fixedWidth: 0.05 * pageWidth,
          label: NarFormLabelWidget(
            label: 'Modifica',
            fontSize: 13,
            fontWeight: 'bold',
            textColor: Color.fromRGBO(131, 131, 131, 1),
            height: 1.15,
          )));
    }

    if (widget.agencyUser.role != 'master') {
      list.add(DataColumn2(
          fixedWidth: 0.05 * pageWidth,
          label: NarFormLabelWidget(
            label: 'Elimina',
            fontSize: 13,
            fontWeight: 'bold',
            textColor: Color.fromRGBO(131, 131, 131, 1),
            height: 1.15,
          )));
    }

    return list;
  }
}
