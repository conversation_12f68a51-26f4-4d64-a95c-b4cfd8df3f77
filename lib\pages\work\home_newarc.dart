
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:newarc_platform/app_const.dart' as appConst;
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/pages/agency/inside_request_view.dart';
import 'package:newarc_platform/pages/work/leads/leads_single_view.dart';
import 'package:newarc_platform/pages/work/leads/leads_view.dart';
import 'package:newarc_platform/pages/work/renovation_quotation/cme_information.dart';
import 'package:newarc_platform/pages/work/gestione/agencies/agencies_view.dart';
import 'package:newarc_platform/pages/work/gestione/agencies/agencies_inside_view.dart';
import 'package:newarc_platform/pages/work/gestione/contractors/contractors_view.dart';
import 'package:newarc_platform/pages/work/gestione/contractors/contractors_inside_view.dart';
import 'package:newarc_platform/pages/work/porte_interne/porte_interne_inside_view.dart';
import 'package:newarc_platform/pages/work/porte_interne/porte_interne_view.dart';
import 'package:newarc_platform/pages/work/suggested_contacts/suggested_contacts_view.dart';
import 'package:newarc_platform/pages/work/acquired_contacts/acquired_contacts_view.dart';
import 'package:newarc_platform/pages/work/cep_view/cep_view.dart';
import 'package:newarc_platform/pages/work/common_widget.dart';
import 'package:newarc_platform/pages/work/immagina_project_review/immagina_project_review.dart';
import 'package:newarc_platform/pages/work/immagina_request_review/request_review_screen.dart';
import 'package:newarc_platform/pages/work/newarc_active_ads.dart';
import 'package:newarc_platform/pages/work/moodboard/moodboard_view.dart';
import 'package:newarc_platform/pages/work/newarc_immagina/newarc_immagina_view.dart';
import 'package:newarc_platform/pages/work/newarc_project_view.dart';
import 'package:newarc_platform/pages/login_page.dart';
import 'package:newarc_platform/pages/work/agenzie_view.dart';
import 'package:newarc_platform/pages/work/ditte_newarc.dart';
import 'package:newarc_platform/pages/work/gestione/professionals/professionals_view.dart';
import 'package:newarc_platform/pages/work/gestione/professionals/professionals_inside_view.dart';
import 'package:newarc_platform/pages/work/newarc_active_projects/newarc_active_projects_view.dart';
import 'package:newarc_platform/pages/work/pavimenti/pavimenti_inside_view.dart';
import 'package:newarc_platform/pages/work/pavimenti/pavimenti_view.dart';
import 'package:newarc_platform/pages/work/renovation_contacts/renovation_contacts_view.dart';
import 'package:newarc_platform/pages/work/renovation_quotation/renovation_quotation_view.dart';
import 'package:newarc_platform/pages/work/gestione/persone/persone_view.dart';
import 'package:newarc_platform/pages/work/gestione/persone/persone.dart';
import 'package:newarc_platform/pages/common/web_leads/web_leads_view.dart';
import 'package:newarc_platform/pages/work/rivestimenti/rivestimenti_inside_view.dart';
import 'package:newarc_platform/pages/work/rivestimenti/rivestimenti_view.dart';
import 'package:newarc_platform/pages/work/tinte/tinte_inside_view.dart';
import 'package:newarc_platform/pages/work/tinte/tinte_view.dart';
import 'package:newarc_platform/pages/work/web-registrations/web_registrations_view.dart';
import 'package:newarc_platform/routes/work_routes.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/work/active_ad_add.dart';
import 'package:newarc_platform/widget/work/custom_appbar_menu.dart';
import 'package:newarc_platform/widget/work/custom_drawer.dart';
import 'package:newarc_platform/widget/work/provisional_economic_account.dart';
import 'package:newarc_platform/widget/work/renovation_quotation.dart';
import 'package:newarc_platform/widget/work/settings.dart';
import 'newarc_immagina_project_archive/newarc_immagina_project_archive_view.dart';

class HomeNewarc extends StatefulWidget {
  const HomeNewarc({Key? key, required this.newarcUser ,required this.child}) : super(key: key);

  static const String route = '/home-newarc';

  final NewarcUser newarcUser;
  final Widget child;

  @override
  State<HomeNewarc> createState() => _HomeNewarcState();
}

class _HomeNewarcState extends State<HomeNewarc> {
  String selectedView = 'progetti-in-corso';


  String? profilePicture;

  ReceivedContactsPageFilters? receivedContactsPageFilters;
  var appBarHeight = AppBar().preferredSize.height;

  @override
  void initState() {
    if (widget.newarcUser.role == 'renderist') {
      selectedView = 'progetti-attivi';
    } else {
      //implement additional logic where and when needed through if-else statements
    }
    getProfilePicture();
    super.initState();
  }

  getProfilePicture() async {
    var url = await printUrl(
        'users/', widget.newarcUser.id, widget.newarcUser.profilePicture);

    if (url != '') {
      setState(() {
        profilePicture = url;
      });
    }
    // widget.agencyUser.profilePicture!
  }

  @override
  Widget build(BuildContext context) {
    final currentLocation = GoRouter.of(context).state.matchedLocation;

    Color backgroundColor;
    if (currentLocation.startsWith('/ristrutturazioni/preventivi/') || currentLocation.startsWith('/ristrutturazioni/cme/')) {
      backgroundColor = const Color(0xFFF9F9F9);
    } else {
      backgroundColor = Colors.white;
    }
    return PopScope(
      canPop: false,
      child: LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          if (constraints.maxWidth > 650) {
            return Scaffold(
              backgroundColor: backgroundColor,
              body: Row(
                children: [
                  CustomDrawer(
                    newarcUser: widget.newarcUser,
                  ),
                  Expanded(
                    child: Column(
                      children: [
                        Container(
                          padding: const EdgeInsets.only(right: 15),
                          color: Colors.white,
                          child: AppBar(
                            backgroundColor: Colors.white,
                            elevation: 0,
                            leading: Container(),
                            actions: <Widget>[
                              // getNotificationTray(),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      crossAxisAlignment: CrossAxisAlignment.end,
                                      children: [
                                        NarFormLabelWidget(
                                            label: widget.newarcUser.firstName! +
                                                ' ' +
                                                widget.newarcUser.lastName!),
                                        NarFormLabelWidget(
                                          label: appConst.userRolesDict[
                                              widget.newarcUser.role!]!,
                                          textColor: Color(0xff696969),
                                          fontWeight: '500',
                                          fontSize: 11,
                                          letterSpacing: 0.4,
                                        )
                                      ]),
                                  SizedBox(width: 10),
                                  getAppbarMenu(profilePicture),
                                ],
                              ),
                            ],
                          ),
                        ),
                        Container(
                          height: 1,
                          color: Color(0xffe0e0e0),
                        ),
                        Expanded(
                          child: Container(
                            color: backgroundColor,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 30.0, vertical: 10),
                            child: widget.child,
                            // child: Text('Test'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          } else {
            // Versione ridotta
            return Scaffold(
              backgroundColor: Color(0xffF9F9F9),
              appBar: AppBar(
                backgroundColor: Colors.black,
                actions: [
                  IconButton(
                    icon: Icon(
                      Icons.settings,
                      color: Colors.grey,
                    ),
                    onPressed: () {
                      // do something
                    },
                  ),
                  // getNotificationTray(),
                  PopupMenuButton(
                    tooltip: "",
                    icon: SvgPicture.asset(
                      'assets/icons/account.svg',
                      color: Colors.grey,
                      width: 20,
                    ),
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        enabled: false,
                        child: Column(children: [
                          Text(widget.newarcUser.firstName!),
                          Text(widget.newarcUser.email!)
                        ]),
                        value: 1,
                        onTap: () {},
                      ),
                      PopupMenuItem(
                        child: Text("Logout"),
                        value: 2,
                        onTap: () async {
                          await FirebaseAuth.instance.signOut();
                          context.go("/");
                        },
                      ),
                    ],
                  )
                ],
              ),
              drawer: CustomDrawer(
                newarcUser: widget.newarcUser,
              ),
              body: Container(
                color: selectedView == 'renovation-quotation-single' || selectedView == "cme-information-inside-view" ? Color(0xFFF9F9F9) : Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 20),
                child: Column(
                  children: [
                    Expanded(child: widget.child),
                  ],
                ),
              ),
            );
          }
        },
      ),
    );
  }


  /*Widget getNotificationTray() {
    return StreamBuilder(
      stream: FirebaseFirestore.instance
          .collection(
            '${appConfig.COLLECT_AGENCIES}/',
          )
          .doc(widget.agencyUser.agency!.id)
          .snapshots(),
      builder: (BuildContext context,
          AsyncSnapshot<DocumentSnapshot<Map<String, dynamic>>> snapshot) {
        //List<NewarcNotification> notifications = [];
        bool notificationsRead = true;

        if (snapshot.hasData) {
          Agency agency =
              Agency.fromDocument(snapshot.data!.data()!, snapshot.data!.id);
          notificationsRead = agency.notificationsRead!;
          /*snapshot.data!.docs.forEach((doc) {
            notifications
                .add(NewarcNotification.fromDocument(doc.data(), doc.id));
          });*/
        }
        return Stack(
          alignment: Alignment.center,
          children: [
            Container(
              height: 40,
              width: 40,
              color: Colors.transparent,
              child: CustomNotificationTray(
                  agency: widget.agencyUser.agency!,
                  notificationsRead: notificationsRead),
            ),
            notificationsRead
                ? Container()
                : Positioned(
                    right: 10,
                    top: 10,
                    child: Container(
                      width: 9,
                      height: 9,
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor,
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                  )
          ],
        );
      },
    );
  }*/

  Widget getAppbarMenu(String? profilePicture) {
    return Container(
      height: 40,
      width: 40,
      margin: EdgeInsets.only(right: 25),
      color: AppColor.white,
      child: CustomAppbarMenu(
        profilePicture: profilePicture,
        onSettingsTapped: () {
          selectedView = 'newarc-team-settings';
          context.go(WorkRoutes.workSetting);
        },
      ),
    );
  }
}
