import 'package:flutter/material.dart';

/// *  textInputType - The type of information for which to optimize the text input control.
/// *  hintText - Text that suggests what sort of input the field accepts.
/// *  prefixIcon - Pass Icon if required
/// *  defaultText - If there is predefined value is there for a text field
/// *  focusNode - Currently focus node
/// *  obscureText - Is Password field?
/// *  controller - Text controller
/// *  functionValidate - Validation function(currently I have used to check empty field)
/// *  parametersValidate - Value to validate
/// *  actionKeyboard - Keyboard action eg. next, done, search, etc
/// *  onSubmitField - Done click action
/// *  onFieldTap - On focus on TextField
class NarRadioWidget extends StatefulWidget {
  final String? label;
  final List<String>? values;
  final int? columns;
  String? groupedValue;

  NarRadioWidget(
      {required this.label,
      required this.values,
      this.columns,
      required this.groupedValue});

  @override
  _NarRadioWidgetState createState() => _NarRadioWidgetState();
}

class _NarRadioWidgetState extends State<NarRadioWidget> {
  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(
        primaryColor: Theme.of(context).primaryColor,
        unselectedWidgetColor: Color.fromRGBO(219, 219, 219, 1),
        radioTheme: RadioThemeData(
          fillColor: WidgetStateProperty.resolveWith<Color>(
              (Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return Color.fromRGBO(219, 219, 219, 1);
            } else if (states.contains(WidgetState.selected)) {
              return Theme.of(context).primaryColor;
            }

            return Color.fromRGBO(219, 219, 219, 1);
          }),
          overlayColor: WidgetStateProperty.resolveWith<Color>(
              (Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return Colors.transparent;
            } else if (states.contains(WidgetState.selected)) {
              return Colors.transparent;
            }

            return Colors.transparent;
          }),

          // shape: RoundedRectangleBorder(
          //   borderRadius: BorderRadius.circular(5),
          // ),
        ),
      ),
      child: new GridView.count(
        // scrollDirection: Axis.horizontal,
        crossAxisCount: widget.columns ?? 3,
        shrinkWrap: true,
        childAspectRatio: 8,
        children: widget.values!.map((String radioValue) {
          return new RadioListTile(
            groupValue: widget.groupedValue,
            controlAffinity: ListTileControlAffinity.leading,
            title: new Text(radioValue,
                style: TextStyle(
                  // color: colorRed,
                  fontSize: 15.0,
                  fontWeight: FontWeight.w800,
                  fontStyle: FontStyle.normal,
                  //fontFamily: 'Visby800'
                )),
            value: radioValue,
            onChanged: (value) {
              setState(() {
                widget.groupedValue = value.toString();
              });
            },
          );
        }).toList(),
      ),
    );
  }
}
