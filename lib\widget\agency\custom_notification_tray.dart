import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/app_config.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:newarc_platform/classes/notification.dart';
import 'package:newarc_platform/utils/firestore.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

import '../../classes/agencyUser.dart';

class CustomNotificationTray extends StatefulWidget {
  const CustomNotificationTray(
      {Key? key, required this.agency, required this.notificationsRead})
      : super(key: key);
  final Agency agency;
  final bool notificationsRead;

  @override
  State<CustomNotificationTray> createState() => _CustomButtonTestState();
}

class _CustomButtonTestState extends State<CustomNotificationTray> {
  bool loading = true;
  List<NewarcNotification> notifications = [];

  @override
  void initState() {
    loadNotifications();

    super.initState();
  }

  loadNotifications() async {
    List<NewarcNotification> _notifications = [];

    try {
      QuerySnapshot querySnapshot = await FirebaseFirestore.instance
          .collection(
            '${appConfig.COLLECT_AGENCIES}/${widget.agency.id}/notifications/',
          )
          .orderBy('date', descending: true)
          .get();
      for (QueryDocumentSnapshot doc in querySnapshot.docs) {
        _notifications.add(NewarcNotification.fromDocument(doc.data(), doc.id));
      }
    } catch (e) {
      print("notification exception");
      print(e);
    }

    setState(() {
      notifications = _notifications;
      loading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: DropdownButtonHideUnderline(
        child: DropdownButton2(
          // buttonSplashColor: Colors.transparent,
          // buttonOverlayColor:
              
          buttonStyleData: ButtonStyleData(
            overlayColor: WidgetStateProperty.all<Color>(Colors.transparent),

          ),
          enableFeedback: false,
          customButton: Container(
            child: Icon(
              Icons.notifications,
              color: Colors.grey,
            ),
          ),
          onMenuStateChange: (isOpen) async {
            if (widget.notificationsRead) {
              return;
            }

            await updateDocument(appConfig.COLLECT_AGENCIES, widget.agency.id!,
                {'notificationsRead': true});
          },
          // customItemsHeights: loading
          //     ? [30]
          //     : [
          //         30,
          //         ...List<double>.filled(
          //             getItems(notifications, context, widget.agency).length,
          //             65),
          //       ],
          items: loading
              ? [
                  DropdownMenuItem<Widget>(
                      value: CircularProgressIndicator(),
                      child: Center(child: CircularProgressIndicator())),
                ]
              : [
                  DropdownMenuItem<Widget>(
                    value: Container(
                      child: NarFormLabelWidget(
                          label: "Notifiche", fontSize: 16, fontWeight: '800'),
                    ),
                    child: Container(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          NarFormLabelWidget(
                              label: "Notifiche",
                              fontSize: 16,
                              fontWeight: '800'),
                          TextButton(
                            child: Text(
                              "Cancella tutte",
                              style:
                                  TextStyle(color: Colors.grey, fontSize: 12),
                            ),
                            onPressed: () async {
                              setState(() {
                                loading = true;
                              });
                              await deleteAllCollectionDocuments(
                                  '${COLLECT_AGENCIES}/${widget.agency.id}/notifications');
                              setState(() {
                                loading = false;
                                Navigator.of(context).pop();
                              });
                              //cancella tutte le notifiche dell'agenzia
                            },
                          ),
                        ],
                      ),
                    ),
                    enabled: false,
                  ),
                  ...getItems(notifications, context, widget.agency),
                ],
          //onTap: (() => {}),
          onChanged: (value) {
            //value as MenuItem;
          },
          menuItemStyleData: MenuItemStyleData(
            padding: const EdgeInsets.only(left: 16, right: 16),
            height: 60,
            // customHeights: loading == false 
            // ? getCustomItemsHeights(getItems(notifications, context, widget.agency), 30)
            // : [30]
          ),
          dropdownStyleData: DropdownStyleData(
            width: 260,
            padding: const EdgeInsets.symmetric(vertical: 6),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    offset: Offset(0, 0),
                    blurRadius: 15,
                  )
                ]),
            elevation: 0,
            offset: const Offset(-200, -16),
            scrollbarTheme: ScrollbarThemeData()
          ),
          //itemHeight: 65,
          // itemPadding: const EdgeInsets.only(left: 16, right: 16),
          // scrollbarAlwaysShow: true,
          // dropdownWidth: 260,
          // dropdownPadding: const EdgeInsets.symmetric(vertical: 6),
          // dropdownDecoration: BoxDecoration(
          //     borderRadius: BorderRadius.circular(10),
          //     color: Colors.white,
          //     boxShadow: [
          //       BoxShadow(
          //         color: Colors.black.withOpacity(0.1),
          //         offset: Offset(0, 0),
          //         blurRadius: 15,
          //       )
          //     ]),
          // dropdownElevation: 0,
          // offset: const Offset(-200, -16),
        ),
      ),
    );
  }
}

List<DropdownMenuItem<Widget>> getItems(List<NewarcNotification> notifications,
    BuildContext context, Agency agency) {
  List<DropdownMenuItem<Widget>> list = [];

  list.addAll(List.generate(
    notifications.length,
    (index) => DropdownMenuItem<Widget>(
      value: getNotificationBubble(context, notifications.elementAt(index)),
      child: getNotificationBubble(context, notifications.elementAt(index)),
    ),
  ));

  return list;
}

Widget getNotificationBubble(
    BuildContext context, NewarcNotification notification) {
  return Container(
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          padding: EdgeInsets.only(top: 6, bottom: 6),
          decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
              borderRadius: BorderRadius.circular(40)),
          child: SvgPicture.asset(
            'assets/icons/newhouse.svg',
            color: Colors.white,
          ),
          width: 32,
          height: 32,
        ),
        SizedBox(width: 6),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Flexible(
                child: Text(
                  notification.message!,
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
                ),
              ),
              SizedBox(height: 4),
              Text(
                agoify(notification.date!),
                style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                    color: Colors.grey),
              ),
            ],
          ),
        ),
      ],
    ),
  );
}

String agoify(int timestamp) {
  try {
    var now = DateTime.now().millisecondsSinceEpoch;
    var diff = now - timestamp;

    if (diff < 60000) {
      return 'Ricevuto ${diff ~/ 1000} secondi fa';
    } else if (diff < 3600000) {
      return 'Ricevuto ${diff ~/ 60000} minuti fa';
    } else if (diff < 86400000) {
      return 'Ricevuto ${diff ~/ 3600000} ore fa';
    } else if (diff < 604800000) {
      return 'Ricevuto ${diff ~/ 86400000} giorni fa';
    } else if (diff < 2.628e+9) {
      // Approximative month, assume all months to have 30 days
      return 'Ricevuto ${diff ~/ 604800000} settimane fa';
    } else if (diff < 31.6e+9) {
      // Approximative year, assume all year to have 365 days
      return 'Ricevuto ${diff ~/ 2.628e+9} mesi fa';
    } else {
      return 'Ricevuto ${diff ~/ 31.6e+9} anni fa';
    }
  } catch (e) {
    print("An error occurred: $e");
    return 'An error occurred';
  }
}
