import 'package:flutter/material.dart';

/// *  textInputType - The type of information for which to optimize the text input control.
/// *  hintText - Text that suggests what sort of input the field accepts.
/// *  prefixIcon - Pass Icon if required
/// *  defaultText - If there is predefined value is there for a text field
/// *  focusNode - Currently focus node
/// *  obscureText - Is Password field?
/// *  controller - Text controller
/// *  functionValidate - Validation function(currently I have used to check empty field)
/// *  parametersValidate - Value to validate
/// *  actionKeyboard - Keyboard action eg. next, done, search, etc
/// *  onSubmitField - Done click action
/// *  onFieldTap - On focus on TextField
class NarTextareaWidget extends StatefulWidget {
  final int? maxLines;
  final int? minLines;
  final String? hintText;
  final Widget? prefixIcon;
  final String? defaultText;
  final FocusNode? focusNode;
  final TextEditingController? controller;
  final String? validationType;
  final String? parametersValidate;
  final TextInputAction? actionKeyboard;
  final Function? onSubmitField;
  final Function? onFieldTap;
  final Function(String)? onChanged;

  const NarTextareaWidget(
      {this.hintText,
      required this.maxLines,
      this.minLines,
      this.focusNode,
      this.defaultText,
      this.controller,
      this.validationType,
      this.parametersValidate,
      this.actionKeyboard = TextInputAction.next,
      this.onSubmitField,
      this.onFieldTap,
      this.prefixIcon,
      this.onChanged,
      });

  @override
  _NarTextareaWidgetState createState() => _NarTextareaWidgetState();
}

class _NarTextareaWidgetState extends State<NarTextareaWidget> {
  double bottomPaddingToError = 12;

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(
        primaryColor: Theme.of(context).colorScheme.primary,
      ),
      child: TextFormField(
        minLines: widget.minLines ?? 4,
        maxLines: widget.maxLines,
        expands: false,
        keyboardType: TextInputType.multiline,
        textInputAction: TextInputAction.newline,
        cursorColor: Colors.black,
        focusNode: widget.focusNode,
        style: TextStyle(
          // color: colorBlack,
          fontSize: 15.0,
          fontWeight: FontWeight.bold,
          fontStyle: FontStyle.normal,
          letterSpacing: 1,
        ),
        initialValue: widget.defaultText,
        decoration: InputDecoration(
            prefixIcon: widget.prefixIcon,
            hintText: widget.hintText,
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.all(Radius.circular(7)),
              borderSide: BorderSide(color: Color.fromRGBO(219, 219, 219, 1)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.all(Radius.circular(7)),
              borderSide: BorderSide(color: Color.fromRGBO(219, 219, 219, 1)),
            ),
            hintStyle: TextStyle(
              color: Colors.grey,
              fontSize: 15.0,
              fontWeight: FontWeight.w800,
              fontStyle: FontStyle.normal,
              letterSpacing: 1,
              //fontFamily: 'Visby800',
            ),
            contentPadding:
                EdgeInsets.only(top: 30, bottom: 18, left: 21.0, right: 8.0),
            isDense: false,
            // errorStyle: TextStyle(
            //   // color: colorRed,
            //   fontSize: 15.0,
            //   fontWeight: FontWeight.w800,
            //   fontStyle: FontStyle.normal,
            //   letterSpacing: 1,
            //   fontFamily: 'Visby800'
            // ),
            errorBorder: OutlineInputBorder(
              borderSide: BorderSide(color: Color.fromARGB(255, 234, 28, 28)),
            ),
            focusedErrorBorder: OutlineInputBorder(
                // borderSide: BorderSide(color: Colors.black ),
                ),
            fillColor: Colors.white),
        controller: widget.controller,
        validator: (value) {
          // if (widget.functionValidate != null) {
          //   String resultValidate =
          //       widget.functionValidate!(value, widget.parametersValidate) ?? true ;
          //   if (resultValidate != null) {
          //     return resultValidate;
          //   }
          // }
          // return null;
          if (widget.validationType != null &&
              widget.parametersValidate != null) {
            if (widget.validationType == 'required' && value!.isEmpty) {
              return widget.parametersValidate;
            }
          }

          return null;
        },
        onFieldSubmitted: (value) {
          if (widget.onSubmitField != null) widget.onSubmitField!();
        },
        onChanged: (val) {
          if (widget.onChanged != null) widget.onChanged!(val);
        },
        onTap: () {
          if (widget.onFieldTap != null) widget.onFieldTap!();
        },
      ),
    );
  }
}
