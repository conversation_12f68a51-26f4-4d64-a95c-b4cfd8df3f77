import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';

import 'package:newarc_platform/classes/immaginaProject.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/common_utils.dart';
import 'package:newarc_platform/widget/UI/tab/tag_widget.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:intl/intl.dart';
import '../../../widget/UI/tab/status_widget.dart';

class ImmaginaProfessionalsDataSource extends DataTableSource {
  final List<ImmaginaProject> projects;
  final BuildContext context;
  final List<Map> status;
  final Function() initialFetchContacts;
  final Function(ImmaginaProject) onProjectTap;

  NumberFormat localCurrencyFormatMain = NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 0);

  ImmaginaProfessionalsDataSource({
    required this.context,
    required this.projects,
    required this.status,
    required this.initialFetchContacts,
    required this.onProjectTap,
  });

  @override
  DataRow? getRow(int index) {
    if (index < projects.length){
      ImmaginaProject proj = projects[index];
      final projectStatus = proj.requestStatus.toLowerCase();
      int indexStatus = status.indexWhere((sta) => sta['value'] == projectStatus);
      var statMap = status[indexStatus];
      String projStartDate = DateFormat('dd/MM/yyyy').format(DateTime.fromMillisecondsSinceEpoch(proj.insertionTimestamp)).toString();
      String projEndDate = proj.projectCompletedDate == null ? '' : DateFormat('dd/MM/yyyy').format(DateTime.fromMillisecondsSinceEpoch(proj.projectCompletedDate!)).toString();
      bool isProfessionals = proj.isForProfessionals ?? false;
      bool isNew = ((DateTime.now().millisecondsSinceEpoch - proj.insertionTimestamp <= 24 * 60 * 60 * 1000) && proj.requestStatus.toLowerCase() != CommonUtils.bloccata);
      return DataRow2(
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
                color: AppColor.borderColor,
                width: 1,
            ),
          ),
        ),
        color: WidgetStateProperty.resolveWith<Color?>(
          (Set<WidgetState> states) {
            int daysPassed = calculateDaysPassed(proj.statusChangedDate,maxDayCount: 5);
            print(daysPassed);
            if (projectStatus == CommonUtils.inLavorazione && daysPassed > 4) {
              return const Color(0xFFDD0000).withOpacity(0.1);
            }

            if (isNew) {
              return const Color(0xFFFEC600).withOpacity(0.1);
            }

            return null;
          },
        ),
        cells: [
          // Indirizzo
          DataCell(
            // Stack(
            //   clipBehavior: Clip.none,
            //   children: [
            //     if (isProfessionals)
            //     Positioned(
            //       top: -18,
            //       child: Row(
            //         children: [
            //           TagWidget(
            //             text: "Professionals",
            //             statusColor: Colors.black,
            //           ),
            //           SizedBox(width: 5,),
            //           isNew
            //           ? TagWidget(
            //             text: "Novità",
            //             statusColor: Color(0xffFEC600),
            //           )
            //           : SizedBox.shrink(),
            //         ],
            //       ),
            //     ),
            //     if (isNew && !isProfessionals)
            //     Positioned(
            //       top: -24,
            //       child: TagWidget(
            //         text: "Novità",
            //         statusColor: Color(0xffFEC600),
            //       ),
            //     ),
                NarLinkWidget(
                  text: proj.addressInfo?.toShortAddress() ?? "",
                  fontSize: 12,
                  fontWeight: '800',
                  textAlign: TextAlign.start,
                  textColor: Colors.black,
                  onClick: () {
                    onProjectTap(proj);
                  },
                ),
            //   ],
            // ),
          ),
          // Stato progetto
          DataCell(
            StatusWidget(
              status: statMap['label'],
              statusColor: statMap['bgColor'],
            ),
          ),
          // Inizio Progetto
          DataCell(
            NarFormLabelWidget(
              label: projStartDate,
              fontSize: 12,
              fontWeight: '600',
              textAlign: TextAlign.start,
              textColor: Colors.black,
            )
          ),
          // Fine progetto
          DataCell(
            NarFormLabelWidget(
              label: projEndDate,
              fontSize: 12,
              fontWeight: '600',
              textAlign: TextAlign.start,
              textColor: Colors.black,
            )
          ),
        ],
      );
    }
    return null;
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => projects.length;

  @override
  int get selectedRowCount => 0;


  int calculateDaysPassed(int? statusChangedDate, {required int maxDayCount}) {
    if (statusChangedDate == null) return 0;

    DateTime startDate = onlyDate(DateTime.fromMillisecondsSinceEpoch(statusChangedDate)).add(Duration(days: 1));
    DateTime now = onlyDate(DateTime.now());

    int workingDays = 0;
    DateTime currentDay = startDate;

    while (workingDays < maxDayCount && (currentDay.isBefore(now) || currentDay.isAtSameMomentAs(now))) {
      if (currentDay.weekday != DateTime.saturday && currentDay.weekday != DateTime.sunday) {
        workingDays++;
      }
      currentDay = currentDay.add(Duration(days: 1));
    }

    return workingDays;
  }

  DateTime onlyDate(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

}

