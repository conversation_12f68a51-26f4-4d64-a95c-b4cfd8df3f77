import 'dart:async';

import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/classes/lead.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';


class LeadAccountInformation extends StatefulWidget {
  final NewarcUser? lead;
  final Function? updateContact;
  
  const LeadAccountInformation({Key? key, this.lead, this.updateContact})
      : super(key: key);

  @override
  State<LeadAccountInformation> createState() => _LeadAccountInformationState();
}

class _LeadAccountInformationState extends State<LeadAccountInformation> {
  bool loading = false;
  TextEditingController? contName = new TextEditingController();
  TextEditingController? contSurname = new TextEditingController();
  TextEditingController? contEmail = new TextEditingController();
  TextEditingController? contPhone = new TextEditingController();
  // List<Map> dataList = []; 
  // Map userRoleIndex = {
  //   'scouter': 0,
  //   'renovator': 1,
  //   'renderist': 2,
  //   'geometra': 3,
  //   'media_creator': 4
  // };
  String progressMessage = '';

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    
    contName!.text = widget.lead!.firstName!;
    contSurname!.text = widget.lead!.lastName!;
    contPhone!.text = widget.lead!.phone!;
    contEmail!.text = widget.lead!.email!;
  }

  @protected
  void didUpdateWidget(LeadAccountInformation oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    super.dispose();
  }

  setInitialValues() {

  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: double.infinity,
      child: loading == true
          ? NarFormLabelWidget(label: 'Loading')
          : Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: [
                Expanded(
                  child: ListView(
                    children: [
                      SizedBox(height: 20),
                      NarFormLabelWidget(
                        label: 'Dati account',
                        fontSize: 20,
                        fontWeight: 'bold',
                      ),
                      SizedBox(height: 30),

                      Row(
                        children: [
                          CustomTextFormField(
                            label: "Nome",
                            controller: contName,
                            validator: (value) {
                              if (value == '') {
                                return 'Required!';
                              }

                              return null;
                            }
                          ),
                          SizedBox(width: 10,),
                          CustomTextFormField(
                            label: "Cognome",
                            controller: contSurname,
                            validator: (value) {
                              if (value == '') {
                                return 'Required!';
                              }

                              return null;
                            }
                          )
                        ],
                      ),
                      SizedBox(height: 10,),
                      Row(
                        children: [
                          CustomTextFormField(
                            label: "E-mail",
                            enabled: false,
                            controller: contEmail,
                            validator: (value) {
                              if (value == '') {
                                return 'Required!';
                              }

                              return null;
                            }
                          ),
                          SizedBox(width: 10,),
                          CustomTextFormField(
                            label: "Telefono",
                            controller: contPhone,
                            validator: (value) {
                              if (value == '') {
                                return 'Required!';
                              }

                              return null;
                            }
                          )
                        ],
                      ),

                      
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    NarFormLabelWidget(label: progressMessage),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        BaseNewarcButton(
                          buttonText: "Salva modifiche",
                          onPressed: () async {
                            
                            if( contName!.text == '' || contSurname!.text == '' || contEmail!.text == '' || contPhone!.text == '' ) {
                              setState(() {
                                progressMessage = 'All values are requried!';
                              }); 
                              return;
                            }

                            setState(() {
                              progressMessage = 'Salvataggio in corso.';
                            });

                            widget.lead!.firstName = contName!.text;
                            widget.lead!.lastName = contSurname!.text;
                            // widget.lead!.basePersonInfo!.email = contEmail!.text;
                            widget.lead!.phone = contPhone!.text;

                            FirebaseFirestore.instance.collection(appConfig.COLLECT_USERS)
                            .doc(widget.lead!.id)
                            .update( widget.lead!.toMap() );

                            widget.updateContact!(widget.lead);

                            setState(() {
                              progressMessage = 'Saved.';
                            });

                            Timer(Duration(milliseconds: 2500), () {
                              setState(() {
                                progressMessage = '';
                              });
                            });

                            
                          },
                        )
                      ],
                    ),
                  ],
                ),
              ],
            ),
    );
  }
}
