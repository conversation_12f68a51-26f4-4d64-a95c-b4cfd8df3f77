import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/app_config.dart';
import 'package:newarc_platform/classes/acquiredContact.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/operation.dart';
import 'package:newarc_platform/classes/proposedEstate.dart';
import 'package:newarc_platform/pages/agency/acquired_contacts_view.dart';
import 'package:newarc_platform/utils/firestore.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

class AddEditOperationPopup extends StatefulWidget {
  final Agency? agency;
  final AgencyUser? agencyUser;
  final Operation? operation;
  final bool? isEdit;
  final Function? refetch;

  const AddEditOperationPopup({
    required this.agency,
    required this.agencyUser,
    required this.operation,
    required this.isEdit,
    required this.refetch,
    Key? key,
  }) : super(key: key);

  @override
  State<AddEditOperationPopup> createState() => _AddEditOperationPopupState();
}

class _AddEditOperationPopupState extends State<AddEditOperationPopup> {
  TextStyle sectionTitleStyle =
      TextStyle(fontSize: 16, fontWeight: FontWeight.bold);
  final _formKey = GlobalKey<FormState>();
  bool loading = false;
  bool clicked = false;

  Operation _operation = Operation.empty();

  bool showOptionMissing(String? option) {
    try {
      return (option == null || option.isEmpty) && clicked;
    } catch (e) {
      print(e);
      return false;
    }
  }

  @override
  void initState() {
    if (widget.operation != null) {
      _operation = widget.operation!;
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BaseNewarcPopup(
      title: widget.isEdit! ? "Modifica operazione" : "Aggiungi operazione",
      column: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomTextFormField(
                initialValue: _operation.addressString,
                hintText: "Inserisci un indirizzo",
                label: "Indirizzo immobile",
                onChangedCallback: (String value) {
                  widget.agency!.name = value;
                },
              ),
              SizedBox(width: 18),
              CustomTextFormField(
                initialValue: _operation.city,
                hintText: "Inserisci una città",
                label: "Città",
                onChangedCallback: (String value) {
                  widget.agency!.email = value;
                },
              ),
            ],
          ),
          SizedBox(height: 15),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomTextFormField(
                initialValue: _operation.addressString,
                hintText: "Inserisci bonus",
                label: "Bonus",
                isPercentage: true,
                onChangedCallback: (String value) {
                  widget.agency!.name = value;
                },
              ),
              SizedBox(width: 18),
              Expanded(
                child: DropdownButtonFormField(
                  decoration: InputDecoration(labelText: "Stato bonus"),
                  initialValue: _operation.bonus!.commissionState,
                  items: CommissionState.values.map((CommissionState value) {
                    return DropdownMenuItem(
                      value: value,
                      child: Text(getCommissionStateString(value)),
                    );
                  }).toList(),
                  onChanged: (CommissionState? value) {
                    setState(() {
                      _operation.bonus!.commissionState = value;
                    });
                  },
                  validator: (value) {
                    if (value == null) {
                      return "Seleziona stato bonus";
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
          SizedBox(height: 15),
          Row(
            children: [
              CustomTextFormField(
                initialValue: _operation.firstCommission!.amount.toString(),
                hintText: "Commissione",
                label: "Prima commissione",
                isPercentage: false,
                onChangedCallback: (String value) {
                  _operation.firstCommission!.amount = double.parse(value);
                  //widget.agency!.name = value;
                },
                isMoney: true,
                isNullable: true,
              ),
              /*Expanded(
                child: _buildTextFormField(
                  'Prima Commissione',
                  _operation.firstCommission!.amount.toString(),
                  (value) =>
                      _operation.firstCommission!.amount = double.parse(value!),
                  isNumber: true,
                  isMoney: true,
                  nullable: true,
                ),
              ),*/
              SizedBox(width: 8),
              Expanded(
                child: DropdownButtonFormField(
                  decoration:
                      InputDecoration(labelText: "Stato prima commissione"),
                  initialValue: _operation.firstCommission!.commissionState,
                  items: CommissionState.values.map((CommissionState value) {
                    return DropdownMenuItem(
                      value: value,
                      child: Text(getCommissionStateString(value)),
                    );
                  }).toList(),
                  onChanged: (CommissionState? value) {
                    setState(() {
                      _operation.firstCommission!.commissionState = value;
                    });
                  },
                  validator: (value) {
                    if (value == null) {
                      return "Seleziona stato lavori";
                    }
                    return null;
                  },
                ),
              )
            ],
          ),
          SizedBox(height: 15),
          Row(
            children: [
              CustomTextFormField(
                initialValue: _operation.secondCommission!.amount.toString(),
                hintText: "Commissione",
                label: "Seconda commissione",
                isPercentage: false,
                onChangedCallback: (String value) {
                  _operation.secondCommission!.amount = double.parse(value);
                  //widget.agency!.name = value;
                },
                isMoney: true,
                isNullable: true,
              ),
              SizedBox(width: 8),
              Expanded(
                child: DropdownButtonFormField(
                  decoration:
                      InputDecoration(labelText: "Stato seconda commissione"),
                  initialValue: _operation.secondCommission!.commissionState,
                  items: CommissionState.values.map((CommissionState value) {
                    return DropdownMenuItem(
                      value: value,
                      child: Text(getCommissionStateString(value)),
                    );
                  }).toList(),
                  onChanged: (CommissionState? value) {
                    setState(() {
                      _operation.secondCommission!.commissionState = value;
                    });
                  },
                  validator: (value) {
                    if (value == null) {
                      return "Seleziona stato lavori";
                    }
                    return null;
                  },
                ),
              )
            ],
          ),
          SizedBox(height: 8),
          Row(
            children: [
              CustomTextFormField(
                initialValue: _operation.minSalePrice.toString(),
                hintText: "Prezzo",
                label: "Prezzo minimo di vendita",
                isPercentage: false,
                onChangedCallback: (String value) {
                  _operation.minSalePrice = double.parse(value);
                  //widget.agency!.name = value;
                },
                isMoney: true,
                isNullable: true,
              ),
              SizedBox(width: 8),
              CustomTextFormField(
                initialValue: _operation.salePriceGoal.toString(),
                hintText: "Prezzo",
                label: "Obiettivo prezzo di vendita",
                isPercentage: false,
                onChangedCallback: (String value) {
                  _operation.salePriceGoal = double.parse(value);
                  //widget.agency!.name = value;
                },
                isMoney: true,
                isNullable: true,
              ),
            ],
          ),
          SizedBox(height: 12),
          Text(
            "Informazioni vendita",
            style: TextStyle(fontWeight: FontWeight.bold, color: Colors.grey),
          ),
          SizedBox(height: 6),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    NarFormLabelWidget(
                      label: "Stato dei lavori",
                      textColor: Color(0xff696969),
                      fontSize: 14,
                      fontWeight: '600',
                    ),
                    SizedBox(height: 4),
                    DropdownButtonFormField(
                      initialValue: _operation.workState,
                      items: WorkState.values.map((WorkState value) {
                        return DropdownMenuItem(
                          value: value,
                          child: Text(getWorkStateString(value)),
                        );
                      }).toList(),
                      onChanged: (WorkState? value) {
                        setState(() {
                          _operation.workState = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return "Seleziona stato lavori";
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
              SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    NarFormLabelWidget(
                      label: "Stato vendita",
                      textColor: Color(0xff696969),
                      fontSize: 14,
                      fontWeight: '600',
                    ),
                    SizedBox(height: 4),
                    DropdownButtonFormField(
                      initialValue: _operation.saleState,
                      items: SaleState.values.map((SaleState value) {
                        return DropdownMenuItem(
                          value: value,
                          child: Text(getSaleStateString(value)),
                        );
                      }).toList(),
                      onChanged: (SaleState? value) {
                        setState(() {
                          _operation.saleState = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return "Seleziona stato vendita";
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  _assignAgencyToOperationWidget(Function(String?) onChanged) {
    return StreamBuilder<QuerySnapshot<Map<String, dynamic>>>(
      stream:
          FirebaseFirestore.instance.collection(COLLECT_AGENCIES).snapshots(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return CircularProgressIndicator();
        }

        List<Agency> agencies = snapshot.data!.docs
            .map(
                (document) => Agency.fromDocument(document.data(), document.id))
            .toList();

        return DropdownButtonFormField(
          initialValue: _operation.assignedAgencyId,
          decoration: InputDecoration(labelText: "Agenzia"),
          items: agencies.map((Agency agency) {
            return DropdownMenuItem(
              value: agency.id,
              child: Text(agency.name!),
            );
          }).toList(),
          onChanged: onChanged,
          validator: (value) {
            if (value == null) {
              return "Seleziona un'agenzia";
            }
            return null;
          },
        );
      },
    );
  }

  _buildDatePickerField(String label, int? initialMs, Function onSelected) {
    final TextEditingController dateController = TextEditingController();
    if (initialMs != null) {
      dateController.text = getFormattedDate(initialMs);
    }
    return TextFormField(
      controller: dateController,
      decoration: InputDecoration(
        labelText: label,
      ),
      onTap: () async {
        final selectedDate = await showDatePicker(
          context: context,
          initialDate: DateTime.now(),
          firstDate: DateTime(1900),
          lastDate: DateTime(2100),
        );
        if (selectedDate != null) {
          onSelected(selectedDate.millisecondsSinceEpoch);
        }
      },
    );
  }

  _buildTextFormField(String label, String? value, Function(String?) onChanged,
      {bool isMoney = false,
      bool isNumber = false,
      bool isPercentage = false,
      bool nullable = false}) {
    if (value == "null") {
      value = "";
    }
    assert(!(isNumber && isPercentage));
    return TextFormField(
      initialValue: value,
      keyboardType: TextInputType.number,
      decoration: InputDecoration(
        prefixText: isMoney ? "€" : null,
        suffixText: isPercentage ? "%" : null,
        labelText: label,
        errorStyle: TextStyle(color: Colors.red),
      ),
      validator: (value) {
        if (nullable) {
          return null;
        }
        if (value == null || value.isEmpty) {
          return '${label} non valido';
        }
        if (isMoney && double.tryParse(value) == null) {
          return "Inserisci un numero valido";
        }
        return null;
      },
      onChanged: onChanged,
    );
  }
}
