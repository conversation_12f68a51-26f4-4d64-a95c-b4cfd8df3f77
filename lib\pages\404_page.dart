import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class Page404 extends StatefulWidget {
  final String accessType;
  const Page404({Key? key,required this.accessType}) : super(key: key);

  @override
  _Page404State createState() => _Page404State();
}

class _Page404State extends State<Page404>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    _animation =
        Tween<double>(begin: 0.9, end: 1.1).animate(CurvedAnimation(
          parent: _controller,
          curve: Curves.easeInOut,
        ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration:  BoxDecoration(
          color: widget.accessType == 'work' ? Color(0xff262626) : widget.accessType == 'professionals' ? Colors.black : Color(0xff1c1e21)
        ),
        child: SafeArea(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ScaleTransition(
                scale: _animation,
                child: Image.asset(
                  widget.accessType == 'work'
                      ? 'assets/logo.png'
                      : widget.accessType == 'professionals'
                      ? 'assets/newarc_professionals_white.png'
                      : 'assets/logo-agenzie-white.png',
                  width: 197,
                ),
              ),
              const SizedBox(height: 30),
              Text(
                '404',
                style: TextStyle(
                  fontSize: 90,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 10),
              const Text(
                'Ops!\nNon abbiamo trovato la pagina richiesta 😞',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 20,
                  color: Colors.white70,
                  height: 1.5,
                ),
              ),
              const SizedBox(height: 40),
              ElevatedButton.icon(
                onPressed: () {
                  context.go("/");
                },
                icon: const Icon(Icons.home, color: Colors.white),
                label: const Text(
                  'Torna alla Home',
                  style: TextStyle(fontSize: 16, color: Colors.white),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.accessType == 'work' ? Color(0xff499B79) : widget.accessType == 'professionals' ? const Color.fromARGB(255, 56, 56, 56) : Color(0xff13427A),
                  padding:
                  const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(30),
                  ),
                  elevation: 6,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

