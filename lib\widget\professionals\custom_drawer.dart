import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:newarc_platform/classes/professionals.dart';
import 'package:newarc_platform/routes/professional_routes.dart';
import 'package:newarc_platform/utils/color_schema.dart';


class CustomDrawer extends StatefulWidget {
  CustomDrawer(
      {Key? key,this.professionalsUser})
      : super(key: key);

  final ProfessionalsUser? professionalsUser;


  @override
  State<CustomDrawer> createState() => _CustomDrawerState();
}

class _CustomDrawerState extends State<CustomDrawer> {
  bool immaginaExpanded = false;
  ExpansibleController immaginaTile = new ExpansibleController();

  bool strumentiExpanded = false;
  ExpansibleController strumentiTile = new ExpansibleController();

  bool isDrawerOpen = true;
  String? selectedView = "immagina-progetti-attivi";

  void toggleDrawer() {
    setState(() {
      isDrawerOpen = !isDrawerOpen;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        AnimatedContainer(
          duration: Duration(milliseconds: 300),
          width: isDrawerOpen ? 257 : 80,
          color: Theme.of(context).primaryColorDark,
          child: Container(
            child: !isDrawerOpen
                ? Column(
                    children: [
                      Align(
                        alignment: Alignment.centerLeft,
                        child: Padding(
                          padding:
                              EdgeInsets.only(left: 20, right: 20, top: 42),
                          child: Image.asset(
                            "assets/logo_work_white.png",
                            height: 25,
                          ),
                        ),
                      ),
                    ],
                  )
                : Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: ListView(
                          shrinkWrap: true,
                          padding: EdgeInsets.all(0),
                          children: [
                            Align(
                              alignment: Alignment.centerLeft,
                              child: Padding(
                                padding: EdgeInsets.only(
                                    left: 20, right: 20, top: 30),
                                child: Image.asset(
                                  "assets/newarc_professionals_white.png",
                                  height: 50,
                                ),
                              ),
                            ),
                            SizedBox(height: 40),
                            menuBuilder(
                              'newarc-immagina',
                              'Newarc Immagina',
                              immaginaTile,
                              immaginaExpanded,
                              childrenLabels: ['Progetti attivi', 'Progetti archiviati'],
                              childrenViews: ['immagina-progetti-attivi', 'immagina-progetti-archiviati'],
                            ),
                            // menuBuilder(
                            //   'strumenti',
                            //   'Strumenti',
                            //   strumentiTile,
                            //   strumentiExpanded,
                            //   childrenLabels: ["Richiedi preventivo"],
                            //   childrenViews: ["strumenti-richiedi-preventivo"],
                            // ),
                          ],
                        ),
                      ),
                    ],
                  ),
          ),
        ),
        Positioned(
          right: -10,
          top: 42,
          child: InkWell(
            onTap: toggleDrawer,
            child: Container(
              height: 28,
              width: 28,
              child: Center(
                child: SvgPicture.asset(
                  isDrawerOpen
                      ? "assets/icons/arrow_left.svg"
                      : "assets/icons/arrow_right.svg",
                  color: AppColor.drawerIconButtonColor,
                  height: 10,
                ),
              ),
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                      offset: Offset(0, 10),
                      blurRadius: 10,
                      color: AppColor.black.withOpacity(0.1))
                ],
                color: AppColor.drawerButtonColor,
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        )
      ],
    );
  }

  menuBuilder(String viewKey, String label, ExpansibleController controller, bool expanded, {required List<String> childrenLabels, required List<String> childrenViews}){
    Map <String, String> iconPath = {
      'newarc-immagina': 'assets/icons/ic_newarc.svg',
      'strumenti': 'assets/icons/ic_report.svg',
    };
    return Theme(
      data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
      child: ExpansionTile(
        key: Key(viewKey),
        controller: controller,
        initiallyExpanded: expanded,
        onExpansionChanged: ((value) => setState(
              () {
                expanded = value;
              },
            )),
        leading: SizedBox(
          height: 30,
          width: 30,
          child: Center(
            child: SvgPicture.asset(
              iconPath[viewKey]!,
              height: 20,
              width: 21,
              alignment: Alignment.center,
            ),
          ),
        ),
        collapsedIconColor: Colors.white,
        iconColor: Colors.white,
        minTileHeight: 0,
        childrenPadding: EdgeInsets.symmetric(horizontal: 15),
        tilePadding: EdgeInsets.only(left: 15, right: 15, top: 5),
        title: Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontFamily: 'semi-bold',
            color: Colors.white,
          ),
        ),
        trailing: SvgPicture.asset(
          expanded ? 'assets/icons/arrow_up.svg' : 'assets/icons/arrow_down.svg',
          height: 6,
          width: 10,
          color: Colors.white,
        ),
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.only(left: 42),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: childrenLabels.map((e) {
                int index = childrenLabels.indexOf(e);
                return InkWell(
                  onTap: () {
                    selectedView = childrenViews[index];
                    log("childrenViews[index] ----< ${childrenViews[index]}");
                    if(childrenViews[index] == "immagina-progetti-attivi"){
                      log("PROJECT ACTIVE");
                      context.go(ProfessionalRoutes.professionalImmagina("progetti-attivi"));
                    }else if(childrenViews[index] == "immagina-progetti-archiviati"){
                      context.go(ProfessionalRoutes.professionalImmagina("progetti-archiviati"));
                    }
                    setState(() {});
                  },
                  child: Row(
                    children: [
                      Container(
                        margin: EdgeInsets.only(bottom: 8),
                        child: Text(
                          e,
                          style: TextStyle(
                            fontSize: 14,
                            fontFamily: 'Raleway-400',
                            color: getColor(childrenViews[index]),
                          ),
                        ),
                      )
                    ],
                  ),
                );
              }).toList()
            ),
          ),
        ],
      ),
    );
  }

  Color? getColor(String view) {
    if (selectedView == view) {
      return Color(0xff499B79); //Color(0xff489B79);
    } else {
      return Colors.white; //Color(0xff7D7D7D);
    }
  }
}

