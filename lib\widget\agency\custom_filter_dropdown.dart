import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';

getColor(String status) {
  switch (status) {
    case 'Da contattare':
      return Color(0xff5FBCEC);
    case 'Contattato':
      return Color(0xffFFC633);
    case 'Non interessato':
      return Color(0xffFF5E53);
    case 'Acquisito':
      return Color(0xff489B79);
  }
}

class CustomFilterDropdown extends StatefulWidget {
  final Function onChangeDropdownTests;

  const CustomFilterDropdown({Key? key, required this.onChangeDropdownTests})
      : super(key: key);

  @override
  State<CustomFilterDropdown> createState() => _CustomButtonTestState();
}

class _CustomButtonTestState extends State<CustomFilterDropdown> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: DropdownButtonHideUnderline(
          child: DropdownButton2(
            customButton: Container(
              height: 80,
              width: 160,
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        "Filtra per stato",
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Icon(
                      Icons.filter_alt,
                      color: Colors.white,
                    )
                  ],
                ),
              ),
            ),
            items: [
              ...MenuItems.firstItems.map(
                (item) => DropdownMenuItem<MenuItem>(
                  value: item,
                  child: MenuItems.buildItem(item),
                ),
              ),
            ],
            onChanged: (value) {
              value as MenuItem;

              MenuItems.onChanged(context, value, widget.onChangeDropdownTests);
            },
            menuItemStyleData: MenuItemStyleData(
              height: 48,
              padding: const EdgeInsets.only(left: 16, right: 16),
            ),
            dropdownStyleData: DropdownStyleData(
              width: 160,
              padding: const EdgeInsets.symmetric(vertical: 6),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                //color: getColor(item) //Theme.of(context).primaryColor,
              ),
              elevation: 8,
              offset: const Offset(0, 8),
            ),
            // itemHeight: 48,
            // itemPadding: const EdgeInsets.only(left: 16, right: 16),
            // dropdownWidth: 160,
            // dropdownPadding: const EdgeInsets.symmetric(vertical: 6),
            // dropdownDecoration: BoxDecoration(
            //   borderRadius: BorderRadius.circular(10),
            //   //color: getColor(item) //Theme.of(context).primaryColor,
            // ),
            // dropdownElevation: 8,
            // offset: const Offset(0, 8),
          ),
        ),
      ),
    );
  }
}

class MenuItem {
  final String text;
  final IconData icon;

  const MenuItem({
    required this.text,
    required this.icon,
  });
}

class MenuItems {
  static const List<MenuItem> firstItems = [home, share, settings, logout];

  static const home = MenuItem(text: 'Da contattare', icon: Icons.home);
  static const share = MenuItem(text: 'Contattato', icon: Icons.share);
  static const settings =
      MenuItem(text: 'Non interessato', icon: Icons.settings);
  static const logout = MenuItem(text: 'Acquisito', icon: Icons.logout);

  static Widget buildItem(MenuItem item) {
    return Row(
      children: [
        Text(
          item.text,
          style:
              const TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
        ),
        SizedBox(width: 10),
        Container(
          height: 8,
          width: 8,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: getColor(item.text),
          ),
        ),
      ],
    );
  }

  static onChanged(
      BuildContext context, MenuItem item, Function onChangeDropdownTests) {
    onChangeDropdownTests(item.text);
  }
}
