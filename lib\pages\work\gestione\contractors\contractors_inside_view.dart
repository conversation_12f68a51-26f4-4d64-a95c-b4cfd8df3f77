import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:country_picker/country_picker.dart';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:newarc_platform/utils/inputFormatters.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/classes/supplier.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/basePersonInfo.dart';
import 'package:newarc_platform/pages/work/gestione/contractors/contractors_controller.dart';
import 'package:newarc_platform/widget/UI/address_completion_google.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/file-picker.dart';
import 'package:newarc_platform/widget/UI/multi-select-dropdown.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/app_const.dart' as appConst;
import 'package:newarc_platform/app_config.dart' as appConfig;


class ContractorsInsideView extends StatefulWidget {
  final String id;
  const ContractorsInsideView({super.key, required this.id});

  @override
  State<ContractorsInsideView> createState() => _ContractorsInsideViewState();
}

class _ContractorsInsideViewState extends State<ContractorsInsideView> {
  final controller = Get.put<ContractorsController>(ContractorsController());
  final List<String> documents=[];
  final _formKey = GlobalKey<FormState>();
  bool  isLoading = false;
  Supplier contractor = Supplier.empty();

  Future<bool> updateContractorActiveStatus(Supplier contractor) async {
    final FirebaseFirestore _db = FirebaseFirestore.instance;
    return await _db
        .collection(appConfig.COLLECT_SUPPLIERS)
        .doc(contractor.id)
        .update(contractor.toMap())
        .then((value) {
      return true;
    }).onError((error, stackTrace) {
      // print({error, stackTrace});
      return false;
    });
  }

  Future<bool> updateData(BuildContext context) async {
    setState(() {
      controller.validationMessage = "";
    });
    contractor.firstName = controller.contactName.text;
    contractor.lastName = controller.contactSurname.text;
    contractor.phone = controller.contractorPhone.text;
    contractor.email = controller.contractorEmail.text;
    contractor.pec = controller.contractorPEC.text;
    contractor.nationality= controller.contractorNationality.text;
    contractor.contactPersonInfo = BasePersonInfo.fromMap(
        {
          'name': controller.contactName.text,
          'surname': controller.contactSurname.text,
          'phone': controller.contactPhone.text,
          'email': controller.contactEmail.text
        }
    );
    contractor.name = controller.contractorName.text;
    contractor.formationType = controller.formationType.text;
    contractor.city = controller.contractorOperativeAddress.city;
    contractor.addressAndCivicNumber = controller.contractorOperativeAddress.fullAddress;
    contractor.iban = controller.contractorIban.text;
    contractor.billingCode = controller.contractorBillingCode.text;
    contractor.iva = controller.contractorVat.text;
    contractor.legalAddress = controller.contractorLegalAddress.fullAddress;
    contractor.operativeAddressInfo = controller.contractorOperativeAddress;
    contractor.legalAddressInfo = controller.contractorLegalAddress;
    contractor.legalEntity = controller.contractorLegalEntity.text;
    contractor.fiscalCode = controller.contractorFiscalCode.text;
    contractor.modificationTimestamp = DateTime.now().millisecondsSinceEpoch;

    contractor.legalRepresentativePersonInfo = LegalRepresentativePersonInfo.fromMap({
      'name': controller.legalRepresentativeName.text,
      'surname': controller.legalRepresentativeSurname.text,
      'phone': controller.legalRepresentativePhone.text,
      'email': controller.legalRepresentativeEmail.text,
      'pec': controller.legalRepresentativePEC.text,
      'fiscalCode': controller.legalRepresentativeFiscalCode.text,
      'birthCountry': controller.legalRepresentativeBirthCountry.text,
      'birthProvince': controller.legalRepresentativeBirthProvince.text,
      'birthCity': controller.legalRepresentativeBirthCity.text,
      'sex': controller.legalRepresentativeSex.text,
      'birthDate': controller.legalRepresentativeBirthDate.text,
      'residentialAddressInfo': controller.legalRepresentativeAddress.toMap()
    });

    contractor.activities!.clear();
    controller.selectedActivities.map((element) {
      contractor.activities!.add(element['value']);
    }).toList();

    contractor.documents!.clear();
    for (var i = 0; i < documents.length; i++) {
      contractor.documents!.add(documents[i]);
    }
    final FirebaseFirestore _db = FirebaseFirestore.instance;
    await _db
        .collection(appConfig.COLLECT_SUPPLIERS)
        .doc(contractor.id)
        .update(contractor.toMap());

    return true;
  }

  Future onVendorFileUploadasync() async {

    final FirebaseFirestore _db = FirebaseFirestore.instance;
    // supplier.documents!.clear();
    for (var i = 0; i < documents.length; i++) {
      contractor.documents!.add(documents[i]);
    }

    try {
      await _db
          .collection(appConfig.COLLECT_SUPPLIERS)
          .doc(contractor.id)
          .update(contractor.toMap());

      // documents.clear();

      if (mounted) {
        setState(() {
          controller.fileProgressMessage.clear();
          controller.fileProgressMessage.add('Saved!');
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          controller.fileProgressMessage.clear();
          controller.fileProgressMessage.add('Error');
        });
      }
    }
  }

  @override
  void initState() {
    super.initState();
    initContractor();
  }

  initContractor()async{
    setState(() => isLoading = true);
    log("widget.id ===> ${widget.id}");
    try{
      DocumentSnapshot<Map<String, dynamic>> docSnap =
      await FirebaseFirestore.instance.collection(appConfig.COLLECT_SUPPLIERS).doc(widget.id).get();

      if(docSnap.exists && docSnap.data() != null){
        contractor = Supplier.fromDocument(docSnap.data()!, docSnap.id);
      }

      // Initialize form fields directly for immediate display
      controller.selectedActivities.clear();
      controller.contractorOperativeAddress = contractor.operativeAddressInfo ?? BaseAddressInfo.empty();
      controller.contractorLegalAddress = contractor.legalAddressInfo ?? BaseAddressInfo.empty();

      // Process activities for dropdown
      if (contractor.activities != null && contractor.activities!.isNotEmpty) {
        for (var activity in contractor.activities!) {
          var matchingOption = controller.jobOptions.firstWhere(
                  (option) => option['value'] == activity,
              orElse: () => {"value": "", "label": ""}
          );
          if (matchingOption["label"] != "") {
            controller.selectedActivities.add(matchingOption);
          }
        }
      }

      // Handle documents safely
      documents.clear();
      if (contractor.documents != null) {
        for (var doc in contractor.documents!) {
          if (doc is String) {
            documents.add(doc);
          }
        }
      }


      controller.contactName.text = contractor.contactPersonInfo?.name ?? contractor.firstName ?? "";
      controller.contactSurname.text = contractor.contactPersonInfo?.surname ?? contractor.lastName ?? "";
      controller.contactPhone.text = contractor.contactPersonInfo?.phone ?? contractor.phone ?? "";
      controller.contactEmail.text = contractor.contactPersonInfo?.email ?? contractor.email ?? "";
      controller.contractorName.text = contractor.name ?? "";
      controller.formationType.text = contractor.formationType ?? "";
      controller.contractorLegalEntity.text = contractor.legalEntity ?? "";
      controller.contractorIban.text = contractor.iban ?? "";
      controller.contractorBillingCode.text = contractor.billingCode ?? "";
      controller.contractorVat.text = contractor.iva ?? "";
      controller.contractorFiscalCode.text = contractor.fiscalCode ?? "";
      controller.contractorPhone.text = contractor.phone ?? "";
      controller.contractorEmail.text = contractor.email ?? "";
      controller.contractorPEC.text = contractor.pec ?? "";
      controller.contractorNationality.text = contractor.nationality ?? "";

      controller.legalRepresentativeName.text = contractor.legalRepresentativePersonInfo?.name ?? "";
      controller.legalRepresentativeSurname.text = contractor.legalRepresentativePersonInfo?.surname ?? "";
      controller.legalRepresentativePhone.text = contractor.legalRepresentativePersonInfo?.phone ?? "";
      controller.legalRepresentativeEmail.text = contractor.legalRepresentativePersonInfo?.email ?? "";
      controller.legalRepresentativePEC.text = contractor.legalRepresentativePersonInfo?.pec ?? "";
      controller.legalRepresentativeFiscalCode.text = contractor.legalRepresentativePersonInfo?.fiscalCode ?? "";
      controller.legalRepresentativeBirthCountry.text = contractor.legalRepresentativePersonInfo?.birthCountry ?? "";
      controller.legalRepresentativeBirthProvince.text = contractor.legalRepresentativePersonInfo?.birthProvince ?? "";
      controller.legalRepresentativeBirthCity.text = contractor.legalRepresentativePersonInfo?.birthCity ?? "";
      controller.legalRepresentativeSex.text = contractor.legalRepresentativePersonInfo?.sex ?? "";
      controller.legalRepresentativeBirthDate.text = contractor.legalRepresentativePersonInfo?.birthDate ?? "";

    }catch(e,s){
      log("Error while loading contractor ==> ${e.toString()}");
      log(s.toString());
    }finally{
      setState(() => isLoading = false);
    }

  }

  @override
  Widget build(BuildContext context) {
    var containerWidth = MediaQuery.of(context).size.width * .75;
    return isLoading ?
    Center(
      child: CircularProgressIndicator(color: Theme.of(context).primaryColor,),
    )
        :
    SingleChildScrollView(
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      hoverColor: Colors.transparent,
                      focusColor: Colors.transparent,
                      onPressed: () {
                        controller.clearInsideViewController();
                        documents.clear();
                        context.pop();
                      },
                      icon: SvgPicture.asset('assets/icons/arrow_left.svg',
                          height: 20, color: Colors.black),
                    ),
                    SizedBox(
                      width: 10,
                    ),
                    NarFormLabelWidget(
                      label:
                          contractor.name! + ' ' + contractor.formationType!,
                      fontSize: 20,
                      fontWeight: 'bold',
                    ),
                  ],
                ),
                Row(
                  children: [
                    NarFormLabelWidget(
                      label: 'Attiva/Disattiva',
                      fontSize: 15,
                      fontWeight: '500',
                    ),
                    Switch(
                      // This bool value toggles the switch.
                      value: contractor.isActive!,
                      activeThumbColor: Theme.of(context).primaryColor,
                      onChanged: (bool value) async {
                        // This is called when the user toggles the switch.
                        setState(() {
                          contractor.isActive = value;
                        });
                        await updateContractorActiveStatus(contractor);
                      },
                    ),
                  ],
                )
              ],
            ),
            SizedBox(height: 35),
            NarFormLabelWidget(
              label: 'Dati Ditta',
              fontSize: 18,
              fontWeight: 'bold',
            ),
            SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomTextFormField(
                  label: 'Nome Ditta',
                  controller: controller.contractorName,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Obbligatorio';
                    }
                    return null;
                  },
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            AddressSearchBar(
              label: "Indirizzo sede operativa",
              initialAddress: contractor.operativeAddressInfo?.fullAddress ?? contractor.addressAndCivicNumber ?? "",
              // validator: (value) {
              //   if (value == null || value.isEmpty) {
              //     return 'Obbligatorio';
              //   }
              //   return null;
              // },
              onPlaceSelected: (selectedPlace){
                log('Selected place: \n$selectedPlace');
                BaseAddressInfo selectedAddress = BaseAddressInfo.fromMap(selectedPlace['place']);
                if (selectedAddress.isValidAddress()){
                  controller.contractorOperativeAddress = selectedAddress;
                } else {
                  controller.contractorOperativeAddress = BaseAddressInfo.empty();
                }
              }
            ),
            SizedBox(
              height: 15,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      NarFormLabelWidget(
                        label: "Attività",
                        textColor: Color(0xff696969),
                        fontSize: 13,
                        fontWeight: '600',
                      ),
                      SizedBox(height: 4),
                      MultiSelectDropdownWidget(
                        options: controller.jobOptions,
                        initialValue: controller.selectedActivities,
                        // validationType: 'required',
                        // parametersValidate: 'Obbligatorio',
                        onChanged: (List<dynamic> selectedValues) {
                          controller.selectedActivities = selectedValues;
                          setState(() {});
                        },
                      )
                    ],
                  ),
                ),
                SizedBox(
                  width: 15,
                ),
                Expanded(flex: 1, child: Container(),),
                SizedBox(
                  height: 0,
                )
              ],
            ),
            SizedBox(
              height: 15,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomTextFormField(
                  label: 'E-mail (login)',
                  controller: controller.contractorEmail,
                  readOnly: true,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
                SizedBox(
                  width: 15
                ),
                CustomTextFormField(
                  label: 'PEC',
                  controller: controller.contractorPEC,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            Row(
              mainAxisAlignment:
                  MainAxisAlignment.center,
              children: [
                Container(
                  width: containerWidth * .525,
                  child: CustomTextFormField(
                    isExpanded: false,
                    textAlign: TextAlign.left,
                    isHaveBorder: true,
                    isCenterLabel: false,
                    flex: 0,
                    suffixIcon: Container(
                      padding: const EdgeInsets.all(10),
                      height: 20,
                      width: 20,
                      // child: Icon(Icons.web),
                    ),
                    // readOnly: true,
                    label: "Nazionalità",
                    controller: controller.contractorNationality,
                    onTap: () {
                      showCountryPicker(
                        context: context,
                        showPhoneCode: false, // Set to true if you want dial codes
                        onSelect: (Country country) {
                          setState(() {
                            controller.contractorNationality.text = country.name;
                          });
                        },
                      );
                    },
                  ),
                ),
                SizedBox(
                  width: 15,
                ),
                CustomTextFormField(
                  label:
                      "Telefono",
                  controller: controller.contractorPhone,
                  inputFormatters: [phoneNumberMaskFormatterInternational],
                  // validator: (value) {
                  //   if (value == '') {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
              ],
            ),
            SizedBox(height:15),
            Divider(
              color: Color(0xffd7d7d7d7),
              thickness: 1,
            ),
            SizedBox(
              height: 15,
            ),
            NarFormLabelWidget(
              label: 'Persona di riferimento',
              fontSize: 18,
              fontWeight: 'bold',
            ),
            SizedBox(
              height: 20,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                CustomTextFormField(
                  label: 'Nome',
                  controller: controller.contactName,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
                SizedBox(
                  width: 15,
                ),
                CustomTextFormField(
                  label: 'Cognome',
                  controller: controller.contactSurname,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                CustomTextFormField(
                  label: 'Telefono',
                  controller: controller.contactPhone,
                  inputFormatters: [phoneNumberMaskFormatterInternational],
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
                SizedBox(
                  width: 15,
                ),
                CustomTextFormField(
                  label: 'E-mail',
                  controller: controller.contactEmail,
                  // readOnly: true,
                ),
              ],
            ),
            SizedBox(height:15),
            Divider(
              color: Color(0xffd7d7d7d7),
              thickness: 1,
            ),
            SizedBox(
              height: 15,
            ),
            NarFormLabelWidget(
              label: 'Legale Rappresentante',
              fontSize: 18,
              fontWeight: 'bold',
            ),
            SizedBox(
              height: 20,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                CustomTextFormField(
                  label: 'Nome',
                  controller: controller.legalRepresentativeName,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
                SizedBox(
                  width: 15,
                ),
                CustomTextFormField(
                  label: 'Cognome',
                  controller: controller.legalRepresentativeSurname,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                CustomTextFormField(
                  label: 'Telefono',
                  controller: controller.legalRepresentativePhone,
                  inputFormatters: [phoneNumberMaskFormatterInternational],
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
                SizedBox(
                  width: 15,
                ),
                CustomTextFormField(
                  label: 'E-mail',
                  controller: controller.legalRepresentativeEmail,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                CustomTextFormField(
                  label: 'PEC',
                  controller: controller.legalRepresentativePEC,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
                SizedBox(
                  width: 15,
                ),
                CustomTextFormField(
                  label: 'Codice Fiscale',
                  controller: controller.legalRepresentativeFiscalCode,
                  inputFormatters: [codiceFiscaleMaskFormatter],
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            Row(
              mainAxisAlignment:
                  MainAxisAlignment.center,
              children: [
                Container(
                  width: containerWidth * .525,
                  child: CustomTextFormField(
                    isExpanded: false,
                    textAlign: TextAlign.left,
                    isHaveBorder: true,
                    isCenterLabel: false,
                    flex: 0,
                    suffixIcon: Container(
                      padding: const EdgeInsets.all(10),
                      height: 20,
                      width: 20,
                      // child: Icon(Icons.web),
                    ),
                    // readOnly: true,
                    label: "Stato di nascita",
                    controller: controller.legalRepresentativeBirthCountry,
                    onTap: () {
                      showCountryPicker(
                        context: context,
                        showPhoneCode: false, // Set to true if you want dial codes
                        onSelect: (Country country) {
                          setState(() {
                            controller.legalRepresentativeBirthCountry.text = country.name;
                          });
                        },
                      );
                    },
                  ),
                ),
                SizedBox(
                  width: 15,
                ),
                CustomTextFormField(
                  label:
                      "Provincia di nascita",
                  controller: controller.legalRepresentativeBirthProvince,
                  // validator: (value) {
                  //   if (value == '') {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                CustomTextFormField(
                  label: 'Città di nascita',
                  controller: controller.legalRepresentativeBirthCity,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
                SizedBox(
                  width: 15,
                ),
                CustomTextFormField(
                  label: 'Sesso',
                  controller: controller.legalRepresentativeSex,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            AddressSearchBar(
              label: "Indirizzo di residenza",
              initialAddress: contractor.legalRepresentativePersonInfo?.residentialAddressInfo?.fullAddress ?? "",
              // validator: (value) {
              //   if (value == null || value.isEmpty) {
              //     return 'Obbligatorio';
              //   }
              //   return null;
              // },
              onPlaceSelected: (selectedPlace){
                log('Selected place: \n$selectedPlace');
                BaseAddressInfo selectedAddress = BaseAddressInfo.fromMap(selectedPlace['place']);
                if (selectedAddress.isValidAddress()){
                  controller.legalRepresentativeAddress = selectedAddress;
                } else {
                  controller.legalRepresentativeAddress = BaseAddressInfo.empty();
                }
              }
            ),
            SizedBox(
              height: 15,
            ),
            CustomTextFormField(
              textAlign: TextAlign.left,
              isHaveBorder: true,
              isCenterLabel: false,
              flex: 0,
              suffixIcon: Container(
                padding: const EdgeInsets.all(10),
                height: 20,
                width: 20,
                child: Image.asset('assets/icons/calendar.png'),
              ),
              readOnly: true,
              label: "Data di nascita legale rappresentante",
              controller: controller.legalRepresentativeBirthDate,
              onTap: () async {
                DateTime? pickedDate = await showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime(1930),
                  lastDate: DateTime(DateTime.now().year + 1),
                );
                if (pickedDate != null) {
                  controller.selectedLegalRepresentativeBirthDate = pickedDate.millisecondsSinceEpoch;
                  String formattedDate = DateFormat('dd/MM/yyyy').format(pickedDate);
                  controller.legalRepresentativeBirthDate.text = formattedDate;
                } else {
                  log("No Date Selected");
                }
              },
            ),
            SizedBox(
              height: 15,
            ),
            Divider(
              color: Color(0xffd7d7d7d7),
              thickness: 1,
            ),
            SizedBox(
              height: 15,
            ),
            NarFormLabelWidget(
              label: 'Fatturazione e pagamenti',
              fontSize: 18,
              fontWeight: 'bold',
            ),
            SizedBox(
              height: 20,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                CustomTextFormField(
                  label: 'Denominazione',
                  controller: controller.contractorLegalEntity,
                  textCapitalization: TextCapitalization.words,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
                SizedBox(
                  width: 15,
                ),
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      NarFormLabelWidget(
                        label: "Forma Societaria",
                        textColor: Color(0xff696969),
                        fontSize: 13,
                        fontWeight: '600',
                      ),
                      SizedBox(height: 4),
                      NarSelectBoxWidget(
                        options: appConst.supplierFormationTypesList,
                        controller: controller.formationType,
                        // validationType: 'required',
                        // parametersValidate: 'Required!',
                        onChanged: (value){
                          setState((){});
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                CustomTextFormField(
                  label: 'IBAN',
                  controller: controller.contractorIban,
                  textCapitalization: TextCapitalization.words,
                  inputFormatters: [ibanMaskFormatter],
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
                SizedBox(
                  width: 15,
                ),
                CustomTextFormField(
                  label: 'Codice Fatturazione Elettronico',
                  controller: controller.contractorBillingCode,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                CustomTextFormField(
                  label: 'P.IVA',
                  controller: controller.contractorVat,
                  inputFormatters: [ivaMaskFormatter],
                  // validator: (value) {
                  //   if (value == null || value.isEmpty || !RegExp(r"^(IT)?[0-9]{11}$").hasMatch(value)) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
                SizedBox(
                  width: 15,
                ),
                CustomTextFormField(
                  label: 'Codice Fiscale',
                  enabled : controller.formationType.text == 'Impresa individuale',
                  controller: controller.contractorFiscalCode,
                  inputFormatters: [codiceFiscaleMaskFormatter],
                  // validator: (value) {
                  //   if (controller.formationType.text != 'Impresa individuale') {
                  //     return null;
                  //   }
                  //   if (value == null || value.isEmpty || value.length < 16) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            AddressSearchBar(
              label: "Indirizzo sede legale",
              initialAddress: contractor.legalAddressInfo?.fullAddress ?? contractor.legalAddress ?? "",
              // validator: (value) {
              //   if (value == null || value.isEmpty) {
              //     return 'Obbligatorio';
              //   }
              //   return null;
              // },
              onPlaceSelected: (selectedPlace){
                log('Selected place: \n$selectedPlace');
                BaseAddressInfo selectedAddress = BaseAddressInfo.fromMap(selectedPlace['place']);
                if (selectedAddress.isValidAddress()){
                  controller.contractorLegalAddress = selectedAddress;
                } else {
                  controller.contractorLegalAddress = BaseAddressInfo.empty();
                }
              }
            ),
            SizedBox(
              height: 15,
            ),
            Divider(
              color: Color(0xffd7d7d7d7),
              thickness: 1,
            ),
            SizedBox(
              height: 15,
            ),
            Row(
              children: [
                Expanded(
                  child: ListView(
                    shrinkWrap: true,
                    // crossAxisAlignment: CrossAxisAlignment.start,
                    // mainAxisSize: MainAxisSize.max,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          NarFormLabelWidget(
                            label: 'Documenti',
                            fontSize: 18,
                            fontWeight: 'bold',
                          ),
                          NarFilePickerWidget(
                            allowMultiple: true,
                            displayFormat: 'inline-button',
                            borderRadius: 7,
                            fontSize: 11,
                            fontWeight: '600',
                            text: 'Carica documenti',
                            borderSideColor: Theme.of(context).primaryColor,
                            hoverColor: Color.fromRGBO(133, 133, 133, 1),
                            allFiles: documents,
                            pageContext: context,
                            storageDirectory:
                                'suppliers/${widget.id}/',
                            progressMessage: controller.fileProgressMessage,
                            onUploadCompleted: onVendorFileUploadasync,
                          )
                        ],
                      ),
                      SizedBox(height: 30),
                      Container(
                        width: 350,
                        child: ListView.builder(
                            shrinkWrap: true,
                            itemCount: 1,
                            itemBuilder: (context, index) {
                              return Column(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    NarFilePickerWidget(
                                      allowMultiple: false,
                                      filesToDisplayInList: 0,
                                      removeButton: true,
                                      isDownloadable: true,
                                      removeButtonText: 'Elimina',
                                      uploadButtonPosition: 'back',
                                      showMoreButtonText: '+ espandi',
                                      actionButtonPosition: 'bottom',
                                      displayFormat: 'inline-widget',
                                      containerWidth: 110,
                                      containerHeight: 110,
                                      containerBorderRadius: 13,
                                      borderRadius: 7,
                                      fontSize: 11,
                                      fontWeight: '600',
                                      text: 'Carica Progetto',
                                      borderSideColor:
                                          Theme.of(context).primaryColor,
                                      hoverColor:
                                          Color.fromRGBO(133, 133, 133, 1),
                                      allFiles: documents,
                                      pageContext: context,
                                      storageDirectory:
                                          'suppliers/${widget.id}/',
                                      removeExistingOnChange: true,
                                      progressMessage: controller.fileProgressMessage,
                                      onUploadCompleted: onVendorFileUploadasync,
                                    )
                                  ]);
                            }),
                      )
                    ],
                  ),
                ),
              ],
            ),
            NarFormLabelWidget(
              label: controller.validationMessage != '' ? controller.validationMessage : '',
              fontSize: 12,
              textColor: Colors.red,
            ),
            SizedBox(
              height: 50,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              mainAxisSize: MainAxisSize.max,
              children: [
                NarFormLabelWidget(
                  label: controller.progressMessage != '' ? controller.progressMessage : '',
                  fontSize: 12,
                )
              ],
            ),
            SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                BaseNewarcButton(
                  buttonText: "Salva",
                  onPressed: () async {
                    if (_formKey.currentState!.validate()) {
                      setState(() {
                        controller.progressMessage = 'Salvataggio in corso...';
                      });
                      bool response = await updateData(context);
                      if (response == true) {
                        setState(() {
                          controller.progressMessage = '';
                        });
                        await showAlertDialog(context, "Salvataggio",
                            "Informazioni ditta salvate con successo");
                      } else {
                        setState(() {
                          controller.progressMessage =
                              'Si è verificato un errore. Contatta l\'assistenza.';
                        });
                      }
                    }
                  }
                )
              ],
            )
            // Row(
            //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //   mainAxisSize: MainAxisSize.max,
            //   children: [
            //     BaseNewarcButton(
            //         notAccent: true,
            //         buttonText: "Elimina ditta",
            //         onPressed: () async {
            //           setState(() {
            //             progressMessage = 'Eliminazione in corso';
            //           });
            //           bool unlock = await showAlertDialog(
            //               context,
            //               "Conferma eliminazione",
            //               "Sei sicuro di voler eliminare questa ditta?",
            //               addCancel: true);
            //           if (unlock) {
            //             bool response = await updateData(context, isArchive: true);
            //             if (response == true) {
            //               widget.fetchSupplier!();
            //             } else {
            //               setState(() {
            //                 progressMessage =
            //                     'Si è verificato un errore. Contatta l\'assistenza.';
            //               });
            //             }
            //           }
            //         }),
            //     BaseNewarcButton(
            //         buttonText: "Salva",
            //         onPressed: () async {
            //           setState(() {
            //             progressMessage = 'Salvataggio in corso...';
            //           });
            //           bool response = await updateData(context, isArchive: false);
            //           if (response == true) {
            //             setState(() {
            //               progressMessage = '';
            //               //widget.getProfilePicture!();
            //             });
            //             await showAlertDialog(context, "Salvataggio",
            //                 "Informazioni ditta salvate con successo");

            //             widget.updateSelectedSupplier!();
            //           } else {
            //             setState(() {
            //               progressMessage =
            //                   'Si è verificato un errore. Contatta l\'assistenza.';
            //             });
            //           }
            //         })
            //   ],
            // )
          ],
        ),
      ),
    );
  }
}