
import 'package:flutter/cupertino.dart';

import 'newarcAdMaterialConfiguration.dart';

class NewarcAdElementConfiguration {
  String? configurationType;
  List? newarcAdMaterialConfigurationIDS;
  List<NewarcAdMaterialConfiguration>? newarcAdMaterialConfiguration;
  double? size;
  bool? isActive;
  String? roomConfigurationId;
  String? id;

  //for UI

  TextEditingController sizeController = TextEditingController();

  // Getter for size (if null, get from controller)
  double get computedSize => size ?? double.tryParse(sizeController.text.replaceAll(',', '.')) ?? 0.0;


  Map<String, Object?> toMap() {
    return {
      'size': computedSize,
      'configurationType': configurationType,
      'isActive': isActive,
      'newarcAdMaterialConfigurationIDS': newarcAdMaterialConfigurationIDS,
      'roomConfigurationId': roomConfigurationId,
      'id': id,
      'newarcAdMaterialConfiguration': newarcAdMaterialConfiguration?.map((element) => element.toMap()).toList(),
    };
  }
  NewarcAdElementConfiguration.empty() {
    this.configurationType = '';
    this.size = 0.0;
    this.isActive = false;
    this.newarcAdMaterialConfigurationIDS = [];
    this.newarcAdMaterialConfiguration = [];
    this.roomConfigurationId = '';
    this.id = '';
    this.sizeController = TextEditingController(text: "");
  }
  NewarcAdElementConfiguration.fromDocument(Map<String, dynamic> data) {
    try {
      this.size = data['size'];
      this.configurationType = data['configurationType'];
      this.isActive = data['isActive'];
      this.roomConfigurationId = data['roomConfigurationId'];
      this.id = data['id'];
      sizeController.text = size?.toString() ?? "";
      this.newarcAdMaterialConfigurationIDS = data['newarcAdMaterialConfigurationIDS'] ?? [];
      this.newarcAdMaterialConfiguration = [];
      if (data['newarcAdMaterialConfiguration'] != null) {
        for (var i = 0; i < data['newarcAdMaterialConfiguration'].length; i++) {
          this.newarcAdMaterialConfiguration?.add(NewarcAdMaterialConfiguration.fromDocument(data['newarcAdMaterialConfiguration'][i]));
        }
      }
    } catch (e, s) {
      print({ 'NewarcAdElementConfiguration Class Error ------->', e, s});
    }
  }
}