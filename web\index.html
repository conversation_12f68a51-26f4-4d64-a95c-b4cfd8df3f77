<!DOCTYPE html>
<html>
<head>
  <base href="/">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Newarc Platform">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="Newarc Platform">

  <!-- Favicon (dynamic per subdomain) -->
  <link id="favicon" rel="icon" type="image/x-icon" href="">
  <script>
    const subdomain = window.location.hostname.split('.')[0];
    let favicon = (subdomain === 'agenzie') ? 'favicon_agenzie.png' : 'favicon.png';
    document.getElementById('favicon').href = favicon;
  </script>

  <title>Newarc Platform</title>
  <link rel="manifest" href="manifest.json">

  <!-- External Libraries -->
  <script src="//cdnjs.cloudflare.com/ajax/libs/pdf.js/2.4.456/pdf.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
  <script src="pdf_to_jpeg.js"></script>
  <script type="text/javascript">
    pdfjsLib.GlobalWorkerOptions.workerSrc = "//cdnjs.cloudflare.com/ajax/libs/pdf.js/2.4.456/pdf.worker.min.js";
  </script>
  <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCH2BcaOkNgjYZqigrewdxXyz5atnsfdRo"></script>

  <!-- 🔹 Styles -->
  <style>
    html, body {
      overscroll-behavior-x: none;
      margin: 0;
      padding: 0;
    }

    /* 🔹 Preloader */
    #preloader {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: #262626;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9999;
    }

    #preloader img {
      width: 197px;
      animation: pulse 1.5s infinite ease-in-out;
    }

    @keyframes pulse {
      0% { transform: scale(1); opacity: 1; }
      50% { transform: scale(1.1); opacity: 0.85; }
      100% { transform: scale(1); opacity: 1; }
    }
  </style>
</head>
<body>
<!-- 🔹 Preloader (instant before Flutter boots) -->
<div id="preloader">
  <img id="preloader-logo" src="icons/logo.png" alt="Loading..." />
</div>

<!-- Flutter Service Worker + App -->
<script>
  var serviceWorkerVersion = '{{flutter_service_worker_version}}';
  var scriptLoaded = false;

  function loadMainDartJs() {
    if (scriptLoaded) return;
    scriptLoaded = true;
    var scriptTag = document.createElement('script');
    scriptTag.src = 'main.dart.js?version=1';
    scriptTag.type = 'application/javascript';
    document.body.append(scriptTag);
  }

  if ('serviceWorker' in navigator) {
    window.addEventListener('load', function () {
      var serviceWorkerUrl = 'flutter_service_worker.js?v=' + serviceWorkerVersion;
      navigator.serviceWorker.register(serviceWorkerUrl).then((reg) => {
        function waitForActivation(serviceWorker) {
          serviceWorker.addEventListener('statechange', () => {
            if (serviceWorker.state == 'activated') {
              console.log('Installed new service worker.');
              loadMainDartJs();
            }
          });
        }
        if (!reg.active && (reg.installing || reg.waiting)) {
          waitForActivation(reg.installing ?? reg.waiting);
        } else if (!reg.active.scriptURL.endsWith(serviceWorkerVersion)) {
          console.log('New service worker available.');
          reg.update();
          waitForActivation(reg.installing);
        } else {
          console.log('Loading app from service worker.');
          loadMainDartJs();
        }
      });
      setTimeout(() => {
        if (!scriptLoaded) {
          console.warn('Failed to load app from service worker. Falling back.');
          loadMainDartJs();
        }
      }, 4000);
    });
  } else {
    loadMainDartJs();
  }
</script>

<!-- Firebase SDKs -->
<script src="https://www.gstatic.com/firebasejs/10.12.3/firebase-app.js"></script>
<script src="https://www.gstatic.com/firebasejs/10.12.3/firebase-functions.js"></script>
<script src="https://www.gstatic.com/firebasejs/10.12.3/firebase-firestore.js"></script>
<script src="https://www.gstatic.com/firebasejs/10.12.3/firebase-auth.js"></script>

<!-- 🔹 Preloader logic -->
<script>
  // Remove preloader once Flutter renders its first frame
  window.addEventListener("flutter-first-frame", function () {
    const preloader = document.getElementById("preloader");
    if (preloader) {
      preloader.style.transition = "opacity 0.4s ease";
      preloader.style.opacity = "0";
      setTimeout(() => preloader.remove(), 400);
    }
  });

  // Change logo based on URL
  const url = window.location.href;
  const logo = document.getElementById("preloader-logo");

  if (url.includes("work")) {
    logo.src = "icons/logo.png"; // Work logo
    preloader.style.background = "#262626";
  } else if (url.includes("pro")) {
    logo.src = "icons/newarc_professionals_white.png"; // Professionals logo
    preloader.style.background = "#000000";
  } else {
    logo.src = "icons/logo-agenzie-white.png"; // Agency logo
    preloader.style.background = "#1c1e21";
  }
</script>
</body>
</html>
