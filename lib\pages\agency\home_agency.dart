import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/routes/agency_routes.dart';
import 'package:newarc_platform/pages/login_page.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/widget/agency/custom_appbar_menu.dart';
import 'package:newarc_platform/widget/agency/custom_drawer.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import '../../widget/agency/custom_notification_tray.dart';


class HomeAgency extends StatefulWidget {
  const HomeAgency({Key? key, required this.agencyUser,required this.child}) : super(key: key);

  static const String route = '/home-agency';

  final AgencyUser agencyUser;
  final Widget child;

  @override
  State<HomeAgency> createState() => _HomeAgencyState();
}

class _HomeAgencyState extends State<HomeAgency> {
  String selectedView = 'progetti-attivi';
  String? profilePicture;

  ReceivedContactsPageFilters? receivedContactsPageFilters;
  var appBarHeight = AppBar().preferredSize.height;

  @override
  void initState() {
    // TODO: implement initState
    getProfilePicture();
    super.initState();
  }

  getProfilePicture() async {
    var url = await agencyProfileUrl(widget.agencyUser.agencyId, widget.agencyUser.profilePicture);

    if (url != '') {
      setState(() {
        profilePicture = url;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          if (constraints.maxWidth > 650) {
            return Scaffold(
              backgroundColor: Colors.white,
              body: Row(
                children: [
                  CustomDrawer(
                    agencyUser: widget.agencyUser,
                  ),
                  Expanded(
                    child: Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(right: 15),
                          child: AppBar(
                            backgroundColor: Colors.transparent,
                            elevation: 0,
                            leading: Container(),
                            actions: <Widget>[
                              getNotificationTray(),
                              getAppbarMenu(context: context,profilePicture: profilePicture ?? ""),
                            ],
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 10.0),
                          child: Container(
                            height: 1,
                            color: Color(0xffe0e0e0),
                          ),
                        ),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 30.0, vertical: 20),
                            child: widget.child,
                            // child: Text('Test'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          } else {
            // Versione ridotta
            return Scaffold(
              backgroundColor: Color(0xffF9F9F9),
              appBar: AppBar(
                backgroundColor: Colors.black,
                actions: [
                  IconButton(
                    icon: Icon(
                      Icons.settings,
                      color: Colors.grey,
                    ),
                    onPressed: () {
                      // do something
                    },
                  ),
                  getNotificationTray(),
                  PopupMenuButton(
                    tooltip: "",
                    icon: SvgPicture.asset(
                      'assets/icons/account.svg',
                      color: Colors.grey,
                      width: 20,
                    ),
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        enabled: false,
                        child: Column(children: [Text(widget.agencyUser.agency!.name!), Text(widget.agencyUser.email!)]),
                        value: 1,
                        onTap: () {},
                      ),
                      PopupMenuItem(
                        child: Text("Logout"),
                        value: 2,
                        onTap: () async {
                          // deleteUserRole();
                          await FirebaseAuth.instance.signOut();
                          context.go("/");
                        },
                      ),
                    ],
                  )
                ],
              ),
              drawer: CustomDrawer(
                agencyUser: widget.agencyUser,
              ),
              body: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 20),
                child: Column(
                  children: [
                    // Expanded(child: selectView(false)),
                    Expanded(child: widget.child),
                  ],
                ),
              ),
            );
          }
        },
      ),
    );
  }


  Widget getNotificationTray() {
    var agencyId = widget.agencyUser.agency!.id;
    return StreamBuilder(
      stream: FirebaseFirestore.instance
          .collection(
            '${appConfig.COLLECT_AGENCIES}/',
          )
          .doc(agencyId)
          .snapshots(),
      builder: (BuildContext context, AsyncSnapshot<DocumentSnapshot<Map<String, dynamic>>> snapshot) {
        //List<NewarcNotification> notifications = [];
        bool notificationsRead = true;

        if (snapshot.hasData) {
          Agency agency = Agency.fromDocument(snapshot.data!.data()!, snapshot.data!.id);
          notificationsRead = agency.notificationsRead!;
          /*snapshot.data!.docs.forEach((doc) {
            notifications
                .add(NewarcNotification.fromDocument(doc.data(), doc.id));
          });*/
        }
        return Stack(
          alignment: Alignment.center,
          children: [
            Container(
              height: 40,
              width: 40,
              color: Colors.transparent,
              child: CustomNotificationTray(agency: widget.agencyUser.agency!, notificationsRead: notificationsRead),
            ),
            notificationsRead
                ? Container()
                : Positioned(
                    right: 10,
                    top: 10,
                    child: Container(
                      width: 9,
                      height: 9,
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor,
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                  )
          ],
        );
      },
    );
  }

  Widget getAppbarMenu({required BuildContext context,required String profilePicture}) {
    return CustomAppbarMenu(
      agency: widget.agencyUser.agency!,
      profilePicture: profilePicture,
      onSettingsTapped: () =>
          context.go(AgencyRoutes.agencyAgencySetting, extra: widget.agencyUser),
      onAbbonamentiTapped: () =>
          context.go(AgencyRoutes.agencyAbbonamento, extra: widget.agencyUser),
      onServiziTapped: () =>
          context.go(AgencyRoutes.agencyServiziPrezzi, extra: widget.agencyUser),
      onPersonaTapped: () =>
          context.go(AgencyRoutes.agencyAgencyPersona, extra: widget.agencyUser),
    );
  }

}
