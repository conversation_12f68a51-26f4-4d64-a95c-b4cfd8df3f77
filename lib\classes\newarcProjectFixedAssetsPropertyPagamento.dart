
import 'package:flutter/material.dart';

class NewarcProjectPagamento{
  String? newarcProjectFixedAssetsPropertyCategoryId;
  List<Rate>? rate;
  double? total;
  double? totalIVA;
  bool? hasConcessions;
  bool? isManualCategory;

  String? uid;
  bool? hasPenalty;
  double? agreedPenalty;
  int? jobStartTimestamp;
  int? jobEndTimestamp;
  bool? contractor;
  String? vendorUserId;
  String? productName;



  //--for UI
  String? categoryName;


  Map<String, Object?> toMap() {
    final map = {
      'newarcProjectFixedAssetsPropertyCategoryId': newarcProjectFixedAssetsPropertyCategoryId,
      'rate': rate?.map((val) => val.toMap()).toList(),
      'total': total,
      'totalIVA': totalIVA,
      'hasConcessions': hasConcessions,
      'isManualCategory': isManualCategory,
      'uid': uid,
      'hasPenalty': hasPenalty,
      'agreedPenalty': agreedPenalty,
      'jobStartTimestamp': jobStartTimestamp,
      'jobEndTimestamp': jobEndTimestamp,
      'contractor': contractor,
      'vendorUserId': vendorUserId,
      'productName': productName,
    };

    if (isManualCategory ?? false) {
      map['categoryName'] = categoryName;
    }else{
      categoryName = categoryName;
    }

    return map;
  }

  NewarcProjectPagamento.empty() {
    this.newarcProjectFixedAssetsPropertyCategoryId = '';
    this.rate = [];
    this.total = 0.0;
    this.totalIVA = 0.0;
    this.categoryName = '';
    this.hasConcessions = false;
    this.isManualCategory = false;
    this.hasPenalty = false;
    this.contractor = false;
    this.uid = "";
    this.agreedPenalty = 0;
    this.jobStartTimestamp = DateTime.now().millisecondsSinceEpoch;
    this.jobEndTimestamp = DateTime.now().millisecondsSinceEpoch;
    this.vendorUserId = "";
    this.productName = "";
  }

  NewarcProjectPagamento.fromDocument(Map<String, dynamic> data, String id) {
    try {
      this.newarcProjectFixedAssetsPropertyCategoryId = data['newarcProjectFixedAssetsPropertyCategoryId'];
      this.total = data['total'];
      this.totalIVA = data['totalIVA'];
      this.uid = data['uid'];
      this.vendorUserId = data['vendorUserId'];
      this.agreedPenalty = data['agreedPenalty'];
      this.hasConcessions = data['hasConcessions'] ?? false;
      this.isManualCategory = data['isManualCategory'] ?? false;
      this.hasPenalty = data['hasPenalty'] ?? false;
      this.contractor = data['contractor'] ?? false;
      this.jobStartTimestamp = data['jobStartTimestamp'];
      this.jobEndTimestamp = data['jobEndTimestamp'];
      this.productName = data['productName'];
      this.rate = [];
      if (data['rate'] != null) {
        for (var i = 0; i < data['rate'].length; i++) {
          this.rate?.add(Rate.fromDocument(data['rate'][i],''));
        }
      }
      if (isManualCategory ?? false) {
        categoryName = data['categoryName'];
      } else {
        categoryName = categoryName;
      }

    } catch (e, s) {
      print({ 'NewarcProjectFixedAssetsPropertyPagamento Class Error ------->', e, s});
    }
  }
}


class Rate{
  String? uniqueId;
  String? newarcProjectFixedAssetsPercentageId;
  int? index;
  double? rate;
  double? paidAmount;
  int? ivaPercentage;
  double? ivaAmount;
  Future<List>? fetchPercentageFuture;
  bool? isPaid;
  int? paidDate;
  String? description;
  TextEditingController? descriptionController;

  List? margeRateUniqueIDS;
  String? mergedIntoRateId;
  bool? isMerged;
  List<NewPayment>? entratePayment;
  String? paymentStatus;

  int? holdedPercentage; //--- Percentuale ritenuta
  double? holdedAmount; //--- Percentuale ritenuta
  double? expectedAmount; //--- Accredito atteso
  double? toBePaidAmount; //--- Da pagara

  Map? requestedPaymentInvoice;
  String? reasonForTransfer;
  String? associatedProjectJobUID;


  // for UI
  int? percentage;
  String? category;
  List? requestedPaymentInvoiceImages;

  int? paymentRequestTimestamp;
  int? paymentCompletionTimestamp;






  Map<String, Object?> toMap() {
    percentage = percentage;
    requestedPaymentInvoiceImages = requestedPaymentInvoiceImages;
    return {
      'paymentCompletionTimestamp': paymentCompletionTimestamp,
      'paymentRequestTimestamp': paymentRequestTimestamp,
      'reasonForTransfer': reasonForTransfer,
      'newarcProjectFixedAssetsPercentageId': newarcProjectFixedAssetsPercentageId,
      'uniqueId': uniqueId,
      'index': index,
      'rate': rate,
      'ivaPercentage': ivaPercentage,
      'ivaAmount': ivaAmount,
      'isPaid': isPaid,
      'paidDate': paidDate,
      'paidAmount': paidAmount,
      'description': description,
      'margeRateUniqueIDS': margeRateUniqueIDS,
      'isMerged': isMerged,
      'mergedIntoRateId': mergedIntoRateId,
      'paymentStatus': paymentStatus,
      'entratePayment': entratePayment?.map((val) => val.toMap()).toList(),
      'holdedPercentage': holdedPercentage,
      'holdedAmount': holdedAmount,
      'expectedAmount': expectedAmount,
      'toBePaidAmount': toBePaidAmount,
      'requestedPaymentInvoice': requestedPaymentInvoice,
      'associatedProjectJobUID': associatedProjectJobUID
    };
  }

  Rate.empty() {
    this.paymentRequestTimestamp = null;
    this.paymentCompletionTimestamp = null;
    this.reasonForTransfer = '';
    this.newarcProjectFixedAssetsPercentageId = '';
    this.index = 0;
    this.uniqueId = "";
    this.rate = 0.0;
    this.ivaPercentage = 0;
    this.ivaAmount = 0;
    this.isPaid = false;
    this.paidDate = null;
    this.percentage = 0;
    this.paidAmount = 0;
    this.category = "";
    this.description = "";
    this.descriptionController?.clear();
    this.margeRateUniqueIDS = [];
    this.entratePayment = [];
    this.isMerged = false;
    this.mergedIntoRateId = "";
    this.paymentStatus = "";
    this.paymentStatus = "in attesa";
    this.holdedPercentage = 0;
    this.holdedAmount = 0;
    this.expectedAmount = 0;
    this.toBePaidAmount = 0;
    this.requestedPaymentInvoice = {};
    this.requestedPaymentInvoiceImages = [];
    this.associatedProjectJobUID = '';
  }

  Rate.fromDocument(Map<String, dynamic> data, String id) {
    percentage = percentage;
    try {
      this.paymentCompletionTimestamp = data['paymentRequestTimestamp'];
      this.paymentRequestTimestamp = data['paymentRequestTimestamp'];
      this.reasonForTransfer = data['reasonForTransfer'];
      this.newarcProjectFixedAssetsPercentageId = data['newarcProjectFixedAssetsPercentageId'];
      this.uniqueId = data['uniqueId'];
      this.index = data['index'];
      this.rate = data['rate'];
      this.ivaPercentage = data['ivaPercentage'];
      this.isPaid = data['isPaid'];
      this.paidDate = data['paidDate'];
      this.ivaAmount = data['ivaAmount'];
      this.paidAmount = data['paidAmount'];
      this.description = data['description'];
      this.margeRateUniqueIDS = data['margeRateUniqueIDS'];
      this.isMerged = data['isMerged'] ?? false;
      this.mergedIntoRateId = data['mergedIntoRateId'];
      this.descriptionController = TextEditingController(text: this.description);
      this.paymentStatus = data['paymentStatus'];
      this.holdedPercentage = data['holdedPercentage'];
      this.holdedAmount = data['holdedAmount'];
      this.expectedAmount = data['expectedAmount'];
      this.toBePaidAmount = data['toBePaidAmount'];
      this.requestedPaymentInvoice = data['requestedPaymentInvoice'] ?? {};
      this.entratePayment = [];
      this.associatedProjectJobUID = data['associatedProjectJobUID']??'';
      requestedPaymentInvoiceImages = (data['requestedPaymentInvoice'] != null && (data['requestedPaymentInvoice'] as Map).isNotEmpty) ? [data['requestedPaymentInvoice']["filename"]] : [];
      if (data['entratePayment'] != null) {
        for (var i = 0; i < data['entratePayment'].length; i++) {
          this.entratePayment?.add(NewPayment.fromDocument(data['entratePayment'][i]));
        }
      }
    } catch (e, s) {
      print({ 'Rate Class Error ------->', e, s});
    }
  }
}


class NewPayment{
  String? uniqueId;
  String? rateUniqueId;
  int? paidDate;
  String? paymentStatus;
  String? paymentType;
  double? remainingAmount; //----- Rimanenza
  double? paidAmount; //--- Somma bonificata
  double? receivedAmount; //--- Somma ricevuta



  Map<String, Object?> toMap() {
    return {
      'uniqueId': uniqueId,
      'paidDate': paidDate,
      'paidAmount': paidAmount,
      'paymentStatus': paymentStatus,
      'rateUniqueId': rateUniqueId,
      'paymentType': paymentType,
      'remainingAmount': remainingAmount,
      'receivedAmount': receivedAmount,
    };
  }

  NewPayment.empty() {
    this.uniqueId = "";
    this.rateUniqueId = "";
    this.paidDate = null;
    this.paidAmount = 0;
    this.paymentStatus = "in attesa";
    this.paymentType = "";
    this.remainingAmount = 0;
    this.receivedAmount = 0;
  }

  NewPayment.fromDocument(Map<String, dynamic> data) {
    try {
      this.rateUniqueId = data['rateUniqueId'];
      this.uniqueId = data['uniqueId'];
      this.paidDate = data['paidDate'];
      this.paidAmount = data['paidAmount'] ?? 0;
      this.paymentStatus = data['paymentStatus'];
      this.paymentType = data['paymentType'];
      this.remainingAmount = data['remainingAmount'] ?? 0;
      this.receivedAmount = data['receivedAmount'] ?? 0;
    } catch (e, s) {
      print({ 'NewPayment Class Error ------->', e, s});
    }
  }
}


