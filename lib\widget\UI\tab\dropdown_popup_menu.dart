import 'package:flutter/material.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/UI/tab/common_icon_button.dart';


class DropdownPopupMenu extends StatefulWidget {
  final String? icon, title, infoTitle, note;
  final bool? isForProfessionals;
  final bool? isSmart;
  final List children;

  DropdownPopupMenu({
    super.key,
    this.icon,
    this.title,
    this.infoTitle,
    this.isForProfessionals,
    this.isSmart,
    required this.children, 
    this.note
  });

  @override
  State<DropdownPopupMenu> createState() => _DropdownPopupMenuState();
}

class _DropdownPopupMenuState extends State<DropdownPopupMenu> {
  final GlobalKey _popupKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      child: PopupMenuButton<Map>(
        key: _popupKey,
        tooltip: '',
        shadowColor: AppColor.black.withOpacity(0.6),
        elevation: 2,
        offset: Offset(0, 40),
        color: AppColor.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        child: IconButtonWidget(
          isOnlyBorder: true,
          width: 20,
          height: 20,
          borderColor: AppColor.buttonBorderColor,
          backgroundColor: AppColor.white,
          isSvgIcon: true,
          borderRadius: 5,
          iconPadding: EdgeInsets.all(3),
          icon: widget.icon ?? 'assets/icons/three_dot.svg',
          iconColor: AppColor.drawerButtonColor,
        ),
        itemBuilder: (BuildContext context) => widget.children.map((childItem){
          return PopupMenuItem<Map>(
            value: childItem['value'],
            child: childItem['widget']
          );
        }).toList(),
        onSelected: (Map value) {
          // print('Selected: $value');
        },
      ),
    );
  }
}
