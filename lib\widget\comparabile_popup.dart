import 'package:flutter/material.dart';
import 'package:newarc_platform/pages/models/home_scouter.model.dart';
import 'package:newarc_platform/widget/custom_icon_button.dart';
import 'package:provider/provider.dart';

class ComparabilePopup extends StatefulWidget {
  ComparabilePopup({
    Key? key,
  }) : super(key: key);

  @override
  _ComparabilePopupState createState() => _ComparabilePopupState();
}

class _ComparabilePopupState extends State<ComparabilePopup> {
  double borderRadius = 10;
  TextStyle menuItemStyle = TextStyle(color: Colors.white, fontSize: 18);
  int? _selectedZoneIndex;
  TextStyle selectedStyle = TextStyle(fontWeight: FontWeight.w600);
  TextStyle normalStyle = TextStyle(fontWeight: FontWeight.w400);

  List<String> zones = [];

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 985,
      height: 760,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.4),
            blurRadius: 20,
            spreadRadius: 10,
          ),
        ],
      ),
      padding: EdgeInsets.symmetric(horizontal: 15),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(width: 30),
              Text(
                "Seleziona una zona",
                style: Theme.of(context)
                    .textTheme
                    .headlineSmall!
                    .copyWith(color: Theme.of(context).primaryColor),
              ),
              IconButton(
                onPressed: () {
                  Provider.of<HomeScouterModel>(context, listen: false)
                      .showComparabile(null);
                },
                icon: Icon(Icons.close),
              )
            ],
          ),
          SizedBox(width: 10),
          CustomIconButton(
            label: "Conferma",
            icon: Container(),
            width: 149,
            height: 56,
            textStyle: TextStyle(color: Colors.white),
            color: Theme.of(context).primaryColor,
            function: () => {},
          ),
          SizedBox(height: 20)
        ],
      ),
    );
  }
}
