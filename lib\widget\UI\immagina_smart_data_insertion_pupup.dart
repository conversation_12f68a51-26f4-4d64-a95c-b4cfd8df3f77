import 'dart:math';
import 'package:web/web.dart' as web;
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/utils/downloadQuotationPDF.dart';
import 'package:newarc_platform/widget/UI/tab/tag_widget.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/address_completion_google.dart';
import 'package:newarc_platform/widget/UI/immagina_data_insertion_utils.dart';
import 'package:newarc_platform/classes/immaginaProject.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/professionals.dart';
import 'package:newarc_platform/functions/various.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/app_const.dart' as appConst;
import 'package:newarc_platform/economics.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as p;
import '../../utils/common_utils.dart';

class ImmaginaSmartDataInsertionPopup extends StatefulWidget {
  final String procedureStep;
  final ImmaginaProject? project;
  final List<String> formErrorMessage;
  final AgencyUser? agencyUser;
  // 17-07-2025
  // was requested to apply this procedure to agencies only, 
  // pretty sure will be implemented for professionals as well in the future.
  // keeping possibility to be called from a professionalsUser 
  final ProfessionalsUser? professionalsUser;
  final onClose;
  final bool? isBlockedSection;

  // final bool isStripPayOnly;

  ImmaginaSmartDataInsertionPopup({
    required this.procedureStep,
    this.project,
    this.formErrorMessage = const [],
    this.agencyUser,
    this.professionalsUser,
    required this.onClose,
    this.isBlockedSection,
    Key? key,
  }) : super(key: key) {
    if (project == null && procedureStep != 'initial') {
      throw ArgumentError(
        'If project is null, procedureStep must be "initial".',
      );
    }
    if (agencyUser == null && professionalsUser == null) {
      throw ArgumentError(
        'Either agencyUser or professionalsUser must be provided.',
      );
    }
    if (agencyUser != null && professionalsUser != null) {
      throw ArgumentError(
        'Only one of agencyUser or professionalsUser must be provided.',
      );
    }
  }
  @override
  State<ImmaginaSmartDataInsertionPopup> createState() => _ImmaginaSmartDataInsertionPopupState();
}

class _ImmaginaSmartDataInsertionPopupState extends State<ImmaginaSmartDataInsertionPopup> {
  TextStyle sectionTitleStyle = TextStyle(fontSize: 16, fontWeight: FontWeight.bold);

  final _formKey = GlobalKey<FormState>();
  bool _isFrozen = false;
  bool loading = false;
  bool paymentError = false;
  bool clicked = false;
  List<String> formErrorMessage = [];
  ImagePicker picker = ImagePicker();

  List viewsOrder = const [
    'initial',
    'localizzazione',
    'input-picture',
    'select-mode',
    'select-style',
    'complete-request',
  ];

  Map<String, String> _errorMessage = {
    'localizzazione': 'Inserisci un indirizzo completo di numero civico.',
    'input-picture': 'Inserisci almeno una foto di una stanza indicandone il tipo.',
    'select-mode': 'Seleziona il tipo di servizio richiesto.',
    'select-style': 'Seleziona lo stile di arredamento e/o ristrutturazione richiesto.',
  };

  // Popup main state variables
  String? selectedView;
  ImmaginaProject? selectedProject;

  // Localizzazione
  TextEditingController filterMarketZone = TextEditingController();
  TextEditingController filterAgency = TextEditingController();
  TextEditingController filterSuggestedAgencyName = TextEditingController();
  TextEditingController filterSuggestedAgencyEmail = TextEditingController();
  BaseAddressInfo filterSuggestedAgencyAddress = BaseAddressInfo.empty();
  List<Agency> agencyList = [];

  // Input picture
  List<Map<String, dynamic>> picturesImages = [];

  // Select mode
  String? selectedMode;

  // Select style
  String? selectedStyle;
  int? selectedStyleIndex;
  
  // Complete request
  bool wasPaymentDetected = false;
  int callCounter = 0;
  int listenerTriggered = 0;

  void initializeControllers() {
    selectedMode = selectedProject!.smartServiceType;
    selectedStyle = selectedProject!.smartServiceStyle;
    if (selectedStyle != null) {
      selectedStyleIndex = stileToFunctionStyle.values.toList().indexOf(selectedStyle!);
    }
  }

  @override
  void initState() {
    formErrorMessage = widget.formErrorMessage;
    selectedView = widget.procedureStep;
    selectedProject = widget.project;
    super.initState();
    if (selectedProject != null) {
      initializeControllers();
      getFirestorePicturesImages(picturesImages, selectedProject!, setState);
    }
  }

  @override
  void didUpdateWidget(ImmaginaSmartDataInsertionPopup oldWidget) {
    super.didUpdateWidget(oldWidget);
    formErrorMessage = widget.formErrorMessage;
    selectedView = widget.procedureStep;
    selectedProject = widget.project;
  }

  Widget selectFooter() {
    if (selectedView == null) {
      return NarFormLabelWidget(label: "view can't be null selected");
    } else if (selectedView == 'initial') {
      return initialFooter();
    } else {
      return standardFooter();
    }
  }

  Widget initialFooter() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        // Container(
        //   width: 150,
        //   child: BaseNewarcButton(
        //     buttonText: 'Inizia',
        //     color: Colors.black,
        //     onPressed: () async {
        //       // create immagina project object
        //       ImmaginaProject _project = ImmaginaProject.empty();
        //       _project.isSmart = true;
        //       if (widget.agencyUser != null) {
        //       _project.agencyId = widget.agencyUser!.agencyId!;
        //       } else if (widget.professionalsUser != null) {
        //       _project.professionalId = widget.professionalsUser!.professionalId!;
        //       }
        //       _project.insertionTimestamp = DateTime.now().millisecondsSinceEpoch;
        //       _project.appliedSuccessFee = "0";
        //       // save project to firestore
        //       DocumentReference projectResponse = await FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).add(_project.toMap());
        //       _project.id = projectResponse.id;
        //       setState(() {
        //         selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
        //         selectedProject = _project;
        //         initializeControllers();
        //       });
        //     }
        //   ),
        // )
      ],
    );
  }

  Widget standardFooter() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        if (!(widget.isBlockedSection ?? false))
        ...[
          selectedView == viewsOrder[1]
            ? SizedBox(width: 150)
            : ElevatedButton(
              onPressed: () => _backButtonSetStateFunction(),
              style: ElevatedButton.styleFrom(
                elevation: 0,
                fixedSize: Size(150, 45),
                backgroundColor: Color(0xffe8e8e8),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
              ),
              child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                Transform.rotate(
                  angle: pi,
                  child: SvgPicture.asset(
                    'assets/icons/arrow.svg',
                    height: 16,
                    color: const Color(0xff6a6a6a),
                  ),
                ),
                NarFormLabelWidget(
                  label: 'Indietro',
                  textColor: const Color(0xff6a6a6a),
                  fontWeight: '600',
                  letterSpacing: .5,
                ),
              ])
            ),
          selectedView == 'complete-request'
            ? SizedBox(width: 150)
            : Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // SALTA BUTTON FUNCTIONALITY
                // if (selectedView != viewsOrder[-2]) ...[
                //   ElevatedButton(
                //     onPressed: () {
                //       setState(() {
                //         selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
                //       });
                //     },
                //     style: ElevatedButton.styleFrom(
                //       elevation: 0,
                //       fixedSize: Size(150, 45),
                //       backgroundColor: Colors.white,
                //       side: BorderSide(color: Color(0xff6a6a6a)),
                //       shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                //     ),
                //     child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                //       NarFormLabelWidget(
                //         label: 'Salta',
                //         textColor: Color(0xff6a6a6a),
                //         fontWeight: '600',
                //         letterSpacing: .5,
                //       ),
                //       SvgPicture.asset(
                //         'assets/icons/arrow.svg',
                //         height: 16,
                //         color: Color(0xff6a6a6a),
                //       ),
                //     ])
                //   ),
                //   SizedBox(width: 20),
                // ],
                ElevatedButton(
                  onPressed: () => _forwardButtonConditions() ? _forwardButtonSetStateFunction() : _forwardButtonErrorFunction(),
                  style: ElevatedButton.styleFrom(
                    elevation: 0,
                    fixedSize: Size(150, 45),
                    backgroundColor: _forwardButtonConditions() ? Colors.black : Color.fromARGB(255, 192, 192, 192),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                  ),
                  child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                    NarFormLabelWidget(
                      label: 'Avanti',
                      textColor: Colors.white,
                      fontWeight: '600',
                      letterSpacing: .5,
                    ),
                    SvgPicture.asset(
                      'assets/icons/arrow.svg',
                      height: 16,
                      color: Colors.white,
                    ),
                  ])
                ),
              ],
            ),
        ],
        // if (widget.isBlockedSection ?? false)
        // ...[
        //   SizedBox(width: 150),
        //   ElevatedButton(
        //     onPressed: 
        //     // check if every child has planimetrie
        //     !(selectedProject!.childrenProjects?.every((child) => child.planimetry.isNotEmpty) ?? false)
        //     ? () {}
        //     : () async {
        //       selectedProject!.requestStatus = 'in analisi';
        //       selectedProject!.blockedSection = null;
        //       selectedProject!.blockedNotes = null;
        //       selectedProject!.statusChangedDate = DateTime.now().millisecondsSinceEpoch;
        //       await FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).doc(selectedProject!.id).set(selectedProject!.toMap());
        //       widget.onClose();
        //       Navigator.of(context).pop();
        //     },
        //     style: ElevatedButton.styleFrom(
        //       elevation: 0,
        //       fixedSize: Size(250, 45),
        //       backgroundColor: !(selectedProject!.childrenProjects?.every((child) => child.planimetry.isNotEmpty) ?? false) ? Color.fromARGB(255, 192, 192, 192) : Colors.black,
        //       shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        //     ),
        //     child: NarFormLabelWidget(
        //       label: 'Invia planimetrie',
        //       textColor: Colors.white,
        //       fontWeight: '600',
        //       letterSpacing: .5,
        //     ),
        //   ),
        //   SizedBox(width: 150),
        // ],
      ]
    );
  }

  bool _forwardButtonConditions() {
    Map conditions = {
      'localizzazione': selectedProject!.localizzazione(),
      'input-picture': selectedProject!.fotografie(),
      'select-mode': selectedProject!.smartServiceType != null,
    };
    conditions["select-style"] = conditions.values.every((value) => value == true) && (selectedProject!.smartServiceStyle != null || selectedProject!.smartServiceType == 'nakedInteriors');
    bool condition = conditions[selectedView] ?? false;
    return condition;
  }

  _backButtonSetStateFunction() {
    if (selectedView != 'localizzazione') {
      if (selectedView == 'complete-request') {
        if (selectedProject!.smartServiceType == 'nakedInteriors') {
          setState(() {
            selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) - 2];
          });
        } else {
          setState(() {
            selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) - 1];
          });
        }
      } else {
        setState(() {
          selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) - 1];
        });
      }
    }
  }

  _forwardButtonErrorFunction() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) {
        return Center(
          child: BaseNewarcPopup(
            title: 'Errore',
            buttonColor: Colors.black,
            column: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                NarFormLabelWidget(
                  label: _errorMessage[selectedView],
                  textAlign: TextAlign.center,
                  fontWeight: '600',
                ),
              ],
            ),
          )
        );
      }
    );
  }

  _forwardButtonSetStateFunction() async {
    setState(() {
      _isFrozen = true;
    });
    String? currentView = selectedView;
    if (currentView == 'localizzazione') {
      if (_forwardButtonConditions()) {
        setState(() {
          selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
        });
      }
    } else if (currentView == 'input-picture') {
      if (_forwardButtonConditions()) {
        await savePicturesImages(picturesImages, selectedProject!, setState);
        setState(() {
          selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
        });
      }
    } else if (currentView == 'select-mode') {
      if (_forwardButtonConditions()) {
        if (selectedProject!.smartServiceType == 'nakedInteriors') {
          setState(() {
            selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 2];
          });
        } else {
          setState(() {
            selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
          });
        }
      }
    } else if (currentView == 'select-style') {
      if (_forwardButtonConditions()) {
        setState(() {
          selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
        });
      }
    } 
    // save selectedProject to firestore
    try {
      await FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).doc(selectedProject!.id).set(selectedProject!.toMap());
    } catch (e) {
      print("_forwardButtonSetStateFunction Error: Saving project to Firestore failed: $e");
    } finally {
      setState(() {
        _isFrozen = false;
      });
    }
  }

  Future<String> computeImmaginaProjectCode () async { 
    // create and assign internal code (projectId) for immaginaProject
    final FirebaseFirestore _db = FirebaseFirestore.instance;
    String _region = selectedProject!.addressInfo!.region!;
    String _regionCode = appConst.composeProjectCode['region']![_region]!;
    String _serviceCode = appConst.composeProjectCode['project']!['immagina']!;
    DateTime _createdDate = DateTime.fromMillisecondsSinceEpoch(selectedProject!.insertionTimestamp);
    String _yearCode = _createdDate.year.toString().substring(2); // Get last two digits of the year
    String _defaultProjectId = "P_${_serviceCode}${_regionCode}${_yearCode}0001";

    QuerySnapshot<Map<String, dynamic>> _immaginaProjectsRegionCollection = await _db
      .collection(appConfig.COLLECT_IMMAGINA_PROJECTS).where('region', isEqualTo: _region)
      .orderBy('insertionTimestamp', descending: true)
      .limit(1).get();
      
    if (_immaginaProjectsRegionCollection.docs.isNotEmpty) {
      Map<String, dynamic> lastDoc = _immaginaProjectsRegionCollection.docs[0].data();
      if (DateTime.fromMillisecondsSinceEpoch(lastDoc['insertionTimestamp']).year == DateTime.now().year) {
        RegExp matcher = RegExp(r"\d{4}$");
        final match = matcher.firstMatch(lastDoc['projectId']);
        if (match != null) {
          final matchedText = match.group(0);
          final seqNumber = int.parse(matchedText!) + 1;
          final stringSeqNumber = seqNumber.toString();
          final paddedStringSeqNumber = stringSeqNumber.padLeft(4, '0');
          _defaultProjectId =
          "P_${appConst.composeProjectCode['project']!['immagina']}${appConst.composeProjectCode['region']![_region]}${(DateTime.now().year) % 100}${paddedStringSeqNumber}";
        }
      }
    }
    return _defaultProjectId;
  }

  void saveCharacteristics(ImmaginaProject project, Map characteristics) {
    project.elevator = characteristics['Ascensore'];
    project.hasCantina = characteristics['Cantina'];
    project.terrace = characteristics['Terrazzo'];
    project.hasConcierge = characteristics['Portineria'];
    project.highEfficiencyFrames = characteristics['Infissi ad alta efficienza'];
    project.doubleEsposition = characteristics['Doppia esposizione'];
    project.tripleEsposition = characteristics['Tripla esposizione'];
    project.quadrupleEsposition = characteristics['Quadrupla esposizione'];
    project.centralizedHeating = characteristics['Risc. centralizzato'];
    project.autonomousHeating = characteristics['Risc. autonomo'];
    project.privateGarden = characteristics['Giardino privato'];
    project.sharedGarden = characteristics['Giardino condominiale'];
    project.surveiledBuilding = characteristics['Stabile videosorvegliato'];
    project.nobleBuilding = characteristics['Stabile signorile'];
    project.fiber = characteristics['Fibra ottica'];
    project.airConditioning = characteristics['Pred. condizionatore'];
    project.securityDoor = characteristics['Porta blindata'];
    project.tvStation = characteristics['Impianto TV'];
    project.alarm = characteristics['Pred. antifurto'];
    project.motorizedSunblind = characteristics['Tapparelle motorizzate'];
    project.domotizedSunblind = characteristics['Tapparelle domotizzate'];
    project.domotizedLights = characteristics['Luci domotizzate'];
    project.highFloor = characteristics['Piano alto'];
    project.metroVicinity = characteristics['Vicinanza Metro'];
    project.bigBalconies = characteristics['Ampi balconi'];
    project.bigLiving = characteristics['Grande zona living'];
    project.doubleBathroom = characteristics['Doppi servizi'];
    project.swimmingPool = characteristics['Piscina'];
    project.hasGarage = characteristics['Box o garage'];
    project.solarPanel = characteristics['Fotovoltaico'];
    project.walkInCloset = characteristics['Cabina armadio'];
  }

  Future savePlanimetryImages(ImmaginaProject childProject, List<XFile> planimetryImages) async {
    String path = '${appConfig.COLLECT_IMMAGINA_PROJECTS}/${selectedProject!.id}/tagli/${childProject.id}/';
    Reference ref = FirebaseStorage.instance.ref(path);
    final listResult = await ref.listAll();
    for (var element in listResult.items) {
      await FirebaseStorage.instance.ref(element.fullPath).delete();
    }
    childProject.planimetry.clear();
    for (int i = 0; i < planimetryImages.length; i++) {
      String pictureFilename = 'planimetry_${i + 1}' + p.extension(planimetryImages[i].name);
      UploadTask? uploadTask = await uploadImageToStorage(path, pictureFilename, planimetryImages[i]);
      if (uploadTask != null) {
        await uploadTask;
      }
      childProject.planimetry.add(pictureFilename);
    }
    await FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).doc(selectedProject!.id).set(selectedProject!.toMap());
  }

  Future<UploadTask?> uploadImageToStorage(String directory, String filename, XFile? file) async {
    if (file == null) {
      return null;
    }
    UploadTask uploadTask;
    Reference ref = FirebaseStorage.instance.ref(directory).child(filename);

    final metadata = SettableMetadata(
      contentType: file.mimeType,
      customMetadata: {'picked-file-path': file.path},
    );

    uploadTask = ref.putData(await file.readAsBytes(), metadata);
    uploadTask.snapshotEvents.listen((TaskSnapshot taskSnapshot) {
      switch (taskSnapshot.state) {
        case TaskState.running:
          final progress = 100.0 * (taskSnapshot.bytesTransferred / taskSnapshot.totalBytes);
          // print("Upload is $progress% complete.");
          break;
        case TaskState.paused:
        // print("Upload is paused.");
          break;
        case TaskState.canceled:
        // print("Upload was canceled");
          break;
        case TaskState.error:
        // Handle unsuccessful uploads
          break;
        case TaskState.success:
        // Handle successful uploads on complete
        // ...
          break;
      }
    });

    return Future.value(uploadTask);
  }

  Future<List<XFile>> getFirestoreImages(ImmaginaProject childProject) async {
  List<XFile> planimetryImages = [];

  if (selectedProject != null && childProject.planimetry.isNotEmpty) {
    String directory = '${appConfig.COLLECT_IMMAGINA_PROJECTS}/${selectedProject!.id}/tagli/${childProject.id}/';
    
    for (String plan in childProject.planimetry) {
      try {
        print('Loading planimetry $plan from $directory');
        Reference ref = FirebaseStorage.instance.ref().child(directory + plan);
        Uint8List? data = await ref.getData();
        if (data != null) {
          planimetryImages.add(XFile.fromData(data, name: plan));
        }
      } catch (e) {
        print('Error loading planimetry $plan: $e');
        // Optionally continue with other images or handle error
      }
    }
  }
  
  return planimetryImages;
}

Future<List<XFile>> getFirestoreCapitolatoImages() async {
  List<XFile> capitolatoImages = [];

  if (selectedProject != null && selectedProject!.capitolato.isNotEmpty) {
    String directory = '${appConfig.COLLECT_IMMAGINA_PROJECTS}/${selectedProject!.id}/capitolato/';
    
    for (String capitolato in selectedProject!.capitolato) {
      try {
        print('Loading capitolato $capitolato from $directory');
        Reference ref = FirebaseStorage.instance.ref().child(directory + capitolato);
        Uint8List? data = await ref.getData();
        if (data != null) {
          capitolatoImages.add(XFile.fromData(data, name: capitolato));
        }
      } catch (e) {
        print('Error loading planimetry $capitolato: $e');
        // Optionally continue with other images or handle error
      }
    }
  }
  return capitolatoImages;
}

  selectView() {
    if (selectedView == null) {
      return NarFormLabelWidget(label: "view can't be null selected");
    } else if (selectedView == 'initial') {
      return initialDialog();
    } else if (selectedView == 'localizzazione') {
      return localizzazioneDialog();
    } else if (selectedView == 'input-picture') {
      return inputPicturesDialog();
    } else if (selectedView == 'select-mode') {
      return selectModeDialog();
    } else if (selectedView == 'select-style') {
      return selectStyleDialog();
    } else if (selectedView == 'complete-request') {
      return completeRequestDialog();
    } else {
      return Center(child: NarFormLabelWidget(label: "view $selectedView unknown"));
    }
  }

  Widget initialDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(height: 20),
        Image.asset(
          'assets/logo_newarc_immagina_smart.png',
          height: 50,
        ),
        SizedBox(height: 80),
        Container(
          width: 600,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              NarFormLabelWidget(
                label: 'Sei pronto per effettuare \nuna nuova richiesta di progetto?',
                overflow: TextOverflow.visible,
                textAlign: TextAlign.center,
                fontWeight: '800',
                fontSize: 30,
                textColor: Theme.of(context).disabledColor,
              ),
              SizedBox(height: 20),
              BaseNewarcButton(
                height: 45,
                width: 200,
                shadow: true,
                shadowColor: Color(0xff499B79),
                buttonText: "               Inizia         ",
                color: Color(0xff499B79),
                textColor: Colors.white,
                fontSize: 16,
                suffixIcon: Padding(
                  padding: const EdgeInsets.only(left: 10.0),
                  child: Image.asset(
                    "assets/icons/smart-ai.png", 
                    color: Colors.white, 
                    width: 20,)
                  ),
                onPressed: () async {
                  // create immagina project object
                  ImmaginaProject _project = ImmaginaProject.empty();
                  _project.isSmart = true;
                  if (widget.agencyUser != null) {
                  _project.agencyId = widget.agencyUser!.agencyId!;
                  } else if (widget.professionalsUser != null) {
                  _project.professionalId = widget.professionalsUser!.professionalId!;
                  }
                  _project.insertionTimestamp = DateTime.now().millisecondsSinceEpoch;
                  _project.appliedSuccessFee = "0";
                  // save project to firestore
                  DocumentReference projectResponse = await FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).add(_project.toMap());
                  _project.id = projectResponse.id;
                  setState(() {
                    selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
                    selectedProject = _project;
                    initializeControllers();
                  });
                }
              ),
            ],
          ),
        ),
        SizedBox(height: 60),
        Container(
          width: 600,
          decoration: BoxDecoration(border: Border.all(color: Color(0xffDBDBDB), width: 1), borderRadius: BorderRadius.all(Radius.circular(15))),
          child: Padding(
            padding: const EdgeInsets.all(10.0),
            child: Stack(
              // alignment: Alignment.topLeft,
              children: [
                Icon(
                  Icons.info_outline_rounded,
                  size: 20,
                  color: Color(0xffbdbdbd),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 25.0),
                  child: NarFormLabelWidget(
                    label: "Newarc Immagina è il servizio di modifica degli interni basato sul nostro modello di Intelligenza Artificiale più avanzato. Non sempre le immagini generate forniscono un risultato perfetto e senza difetti. In caso di problematiche importanti riscontrate dopo la generazione delle immagini, sarà possibile inviare una segnalazione per richiedere un’azione manuale. Clicca qui per vedere alcuni esempi relativi a un livello di errore accettabile oppure quando è il caso di segnalarci un problema.",
                    fontWeight: '500',
                    fontSize: 14,
                    textColor: Color(0xff696969),
                    overflow: TextOverflow.clip,
                  ),
                )
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget localizzazioneDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        NarFormLabelWidget(
          label: 'Localizzazione',
          fontWeight: '800',
          fontSize: 23,
          textColor: Theme.of(context).disabledColor,
        ),
        Padding(
            padding: EdgeInsets.only(top: 30),
            child:
            Container(
                height: MediaQuery.of(context).size.height / 2,
                width: 400,
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      AddressSearchBar(
                        label: 'Indirizzo',
                        onPlaceSelected: (selectedPlace) async {
                          if (BaseAddressInfo.fromMap(selectedPlace["place"]).isValidAddress()){
                            setState(() {
                              selectedProject!.streetName = selectedPlace["place"]["streetName"];
                              selectedProject!.streetNumber = selectedPlace["place"]["streetNumber"];
                              selectedProject!.city = selectedPlace["place"]["city"];
                              selectedProject!.postalCode = selectedPlace["place"]["postalCode"];
                              selectedProject!.province = selectedPlace["place"]["province"];
                              selectedProject!.region = selectedPlace["place"]["region"];
                              selectedProject!.country = selectedPlace["place"]["country"];
                              selectedProject!.latitude = selectedPlace["place"]["latitude"];
                              selectedProject!.longitude = selectedPlace["place"]["longitude"];
                              selectedProject!.fullAddress = selectedPlace["description"];
                              selectedProject!.addressInfo = BaseAddressInfo.fromMap(selectedPlace["place"]);
                            });
                            selectedProject!.projectId = await computeImmaginaProjectCode();
                          } else{
                            setState(() {
                              selectedProject!.streetName = null;
                              selectedProject!.streetNumber = null;
                              selectedProject!.city = null;
                              selectedProject!.postalCode = null;
                              selectedProject!.province = null;
                              selectedProject!.region = null;
                              selectedProject!.country = null;
                              selectedProject!.latitude = null;
                              selectedProject!.longitude = null;
                              selectedProject!.fullAddress = null;
                              selectedProject!.addressInfo = null;
                            });
                          }
                        },
                        initialAddress: selectedProject!.fullAddress,
                      ),
                    ],
                  ),
                )
            )
        ),
      ],
    );
  }

  Widget inputPicturesDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        NarFormLabelWidget(
          label: 'Carica fotografie',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        Padding(
          padding: EdgeInsets.only(top: 30),
          child: Container(
            // height: MediaQuery.of(context).size.height / 2,
            child: Column(
              children: [
                SizedBox(
                  height: 10,
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.info,
                      size: 20,
                      color: Color(0xffbdbdbd),
                    ),
                    SizedBox(
                      width: 5,
                    ),
                    RichText(
                      textAlign: TextAlign.center,
                      text: TextSpan(
                        text: 'Inserisci foto di interni, per ogni immmagine inserita verrà scalato un credito.',
                        style: TextStyle(
                          fontFamily: 'Raleway-600',
                          fontSize: 13,
                          color: Color.fromRGBO(133, 133, 133, 1),
                          height: 1.5,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 20,
                ),
                // Image picker
                Container(
                  // height: MediaQuery.of(context).size.height / 2.6,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Color(0xffd3d3d3),
                    ),
                  ),
                  constraints: BoxConstraints(
                    minHeight: 200,
                    maxHeight: 300
                  ),
                  child: Stack(
                    children: [
                      SingleChildScrollView(
                        padding: EdgeInsets.only(top: 20, bottom: 20, left: 8, right: 100),
                        child: Wrap(
                          spacing: 12,
                          runSpacing: 20,
                          children: picturesImages
                              .map((imageFile) => FutureBuilder<Uint8List>(
                            future: imageFile['file'].readAsBytes(),
                            builder: (context, snapshot) {
                              TextEditingController tempController = TextEditingController(text: imageFile['tag'] ?? "");
                              if (snapshot.connectionState == ConnectionState.done && snapshot.hasData) {
                                return Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Stack(
                                      children: [
                                        MouseRegion(
                                          cursor: SystemMouseCursors.click,
                                          child: GestureDetector(
                                            onTap: () {
                                              showEnlargedDownloadableImage(context, imageFile);
                                            },
                                            child: ClipRRect(
                                              borderRadius: BorderRadius.circular(8),
                                              child: Image.memory(
                                                snapshot.data!,
                                                width: 150,
                                                height: 150,
                                                fit: BoxFit.cover,
                                              ),
                                            ),
                                          ),
                                        ),
                                        Positioned(
                                            top: 3,
                                            right: 3,
                                            child: GestureDetector(
                                                onTap: () {
                                                  setState(() {
                                                    picturesImages.remove(imageFile);
                                                  });
                                                },
                                                child: Container(
                                                    decoration: BoxDecoration(
                                                      shape: BoxShape.circle,
                                                      color: Theme.of(context).primaryColorDark,
                                                    ),
                                                    child: Padding(
                                                        padding: EdgeInsets.all(3),
                                                        child: SvgPicture.asset(
                                                          'assets/icons/close-popup.svg',
                                                          height: 10,
                                                          color: Theme.of(context).unselectedWidgetColor,
                                                        )
                                                    )
                                                )
                                            )
                                        ),
                                      ],
                                    ),
                                    SizedBox(
                                      height: 5,
                                    ),
                                    Container(
                                      width: 150,
                                      child: NarSelectBoxWidget(
                                        label: 'Seleziona ambiente',
                                        controller: tempController,
                                        onChanged: (value) async {
                                          if (value != null && value.isNotEmpty) {
                                            imageFile['tag'] = value;
                                            selectedProject!.pictures.removeWhere((pic) => pic['file'] == imageFile['file'].name);
                                            selectedProject!.pictures.add({'tag': value, 'file': imageFile['file'].name});
                                            await savePicturesImages(picturesImages, selectedProject!, setState);
                                            setState(() {});
                                          }
                                        },
                                        options: List.of(appConst.roomsList)..sort((a, b) => a.toLowerCase().compareTo(b.toLowerCase())),
                                        contentPadding: EdgeInsets.only(top: 10, bottom: 10, left: 10, right: 2),
                                      ),
                                    ),
                                  ],
                                );
                              } else if (snapshot.connectionState == ConnectionState.waiting) {
                                return Container(
                                  width: 80,
                                  height: 80,
                                  color: Colors.grey[300],
                                  child: Center(
                                    child: CircularProgressIndicator(),
                                  ),
                                );
                              } else {
                                return Container(
                                  width: 80,
                                  height: 80,
                                  color: Colors.grey[300],
                                  child: Center(
                                    child: Icon(Icons.error, color: Colors.red),
                                  ),
                                );
                              }
                            },
                          ))
                              .toList(),
                        ),
                      ),
                      Positioned(
                        top: picturesImages.isEmpty ? 200/2.5 : 5,
                        right: picturesImages.isEmpty ? MediaQuery.of(context).size.width/3.2 : 5,
                        child: picturesImages.isEmpty 
                        ?  BaseNewarcButton(
                          height: 45,
                          width: 300,
                          shadow: true,
                          shadowColor: Color(0xff499B79),
                          buttonText: "               Carica Immagini         ",
                          color: Color(0xff499B79),
                          textColor: Colors.white,
                          fontSize: 16,
                          suffixIcon: Padding(
                            padding: const EdgeInsets.only(left: 10.0),
                            child: Image.asset(
                              "assets/icons/upload_imgs.png", 
                              color: Colors.white, 
                              width: 20,)
                            ),
                          onPressed: () async {
                            await showUploadPicturesDialog(context, setState, picturesImages, selectedProject!);
                          },
                        )
                        : BaseNewarcButton(
                          color: Color(0xffE5E5E5),
                          textColor: Colors.black,
                          buttonText: "Carica altre",
                          fontWeight: '700',
                          fontSize: 14,
                          height: 35,
                          onPressed: () async {
                            await showUploadPicturesDialog(context, setState, picturesImages, selectedProject!);
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        )
      ],
    );
  }

  Widget selectModeDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        NarFormLabelWidget(
          label: 'Tipologia di generazione',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        Padding(
          padding: EdgeInsets.only(top: MediaQuery.of(context).size.height / 18),
          child: NarFormLabelWidget(
            label: "Seleziona la tipologia di generazione che preferisci per i tuoi interni.",
            fontWeight: '600',
            fontSize: 16,
            textColor: Theme.of(context).disabledColor,
          ),
        ),
        SizedBox(height: MediaQuery.of(context).size.height / 18,),
        Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: modeToFunctionName.keys.map((mode) {
            bool isSelectable = modeToFunctionName[mode] == "nakedInteriors";
            return Stack(
              clipBehavior: Clip.none,
              children: [
                Padding(
                  padding: const EdgeInsets.only(right: 12.0),
                  child: Opacity(
                    opacity: isSelectable ? 1 : 0.5,
                    child: IgnorePointer(
                      ignoring: !isSelectable,
                      child: _serviceModeWidget(mode, modeToDescriptionName[mode]!, modeToFunctionName[mode]!)
                    )
                  ),
                ),
                if (!isSelectable)
                Positioned(
                  top: -8,
                  left: 0,
                  right: 8,
                  child: Center(
                    child: TagWidget(
                      text: 'Presto disponibile',
                      changingColors: [
                        Color(0xff5abdb5),
                        Color(0xff499b79),
                        Color(0xff145935),
                      ],
                    ),
                  ),
                ),
              ],
            );}
          ).toList(),
        )
      ],
    );
  }

  Widget _serviceModeWidget(label, description, functionName, {isActive = true}){
    return MouseRegion(
      cursor: isActive ? SystemMouseCursors.click : MouseCursor.defer,
      child: GestureDetector(
        onTap: isActive 
        ? () {
          if (selectedMode == functionName) {
            setState(() {
              selectedMode = null;
              selectedProject!.smartServiceType = null;
            });
          } else {
            setState(() {
              selectedMode = functionName;
              selectedProject!.smartServiceType = functionName;
            });
          }
        }
        : (){},
        child: Stack(
          children: [
            if (isActive)
            Positioned(
              top: 10,
              right: 10,
              child: Container(
                // padding: EdgeInsets.all(2),
                child: Icon(
                  Icons.check, 
                  color: Colors.white,
                  size: 15,
                  weight: 100,
                ),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: selectedMode == functionName ? Colors.black : Colors.white,
                  border: Border.all(
                    color: selectedMode == functionName ? Colors.black : Color(0xffDBDBDB),
                    width: 1,
                  ),
                ),
              ),
            ),
            Container(
              width: 300,
              padding: isActive 
              ? EdgeInsets.only(
                left: 15, 
                top: 10, 
                right: 15,
                bottom: 10,
              ) : EdgeInsets.only(
                left: 5, 
                top: 5, 
                right: 5,
                bottom: 5,
              ),
              decoration: BoxDecoration(
                border: 
                (isActive && selectedMode == functionName)
                  ? Border.all(
                      color: Colors.black,
                      width: 2,
                    )
                  : isActive 
                    ? Border.all(
                      color: Color(0xffDBDBDB),
                      width: 1,
                    ) : null,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: isActive ? CrossAxisAlignment.start : CrossAxisAlignment.center,
                children: [
                  SizedBox(height: 5,),
                  Text(
                    label,
                    style: TextStyle(
                      fontFamily: isActive ? 'Raleway-800' : 'Raleway-600',
                      fontSize: 16,
                      color: Colors.black,
                    ),
                    overflow: TextOverflow.ellipsis,
                    // textAlign: TextAlign.left,
                  ),
                  if(isActive)
                  SizedBox(height: 10,),
                  if(isActive)
                  Container(
                    height: 50,
                    child: Text(
                      description,
                      style: TextStyle(
                        fontFamily: 'Raleway-500',
                        fontSize: 13,
                        color: Theme.of(context).disabledColor,
                      ),
                      overflow: TextOverflow.clip,
                    ),
                  ),
                  SizedBox(height: 10,),
                  Container(
                    height: isActive ? 250 : 120,
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: AssetImage(examplePicturePath[functionName]!),
                        fit: BoxFit.fitHeight,
                      ),
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget selectStyleDialog() {
    String? serviceType = selectedProject!.smartServiceType;
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        NarFormLabelWidget(
          label: 'Stile di generazione',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        Padding(
          padding: EdgeInsets.only(top: MediaQuery.of(context).size.height / 18),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              NarFormLabelWidget(
                label: "Seleziona lo stile che vorresti vedere applicato agli ${serviceType == 'furnishedRenewed' ? 'arredi e alle ristrutturazioni.' : 'arredi.'}",
                fontWeight: '600',
                fontSize: 16,
                textColor: Theme.of(context).disabledColor,
              ),
              SizedBox(height: 20,),
              Container(
                height: 380,
                width: MediaQuery.of(context).size.width/1.15,
                child: HorizontalScrollList(
                  initialPage: (selectedStyleIndex ?? 0) == 0 
                    ? 1 
                    : selectedStyleIndex! == stileToFunctionStyle.keys.length - 1
                      ? selectedStyleIndex! - 1
                      : selectedStyleIndex!,
                  items: stileToFunctionStyle.keys.map((mode) => Padding(
                    padding: const EdgeInsets.only(right: 8.0),
                    child: _serviceStyleWidget(mode, stileToDescriptionName[mode]!, stileToFunctionStyle[mode]!),
                  )).toList(),
                ),
              ),
            ],
          ),
        )
      ],
    );
  }

  Widget _serviceStyleWidget(label, description, styleName, {isActive = true}){
    return MouseRegion(
      cursor: isActive ? SystemMouseCursors.click : MouseCursor.defer,
      child: GestureDetector(
        onTap: !isActive 
        ? (){}
        : () {
          if (selectedStyle == styleName) {
            setState(() {
              selectedStyle = null;
              selectedProject!.smartServiceStyle = null;
            });
            selectedStyleIndex = stileToFunctionStyle.values.toList().indexOf(selectedStyle!);
          } else {
            setState(() {
              selectedStyle = styleName;
              selectedProject!.smartServiceStyle = styleName;
            });
            selectedStyleIndex = stileToFunctionStyle.values.toList().indexOf(selectedStyle!);
          }
        },
        child: Stack(
          children: [
            if (isActive)
            Positioned(
              top: 10,
              right: 20,
              child: Container(
                child: Icon(
                  Icons.check, 
                  color: Colors.white,
                  size: 15,
                  weight: 100,
                ),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: selectedStyle == styleName ? Colors.black : Colors.white,
                  border: Border.all(
                    color: selectedStyle == styleName ? Colors.black : Color(0xffDBDBDB),
                    width: 1,
                  ),
                ),
              ),
            ),
            Container(
              height: isActive ? 350 : null,
              width: isActive ? 300 : null,
              padding: isActive 
              ? EdgeInsets.only(
                left: 15, 
                top: 10, 
                right: 15,
                bottom: 10,
              ) : EdgeInsets.only(
                left: 5, 
                top: 5, 
                right: 5,
                bottom: 5,
              ),
              decoration: BoxDecoration(
                border: (isActive && selectedStyle == styleName)
                ? Border.all(
                    color: Colors.black,
                    width: 2,
                  )
                : isActive 
                  ? Border.all(
                    color: Color(0xffDBDBDB),
                    width: 1,
                  ) : null,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: isActive ? CrossAxisAlignment.start : CrossAxisAlignment.center,
                children: [
                  Text(
                    label,
                    style: TextStyle(
                      fontFamily: isActive ? 'Raleway-800' : 'Raleway-600',
                      fontSize: 16,
                      color: Colors.black,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                  if(isActive)
                  Text(
                    description,
                    style: TextStyle(
                      fontFamily: 'Raleway-500',
                      fontSize: 14,
                      color: Theme.of(context).disabledColor,
                    ),
                    overflow: TextOverflow.clip,
                  ),
                  SizedBox(height: 5,),
                  Container(
                    height: isActive ? 180 : 120,
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: AssetImage(stileToPicturePath[label]!),
                        fit: BoxFit.fitHeight,
                      ),
                      borderRadius: BorderRadius.circular(10),
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget completeRequestDialog(){// payment counters
    DateTime now = DateTime.now();
    final int subscriptionServiceCountLeft = widget.agencyUser?.agency?.smartSubscriptionServiceCountLeft ?? -1;
    DateTime expiryDate = DateTime.fromMillisecondsSinceEpoch(widget.agencyUser?.agency?.smartSubscriptionEndDate ?? 0);

    final bool areCreditsFinished = subscriptionServiceCountLeft <= 0;
    final bool isSubscriptionExpired = now.isAfter(expiryDate);
    final bool isSubscriptionOver = areCreditsFinished || isSubscriptionExpired;
    final bool canCompleteUsingCredits = subscriptionServiceCountLeft >= selectedProject!.pictures.length && !isSubscriptionOver;

    ImmaginaProjectEconomics economics = ImmaginaProjectEconomics(project: selectedProject!);

    String paymentTypeLabel = isSubscriptionOver 
      ? "Verrà aperta una nuova finestra per il pagamento di ${localCurrencyFormatMain.format(economics.computeSmartRequestFee() ?? (selectedProject!.pictures.length * ImmaginaProjectEconomics.smartRequestFee['price']))}€ +iva tramite Stripe." 
      : canCompleteUsingCredits
        ? "Verranno scalati ${selectedProject!.pictures.length} crediti Newarc Smart da quelli disponibili"
        : "Non hai abbastanza crediti per completare la richiesta. Prova a selezionare meno immagini o contatta l'assistenza per acquisire nuovi crediti.";

    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      mainAxisSize: MainAxisSize.min,
      children: [
        NarFormLabelWidget(
          label: 'Completa la richiesta',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        Container(
          height: MediaQuery.of(context).size.height/1.7,
          child: Row(
            children: [
              Expanded(
                child: Container(
                  padding: EdgeInsets.only(top:5),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Color(0xffDBDBDB),
                      width: 1,
                    ),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        'Immagini da elaborare',
                        style: TextStyle(
                          fontFamily: 'Raleway-800',
                          fontSize: 16,
                          color: Colors.black,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 20,),
                      Expanded(
                        child: SingleChildScrollView(
                          scrollDirection: Axis.vertical,
                          child: Wrap(
                            spacing: 12,
                            runSpacing: 20,
                            children: picturesImages.map((imageFile) {
                              return FutureBuilder<Uint8List>(
                                future: imageFile['file'].readAsBytes(),
                                builder: (context, snapshot) {
                                  if (snapshot.connectionState == ConnectionState.done && snapshot.hasData) {
                                    return Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        ClipRRect(
                                          borderRadius: BorderRadius.circular(8),
                                          child: Image.memory(
                                            snapshot.data!,
                                            width: 210,
                                            height: 140,
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                        SizedBox(height: 5,),
                                        Text(imageFile['tag'] ?? 'Nessun tag', style: TextStyle(fontSize: 12)),
                                      ],
                                    );
                                  } else if (snapshot.connectionState == ConnectionState.waiting) {
                                    return Container(
                                      width: 80,
                                      height: 80,
                                      color: Colors.grey[300],
                                      child: Center(child: CircularProgressIndicator()),
                                    );
                                  } else {
                                    return Container(
                                      width: 80,
                                      height: 80,
                                      color: Colors.grey[300],
                                      child: Center(child: Icon(Icons.error, color: Colors.red)),
                                    );
                                  }
                                },
                              );
                            }).toList(),
                          ),
                        ),
                      ),
                    ]
                  ),
                ),
              ),
              SizedBox(width: 20,),
              Expanded(
                child: Column(
                  children: [
                    Expanded(
                      child: Container(
                        width: double.infinity,
                        padding: EdgeInsets.only(top:5),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: Color(0xffDBDBDB),
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text(
                              'Tipologia di generazione',
                              style: TextStyle(
                                fontFamily: 'Raleway-800',
                                fontSize: 16,
                                color: Colors.black,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            _serviceModeWidget( 
                              functionNameToMode[selectedProject!.smartServiceType!]!, 
                              modeToDescriptionName[functionNameToMode[selectedProject!.smartServiceType!]!]!,
                              selectedProject!.smartServiceType!,
                              isActive: false,
                            )
                          ],
                        ),
                      )
                    ),
                    SizedBox(height: 20,),
                    Expanded(
                      child: selectedProject!.smartServiceType == 'nakedInteriors' 
                      ? SizedBox.shrink()
                      : Container(
                        padding: EdgeInsets.only(top:5),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: Color(0xffDBDBDB),
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text(
                              'Stile di generazione',
                              style: TextStyle(
                                fontFamily: 'Raleway-800',
                                fontSize: 16,
                                color: Colors.black,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            _serviceStyleWidget(
                              functionStyleToStile[selectedProject!.smartServiceStyle!]!, 
                              stileToDescriptionName[functionStyleToStile[selectedProject!.smartServiceStyle!]!]!,
                              selectedProject!.smartServiceStyle!, 
                              isActive: false,
                            )
                          ],
                        ),
                      )
                    )
                  ]
                ),
              ),
            ],
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Column(
              children: [
                BaseNewarcButton(
                  height: 45,
                  shadow: true,
                  shadowColor: Color(0xff499B79),
                  buttonText: isSubscriptionOver ? '     Paga e genera' : canCompleteUsingCredits ? '     Avvia generazione' : '     Credito insufficiente',
                  color: (isSubscriptionOver || canCompleteUsingCredits) ? Color(0xff499B79) : Colors.grey,
                  textColor: Colors.white,
                  fontSize: 16,
                  suffixIcon: Padding(
                    padding: const EdgeInsets.only(left: 10.0),
                    child: (isSubscriptionOver || canCompleteUsingCredits) 
                    ? Image.asset(
                      "assets/icons/smart-ai.png", 
                      color: Colors.white, 
                      width: 20,)
                      : SizedBox.shrink(),
                    ),
                  onPressed: (isSubscriptionOver || canCompleteUsingCredits) 
                  ? () async {
                    final docRef = FirebaseFirestore.instance
                          .collection(appConfig.COLLECT_IMMAGINA_PROJECTS)
                          .doc(selectedProject!.id);
                    // payment routine + send request to heroku server
                    if (isSubscriptionOver) {
                      setState(() {
                        loading = true;
                        _isFrozen = true;
                      });
                      // open tab before async calls so to avoid iOS popup blocking
                      final newTab = web.window.open('', '_blank');
                      
                      wasPaymentDetected = false;
                      callCounter = 0;
                      listenerTriggered = 0;

                      Map<String, dynamic> stripePriceIdsMap = economics.getStripePriceIdsForSmart();
    
                      try {
                        var linkMap = await getStripeCheckoutLink(
                          stripePriceIdsMap,
                          selectedProject!.id,
                          widget.agencyUser!.agencyId!,
                          origin: "smart",
                        );
    
                        if (linkMap['link'] == null) {
                          setState(() {
                            loading = false;
                            paymentError = true;
                            _isFrozen = false;
                          });
                          newTab!.close();
                          return;
                        }
    
                        // Assign the Stripe payment link to the tab
                        newTab!.location.href = linkMap['link']!;
    
                        // Check if the user closed the tab without completing payment
                        Future.doWhile(() async {
                          await Future.delayed(Duration(seconds: 1));
                          return !wasPaymentDetected && newTab.closed == false;
                        }).then((_) {
                          if (!wasPaymentDetected) {
                            setState(() {
                              _isFrozen = false;
                              loading = false;
                              paymentError = true;
                            });
                          }
                        });
    
                        // Firestore listener for successful payment
                        Future.delayed(Duration(seconds: 1), () {
                          StreamSubscription? subscription;
                          subscription = docRef
                              .snapshots()
                              .listen((event) async {
                            final data = event.data() ?? {};
                            print("Listener fired!");
                            print("listenerTriggered: $listenerTriggered,\ncallCounter: $callCounter");
                            listenerTriggered++;
                            print("updated listenerTriggered: $listenerTriggered");
                            if ((data['receivedPayment'] ?? false) == false) {
                              print("Payment not detected, skipping");
                              return;
                            }
                            if (wasPaymentDetected) {
                              print("Payment already detected, skipping duplicate processing");
                              return;
                            }
                            wasPaymentDetected = true;
                            print("Payment detected, processing subscription activation");
                            
                            // call heroku ai server
                            try {
                              await sendEmail(
                                templateId: CommonUtils.smartRequestReceivedAgencysideTemplateId, 
                                subject: CommonUtils.smartRequestReceivedAgencysideSubject, 
                                recipientEmail: widget.agencyUser?.agency?.email ?? widget.professionalsUser?.professional?.email ?? "",
                                recipientName: widget.agencyUser?.agency?.name ?? widget.professionalsUser?.professional?.companyName ?? "",
                                variables: {"projectid": selectedProject!.projectId ?? ""},
                              );
                              callCounter++;
                              print("updated callCounter: $callCounter");
                              var response = await callImmaginaSmartEndpoint(
                                projectId: selectedProject!.id, 
                                userId: (widget.agencyUser?.agencyId ?? widget.professionalsUser!.professionalId!), 
                                userType: widget.agencyUser != null ? "agency" : "professional",
                                generationType: selectedProject!.smartServiceType!,
                                generationStyle: selectedProject!.smartServiceStyle,
                              );
                              if (response['status'] == 200) {
                                print("callImmaginaSmartEndpoint called successfully");
                                // unlock screen and show confirmation popup
                                // await subscription?.cancel();
                                // Navigator.of(context).pop();
                                // confirmationPopup(context);
                                // setState(() {
                                //   loading = false;
                                //   _isFrozen = false;
                                //   paymentError = false;
                                // });
                              } else {
                                String errMessage = "callImmaginaSmartEndpoint failed with status and message: \n${response['status']}  \n${response['error']}";
                                print(errMessage);
                                // notify us about generation error
                                List workRecipientEmails = ["<EMAIL>", "<EMAIL>"];
                                Map <String, dynamic> workEmailVariables = {
                                  "projectid": selectedProject!.projectId ?? "",
                                  "agencyname": widget.agencyUser?.agency?.name ?? widget.professionalsUser?.professional?.companyName ?? "", 
                                  "failureorigin": "piattaforma - callImmaginaSmartEndpoint",
                                  "generationerror": errMessage,
                                  };
                                for (var recipientEmail in workRecipientEmails) {
                                  await sendEmail(
                                    templateId: CommonUtils.smartGenerationErrorForWorksideTemplateId, 
                                    subject: CommonUtils.smartGenerationErrorForWorksideSubject, 
                                    recipientEmail: recipientEmail, 
                                    recipientName: "Sviluppo",
                                    variables: workEmailVariables,
                                  );
                                };
                                // notify client
                                await sendEmail(
                                  templateId: CommonUtils.smartGenerationErrorAgencysideTemplateId, 
                                  subject: CommonUtils.smartGenerationErrorAgencysideSubject, 
                                  recipientEmail: widget.agencyUser?.agency?.email ?? widget.professionalsUser?.professional?.email ?? "",
                                  recipientName: widget.agencyUser?.agency?.name ?? widget.professionalsUser?.professional?.companyName ?? "",
                                  variables: {"projectid": selectedProject!.projectId ?? ""},
                                );
                                // set request status to "bloccata"
                                await docRef.update({
                                  "requestStatus": "bloccata",
                                  "blockNotes": "Problema durante la generazione, in risoluzione.",
                                  "blockedSection": "Errore generazione",
                                });

                                // // 31/07/2025 
                                // // Ste asks to disable giving back credits functionality
                                // // give back credits bought, if paying he has no subscription
                                // await FirebaseFirestore.instance
                                //   .collection(widget.agencyUser != null ? appConfig.COLLECT_AGENCIES : appConfig.COLLECT_PROFESSIONALS)
                                //   .doc(widget.agencyUser?.agencyId ?? widget.professionalsUser!.professionalId!)
                                //   .update({
                                //     "smartSubscriptionServiceCountLeft": selectedProject!.pictures.length,
                                //     "smartSubscriptionServiceCount": selectedProject!.pictures.length,
                                //     "smartSubscriptionEndDate": DateTime.now().millisecondsSinceEpoch + Duration(days: 30).inMilliseconds,
                                //     "smartSubscriptionStartDate": DateTime.now().millisecondsSinceEpoch,
                                //     "smartSubscriptionPaymentDate": DateTime.now().millisecondsSinceEpoch,
                                //     "smartSubscriptionId": null,
                                //   });

                                // // unlock screen and show error popup
                                // setState(() {
                                //   loading = false;
                                //   _isFrozen = false;
                                //   paymentError = false;
                                // });
                                // await subscription?.cancel();
                                // Navigator.of(context).pop();
                                // errorPopup(context, usingCredits: false);
                              };
                              await subscription?.cancel();
                              setState(() {
                                loading = false;
                                _isFrozen = false;
                                paymentError = false;
                              });
                              Navigator.of(context).pop();
                              confirmationPopup(context);
                            } catch (e) {
                              await subscription?.cancel();
                              print("Error calling heroku ai server: $e");
                              setState(() {
                                loading = false;
                                _isFrozen = false;
                                paymentError = false;
                              });
                            }
                          },
                          onError: (error) async {
                            await subscription?.cancel();
                            print("-------ERROR ${error.toString()}");
                            setState(() {
                              loading = false;
                              _isFrozen = false;
                              paymentError = false;
                            });
                          });
                        });
                      } catch (e, stackTrace) {
                        print("-------CATCH ERROR ${e.toString()}");
                        print("-------CATCH stackTrace ${stackTrace.toString()}");
                        setState(() {
                          loading = false;
                          paymentError = true;
                          _isFrozen = false;
                        });
                        newTab!.close();
                      }
                    } else {
                      try {
                        setState(() {
                          loading = true;
                          _isFrozen = true;
                        });
                        await sendEmail(
                          templateId: CommonUtils.smartRequestReceivedAgencysideTemplateId, 
                          subject: CommonUtils.smartRequestReceivedAgencysideSubject, 
                          recipientEmail: widget.agencyUser!.agency!.email, 
                          recipientName: widget.agencyUser!.agency!.name,
                          variables: {"projectid": selectedProject!.projectId ?? ""},
                        );
                        // update user credits       
                        await FirebaseFirestore.instance
                          .collection(widget.agencyUser != null ? appConfig.COLLECT_AGENCIES : appConfig.COLLECT_PROFESSIONALS)
                          .doc(widget.agencyUser?.agencyId ?? widget.professionalsUser!.professionalId!)
                          .update({
                            "smartSubscriptionServiceCountLeft": subscriptionServiceCountLeft - selectedProject!.pictures.length,
                          });
                        // update project status on firebase
                        await docRef
                          .update({
                            "receivedPayment": true,
                            "receivedPaymentDate": DateTime.now().millisecondsSinceEpoch,
                            "requestStatus": 'in analisi',
                            "statusChangedDate": DateTime.now().millisecondsSinceEpoch,
                            "isPaidWithCredits": true,
                          });
                        // call endpoint
                        final response = await callImmaginaSmartEndpoint(
                          projectId: selectedProject!.id, 
                          userId: (widget.agencyUser?.agencyId ?? widget.professionalsUser!.professionalId!), 
                          userType: widget.agencyUser != null ? "agency" : "professional",
                          generationType: selectedProject!.smartServiceType!,
                          generationStyle: selectedProject!.smartServiceStyle,
                        );
                        if (response['status'] == 200) {
                          print("callImmaginaSmartEndpoint called successfully");
                          // // unlock screen and show confirmation popup
                          // setState(() {
                          //   loading = false;
                          //   _isFrozen = false;
                          //   paymentError = false;
                          // });
                          // Navigator.of(context).pop();
                          // confirmationPopup(context);
                        } else {
                          String errMessage = "callImmaginaSmartEndpoint failed with status and message: \n${response['status']}  \n${response['error']}";
                          print(errMessage);
                          // notify us about generation error
                          List workRecipientEmails = ["<EMAIL>", "<EMAIL>"];
                          Map <String, dynamic> workEmailVariables = {
                            "projectid": selectedProject!.projectId ?? "",
                            "agencyname": widget.agencyUser?.agency?.name ?? widget.professionalsUser?.professional?.companyName ?? "", 
                            "failureorigin": "piattaforma - callImmaginaSmartEndpoint",
                            "generationerror": errMessage,
                            };
                          for (var recipientEmail in workRecipientEmails) {
                            await sendEmail(
                              templateId: CommonUtils.smartGenerationErrorForWorksideTemplateId, 
                              subject: CommonUtils.smartGenerationErrorForWorksideSubject, 
                              recipientEmail: recipientEmail, 
                              recipientName: "Sviluppo",
                              variables: workEmailVariables,
                            );
                          };
                          // notify client
                          await sendEmail(
                            templateId: CommonUtils.smartGenerationErrorAgencysideTemplateId, 
                            subject: CommonUtils.smartGenerationErrorAgencysideSubject, 
                            recipientEmail: widget.agencyUser?.agency?.email ?? widget.professionalsUser?.professional?.email ?? "",
                            recipientName: widget.agencyUser?.agency?.name ?? widget.professionalsUser?.professional?.companyName ?? "",
                            variables: {"projectid": selectedProject!.projectId ?? ""},
                          );
                          // set request status to "bloccata"
                          await docRef.update({
                            "requestStatus": "bloccata",
                            "blockNotes": "Problema durante la generazione, in risoluzione.",
                            "blockedSection": null,
                          });
  
                          // // unlock screen and show error popup
                          // setState(() {
                          //   loading = false;
                          //   _isFrozen = false;
                          //   paymentError = false;
                          // });
                          // Navigator.of(context).pop();
                          // errorPopup(context, usingCredits: true);
                        }
                        setState(() {
                          loading = false;
                          _isFrozen = false;
                          paymentError = false;
                        });
                        Navigator.of(context).pop();
                        confirmationPopup(context);
                      } catch (e) {
                        print("Error calling heroku ai server: $e");
                        setState(() {
                          loading = false;
                          _isFrozen = false;
                          paymentError = true;
                        });
                      }
                    }
                  } : (){},
                ),
                SizedBox(height: 15,),
                NarFormLabelWidget(
                  label: paymentTypeLabel,
                  fontSize: 14,
                  fontWeight: '500',
                  textColor: Color(0xff696969),
                  overflow: TextOverflow.clip,
                  ),
                if (paymentError) ...[
                  // SizedBox(
                  //   height: 5,
                  // ),
                  Text(
                    "Qualcosa è andato storto con il pagamento, riprova.",
                    textAlign: TextAlign.center,
                    style: TextStyle(fontFamily: 'Raleway-500', fontSize: 10, color: Colors.red),
                  ),
                ]
              ],
            )
          ],
        ),
      ],
    );
  }

  dynamic confirmationPopup(context) {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Center(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 50),
            child: Container(
              width: 800,
              child: Material(
                color: Colors.white,
                borderRadius: BorderRadius.circular(15),
                child: Container(
                  width: MediaQuery.of(context).size.width / 1.8,
                  // height: MediaQuery.of(context).size.height / 2,
                  padding: EdgeInsets.all(30),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        'assets/icons/check.svg',
                        color: Theme.of(context).primaryColor,
                      ),
                      SizedBox(
                        height: 30,
                      ),
                      Text(
                        "Richiesta di generazione inviata!",
                        textAlign: TextAlign.center,
                        style: TextStyle(fontFamily: 'Raleway-700', fontSize: 30, color: Colors.black),
                      ),
                      SizedBox(
                        height: 30,
                      ),
                      Container(
                        width: MediaQuery.of(context).size.width / 2.5,
                        child: Column(
                          children: [
                            Text(
                              "La tua richiesta sarà soddisfatta al più presto possibile.",
                              textAlign: TextAlign.center,
                              style: TextStyle(fontFamily: 'Raleway-500', fontSize: 14, color: Color(0xff4A4A4A)),
                            ),
                            SizedBox(
                              height: 30,
                            ),
                            Text(
                              "La generazione richiederà ${selectedProject!.pictures.length <= 5 ? "qualche minuto" : "circa ${((selectedProject!.pictures.length * 2).round()).toString()} minuti"}, verrai contattato via email quando la generazione sarà stata completata.",
                              textAlign: TextAlign.center,
                              overflow: TextOverflow.clip,
                              style: TextStyle(fontFamily: 'Raleway-500', fontSize: 14, color: Colors.grey[500]),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 50,
                      ),
                      SizedBox(
                        width: 200,
                        child: BaseNewarcButton(
                          buttonText: 'OK',
                          onPressed: () async {
                            Navigator.of(context).pop();
                            widget.onClose();
                          },
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ),
          )
        );
      }
    );
  }

  dynamic errorPopup(context, {usingCredits = true}) {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Center(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 50),
            child: Container(
              width: 800,
              child: Material(
                color: Colors.white,
                borderRadius: BorderRadius.circular(15),
                child: Container(
                  width: MediaQuery.of(context).size.width / 1.8,
                  // height: MediaQuery.of(context).size.height / 2,
                  padding: EdgeInsets.all(30),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        'assets/icons/close-popup.svg',
                        color: Theme.of(context).primaryColor,
                      ),
                      SizedBox(
                        height: 30,
                      ),
                      Text(
                        "Errore nella richiesta di generazione",
                        textAlign: TextAlign.center,
                        style: TextStyle(fontFamily: 'Raleway-700', fontSize: 30, color: Colors.black),
                      ),
                      SizedBox(
                        height: 30,
                      ),
                      Container(
                        width: MediaQuery.of(context).size.width / 2.5,
                        child: Column(
                          children: [
                            Text(
                              "Qualcosa è andato storto con la richiesta di generazione immagini.\nIl nostro team tecnico è stato allertato e risolverà il problema nel più breve tempo possibile.\nNel frattempo ${usingCredits ? 'i crediti utilizzati per la richiesta sono stati restituiti, riprova più avanti.' : 'ti verranno assegnati crediti Immagina Smart con la quale riprovare la generazione più avanti.'}",
                              textAlign: TextAlign.center,
                              style: TextStyle(fontFamily: 'Raleway-500', fontSize: 14, color: Color(0xff4A4A4A)),
                            ),
                            SizedBox(
                              height: 30,
                            ),
                            Text(
                              "Ci scusiamo per il disagio.",
                              textAlign: TextAlign.center,
                              style: TextStyle(fontFamily: 'Raleway-500', fontSize: 14, color: Colors.grey[500]),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 50,
                      ),
                      SizedBox(
                        width: 200,
                        child: BaseNewarcButton(
                          buttonText: 'OK',
                          onPressed: () async {
                            Navigator.of(context).pop();
                            widget.onClose();
                          },
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ),
          )
        );
      }
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: 20,
        vertical: 20,
      ),
      constraints: BoxConstraints(
        maxWidth: 1300,
        maxHeight: MediaQuery.of(context).size.height
      ),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        child: Stack(
          children: [
            Positioned(
              top: 18,
              right: 20,
              child: Container(
                height: 20,
                width: 20,
                child: MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                      child: SvgPicture.asset(
                        'assets/icons/close-popup.svg',
                        width: 18,
                        color: Color(0xffb3b3b3),
                      ),
                      onTap: () {
                        if (selectedView == 'initial') {
                          Navigator.of(context).pop();
                        } else if (
                          selectedView == 'complete-request' && 
                          ((selectedProject!.childrenProjects != null && selectedProject!.childrenProjects!.isNotEmpty)
                            ? selectedProject!.childrenProjects!.any((child) => child.grossSquareFootage! >= professionalsGSFUpperLimit)
                            : false)
                        ) {
                          Navigator.of(context).pop();
                          widget.onClose();
                        } else {
                          showDialog(
                              context: context,
                              barrierDismissible: false,
                              builder: (BuildContext context) {
                                return Center(
                                    child: Container(
                                      width: 400,
                                      child: BaseNewarcPopup(
                                        title: 'Vuoi davvero uscire \ndalla procedura?',
                                        noButton: true,
                                        isShowCloseIcon: true,
                                        closeIconColor: Color(0xFFB3B3B3),
                                        titleTextAlign: TextAlign.center,
                                        column: StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
                                          return Column(
                                            mainAxisAlignment: MainAxisAlignment.end,
                                            children: [
                                              ...!_isFrozen ? [BaseNewarcButton(
                                                buttonText: 'Rimani',
                                                onPressed: () {
                                                  Navigator.of(context).pop();
                                                },
                                                textColor: Colors.white,
                                                color: Colors.black,
                                                disableButton: _isFrozen,
                                              ),
                                              SizedBox(
                                                height: 15,
                                              ),
                                              BaseNewarcButton(
                                                buttonText: 'Salva e chiudi',
                                                onPressed: () async {
                                                  setState(() {
                                                    _isFrozen = true;
                                                    loading = true;
                                                  });
                                                  // save to selectedProject features not saved onChange
                                                  await savePicturesImages(picturesImages, selectedProject!, setState);
                                                  // save selectedProject to firestore
                                                  await FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).doc(selectedProject!.id).set(selectedProject!.toMap());
                                                  setState(() {
                                                    _isFrozen = false;
                                                    loading = false;
                                                  });
                                                  Navigator.of(context).pop();
                                                  Navigator.of(context).pop();
                                                  widget.onClose();
                                                },
                                                textColor: Colors.black,
                                                color: Colors.white,
                                                disableButton: _isFrozen,
                                              )]
                                              : [Center(child: CircularProgressIndicator())]
                                            ],
                                          );
                                        }),
                                      ),
                                    ));
                              });
                        }
                      }),
                ),
              ),
            ),
            Form(
              key: _formKey,
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Padding(
                    //   padding: EdgeInsets.symmetric(
                    //     horizontal: 20,
                    //   ),
                    //   child: NarFormLabelWidget(
                    //     label: 'Nuova richiesta progetto per professionisti',
                    //     textColor: Color(0xFF565656),
                    //     fontSize: 18,
                    //     fontWeight: '600',
                    //   ),
                    // ),
                    Padding(
                      padding: const EdgeInsets.only(
                        // left: 40.0,
                        // right: 40.0,
                        bottom: 20,
                        // top: 25,
                      ),
                      child: Container(
                        // width: MediaQuery.of(context).size.width / 1.5,
                        // height: MediaQuery.of(context).size.height / 1.5,
                        child: selectView(),
                      ),
                    ),
                    // SizedBox(height: 5),
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            formErrorMessage.length == 0 || formErrorMessage[0] == ''
                                ? Container()
                                : NarFormLabelWidget(
                              label: formErrorMessage.length > 0 ? formErrorMessage.join('') : '',
                              fontSize: 12,
                              fontWeight: 'bold',
                            ),
                          ],
                        ),
                        // SizedBox(height: 5),
                        Container(
                          // width: MediaQuery.of(context).size.width / 1.5, 
                          // height: MediaQuery.of(context).size.height / 10, 
                          child: Center(child: selectFooter())
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            if (_isFrozen)
              Positioned.fill(
                child: Container(
                  color: Colors.black54,
                  child: Center(
                    child: !loading ? CircularProgressIndicator() : null,
                  ), // Semi-transparent overlay
                ),
              ),
          ],
        ),
      ),
    );
  }
}


class HorizontalScrollList extends StatefulWidget {
  final List<Widget> items;
  final int initialPage;

  const HorizontalScrollList({Key? key, required this.items, this.initialPage = 0}) : super(key: key);

  @override
  State<HorizontalScrollList> createState() => _HorizontalScrollListState();
}

class _HorizontalScrollListState extends State<HorizontalScrollList> {
  late final PageController _pageController;
  late int _currentPage;

  @override
  void initState() {
    super.initState();
    _currentPage = widget.initialPage;
    _pageController = PageController(
      initialPage: _currentPage,
      viewportFraction: 1 / 3,
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _goLeft() {
    if (_currentPage > 1) {
      _currentPage--;
      _pageController.animateToPage(
        _currentPage,
        duration: const Duration(milliseconds: 300),
        curve: Curves.ease,
      );
    }
  }

  void _goRight() {
    if (_currentPage < widget.items.length - 2) {
      _currentPage++;
      _pageController.animateToPage(
        _currentPage,
        duration: const Duration(milliseconds: 300),
        curve: Curves.ease,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        IconButton(
          icon: const Icon(Icons.arrow_back_ios_rounded),
          onPressed: _goLeft,
        ),
        Expanded(
          child: SizedBox(
            child: PageView.builder(
              controller: _pageController,
              itemCount: widget.items.length,
              itemBuilder: (_, index) => widget.items[index],
              physics: const NeverScrollableScrollPhysics(),
            ),
          ),
        ),
        IconButton(
          icon: const Icon(Icons.arrow_forward_ios_rounded),
          onPressed: _goRight,
        ),
      ],
    );
  }
}

