import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/supplier.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/app_const.dart' as appConst;
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/file-picker.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/widget/UI/image-picker.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:path/path.dart' as p;
import 'package:firebase_auth/firebase_auth.dart';
import 'package:newarc_platform/widget/UI/multi-select-dropdown.dart';

class DitteSettingWidget extends StatefulWidget {
  final Supplier supplier;
  final Function? updateSelectedSupplier;
  final Function? fetchSupplier;

  const DitteSettingWidget(
      {Key? key,
      required this.supplier,
      this.updateSelectedSupplier,
      this.fetchSupplier})
      : super(key: key);

  @override
  State<DitteSettingWidget> createState() => _DitteSettingWidgetState();
}

class _DitteSettingWidgetState extends State<DitteSettingWidget> {
  Supplier? user;

  TextEditingController name = new TextEditingController();
  TextEditingController formationType = new TextEditingController();
  TextEditingController city = new TextEditingController();
  TextEditingController addressAndCivicNumber = new TextEditingController();
  TextEditingController firstName = new TextEditingController();
  TextEditingController lastName = new TextEditingController();
  TextEditingController phone = new TextEditingController();
  TextEditingController email = new TextEditingController();
  TextEditingController iban = new TextEditingController();
  TextEditingController billingCode = new TextEditingController();
  TextEditingController iva = new TextEditingController();
  TextEditingController legalAddress = new TextEditingController();

  bool isNewPasswordVisible = false;
  bool isConfirmPasswordVisible = false;

  final FirebaseFirestore _db = FirebaseFirestore.instance;
  final profilePicture = [];
  String? agencyProfilePicture;
  String? profilePictureFilename;
  String? validationMessage;
  final List<String> documents=[];
  List activityCategories = [];
  List selectedActivities = [];

  String? progressMessage;

  double containerWidth = 0;
  @override
  void initState() {
    super.initState();

    setValues();
    
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      fetchCategoryActivities();
    });
  }

  @protected
  void didUpdateWidget(DitteSettingWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    setValues();
    fetchCategoryActivities();
  }

  setValues() {
    name.text = widget.supplier.name!;
    formationType.text = widget.supplier.formationType!;
    city.text = widget.supplier.city!;
    addressAndCivicNumber.text = widget.supplier.addressAndCivicNumber!;
    firstName.text = widget.supplier.firstName!;
    lastName.text = widget.supplier.lastName!;
    phone.text = widget.supplier.phone!;
    email.text = widget.supplier.email!;
    iban.text = widget.supplier.iban!;
    billingCode.text = widget.supplier.billingCode!;
    iva.text = widget.supplier.iva!;
    legalAddress.text = widget.supplier.legalAddress!;
    
    
    /*selectedActivities.clear();

    activityCategories = appConst.professionalActivities
      .map((activity) => activity['category'] as String)
      .where((category) => category.isNotEmpty) // Remove empty categories
      .toSet() // Get unique categories
      .map((category) => {'label': category, 'value': category}) // Map to desired format
      .toList();
    
    if( widget.supplier.activities!.isNotEmpty ) {

      List _tempActivities = [];
      _tempActivities.addAll(widget.supplier.activities!);

      for (dynamic element in activityCategories) {

        if( _tempActivities.contains(element['value']) ) {
          selectedActivities.add(element);
          _tempActivities.remove(element['value']);
        }

        if( _tempActivities.isEmpty ) break;
      }

    }*/
    
    documents.clear();
    documents.addAll(widget.supplier.documents! as List<String>);
    
  }

  fetchCategoryActivities() async {

    activityCategories.clear();
    try {
      final FirebaseFirestore _db = FirebaseFirestore.instance;
      QuerySnapshot<Map<String, dynamic>> snapshot = await _db
          .collection(appConfig.COLLECT_CATEGORY_ACTIVITY)
          .get();

      if( snapshot.docs.length > 0 ) {
        
        for (var i = 0; i < snapshot.docs.length; i++) {
          // List<Map<String, dynamic>> existingActivities = 

          activityCategories.add({
            'value': snapshot.docs[i].data()['categoryName'],
            'label': snapshot.docs[i].data()['categoryName']
          });

        }

        selectedActivities.clear();
        if( widget.supplier.activities!.isNotEmpty ) {

          List _tempActivities = [];
          _tempActivities.addAll(widget.supplier.activities!);

          for (dynamic element in activityCategories) {

            if( _tempActivities.contains(element['value']) ) {
              selectedActivities.add(element);
              _tempActivities.remove(element['value']);
            }

            if( _tempActivities.isEmpty ) break;
          }

        }

      }
      setState(() {
        
      });
    } catch (e,s) {
      print({'fetchCategoryActivities',e,s});
    }

  }

  Future<bool> updateData(BuildContext context,
      {bool isArchive = false}) async {
    setState(() {
      validationMessage = null;
    });

    widget.supplier.name = name.text;
    widget.supplier.formationType = formationType.text;
    widget.supplier.city = city.text;
    widget.supplier.addressAndCivicNumber = addressAndCivicNumber.text;
    widget.supplier.firstName = firstName.text;
    widget.supplier.lastName = lastName.text;
    widget.supplier.phone = phone.text;
    widget.supplier.email = email.text;
    widget.supplier.iban = iban.text;
    widget.supplier.billingCode = billingCode.text;
    widget.supplier.iva = iva.text;
    widget.supplier.legalAddress = legalAddress.text;
    widget.supplier.isArchived = isArchive;

    widget.supplier.activities!.clear();
    selectedActivities.map((element) {
      widget.supplier.activities!.add(element['value']);
    }).toList();
    
    // print({'supplier', widget.supplier.toMap()});
    // return true;

    widget.supplier.documents!.clear();
    for (var i = 0; i < documents.length; i++) {
      widget.supplier.documents!.add(documents[i]);  
    }
    
    await _db
        .collection(appConfig.COLLECT_SUPPLIERS)
        .doc(widget.supplier.id)
        .update(widget.supplier.toMap());

    return true;
  }

  Future<bool> updateSupplierActiveStatus(Supplier supplier) async {
    final FirebaseFirestore _db = FirebaseFirestore.instance;
    return await _db
        .collection(appConfig.COLLECT_SUPPLIERS)
        .doc(supplier.id)
        .update(supplier.toMap())
        .then((value) {
      return true;
    }).onError((error, stackTrace) {
      // print({error, stackTrace});
      return false;
    });
  }

  final List<String> fileProgressMessage = [''];

  Future onVendorFileUploadasync() async {
    
    final FirebaseFirestore _db = FirebaseFirestore.instance;
    // widget.supplier.documents!.clear();
    for (var i = 0; i < documents.length; i++) {
      widget.supplier.documents!.add(documents[i]);
    }

    // widget.supplier.documents = documents;
    
    // print({ 'file save', widget.supplier.toMap() });

    try {
      await _db
          .collection(appConfig.COLLECT_SUPPLIERS)
          .doc(widget.supplier.id)
          .update(widget.supplier.toMap());

      // documents.clear();

      if (mounted) {
        setState(() {
          fileProgressMessage.clear();
          fileProgressMessage.add('Saved!');
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          fileProgressMessage.clear();
          fileProgressMessage.add('Error');
        });
      }
    }
  }

  popupAddCategory(BuildContext context, StateSetter initialSetStateFn){

    TextEditingController contCategoryName = new TextEditingController();
    String progressMessage = '';
    return showDialog(
      context: context,
      builder: (BuildContext _bc1) {
        return StatefulBuilder(
          builder: (BuildContext _bc2, StateSetter acSetState) {
            return Center(
              child: BaseNewarcPopup(
                title: 'Aggiungi Category',
                buttonText: 'Aggiungi',
                onPressed: () async {

                  try {
                    int existing = activityCategories.indexWhere((e) => e['value'].toString().toLowerCase() == contCategoryName.text.toLowerCase() );

                    if( existing > -1 ) {
                      acSetState((){
                        progressMessage = 'Category already exists!';
                      });
                      return false;
                    }

                    acSetState((){
                      progressMessage = 'Saving';
                    });
                    final FirebaseFirestore _db = FirebaseFirestore.instance;
                    await _db
                        .collection(appConfig.COLLECT_CATEGORY_ACTIVITY)
                        .add({
                          'categoryName' : contCategoryName.text
                        });

                    // add newly added category to the list.
                    activityCategories.add({ 'value': contCategoryName.text, 'label': contCategoryName.text});
                    acSetState((){
                      progressMessage = 'Saved';
                    });
                    setState(() {});

                    return true;
                  } catch (e,s) {
                    debugPrint('popupAddCategory');
                    debugPrint(e.toString());
                    debugPrintStack(stackTrace: s);
                    acSetState((){
                      progressMessage = 'Error occured! Try again later.';
                    });
                    return false;
                    
                  }

                  
                },
                column: Container(
                  height: 150,
                  width: 480,
                  padding: EdgeInsets.only(left: 40, right: 40),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomTextFormField(
                        label: "Category name",
                        controller: contCategoryName,
                        validator: (value) {
                          if (value == '') {
                            return 'Required!';
                          }

                          return null;
                        },
                      ),
                      SizedBox(
                        height: 5,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          NarFormLabelWidget(
                            label: progressMessage,
                            textAlign: TextAlign.center,
                            textColor: Color(0xff696969),
                            fontSize: 13,
                            fontWeight: '600',
                          ),
                        ],
                      ),
                    ],
                  ),
                )
              )
            );
          }
        );
      }
    ).then((_) {
      setState(() {
        
      });
    });
  
  }

  @override
  Widget build(BuildContext context) {
    containerWidth = MediaQuery.of(context).size.width * .75;

    return ListView(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            NarFormLabelWidget(
              label:
                  widget.supplier.name! + ' ' + widget.supplier.formationType!,
              fontSize: 20,
              fontWeight: 'bold',
            ),
            Row(
              children: [
                NarFormLabelWidget(
                  label: 'Attiva/Disattiva',
                  fontSize: 15,
                  fontWeight: '500',
                ),
                Switch(
                  // This bool value toggles the switch.
                  value: widget.supplier.isActive!,
                  activeThumbColor: Theme.of(context).primaryColor,
                  onChanged: (bool value) async {
                    // This is called when the user toggles the switch.

                    setState(() {
                      widget.supplier.isActive = value;
                    });
                    await updateSupplierActiveStatus(widget.supplier);
                  },
                ),
              ],
            )
          ],
        ),
        SizedBox(height: 35),
        NarFormLabelWidget(
          label: 'Dati Ditta',
          fontSize: 16,
          fontWeight: 'bold',
        ),
        SizedBox(height: 15),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomTextFormField(
              label: 'Nome Ditta',
              hintText: '',
              // initialValue: widget.agency.name,
              controller: name,
            ),
            SizedBox(
              width: 15,
            ),
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  NarFormLabelWidget(
                    label: "Forma Societaria",
                    textColor: Color(0xff696969),
                    fontSize: 14,
                    fontWeight: '600',
                  ),
                  SizedBox(height: 4),
                  NarSelectBoxWidget(
                      options: appConst.supplierFormationTypesList,
                      controller: formationType,
                      validationType: 'required',
                      parametersValidate: 'Required!'),
                ],
              ),
            ),
          ],
        ),
        SizedBox(
          height: 15,
        ),
        Row(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  NarFormLabelWidget(
                    label: "Città",
                    textColor: Color(0xff696969),
                    fontSize: 14,
                    fontWeight: '600',
                  ),
                  SizedBox(height: 4),
                  NarSelectBoxWidget(
                      options: ["", "Torino", "Milano", "Roma"],
                      controller: city,
                      validationType: 'required',
                      parametersValidate: ''),
                ],
              ),
            ),
            SizedBox(
              width: 15,
            ),
            CustomTextFormField(
              label: 'Indirizzo e numero civico',
              hintText: '',
              controller: addressAndCivicNumber,
            ),
            SizedBox(
              height: 0,
            )
          ],
        ),

        SizedBox(
          height: 15,
        ),
        Row(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      NarFormLabelWidget(
                        label: "Attività",
                        textColor: Color(0xff696969),
                        fontSize: 14,
                        fontWeight: '600',
                      ),
                      NarLinkWidget(
                        text: 'Aggiungi',
                        onClick: (){
                          popupAddCategory(context, setState);
                        },
                      )
                    ],
                  ),
                  SizedBox(height: 4),
                  MultiSelectDropdownWidget(
                                      
                    options: activityCategories,
                    initialValue: selectedActivities,
                    validationType: 'required',
                    parametersValidate: 'Obbligatorio',
                    onChanged: (List<dynamic> selectedValues) {
                      selectedActivities = selectedValues;
                      setState(() {});
                    },
                  )
                ],
              ),
            ),
            SizedBox(
              width: 15,
            ),
            Expanded(flex: 1, child: Container(),),
            SizedBox(
              height: 0,
            )
          ],
        ),
        SizedBox(
          height: 15,
        ),
        Container(
          width: containerWidth * .80,
          height: 1,
          decoration: BoxDecoration(
            color: Color(0xFFDCDCDC),
          ),
          child: SizedBox(height: 0),
        ),
        SizedBox(
          height: 15,
        ),
        NarFormLabelWidget(
          label: 'Persona di riferimento',
          fontSize: 16,
          fontWeight: 'bold',
        ),
        SizedBox(
          height: 15,
        ),
        Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            CustomTextFormField(
              label: 'Nome',
              hintText: '',
              controller: firstName,
            ),
            SizedBox(
              width: 15,
            ),
            CustomTextFormField(
                label: 'Cognome', hintText: '', controller: lastName),
          ],
        ),
        SizedBox(
          height: 15,
        ),
        Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            CustomTextFormField(
              label: 'Telefono',
              hintText: '',
              controller: phone,
            ),
            SizedBox(
              width: 15,
            ),
            CustomTextFormField(
              label: 'E-mail',
              hintText: '',
              controller: email,
            ),
          ],
        ),
        Container(
          width: containerWidth * .80,
          height: 1,
          decoration: BoxDecoration(
            color: Color(0xFFDCDCDC),
          ),
          child: SizedBox(height: 0),
        ),
        SizedBox(
          height: 15,
        ),
        NarFormLabelWidget(
          label: 'Fatturazione e pagamenti',
          fontSize: 16,
          fontWeight: 'bold',
        ),
        SizedBox(
          height: 15,
        ),
        Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            CustomTextFormField(
              label: 'Iban',
              hintText: '',
              controller: iban,
              inputFormatters: [ UpperCaseTextFormatter() ],
              textCapitalization: TextCapitalization.words,
            ),
            SizedBox(
              width: 15,
            ),
            CustomTextFormField(
                label: 'Codice Fatturazione Elettronico',
                hintText: '',
                controller: billingCode),
          ],
        ),
        SizedBox(
          height: 15,
        ),
        Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            CustomTextFormField(
              label: 'P.Iva',
              hintText: '',
              controller: iva,
            ),
            SizedBox(
              width: 15,
            ),
            CustomTextFormField(
              label: 'Sede Legale',
              hintText: '',
              controller: legalAddress,
            ),
          ],
        ),

        Container(
          width: containerWidth * .80,
          height: 1,
          decoration: BoxDecoration(
            color: Color(0xFFDCDCDC),
          ),
          child: SizedBox(height: 0),
        ),
        SizedBox(
          height: 15,
        ),


        Row(
          children: [
            Expanded(
              child: ListView(
                shrinkWrap: true,
                // crossAxisAlignment: CrossAxisAlignment.start,
                // mainAxisSize: MainAxisSize.max,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      NarFormLabelWidget(
                        label: 'Documenti',
                        fontSize: 20,
                        fontWeight: 'bold',
                      ),
                      NarFilePickerWidget(
                        allowMultiple: true,
                        displayFormat: 'inline-button',
                        borderRadius: 7,
                        fontSize: 11,
                        fontWeight: '600',
                        text: 'Carica documenti',
                        borderSideColor: Theme.of(context).primaryColor,
                        hoverColor: Color.fromRGBO(133, 133, 133, 1),
                        allFiles: documents,
                        pageContext: context,
                        storageDirectory:
                            'suppliers/${widget.supplier.id}/',
                        progressMessage: fileProgressMessage,
                        // displayInlineWidget: displayInlineWidget,
                        onUploadCompleted: onVendorFileUploadasync,
                      )
                    ],
                  ),
                  SizedBox(height: 30),
                  Container(
                    width: 350,
                    child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: 1,
                        itemBuilder: (context, index) {
                          return Column(
                              mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                NarFilePickerWidget(
                                  allowMultiple: false,
                                  filesToDisplayInList: 0,
                                  removeButton: true,
                                  isDownloadable: true,
                                  removeButtonText: 'Elimina',
                                  uploadButtonPosition: 'back',
                                  showMoreButtonText: '+ espandi',
                                  actionButtonPosition: 'bottom',
                                  displayFormat: 'inline-widget',
                                  containerWidth: 110,
                                  containerHeight: 110,
                                  containerBorderRadius: 13,
                                  borderRadius: 7,
                                  fontSize: 11,
                                  fontWeight: '600',
                                  text: 'Carica Progetto',
                                  borderSideColor:
                                      Theme.of(context).primaryColor,
                                  hoverColor:
                                      Color.fromRGBO(133, 133, 133, 1),
                                  allFiles: documents,
                                  pageContext: context,
                                  storageDirectory:
                                      'suppliers/${widget.supplier.id}/',
                                  removeExistingOnChange: true,
                                  progressMessage: fileProgressMessage,
                                  onUploadCompleted: onVendorFileUploadasync,
                                )
                              ]);
                        }),
                  )
                ],
              ),
            ),
          ],
        ),
        

        NarFormLabelWidget(
          label: validationMessage != '' ? validationMessage : '',
          fontSize: 12,
          textColor: Colors.red,
        ),
        SizedBox(
          height: 50,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          mainAxisSize: MainAxisSize.max,
          children: [
            NarFormLabelWidget(
              label: progressMessage != '' ? progressMessage : '',
              fontSize: 12,
            )
          ],
        ),
        SizedBox(height: 10),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          mainAxisSize: MainAxisSize.max,
          children: [
            BaseNewarcButton(
                notAccent: true,
                buttonText: "Elimina ditta",
                onPressed: () async {
                  setState(() {
                    progressMessage = 'Eliminazione in corso';
                  });
                  bool unlock = await showAlertDialog(
                      context,
                      "Conferma eliminazione",
                      "Sei sicuro di voler eliminare questa ditta?",
                      addCancel: true);
                  if (unlock) {
                    bool response = await updateData(context, isArchive: true);
                    if (response == true) {
                      widget.fetchSupplier!();
                    } else {
                      setState(() {
                        progressMessage =
                            'Si è verificato un errore. Contatta l\'assistenza.';
                      });
                    }
                  }
                }),
            BaseNewarcButton(
                buttonText: "Salva",
                onPressed: () async {
                  setState(() {
                    progressMessage = 'Salvataggio in corso...';
                  });
                  bool response = await updateData(context, isArchive: false);
                  if (response == true) {
                    setState(() {
                      progressMessage = '';
                      //widget.getProfilePicture!();
                    });
                    await showAlertDialog(context, "Salvataggio",
                        "Informazioni ditta salvate con successo");

                    widget.updateSelectedSupplier!();
                  } else {
                    setState(() {
                      progressMessage =
                          'Si è verificato un errore. Contatta l\'assistenza.';
                    });
                  }
                })
          ],
        )
      ],
    );
  }
}
