
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/classes/renovationContact.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/utils/const.dart' as utilConst;

class ProjectDashboard extends StatefulWidget {
  final NewarcProject? project;
  final Function? updateProject;

  const ProjectDashboard({Key? key, this.project, this.updateProject})
      : super(key: key);

  @override
  State<ProjectDashboard> createState() => _ProjectDashboardState();
}

class _ProjectDashboardState extends State<ProjectDashboard> {
  String progressMessage = '';
  bool loading = false;
  List<String> formMessages = [''];
  bool loadingContacts = false;
  List<Map> contacts = [];

  bool titleEnabled = true;
  TextEditingController contProjectType = new TextEditingController();
  TextEditingController contProjectName = new TextEditingController();
  List<RenovationContact> renovationContact = [];
  List<String> jobsInProgress = [];

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setInitialValues();
    });

    // print({'widget.project!.projectJobs', widget.project!.projectJobs});
    jobsInProgress = getJobsInProgress(widget.project!.projectJobs);
  }

  @protected
  void didUpdateWidget(ProjectDashboard oldWidget) {
    setInitialValues();
    super.didUpdateWidget(oldWidget);
  }

  setInitialValues() async {

    utilConst.projectJobsActivities = await getProjectActivities();
  }

  projectArchivePopup() {
    return BaseNewarcPopup(
      formErrorMessage: formMessages,
      buttonText: 'Termina',
      onPressed: () async {
        setState(() {
          formMessages.clear();
          formMessages.add('Termina in corso');
        });

        final FirebaseFirestore _db = FirebaseFirestore.instance;

        widget.project!.isArchived = true;
        await _db
            .collection(appConfig.COLLECT_NEWARC_PROJECTS)
            .doc(widget.project!.id)
            .update(widget.project!.toMap());

        await _db
            .collection(appConfig.COLLECT_RENOVATION_CONTACTS)
            .doc(widget.project!.renovationContactId)
            .update({'renovationStatus': 'completato'});

        widget.updateProject!(widget.project);

        setState(() {
          formMessages.clear();
          formMessages.add('Project created!');
        });

        return true;
      },
      title: "Termina progetto",
      column: Container(
        width: 600,
        margin: EdgeInsets.symmetric(vertical: 30),
        child: Center(
          child: ListView(
            shrinkWrap: true,
            children: [
              NarFormLabelWidget(
                label: 'Vuoi davvero terminare il progetto?',
                fontSize: 18,
                textAlign: TextAlign.center,
                fontWeight: '600',
              )
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: double.infinity,
      child: loading == true
          ? NarFormLabelWidget(label: 'Loading')
          : Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Expanded(
                  child: ListView(
                    // crossAxisAlignment: CrossAxisAlignment.start,
                    // mainAxisAlignment: MainAxisAlignment.start,
                    // mainAxisSize: MainAxisSize.max,
                    children: [
                      SizedBox(height: 20),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          NarFormLabelWidget(
                            label: 'Informazioni generali',
                            fontSize: 20,
                            fontWeight: 'bold',
                          ),
                          Row(
                            children: [
                              
                              widget.project!.type == 'Immagina'
                              ? BaseNewarcButton(
                                  onPressed: () {
                                    showDialog(
                                        context: context,
                                        builder: (BuildContext _bc1) {
                                          return StatefulBuilder(builder: (BuildContext _bc2, StateSetter _setState) {
                                            return Center(
                                              child: BaseNewarcPopup(
                                                title: 'Trasforma progetto',
                                                buttonText: 'Trasforma',

                                                onPressed: () async{

                                                  final FirebaseFirestore _db = FirebaseFirestore.instance;

                                                  widget.project!.type = 'Ristrutturazione';
                                                  await _db
                                                      .collection(appConfig.COLLECT_NEWARC_PROJECTS)
                                                      .doc(widget.project!.id)
                                                      .update(widget.project!.toMap());

                                                  widget.updateProject!(widget.project, updateTabs: true);

                                                  setState((){});
                                                  return true;
                                                },
                                                column: Container(
                                                  height: 100,
                                                  width: 500,
                                                  child: Center(
                                                  
                                                    child: NarFormLabelWidget(
                                                        label: "Vuoi trasformare il progetto\nin una ristrutturazione?",
                                                        fontSize: 18,
                                                        fontWeight: '600',
                                                        textColor: Color(0xff696969),
                                                        textAlign: TextAlign.center,
                                                        height: 1.5,
                                                      ),
                                                  ),
                                                ),
                                              ),
                                            );
                                          });
                                        });
                                  },
                                  buttonText: 'Trasforma progetto',
                                  fontSize: 15
                                )
                              : Container(),
                              SizedBox(width: 10,),

                              widget.project!.isArchived == true
                                  ? SizedBox(
                                      height: 0,
                                    )
                                  : BaseNewarcButton(
                                      onPressed: () {
                                        showDialog(
                                            context: context,
                                            builder: (BuildContext _bc1) {
                                              return StatefulBuilder(builder:
                                                  (BuildContext _bc2,
                                                      StateSetter setState) {
                                                return Center(
                                                  child: projectArchivePopup(),
                                                );
                                              });
                                            });
                                      },
                                      buttonText: 'Termina progetto',
                                      notAccent: true,
                                      fontSize: 15)
                            ],
                          )
                        ],
                      ),
                      SizedBox(height: 30),
                      Container(
                          // color: Colors.grey,
                          // width: 200,
                          padding: EdgeInsets.symmetric(
                              vertical: 20, horizontal: 15),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(
                              width: 1,
                              color: Color.fromRGBO(230, 230, 230, 1),
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              NarFormLabelWidget(
                                label: 'Ristrutturazione',
                                fontSize: 17,
                                fontWeight: '700',
                                textColor: Color.fromRGBO(0, 0, 0, 1),
                              ),
                              Row(
                                children: [
                                  Column(
                                    children: [
                                      NarFormLabelWidget(
                                        label: "Inizio Lav.",
                                        textColor: Color(0xff838383),
                                        fontSize: 12,
                                        fontWeight: '500',
                                      ),
                                      NarFormLabelWidget(
                                        label: timestampToUtcDate(
                                            widget.project!.jobStartDate!),
                                        textColor: Colors.black,
                                        fontSize: 12,
                                        fontWeight: '800',
                                      ),
                                    ],
                                  ),
                                  SizedBox(width: 8),
                                  Stack(
                                    alignment: Alignment.centerLeft,
                                    children: [
                                      Container(
                                        height: 20,
                                        width:
                                            MediaQuery.sizeOf(context).width -
                                                940,
                                        decoration: BoxDecoration(
                                            color: Color(0xffd9d9d9),
                                            borderRadius:
                                                BorderRadius.circular(100)),
                                      ),
                                      Container(
                                        height: 20,
                                        width: (MediaQuery.sizeOf(context)
                                                    .width -
                                                940) *
                                            calculateProjectCompletionPercentage(
                                                widget.project!.projectJobs),
                                        decoration: BoxDecoration(
                                            color:
                                                Theme.of(context).primaryColor,
                                            borderRadius:
                                                BorderRadius.circular(100)),
                                      ),
                                      Padding(
                                        padding: EdgeInsets.only(
                                          left: (MediaQuery.sizeOf(context)
                                                      .width -
                                                  960) *
                                              calculateProjectCompletionPercentage(
                                                  widget.project!.projectJobs),
                                        ),
                                        child: Container(
                                          height: 40,
                                          width: 40,
                                          decoration: BoxDecoration(
                                              color: Colors.white,
                                              borderRadius:
                                                  BorderRadius.circular(100),
                                              boxShadow: [
                                                BoxShadow(
                                                    color: Colors.black
                                                        .withOpacity(0.2),
                                                    spreadRadius: 3,
                                                    blurRadius: 10)
                                              ]),
                                        ),
                                      ),
                                      Padding(
                                        padding: EdgeInsets.only(
                                            left: (MediaQuery.sizeOf(context)
                                                        .width -
                                                    1050) *
                                                calculateProjectCompletionPercentage(
                                                    widget
                                                        .project!.projectJobs),
                                            top: 80 +
                                                20 *
                                                    (jobsInProgress.length -
                                                        1)),
                                        child: jobsInProgress.length == 0
                                            ? Container()
                                            : Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  NarFormLabelWidget(
                                                    label: "IN CORSO",
                                                    textColor: Theme.of(context)
                                                        .primaryColor,
                                                    fontSize: 14,
                                                  ),
                                                  Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: List.generate(
                                                        jobsInProgress.length,
                                                        (index) => Row(
                                                              children: [
                                                                Container(
                                                                  width: 3,
                                                                  height: 3,
                                                                  decoration:
                                                                      BoxDecoration(
                                                                    color: Colors
                                                                        .black,
                                                                    borderRadius:
                                                                        BorderRadius.circular(
                                                                            50),
                                                                  ),
                                                                ),
                                                                SizedBox(
                                                                    width: 5),
                                                                NarFormLabelWidget(
                                                                    label: jobsInProgress
                                                                        .elementAt(
                                                                            index))
                                                              ],
                                                            )),
                                                  )
                                                ],
                                              ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(width: 8),
                                  Column(
                                    children: [
                                      NarFormLabelWidget(
                                        label: "Fine Lav.",
                                        textColor: Color(0xff838383),
                                        fontSize: 12,
                                        fontWeight: '500',
                                      ),
                                      NarFormLabelWidget(
                                        label: timestampToUtcDate(widget
                                            .project!.hypotheticalJobEndDate!),
                                        textColor: Colors.black,
                                        fontSize: 12,
                                        fontWeight: '700',
                                      ),
                                    ],
                                  ),
                                ],
                              )
                            ],
                          ))
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    NarFormLabelWidget(label: progressMessage),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          BaseNewarcButton(
                            buttonText: "Salva",
                            onPressed: () async {
                              setState(() {
                                progressMessage = 'Salvataggio in corso...';
                              });

                              Map<String, dynamic> vendorData = {};

                              // print(vendorData);
                              // return;``

                              widget.project!.assignedVendor = vendorData;
                              final FirebaseFirestore _db =
                                  FirebaseFirestore.instance;

                              try {
                                await _db
                                    .collection(
                                        appConfig.COLLECT_NEWARC_PROJECTS)
                                    .doc(widget.project!.id)
                                    .update(widget.project!.toMap());

                                setState(() {
                                  progressMessage = 'Saved!';
                                });
                              } catch (e) {
                                setState(() {
                                  progressMessage = 'Errore: ' + e.toString();
                                });
                              }
                            },
                          )
                        ],
                      ),
                    ),
                  ],
                )
              ],
            ),
    );
  }
}
