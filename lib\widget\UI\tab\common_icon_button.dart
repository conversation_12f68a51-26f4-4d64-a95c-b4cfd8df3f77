import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:newarc_platform/utils/color_schema.dart';

class IconButtonWidget extends StatelessWidget {
  const IconButtonWidget({
    super.key,
    this.icon,
    this.iconColor,
    this.backgroundColor,
    this.borderRadius,
    this.isSvgIcon,
    this.onTap,
    this.isOnlyBorder,
    this.borderColor,
    this.iconPadding,
    this.height,
    this.width,
  });

  final String? icon;
  final bool? isSvgIcon, isOnlyBorder;
  final Color? iconColor, backgroundColor, borderColor;
  final double? borderRadius, height, width;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? iconPadding;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          height: height ?? 30,
          width: width ?? 30,
          decoration: BoxDecoration(
            color: backgroundColor ?? AppColor.lightGreyColor,
            border: isOnlyBorder == true
                ? Border.all(
                    width: 1,
                    color: borderColor ?? AppColor.lightGreyColor,
                  )
                : null,
            borderRadius: BorderRadius.circular(
              borderRadius ?? 8,
            ),
          ),
          padding: iconPadding ?? EdgeInsets.all(6),
          child: isSvgIcon == true
              ? SvgPicture.asset(
                  icon ?? "",
                  color: iconColor,
                )
              : Image.asset(
                  icon ?? "",
                  color: iconColor,
                ),
        ),
      ),
    );
  }
}


