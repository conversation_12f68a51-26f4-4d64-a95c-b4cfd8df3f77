import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/supplier.dart';
import 'package:newarc_platform/classes/user.dart';
//import 'package:newarc_platform/widget/agency/custom_chart.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/app_const.dart' as appConst;
// import 'package:newarc_platform/pages/work/agency_settings_widget.dart';
import 'package:newarc_platform/pages/work/ditte_settings_widget.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
// import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/utils/various.dart';
// import 'package:newarc_platform/widget/work/inner_side_menu_entry.dart';

class DitteNewarc extends StatefulWidget {
  const DitteNewarc({Key? key, required this.responsive}) : super(key: key);

  final bool responsive;

  @override
  State<DitteNewarc> createState() => _DitteNewarcState();
}

class _DitteNewarcState extends State<DitteNewarc> {
  bool loading = false;
  Supplier? selectedSupplier = new Supplier({}, "");
  bool isSelected = false;
  List<NewarcUser> users = [];
  final List<String> formMessages = [];
  int supplierCount = 0;

  TextEditingController name = new TextEditingController();
  TextEditingController formationType = new TextEditingController();
  TextEditingController city = new TextEditingController();
  TextEditingController addressAndCivicNumber = new TextEditingController();
  TextEditingController firstName = new TextEditingController();
  TextEditingController lastName = new TextEditingController();
  TextEditingController phone = new TextEditingController();
  TextEditingController email = new TextEditingController();
  TextEditingController iban = new TextEditingController();
  TextEditingController billingCode = new TextEditingController();
  TextEditingController iva = new TextEditingController();
  TextEditingController legalAddress = new TextEditingController();

  Map supplierTypeIndex = {'Torino': 0, 'Milano': 1, 'Roma': 2};

  List dataList = [];

  @override
  void initState() {
    loading = true;
    if (mounted) {
      fetchSupplier();
    }

    super.initState();
  }

  updateSelectedSupplier() {
    for (var i = 0; i < dataList.length; i++) {
      for (var j = 0; j < dataList[i]['subMenu'].length; j++) {
        if (dataList[i]['subMenu'][j]['id'] == selectedSupplier!.id) {
          setState(() {
            dataList[i]['subMenu'][j]['name'] = selectedSupplier!.name! +
                ' ' +
                selectedSupplier!.formationType!;
            dataList[i]['subMenu'][j]['supplier'] = selectedSupplier;
          });
          break;
        }
      }
    }
  }

  Future<void> fetchSupplier() async {
    dataList = [
      {"name": "Torino", "isExpanded": false, "subMenu": []},
      {"name": "Milano", "isExpanded": false, "subMenu": []},
      {"name": "Roma", "isExpanded": false, "subMenu": []}
    ];
    isSelected = false;

    QuerySnapshot<Map<String, dynamic>> collectionSnapshot =
        await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_SUPPLIERS)
            .where('isArchived', isEqualTo: false)
            .get();

    for (var element in collectionSnapshot.docs) {
      try {
        Supplier supplier = Supplier.fromDocument(element.data(), element.id);
        if (supplier.name == "Newarc") {
          continue;
        }
        var selected = false;

        if (isSelected == false) {
          selectedSupplier = supplier;
          isSelected = true;
          selected = true;
        }

        supplierCount++;

        var typeIndex = supplierTypeIndex[supplier.city];

        dataList[typeIndex]['isExpanded'] = selected;
        dataList[typeIndex]['subMenu'].add({
          'id': supplier.id!,
          'name': supplier.name! + ' ' + supplier.formationType!,
          'formationType': supplier.formationType,
          'isActive': supplier.isActive,
          'selected': selected,
          'supplier': supplier
        });
      } catch (e) {
        // print("error in document ${element.id}");
        // print(e);
      }
    }

    // Select first tab of the first menu
    if (dataList[0]['subMenu'].length > 0) {
      unselectAllTabs();
      dataList[0]['isExpanded'] = true;
      selectedSupplier = dataList[0]['subMenu'][0]['supplier'];
      dataList[0]['subMenu'][0]['selected'] = true;
    }

    setState(() {
      // users = _users;
      loading = false;
    });
  }

  unselectAllTabs() {
    for (var i = 0; i < dataList.length; i++) {
      dataList[i]['isExpanded'] = false;
      for (var j = 0; j < dataList[i]['subMenu'].length; j++) {
        dataList[i]['subMenu'][j]['selected'] = false;
      }
    }
  }

  _buildExpandableContent(userType) {
    List<Widget> columnContent = [];

    for (var i = 0; i < userType['subMenu'].length; i++)
      // print({ vehicle['subMenu'][i] });
      columnContent.add(
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () {
              unselectAllTabs();
              setState(() {
                selectedSupplier = userType['subMenu'][i]['supplier'];
                userType['subMenu'][i]['selected'] =
                    !userType['subMenu'][i]['selected'];
              });
            },
            child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: userType['subMenu'][i]['selected']
                      ? Color.fromRGBO(72, 155, 121, 1)
                      : Color.fromRGBO(240, 240, 240, 1),
                ),
                width: double.infinity,
                padding: EdgeInsets.symmetric(vertical: 15, horizontal: 15),
                margin: EdgeInsets.only(bottom: 8),
                child: NarFormLabelWidget(
                  label: userType['subMenu'][i]['name'],
                  fontSize: 14,
                  fontWeight: 'bold',
                  textColor: userType['subMenu'][i]['selected']
                      ? Colors.white
                      : Colors.black,
                )),
          ),
        ),
      );

    return columnContent;
  }

  @override
  Widget build(BuildContext context) {
    return loading
        ? Center(
            child: CircularProgressIndicator(
            color: Theme.of(context).primaryColor,
          ))
        : Column(
            children: [
              Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    NarFormLabelWidget(
                      label: 'Gestisci Ditte',
                      fontSize: 22,
                      fontWeight: 'bold',
                    ),
                    BaseNewarcButton(
                        buttonText: "Aggiungi Ditta",
                        onPressed: () async {
                          return await showDialog(
                              context: context,
                              builder: (BuildContext context) {
                                return StatefulBuilder(
                                    builder: (context, state) {
                                  return Center(
                                    child: BaseNewarcPopup(
                                      formErrorMessage: formMessages,
                                      buttonText: 'Aggiungi ditta',
                                      onPressed: () async {
                                        setState(() {
                                          formMessages.clear();
                                          formMessages
                                              .add('Salvataggio in corso...');
                                        });

                                        Supplier supplierData =
                                            new Supplier({}, "");

                                        supplierData.name = name.text;
                                        supplierData.formationType =
                                            formationType.text;
                                        supplierData.city = city.text;
                                        supplierData.addressAndCivicNumber =
                                            addressAndCivicNumber.text;
                                        supplierData.firstName = firstName.text;
                                        supplierData.lastName = lastName.text;
                                        supplierData.phone = phone.text;
                                        supplierData.email = email.text;
                                        supplierData.iban = iban.text;
                                        supplierData.billingCode =
                                            billingCode.text;
                                        supplierData.iva = iva.text;
                                        supplierData.legalAddress =
                                            legalAddress.text;
                                        supplierData.isArchived = false;

                                        final FirebaseFirestore _db =
                                            FirebaseFirestore.instance;
                                        try {
                                          await _db
                                              .collection(
                                                  appConfig.COLLECT_SUPPLIERS)
                                              .add(supplierData.toMap());

                                          setState(() {
                                            formMessages.clear();
                                            formMessages
                                                .add('Supplier created!');
                                          });

                                          fetchSupplier();
                                          return true;
                                        } catch (e) {
                                          setState(() {
                                            formMessages.clear();
                                            formMessages.add(
                                                'Something went wrong! ' +
                                                    e.toString());
                                          });
                                          return false;
                                        }
                                      },
                                      title: "Aggiungi una ditta",
                                      column: Container(
                                        width: 600,
                                        height: 400,
                                        child: ListView(
                                          children: [
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.start,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                CustomTextFormField(
                                                  // hintText: "Nome Ditta",
                                                  label: "Nome Ditta",
                                                  controller: name,
                                                  // validationMessage: 'Required!',
                                                  validator: (value) {
                                                    if (value == '') {
                                                      return 'Required!';
                                                    }

                                                    return null;
                                                  },
                                                ),
                                                SizedBox(
                                                  width: 15,
                                                ),
                                                Expanded(
                                                  child: Column(
                                                    mainAxisSize:
                                                        MainAxisSize.max,
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    mainAxisAlignment:
                                                        MainAxisAlignment.start,
                                                    children: [
                                                      NarFormLabelWidget(
                                                        label:
                                                            "Forma Societaria",
                                                        textColor:
                                                            Color(0xff696969),
                                                        fontSize: 14,
                                                        fontWeight: '600',
                                                      ),
                                                      SizedBox(height: 4),
                                                      NarSelectBoxWidget(
                                                          options: appConst.supplierFormationTypesList,
                                                          controller:
                                                              formationType,
                                                          validationType:
                                                              'required',
                                                          parametersValidate:
                                                              'Required!'),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: 10),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.start,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Expanded(
                                                  child: Column(
                                                    mainAxisSize:
                                                        MainAxisSize.max,
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    mainAxisAlignment:
                                                        MainAxisAlignment.start,
                                                    children: [
                                                      NarFormLabelWidget(
                                                        label: "Città",
                                                        textColor:
                                                            Color(0xff696969),
                                                        fontSize: 14,
                                                        fontWeight: '600',
                                                      ),
                                                      SizedBox(height: 4),
                                                      NarSelectBoxWidget(
                                                          options: [
                                                            "Torino",
                                                            "Milano",
                                                            "Roma"
                                                          ],
                                                          controller: city,
                                                          validationType:
                                                              'required',
                                                          parametersValidate:
                                                              'Required!'),
                                                    ],
                                                  ),
                                                ),
                                                SizedBox(
                                                  width: 15,
                                                ),
                                                CustomTextFormField(
                                                  // hintText: "Indirizzo e numero civico",
                                                  label:
                                                      "Indirizzo e numero civico",
                                                  controller:
                                                      addressAndCivicNumber,
                                                  // validationMessage: 'Required!',
                                                  validator: (value) {
                                                    if (value == '') {
                                                      return 'Required!';
                                                    }
                                                    return null;
                                                  },
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: 10),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                CustomTextFormField(
                                                  // hintText: "P.Iva",
                                                  label: "P.Iva",
                                                  controller: iva,
                                                  // validationMessage: 'Required!',
                                                  validator: (value) {
                                                    if (value == '') {
                                                      return 'Required!';
                                                    }

                                                    return null;
                                                  },
                                                ),
                                                SizedBox(
                                                  width: 15,
                                                ),
                                                CustomTextFormField(
                                                  // hintText: "Sede Legale",
                                                  label: "Sede Legale",
                                                  controller: legalAddress,
                                                  // validationMessage: 'Required!',
                                                  validator: (value) {
                                                    if (value == '') {
                                                      return 'Required!';
                                                    }

                                                    return null;
                                                  },
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: 10),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                CustomTextFormField(
                                                  // hintText: "Nome persona di riferimento",
                                                  label:
                                                      "Nome persona di riferimento",
                                                  controller: firstName,
                                                  // validationMessage: 'Required!',
                                                  validator: (value) {
                                                    if (value == '') {
                                                      return 'Required!';
                                                    }

                                                    return null;
                                                  },
                                                ),
                                                SizedBox(
                                                  width: 15,
                                                ),
                                                CustomTextFormField(
                                                  // hintText: "Cognome persona di riferimento",
                                                  label:
                                                      "Cognome persona di riferimento",
                                                  controller: lastName,
                                                  // validationMessage: 'Required!',
                                                  validator: (value) {
                                                    if (value == '') {
                                                      return 'Required!';
                                                    }

                                                    return null;
                                                  },
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: 10),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                CustomTextFormField(
                                                  // hintText: "Telefono",
                                                  label: "Telefono",
                                                  controller: phone,
                                                  // validationMessage: 'Required!',
                                                  validator: (value) {
                                                    if (value == '') {
                                                      return 'Required!';
                                                    }

                                                    return null;
                                                  },
                                                ),
                                                SizedBox(
                                                  width: 15,
                                                ),
                                                CustomTextFormField(
                                                  // hintText: "E-mail",
                                                  label: "E-mail",
                                                  controller: email,
                                                  // validationMessage: 'Required!',
                                                  validator: (value) {
                                                    if (value == '') {
                                                      return 'Required!';
                                                    }

                                                    return null;
                                                  },
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: 10),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                CustomTextFormField(
                                                  // hintText: "IBAN",
                                                  label: "IBAN",
                                                  controller: iban,
                                                  inputFormatters: [
                                                    UpperCaseTextFormatter()
                                                  ],
                                                  textCapitalization:
                                                      TextCapitalization.words,
                                                  // validationMessage: 'Required!',
                                                  validator: (value) {
                                                    if (value == '') {
                                                      return 'Required!';
                                                    }

                                                    return null;
                                                  },
                                                ),
                                                SizedBox(
                                                  width: 15,
                                                ),
                                                CustomTextFormField(
                                                  // hintText:
                                                  //     "Codice Fatturazione Elettronico",
                                                  label:
                                                      "Codice Fatturazione Elettronico",
                                                  controller: billingCode,
                                                  // validationMessage: 'Required!',
                                                  validator: (value) {
                                                    if (value == '') {
                                                      return 'Required!';
                                                    }

                                                    return null;
                                                  },
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: 10),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                });
                              });
                        })
                  ]),
              SizedBox(height: 20),
              Expanded(
                child: Row(
                  children: [
                    Container(
                      width: 330,
                      padding:
                          EdgeInsets.symmetric(horizontal: 10, vertical: 20),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(13),
                        border: Border.all(color: Color(0xffe7e7e7)),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                              child: ListView.builder(
                                  itemCount: dataList.length,
                                  itemBuilder: (context, index) {
                                    return Theme(
                                      data: ThemeData().copyWith(
                                          dividerColor: Colors.transparent),
                                      child: ExpansionTile(
                                          initiallyExpanded: dataList[index]
                                              ['isExpanded'],
                                          trailing: Image.asset(
                                            dataList[index]['isExpanded']
                                                ? 'assets/icons/arrow_up.png'
                                                : 'assets/icons/arrow_down.png',
                                            width: 12,
                                            color: Color(0xff838383),
                                          ),
                                          onExpansionChanged: ((value) =>
                                              setState(() {
                                                dataList[index]['isExpanded'] =
                                                    value;
                                              })),
                                          tilePadding: EdgeInsets.only(left: 5),
                                          title: NarFormLabelWidget(
                                            label: dataList[index]['name']
                                                .toString()
                                                .toUpperCase(),
                                            fontSize: 15,
                                            textColor: Color.fromRGBO(
                                                117, 117, 117, 1),
                                          ),
                                          children: <Widget>[
                                            Column(
                                              children: _buildExpandableContent(
                                                  dataList[index]),
                                            ),
                                          ]),
                                    );
                                  })),
                        ],
                      ),
                    ),
                    SizedBox(width: 10),
                    Expanded(
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 30, vertical: 10),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(13),
                          border: Border.all(color: Color(0xffe7e7e7)),
                        ),
                        child: supplierCount > 0
                            ? DitteSettingWidget(
                                supplier: selectedSupplier!,
                                updateSelectedSupplier: updateSelectedSupplier,
                                fetchSupplier: fetchSupplier)
                            : Container(
                                height: double.infinity,
                                width: double.infinity,
                                child: Center(
                                  child: NarFormLabelWidget(
                                      label: 'Nessuna ditta da mostrare'),
                                ),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
  }

  Widget getIndicatorWidget(String title, String value) {
    return Container(
      height: 138,
      width: 205,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 8.0, left: 8),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                )
              ],
            ),
          ),
          SizedBox(height: 18),
          loading
              ? CircularProgressIndicator(color: Theme.of(context).primaryColor)
              : Text(
                  value,
                  style: TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                    fontSize: 50,
                  ),
                )
        ],
      ),
    );
  }
}
