import 'package:flutter/material.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

class HoverMessageIcon extends StatefulWidget {

  final String message;
  final Widget icon;
  final Alignment messageAlignment;
  final double? messageWidth;

  const HoverMessageIcon({
    Key? key,
    required this.message,
    this.icon = const Icon(Icons.question_mark_rounded, size: 30, color: Colors.grey),
    this.messageAlignment = Alignment.topCenter,
    this.messageWidth,
  }) : super(key: key);

  @override
  _HoverMessageIconState createState() => _HoverMessageIconState();
}

class _HoverMessageIconState extends State<HoverMessageIcon> {
  bool _isHovering = false;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: MouseRegion(
        onEnter: (_) => setState(() => _isHovering = true),
        onExit: (_) => setState(() => _isHovering = false),
        child: Stack(
          alignment: widget.messageAlignment,
          clipBehavior: Clip.none,
          children: [
            widget.icon,
            if (_isHovering)
            Positioned(
              top: 35,
              child: Material(
                elevation: 2,
                borderRadius: BorderRadius.circular(4),
                color: Colors.white.withOpacity(0.7),
                child: Container(
                  width: widget.messageWidth,
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  child: NarFormLabelWidget(
                    label: widget.message,
                    fontSize: 12,
                    fontWeight: '700',
                    textColor: const Color.fromARGB(255, 196, 13, 0),
                    overflow: TextOverflow.clip,
                  )
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
