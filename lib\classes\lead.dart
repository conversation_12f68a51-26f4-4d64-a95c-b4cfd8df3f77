import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:newarc_platform/classes/basePersonInfo.dart';

class Lead{
  String? firebaseId;
  BasePersonInfo? basePersonInfo;
  int? insertTimestamp;
  String? source;
  DocumentReference? sourceReference;
  
  Lead(Map<String, dynamic> fixedProperty) {
    this.firebaseId = fixedProperty['firebaseId'];
    this.basePersonInfo = fixedProperty['basePersonInfo'];
    this.insertTimestamp = fixedProperty['insertTimestamp'];
    this.source = fixedProperty['source'];
    this.sourceReference = fixedProperty['sourceReference'];
  }

  Lead.empty(){
    this.firebaseId = null;
    this.basePersonInfo = BasePersonInfo.empty();
    this.insertTimestamp = Timestamp.now().millisecondsSinceEpoch;
    this.source = '';
    this.sourceReference = null;
    
  }

  Map<String, dynamic> toMap() {
    return {
      'basePersonInfo': this.basePersonInfo!.toMap(),
      'insertTimestamp': this.insertTimestamp,
      'source': this.source,
      'sourceReference': this.sourceReference
    };
  }

  Lead.fromDocument(Map<String, dynamic> data, String id) {

    try {
      this.firebaseId = id;
      // print({ 'data-->', data});
      this.basePersonInfo = data['basePersonInfo'] != null
          ? BasePersonInfo.fromMap(data['basePersonInfo'])
          : BasePersonInfo.empty();
      this.insertTimestamp = data['insertTimestamp']??Timestamp.now().millisecondsSinceEpoch;
      this.source = data['source'];
      this.sourceReference = data['sourceReference'];
    } catch (e, s) {
      print({'agreement.dart', e, s});
    }
    
  }

}

