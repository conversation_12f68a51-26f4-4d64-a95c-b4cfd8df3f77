import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:newarc_platform/classes/notification.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import '../../classes/agencyUser.dart';

class CustomAppbarMenu extends StatefulWidget {
  const CustomAppbarMenu(
      {Key? key,
      required this.agency,
      required this.profilePicture,
      required this.onSettingsTapped,
      required this.onAbbonamentiTapped,
      required this.onServiziTapped,
      required this.onPersonaTapped,
      })
      : super(key: key);
  final Agency agency;
  final String? profilePicture;
  final Function? onSettingsTapped;
  final Function? onAbbonamentiTapped;
  final Function? onServiziTapped;
  final Function? onPersonaTapped;

  @override
  State<CustomAppbarMenu> createState() => _CustomButtonTestState();
}

class _CustomButtonTestState extends State<CustomAppbarMenu> {
  List<NewarcNotification> notifications = [];

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {

    return Center(
      child: DropdownButtonHideUnderline(
        child: DropdownButton2(
          buttonStyleData: ButtonStyleData(
            height: 20,
            overlayColor: WidgetStateProperty.all<Color>(Colors.transparent)
          ),
          enableFeedback: false,
          customButton: Container(
            child: widget.profilePicture == null || widget.profilePicture == ''
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(40.0),
                    child: Image.asset(
                      'assets/icons/account_placeholder.png',
                      width: 40,
                    ),
                  )
                : 
                Container(
                  height: 40,
                  width: 40,
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      fit: BoxFit.cover,
                      image: NetworkImage( widget.profilePicture! ),
                    ),
                    color: Colors.white,
                    borderRadius: BorderRadius.all(
                      Radius.circular(20),
                    ),
                    
                  ),
                  child: SizedBox(height: 0,)
                  
                  
                ),
                
          ),
          items: [
            DropdownMenuItem<Widget>(
              value: Container(
                child: NarFormLabelWidget(
                    label: "Account", fontSize: 14, fontWeight: '800'),
              ),
              child: Container(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    NarFormLabelWidget(
                        label: "Account", fontSize: 14, fontWeight: '800'),
                  ],
                ),
              ),
              enabled: false,
            ),
            ...getItems(context, widget.agency, widget.onSettingsTapped!, widget.onAbbonamentiTapped!, widget.onServiziTapped!, widget.onPersonaTapped!),
          ],
          //onTap: (() => {}),
          onChanged: (value) {
            //value as MenuItem;
          },

          //itemHeight: 65,
          menuItemStyleData: MenuItemStyleData(
            height: 40,
            padding: const EdgeInsets.only(left: 16, right: 16),
            // customHeights: getCustomItemsHeights(getItems(context, widget.agency, widget.onSettingsTapped!), 60)
          ),
          // itemPadding: const EdgeInsets.only(left: 16, right: 16),
          dropdownStyleData: DropdownStyleData(
            width: 220,
            padding: const EdgeInsets.symmetric(vertical: 6),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    offset: Offset(0, 0),
                    blurRadius: 15,
                  )
                ]),
            elevation: 0,
            offset: const Offset(-200, -5),
          ),
        ),
      ),
    );
  }
}

List<DropdownMenuItem<Widget>> getItems(
    BuildContext context, Agency agency, Function onSettingsTapped, Function onAbbonamentiTapped, Function onServiziTapped, Function onPersonaTapped) {
  List<DropdownMenuItem<Widget>> list = [];

  list.addAll([
    DropdownMenuItem<Widget>(
      value: getSettings(context, onSettingsTapped),
      child: getSettings(context, onSettingsTapped),
      onTap: (() {
        onSettingsTapped();
      }),
    ),
    DropdownMenuItem<Widget>(

      value: getPersona(context, onPersonaTapped) ,
      child: getPersona(context, onPersonaTapped),
      onTap: (() {
        onPersonaTapped();
      }),
    ),
    DropdownMenuItem<Widget>(

      value: getAbbonamenti(context, onAbbonamentiTapped) ,
      child: getAbbonamenti(context, onAbbonamentiTapped),
      onTap: (() {
        onAbbonamentiTapped();
      }),
    ),
    DropdownMenuItem<Widget>(

      value: getServizi(context, onServiziTapped) ,
      child: getServizi(context, onServiziTapped),
      onTap: (() {
        onServiziTapped();
      }),
    ),

    DropdownMenuItem<Widget>(
      value: getLogout(context),
      child: getLogout(context),
      onTap: () async {
        //auth
        await FirebaseAuth.instance.signOut();
        context.go("/");
      },
    ),
  ]);
  return list;
}

Widget getLogout(BuildContext context) {
  return Container(
    child: Row(children: [SvgPicture.asset("assets/icons/logout.svg",height: 14,width: 16,), SizedBox(width: 10,), NarFormLabelWidget(label: "Logout", fontSize: 14, fontWeight: '500')],),
  );
}

Widget getSettings(BuildContext context, Function onSettingsTapped) {
  return Container(
    child: Row(
      children: [
        SvgPicture.asset("assets/icons/setting.svg",height: 14,width: 16,), SizedBox(width: 10,),
        NarFormLabelWidget(
          label: "Impostazioni",
          fontSize: 14,
          fontWeight: '500',
        ),
      ],
    ),
  );
}


Widget getAbbonamenti(BuildContext context, Function onAbbonamentiTapped) {
  return Container(
    child: Row(
      children: [
        SvgPicture.asset("assets/icons/price_tag.svg",height: 14,width: 16,), SizedBox(width: 10,),
        NarFormLabelWidget(
          label: "I tuoi abbonamenti",
          fontSize: 14,
          fontWeight: '500',
        ),
      ],
    ),
  );
}

Widget getServizi(BuildContext context, Function onServiziTapped) {
  return Container(
    child: Row(
      children: [
        SvgPicture.asset("assets/icons/servizi-prezzi.svg",height: 14,width: 16,), SizedBox(width: 10,),
        NarFormLabelWidget(
          label: "Servizi e Prezzi",
          fontSize: 14,
          fontWeight: '500',
        ),
      ],
    ),
  );
}

Widget getPersona(BuildContext context, Function onPersonaTapped) {
  return Container(
    child: Row(
      children: [
        SvgPicture.asset("assets/icons/account.svg",height: 14,width: 16,color: Color(0xFFC4C4C4),), SizedBox(width: 10,),
        NarFormLabelWidget(
          label: "Persone",
          fontSize: 14,
          fontWeight: '500',
        ),
      ],
    ),
  );
}
