
class NewarcMaterialSupplier {
 String? firebaseId;
 int? insertTimestamp;
 String? uid; // The Firebase Id of the logged in user
 String? name; // Supplier Name




 Map<String, Object?> toMap() {
  return {
   'insertTimestamp': insertTimestamp,
   'uid': uid,
   'name': name,
  };
 }

 NewarcMaterialSupplier.empty() {
  this.firebaseId = '';
  this.insertTimestamp = null;
  this.uid = '';
  this.name = '';
 }

 NewarcMaterialSupplier.fromDocument(Map<String, dynamic> data, String id) {
  this.firebaseId = id;
  try {
   this.insertTimestamp = data['insertTimestamp'];
   this.uid = data['uid'];
   this.name = data['name'];
  } catch (e, s) {
   print({ 'NewarcMaterialVariantSupplierPrice Class Error ------->', e, s});
  }
 }
}