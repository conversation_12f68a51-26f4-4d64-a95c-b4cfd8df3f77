

class NewarcAdConfiguration{
  String? firebaseId;
  List? newarcAdRoomConfigurationIDS;
  int? insertTimestamp;
  int? modificationTimestamp;
  bool? isActive;
  String? newarcHomesId;
  List? newarcAdOptionalConfigurationIDS;


  Map<String, Object?> toMap() {
    return {
      'newarcAdRoomConfigurationIDS': newarcAdRoomConfigurationIDS,
      'insertTimestamp': insertTimestamp,
      'modificationTimestamp': modificationTimestamp,
      'isActive': isActive,
      'newarcHomesId': newarcHomesId,
      'newarcAdOptionalConfigurationIDS': newarcAdOptionalConfigurationIDS,
    };
  }
  NewarcAdConfiguration.empty() {
    this.firebaseId = '';
    this.insertTimestamp = null;
    this.modificationTimestamp = null;
    this.newarcHomesId = '';
    this.isActive = false;
    this.newarcAdRoomConfigurationIDS = [];
    this.newarcAdOptionalConfigurationIDS = [];
  }
  NewarcAdConfiguration.fromDocument(Map<String, dynamic> data, String id) {
    this.firebaseId = id;
    try {
      this.insertTimestamp = data['insertTimestamp'];
      this.modificationTimestamp = data['modificationTimestamp'];
      this.newarcHomesId = data['newarcHomesId'];
      this.isActive = data['isActive'];
      this.newarcAdRoomConfigurationIDS = data['newarcAdRoomConfigurationIDS'] ?? [];
      this.newarcAdOptionalConfigurationIDS = data['newarcAdOptionalConfigurationIDS'] ?? [];
    } catch (e, s) {
      print({ 'NewarcAdElementConfiguration Class Error ------->', e, s});
    }
  }

}