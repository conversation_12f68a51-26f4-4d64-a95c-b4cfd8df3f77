
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

class NewarcDataTable extends StatelessWidget {
  NewarcDataTable({
    this.columns,
    required this.source,
    this.rowsPerPage,
    this.onPageChanged,
    this.hidePaginator = false,
    this.isHasDecoration = true,
    this.minWidth,
    this.columnSpacing,
    this.dividerThickness,
    this.horizontalMargin,
    this.empty,
  });

  final List<DataColumn>? columns;
  final DataTableSource source;
  final int? rowsPerPage;
  final Function(int)? onPageChanged;
  final bool hidePaginator;
  final bool isHasDecoration;
  final double? minWidth;
  final double? columnSpacing;
  final double? dividerThickness;
  final double? horizontalMargin;
  final Widget? empty;

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: ThemeData(
        useMaterial3: false,
      ),
      child: Container(
        decoration: isHasDecoration
            ? BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: AppColor.white,
          border: Border.all(width: 1.5, color: AppColor.borderColor),
        )
            : null,
        child: PaginatedDataTable2(
          hidePaginator: hidePaginator,
          onPageChanged: onPageChanged,
          isHorizontalScrollBarVisible: true,
          isVerticalScrollBarVisible: true,
          wrapInCard: false,
          dataRowHeight: 44,
          rowsPerPage: rowsPerPage ?? 10,
          dividerThickness: dividerThickness ?? 1,
          headingTextStyle: TextStyle(
            fontSize: 12,
            fontFamily: 'Raleway-600',
            color: AppColor.greyColor,
          ),
          // headingTextStyle: TextStyle().text12w600.textColor(AppColor.greyColor),
          horizontalMargin: horizontalMargin ?? 18,
          renderEmptyRowsInTheEnd: false,
          autoRowsToHeight: false,
        empty: empty ??
        Center(
        child: NarFormLabelWidget(
        label: 'Nessun dato',
        fontSize: 12,
        fontWeight: '600',
        textColor: AppColor.black,
        ),
        // child: Text(
        //   'Nessun dato',
        //   style: TextStyle().text12w600.textColor(
        //         AppColor.black,
        //       ),
        // ),
      ),
      columnSpacing: columnSpacing ?? 15,
      minWidth: minWidth ?? 0,
      source: source,
      columns: columns ?? [],
      fixedTopRows: 0,
    ),)
    ,
    );
  }
}
