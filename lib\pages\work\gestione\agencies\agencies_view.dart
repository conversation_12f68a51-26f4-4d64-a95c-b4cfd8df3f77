import 'dart:developer';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/routes/work_routes.dart';
import 'package:newarc_platform/widget/UI/filter.dart';
import 'package:newarc_platform/widget/UI/image-select-box.dart';
import 'package:newarc_platform/widget/UI/newarc_data_table.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/pages/work/gestione/agencies/agencies_controller.dart';
import 'package:newarc_platform/pages/work/gestione/agencies/agencies_data_source.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/app_const.dart' as appConst;


class AgenciesView extends StatefulWidget {

  AgenciesView({
    Key? key,
    }) : super(key: key);

  @override
  State<AgenciesView> createState() =>
      _AgenciesViewState();
}

class _AgenciesViewState extends State<AgenciesView> {
  
  final controller = Get.put<AgenciesController>(AgenciesController());
  Key? paddingKey;
  
  @override
  void initState() {
    controller.regionFilterController.text = '';
    initialFetchAgencies();
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> initialFetchAgencies() async {

    setState(() {
      controller.agencies = [];
      controller.loading = true;
    });

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance
        .collection(appConfig.COLLECT_AGENCIES);

      if (controller.regionFilterController.text != '') {
        collectionSnapshotQuery = collectionSnapshotQuery.where('region',
            isEqualTo: controller.regionFilterController.text);
      }
        // .orderBy('insertTimestamp', descending: true);
      collectionSnapshot = await collectionSnapshotQuery.get();

      for (var i = 0; i < collectionSnapshot.docs.length; i++) {
        Agency tmpCont = Agency.fromDocument(
            collectionSnapshot.docs[i].data(), collectionSnapshot.docs[i].id);
        controller.agencies.add(tmpCont);
      }
      setState(() {
        controller.loading = false;
      });
    } catch (e, s) {
      setState(() {
        controller.loading = false;
      });
      log('Following error', error: e, stackTrace: s);
    }
  }

  @override
  Widget build(BuildContext context) {
    double pageWidth = MediaQuery.of(context).size.width - 260;
    if (pageWidth < 1800) {
      pageWidth = 1800;
    }
    List regionOptions = appConst.composeProjectCode['region']!.entries.map((toElement) => {
                        'label': toElement.key,
                        'value': toElement.key,
                      }).toList();
    regionOptions.sort((a, b) => a["label"]!.toLowerCase().compareTo(b["label"]!.toLowerCase()));

    return LayoutBuilder(builder: (context, constraints) {
      return Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              NarFormLabelWidget(
                label: 'Gestisci agenzie',
                fontSize: 19,
                fontWeight: '700',
              ),
            ],
          ),
          Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              controller.loading
                  ? Container()
                  : NarFilter(
                showSearchInput: false,
                textEditingControllers: [
                  controller.regionFilterController,
                ],
                selectedFilters: [
                  controller.regionSelectedFilter,
                ],
                filterFields: [
                  {
                    'Regione': NarImageSelectBoxWidget(
                      options: regionOptions,
                      onChanged: (dynamic val) {
                        controller.regionSelectedFilter = val['label'];
                        setState(() {});
                      },
                      controller: controller.regionFilterController,
                    ),
                  },
                ],
                onSubmit: () async {
                  await initialFetchAgencies();
                },
                onReset: () async {
                  controller.clearFilter();
                  await initialFetchAgencies();
                },
              ),
            ],
          ),
          SizedBox(height: 10),
          Container(
            height: constraints.maxHeight / 1.2,
            child: Column(
              children: [
                Expanded(
                  child: Stack(
                    children: [
                      Opacity(
                        opacity: controller.loading ? 0.5 : 1,
                        child: NewarcDataTable(
                          rowsPerPage: 20,
                          dividerThickness: 1,
                          columns: [
                            DataColumn2(
                              size: ColumnSize.L,
                              label: Text(
                                'Nome agenzia',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.S,
                              label: Text(
                                'Provincia',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.L,
                              label: Text(
                                'Indirizzo',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.S,
                              label: Text(
                                'Attivo',
                              ),
                            ),
                          ],
                          source: AgenciesDataSource(
                            onAgencyTap: (Agency agency) {
                              context.go(WorkRoutes.workAgenzieInside(agency.id!));
                            },
                            agencies: controller.agencies,
                            context: context,
                          ),
                        ),
                      ),
                      if (controller.loading)
                        Positioned.fill(
                          child: Center(
                            child: CircularProgressIndicator(
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    });
  }
}
