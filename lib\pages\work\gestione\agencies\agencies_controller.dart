import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/agencyUser.dart';

class AgenciesController extends GetxController {
  bool loading = false;
  // suggested agencies to be shown in table
  List<Agency> agencies = [];
  // filter for table view
  TextEditingController regionFilterController = new TextEditingController();
  String regionSelectedFilter = "";
  // controllers for agency inside view
  // Dati agenzia
  TextEditingController agencyName = new TextEditingController();
  // TextEditingController agencyCity = new TextEditingController();
  BaseAddressInfo agencyOperativeAddress = BaseAddressInfo.empty();
  TextEditingController agencyPhone = new TextEditingController();
  TextEditingController agencyEmail = new TextEditingController();
  // Persona di riferimento
  TextEditingController contactName = new TextEditingController();
  TextEditingController contactSurname = new TextEditingController();
  TextEditingController contactPhone = new TextEditingController();
  TextEditingController contactEmail = new TextEditingController();
  // Fatturazione e pagamenti
  TextEditingController agencyLegalEntity = new TextEditingController();
  TextEditingController agencyFormationType = new TextEditingController();
  TextEditingController agencyVat = new TextEditingController();
  BaseAddressInfo agencyLegalAddress = BaseAddressInfo.empty();
  TextEditingController agencyBillingCode = new TextEditingController();
  TextEditingController agencyIban = new TextEditingController();
  TextEditingController agencyFiscalCode = new TextEditingController();

  TextEditingController renovationFeeController = new TextEditingController();
  // Gestione servizi
  bool agencyIsImmaginaActive = true;
  bool agencyIsContattiActive = true;
  bool agencyIsValutatoreActive = true;
  // Error messages
  String validationMessage = "";
  String progressMessage = "";
  List<String> fileProgressMessage = [""];
  List<String> formMessages = [""];

  initInsideViewController(Agency agency){
    agencyName.text = agency.name ?? '';
    agencyOperativeAddress = BaseAddressInfo.fromMap(
      {
        'streetName': agency.streetName,
        'streetNumber': agency.streetNumber,
        'city': agency.city,
        'locality': agency.locality,
        'region': agency.region,
        'province': agency.province,
        'postalCode': agency.postalCode,
        'country': agency.country,
        'latitude': agency.latitude,
        'longitude': agency.longitude,
        'fullAddress': agency.address
      }
    );
    agencyPhone.text = agency.phone ?? '';
    agencyEmail.text = agency.email ?? '';
    contactName.text = agency.referencePerson.name ?? '';
    contactSurname.text = agency.referencePerson.surname ?? '';
    contactPhone.text = agency.referencePerson.phone ?? '';
    contactEmail.text = agency.referencePerson.email ?? '';
    agencyLegalEntity.text = agency.legalEntity ?? '';
    agencyFormationType.text = agency.formationType ?? '';
    agencyVat.text = agency.vat ?? '';
    agencyLegalAddress = agency.sedeLegaleFull ?? BaseAddressInfo.empty();
    agencyBillingCode.text = agency.sdi ?? '';
    agencyIban.text = agency.iban ?? '';
    agencyFiscalCode.text = agency.fiscalCode ?? '';
    renovationFeeController.text = agency.renovationFee ?? "";

    agencyIsImmaginaActive = true;
    agencyIsContattiActive = true;
    agencyIsValutatoreActive = true;

    validationMessage = "";
    progressMessage = "";
    fileProgressMessage = [""];
    formMessages = [""];
  }

  clearInsideViewController(){
    renovationFeeController.clear();
    agencyName.clear();
    agencyOperativeAddress = BaseAddressInfo.empty();
    agencyPhone.clear();
    agencyEmail.clear();
    contactName.clear();
    contactSurname.clear();
    contactPhone.clear();
    contactEmail.clear();
    agencyLegalEntity.clear();
    agencyFormationType.clear();
    agencyVat.clear();
    agencyLegalAddress = BaseAddressInfo.empty();
    agencyBillingCode.clear();
    agencyIban.clear();
    agencyFiscalCode.clear();

    validationMessage = "";
    progressMessage = "";
    fileProgressMessage = [""];
    formMessages = [""];
  }
  
  clearFilter() {
    regionSelectedFilter = '';
    regionFilterController.clear();
  }
}
