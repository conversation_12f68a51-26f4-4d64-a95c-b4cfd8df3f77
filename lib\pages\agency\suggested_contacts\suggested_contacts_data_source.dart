import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:newarc_platform/classes/renovationContact.dart';
import 'package:newarc_platform/classes/renovationContactAddress.dart';
import 'package:newarc_platform/pages/agency/suggested_contacts/suggested_contacts_controller.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:intl/intl.dart';
import '../../../widget/UI/tab/status_widget.dart';

class SuggestedContactsDataSource extends DataTableSource {
  final List<RenovationContact> contacts;
  final BuildContext context;
  final List<Map> status;
  final Function() initialFetchContacts;
  final controller = Get.put<SuggestedContactsController>(SuggestedContactsController());

  NumberFormat localCurrencyFormatMain = NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 0);

  SuggestedContactsDataSource({
    required this.context,
    required this.contacts,
    required this.status,
    required this.initialFetchContacts,
  });

  @override
  DataRow? getRow(int index) {
    if (index < contacts.length){
      RenovationContact cont = contacts[index];

      var statMap = status[status.indexWhere((sta) => sta['value'] == cont.suggestionStatus)];
      String commission = cont.agencyCommission != null ? localCurrencyFormatMain.format(cont.agencyCommission) + '€' : '';
      return DataRow2(
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
                color: AppColor.borderColor,
                width: 1,
            ),
          ),
        ),
        cells: [
          // Indirizzo
          DataCell(
            FutureBuilder<RenovationContactAddress>(
              future: getRenovationContactAddress(cont.addressInfo!.first ),
              builder: (context, snapshot) {

                /* We don't need to show any special operation status */
                if( snapshot.connectionState == ConnectionState.waiting || snapshot.hasError || !snapshot.hasData || snapshot.data!.id == '' ){
                  return SizedBox(height: 0,);
                } 

                return NarFormLabelWidget(
                  label: snapshot.data!.addressInfo!.toShortAddress(),
                  fontSize: 12,
                  fontWeight: '800',
                  textAlign: TextAlign.start,
                  textColor: Colors.black,
                );
                
                
              }
            )
          ),
          // Nome e cognome
          DataCell(
            NarFormLabelWidget(
              label: "${cont.personInfo!.name} ${cont.personInfo!.surname}",
              fontSize: 12,
              fontWeight: '600',
              textAlign: TextAlign.start,
              textColor: Colors.black,
            )
          ),
          // Telefono
          DataCell(
            NarFormLabelWidget(
              label: "${cont.personInfo!.phone}",
              fontSize: 12,
              fontWeight: '600',
              textAlign: TextAlign.start,
              textColor: Colors.black,
            )
          ),
          // Email
          DataCell(
            NarFormLabelWidget(
              label: "${cont.personInfo!.email}",
              fontSize: 12,
              fontWeight: '600',
              textAlign: TextAlign.start,
              textColor: Colors.black,
            )
          ),
          // Acquisito
          DataCell(
            StatusWidget(
              status: statMap['label'],
              statusColor: statMap['bgColor'],
            ),
          ),
          // Commissione
          DataCell(
            NarFormLabelWidget(
              label: commission,
              fontSize: 12,
              fontWeight: '600',
              textAlign: TextAlign.start,
              textColor: Colors.black,
            )
          ),
          // Data segnalazione
          DataCell(
            NarFormLabelWidget(
              label: DateFormat('dd/MM/yyyy').format(DateTime.fromMillisecondsSinceEpoch(cont.created!)).toString(),
              fontSize: 12,
              fontWeight: '600',
              textAlign: TextAlign.start,
              textColor: Colors.black,
            )
          ),
        ],
      );
    }
    return null;
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => contacts.length;

  @override
  int get selectedRowCount => 0;
}

