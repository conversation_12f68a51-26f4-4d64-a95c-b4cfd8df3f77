import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/widget/UI/image-picker.dart';
import 'package:path/path.dart' as p;
import 'package:firebase_auth/firebase_auth.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;

class NewarcTeamSetting extends StatefulWidget {
  final NewarcUser newarcUser;
  final Function? getProfilePicture;

  const NewarcTeamSetting(
      {Key? key, required this.newarcUser, this.getProfilePicture})
      : super(key: key);

  @override
  State<NewarcTeamSetting> createState() => NewarcTeamSettingState();
}

class NewarcTeamSettingState extends State<NewarcTeamSetting> {
  TextEditingController confirmPassword = new TextEditingController();
  TextEditingController newPassword = new TextEditingController();

  bool isNewPasswordVisible = false;
  bool isConfirmPasswordVisible = false;

  final FirebaseFirestore _db = FirebaseFirestore.instance;
  final profilePicture = [];
  String? profilePictureFilename;
  String? validationMessage;
  String? progressMessage;
  double containerWidth = 0;

  @override
  void initState() {
    confirmPassword.text = '';
    newPassword.text = '';

    getProfileImageUrl();

    super.initState();
  }

  @protected
  void didUpdateWidget(NewarcTeamSetting oldWidget) {
    super.didUpdateWidget(oldWidget);
    getProfileImageUrl();
  }

  getProfileImageUrl() async {
    profilePicture.clear();
    setState(() {
      profilePictureFilename = widget.newarcUser.profilePicture;
    });
  }

  Future<bool> updateData() async {
    setState(() {
      validationMessage = null;
    });
    if (confirmPassword.text != '' && newPassword.text != '') {
      if (newPassword.text == confirmPassword.text) {
        setState(() {
          validationMessage = null;
        });
        updatePassword();
      } else {
        setState(() {
          validationMessage = 'Password mismatch!';
        });

        return false;
      }
    }

    if (profilePicture.length > 0) {
      String __profilePictureFilename =
          'profile' + p.extension(profilePicture[0].name);
      await uploadProfilePicture('users/', widget.newarcUser.id!,
              __profilePictureFilename, profilePicture[0])
          .then((uploadTask) {
        try {
          // files.add(filename);
        } catch (e) {
          // print({e,s});
        }
      });

      widget.newarcUser.profilePicture = __profilePictureFilename;
      profilePicture.clear();
      profilePictureFilename = __profilePictureFilename;
    } else {
      profilePictureFilename = widget.newarcUser.profilePicture;
    }

    setState(() {
      progressMessage = "Saved!";
    });

    await _db
        .collection(appConfig.COLLECT_USERS)
        .doc(widget.newarcUser.id)
        .update(widget.newarcUser.toMap());

    return true;
  }

  updatePassword() async {
    User? user = FirebaseAuth.instance.currentUser;

    user!.updatePassword(newPassword.text).then((_) {
      print('password changed');

      newPassword.text = '';
      confirmPassword.text = '';
    }).catchError((error) {
      // print({'password no changed', error});
      //Error, show something
    });
  }

  @override
  Widget build(BuildContext context) {
    containerWidth = MediaQuery.of(context).size.width * .75;

    return ListView(
      shrinkWrap: true,
      // mainAxisSize: MainAxisSize.max,
      // mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          mainAxisSize: MainAxisSize.max,
          children: [
            NarFormLabelWidget(
              label: 'Impostazioni',
              fontSize: 20,
              fontWeight: 'bold',
            ),
            SizedBox(height: 15),
            Row(
              children: [
                Container(
                  width: containerWidth,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(13),
                    border: Border.all(
                      color: Color(0xFFE7E7E7),
                      width: 1.0,
                    ),
                  ),
                  padding: EdgeInsets.all(15),
                  child: Column(
                    children: [
                      Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Logo
                          NarFormLabelWidget(
                            label: 'Immagine profilo',
                            fontSize: 16,
                            fontWeight: 'bold',
                          ),
                          SizedBox(
                            height: 15,
                          ),
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              NarImagePickerWidget(
                                  allowMultiple: false,
                                  imagesToDisplayInList: 0,
                                  removeButton: false,
                                  removeButtonText: 'rimuovi',
                                  uploadButtonPosition: 'back',
                                  showMoreButtonText: '+ espandi',
                                  removeButtonPosition: 'bottom',
                                  displayFormat: 'row',
                                  imageDimension: 100,
                                  imageBorderRadius: 50,
                                  borderRadius: 7,
                                  fontSize: 14,
                                  fontWeight: '600',
                                  text: 'Carica immagine profilo',
                                  borderSideColor:
                                      Theme.of(context).primaryColor,
                                  hoverColor: Color.fromRGBO(133, 133, 133, 1),
                                  images: profilePicture,
                                  pageContext: context,
                                  storageDirectory: 'users/',
                                  preloadedImages:
                                      profilePictureFilename == null ||
                                              profilePictureFilename == ''
                                          ? []
                                          : [profilePictureFilename],
                                  firebaseId: widget.newarcUser.id,
                                  removeExistingOnChange: true)
                            ],
                          ),

                          SizedBox(
                            height: 15,
                          ),

                          Container(
                            width: containerWidth * .80,
                            height: 1,
                            decoration: BoxDecoration(
                              color: Color(0xFFDCDCDC),
                            ),
                            child: SizedBox(height: 0),
                          ),

                          SizedBox(
                            height: 15,
                          ),

                          NarFormLabelWidget(
                            label: 'Modifica password',
                            fontSize: 16,
                            fontWeight: 'bold',
                          ),
                          SizedBox(
                            height: 15,
                          ),
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              CustomTextFormField(
                                  label: 'Forza nuova password',
                                  hintText: '',
                                  controller: confirmPassword,
                                  isObscureText: !isNewPasswordVisible,
                                  suffixIcon: IconButton(
                                    icon: isNewPasswordVisible
                                        ? Icon(
                                            Icons.visibility_rounded,
                                            color:
                                                Theme.of(context).primaryColor,
                                          )
                                        : Icon(Icons.visibility_off_rounded,
                                            color: Colors.grey),
                                    onPressed: () {
                                      setState(() {
                                        isNewPasswordVisible =
                                            !isNewPasswordVisible;
                                      });
                                    },
                                  )),
                              SizedBox(
                                width: 15,
                              ),
                              CustomTextFormField(
                                  label: 'Ripeti nuova password',
                                  hintText: '',
                                  controller: newPassword,
                                  isObscureText: !isConfirmPasswordVisible,
                                  suffixIcon: IconButton(
                                    icon: isConfirmPasswordVisible
                                        ? Icon(
                                            Icons.visibility_rounded,
                                            color:
                                                Theme.of(context).primaryColor,
                                          )
                                        : Icon(Icons.visibility_off_rounded,
                                            color: Colors.grey),
                                    onPressed: () {
                                      setState(() {
                                        isConfirmPasswordVisible =
                                            !isConfirmPasswordVisible;
                                      });
                                    },
                                  )),
                              Expanded(
                                  flex: 1,
                                  child: SizedBox(
                                    height: 0,
                                  ))
                            ],
                          ),
                          NarFormLabelWidget(
                            label: validationMessage != ''
                                ? validationMessage
                                : '',
                            fontSize: 12,
                            textColor: Colors.red,
                          ),
                        ],
                      ),
                      Column(
                        children: [
                          SizedBox(
                            height: 50,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              NarFormLabelWidget(
                                label: progressMessage != ''
                                    ? progressMessage
                                    : '',
                                fontSize: 12,
                              )
                            ],
                          ),
                          SizedBox(height: 10),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              BaseNewarcButton(
                                  buttonText: "Salva",
                                  onPressed: () async {
                                    setState(() {
                                      profilePictureFilename = '';
                                      progressMessage =
                                          'Salvataggio in corso...';
                                    });
                                    bool response = await updateData();

                                    if (response == true) {
                                      setState(() {
                                        progressMessage = '';
                                        widget.getProfilePicture!();
                                        profilePicture.clear();
                                      });
                                      await showAlertDialog(
                                          context,
                                          "Salvataggio",
                                          "Informazioni agenzia salvate con successo");
                                    } else {
                                      setState(() {
                                        progressMessage =
                                            'Si è verificato un errore. Contatta l\'assistenza.';
                                      });
                                    }
                                  })
                            ],
                          )
                        ],
                      )
                    ],
                  ),
                ),
              ],
            ),
          ],
        )
      ],
    );
  }
}
