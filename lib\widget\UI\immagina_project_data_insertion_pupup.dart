import 'dart:math';
import 'dart:typed_data';
import 'package:web/web.dart' as web;
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/dropdown_search.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/checkbox.dart';
import 'package:newarc_platform/widget/UI/bar_toggle_button.dart';
import 'package:newarc_platform/widget/UI/si_no_button.dart';
import 'package:newarc_platform/widget/UI/address_completion_google.dart';
import 'package:newarc_platform/classes/immaginaProject.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/functions/various.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/app_const.dart' as appConst;
import 'package:newarc_platform/economics.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path/path.dart' as p;
import '../../utils/common_utils.dart';
import '../../utils/heicToJpegConverter.dart' as heicToJpeg;

class ImmaginaDataInsertionPopup extends StatefulWidget {
  final String procedureStep;
  final ImmaginaProject? project;
  final List<String> formErrorMessage;
  final AgencyUser agencyUser;
  final onClose;

  // final bool isStripPayOnly;

  ImmaginaDataInsertionPopup({
    required this.procedureStep,
    this.project,
    this.formErrorMessage = const [],
    required this.agencyUser,
    required this.onClose,
    Key? key,
    // required this.isStripPayOnly,
  }) : super(key: key) {
    if (project == null && procedureStep != 'initial') {
      throw ArgumentError(
        'If project is null, procedureStep must be "initial".',
      );
    }
  }

  @override
  State<ImmaginaDataInsertionPopup> createState() => _ImmaginaDataInsertionPopupState();
}

class _ImmaginaDataInsertionPopupState extends State<ImmaginaDataInsertionPopup> {
  TextStyle sectionTitleStyle = TextStyle(fontSize: 16, fontWeight: FontWeight.bold);

  final _formKey = GlobalKey<FormState>();
  bool loading = false;
  bool paymentError = false;
  bool clicked = false;
  List<String> formErrorMessage = [];
  ImagePicker picker = ImagePicker();

  List viewsOrder = const [
    'initial',
    'localizzazione',
    'info-generali',
    'descrizione',
    'caratteristiche',
    'planimetria',
    'fotografie',
    'indicazioni-speciali',
    'pagamento',
  ];

  Map<String, String> _errorMessage = {
    'localizzazione': 'Inserisci tutti i campi richiesti prima di procedere',
    'info-generali': 'Inserisci tutti i campi richiesti prima di procedere',
    'descrizione': 'Inserisci una descrizione del tuo immobile prima di procedere',
    'caratteristiche': 'Inserisci le caratteristiche richieste prima di procedere.\nClasse energetica, anno di costruzione, esposizione e almeno uno dei campi selezionabili sono obbligatori.',
    'planimetria': 'Fornisci le tue planimetrie o richiedi il servizio a Newarc',
    'fotografie': 'Fornisci le tue fotografie indicando la relativa stanza o richiedi il servizio a Newarc',
    'indicazioni-speciali': 'Se necessario, inserire le informazioni aggiuntive. \nRicontrolla che tutte le sezioni siano complete prima di procedere con la pagina di pagamento.\nPuoi verificare le sezioni incomplete chiudendo il popup Nuova richiesta progetto dopo aver salvato. ',
  };

  List<String> allowedPlanimetryExtensions = ['dwg', 'pdf', 'jpg', 'jpeg', 'png'];
  List<String> allowedPicturesExtensions = ['jpg', 'jpeg','heic'];

  Map<String, List<String>> cityMarketZoneMap = appConst.cityZones;

  // Images loaders
  late List<XFile> planimetryImages = [];
  late List<Map<String, dynamic>> picturesImages = [];

  // Popup main state variables
  String? selectedView;
  ImmaginaProject? selectedProject;

  // Pagamento state variable
  bool _isFrozen = false;

  // Localizzazione
  TextEditingController filterMarketZone = TextEditingController();

  // Info generali
  TextEditingController filterGSF = TextEditingController();
  TextEditingController filterRooms = TextEditingController();
  TextEditingController filterBathrooms = TextEditingController();
  TextEditingController filterUnitFloor = TextEditingController();
  TextEditingController filterListingPrice = TextEditingController();
  TextEditingController filterPropertyType = TextEditingController();

  // Descrizione
  TextEditingController filterDescription = TextEditingController();

  // Caratteristiche
  Map<String, dynamic> characteristicsMap = Map.from(appConst.houseFeatures);

  TextEditingController filterEnergyClass = TextEditingController();
  TextEditingController filterConstructionYear = TextEditingController();
  // Indicazioni Speciali
  TextEditingController filterSpecialHints = TextEditingController();
  TextEditingController filterPropertyUpForSaleAnswer = TextEditingController();

  void initializeControllers() {
    // Localizzazione
    filterMarketZone.text = selectedProject!.marketZone ?? "";
    // Info generali
    filterGSF.text = selectedProject!.grossSquareFootage != null ? selectedProject!.grossSquareFootage.toString() : "";
    filterRooms.text = selectedProject!.rooms != null ? selectedProject!.rooms.toString() : "";
    if (filterRooms.text == "5") {
      filterRooms.text = "5+";
    }
    filterBathrooms.text = selectedProject!.numberOfBathrooms != null ? selectedProject!.numberOfBathrooms.toString() : "";
    if (filterBathrooms.text == "3") {
      filterBathrooms.text = "3+";
    }
    filterUnitFloor.text = selectedProject!.unitFloor ?? "";
    filterListingPrice.text = selectedProject!.listingPrice != null ? selectedProject!.listingPrice.toString() : "";
    filterPropertyType.text = selectedProject!.propertyType != null ? selectedProject!.propertyType.toString() : "";
    // Descrizione
    filterDescription.text = selectedProject!.description ?? "";
    // Caratteristiche
    characteristicsMap['Ascensore'] = selectedProject!.elevator ?? false;
    characteristicsMap['Cantina'] = selectedProject!.hasCantina ?? false;
    characteristicsMap['Terrazzo'] = selectedProject!.terrace ?? false;
    characteristicsMap['Portineria'] = selectedProject!.hasConcierge ?? false;
    characteristicsMap['Infissi ad alta efficienza'] = selectedProject!.highEfficiencyFrames ?? false;
    characteristicsMap['Doppia esposizione'] = selectedProject!.doubleEsposition ?? false;
    characteristicsMap['Tripla esposizione'] = selectedProject!.tripleEsposition ?? false;
    characteristicsMap['Quadrupla esposizione'] = selectedProject!.quadrupleEsposition ?? false;
    characteristicsMap['Risc. centralizzato'] = selectedProject!.centralizedHeating ?? false;
    characteristicsMap['Risc. autonomo'] = selectedProject!.autonomousHeating ?? false;
    characteristicsMap['Giardino privato'] = selectedProject!.privateGarden ?? false;
    characteristicsMap['Giardino condominiale'] = selectedProject!.sharedGarden ?? false;
    characteristicsMap['Stabile signorile'] = selectedProject!.nobleBuilding ?? false;
    characteristicsMap['Stabile videosorvegliato'] = selectedProject!.surveiledBuilding ?? false;
    characteristicsMap['Fibra ottica'] = selectedProject!.fiber ?? false;
    characteristicsMap['Pred. condizionatore'] = selectedProject!.airConditioning ?? false;
    characteristicsMap['Porta blindata'] = selectedProject!.securityDoor ?? false;
    characteristicsMap['Impianto TV'] = selectedProject!.tvStation ?? false;
    characteristicsMap['Pred. antifurto'] = selectedProject!.alarm ?? false;
    characteristicsMap['Tapparelle motorizzate'] = selectedProject!.motorizedSunblind ?? false;
    characteristicsMap['Tapparelle domotizzate'] = selectedProject!.domotizedSunblind ?? false;
    characteristicsMap['Luci domotizzate'] = selectedProject!.domotizedLights ?? false;
    characteristicsMap['Piano alto'] = selectedProject!.highFloor ?? false;
    characteristicsMap['Vicinanza Metro'] = selectedProject!.metroVicinity ?? false;
    characteristicsMap['Ampi balconi'] = selectedProject!.bigBalconies ?? false;
    characteristicsMap['Grande zona living'] = selectedProject!.bigLiving ?? false;
    characteristicsMap['Doppi servizi'] = selectedProject!.doubleBathroom ?? false;
    characteristicsMap['Piscina'] = selectedProject!.swimmingPool ?? false;
    characteristicsMap['Box o garage'] = selectedProject!.hasGarage ?? false;
    characteristicsMap['Cabina armadio'] = selectedProject!.walkInCloset ?? false;
    characteristicsMap['Fotovoltaico'] = selectedProject!.solarPanel ?? false;

    filterEnergyClass.text = selectedProject!.energyClass ?? "";
    filterConstructionYear.text = selectedProject!.constructionYear != null ? selectedProject!.constructionYear.toString() : "";
    // Indicazioni Speciali
    filterSpecialHints.text = selectedProject!.specialHints ?? "";
    filterPropertyUpForSaleAnswer.text = selectedProject!.propertyUpForSaleAnswer ?? "";
  }

  @override
  void initState() {
    cityMarketZoneMap.forEach((key, value) {
      value.sort();
      value.map((zone)=>zone.toTitleCase());
      });
    formErrorMessage = widget.formErrorMessage;
    selectedView = widget.procedureStep;
    selectedProject = widget.project;
    super.initState();
    if (selectedProject != null) {
      initializeControllers();
    }
    getFirestoreImages("${appConfig.COLLECT_IMMAGINA_PROJECTS}/");
  }

  @override
  void didUpdateWidget(ImmaginaDataInsertionPopup oldWidget) {
    super.didUpdateWidget(oldWidget);
    formErrorMessage = widget.formErrorMessage;
    selectedView = widget.procedureStep;
    selectedProject = widget.project;
  }

  Widget selectFooter() {
    if (selectedView == null) {
      return NarFormLabelWidget(label: "view can't be null selected");
    } else if (selectedView == 'initial') {
      return initialFooter();
    } else {
      return standardFooter();
    }
  }

  Widget initialFooter() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Container(
          width: 150,
          child: BaseNewarcButton(
              buttonText: 'Inizia',
              color: Colors.black,
              onPressed: () async {
                // create immagina project object
                final FirebaseFirestore _db = FirebaseFirestore.instance;
                ImmaginaProject _project = ImmaginaProject.empty();
                // assign agency
                _project.agencyId = widget.agencyUser.agencyId!;
                DocumentSnapshot<Map<String, dynamic>> _agencyDocument = await _db.collection(appConfig.COLLECT_AGENCIES).doc(_project.agencyId).get();
                // save project to firestore
                DocumentReference projectResponse = await _db.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).add(_project.toMap());
                _project.id = projectResponse.id;
                setState(() {
                  selectedView = 'localizzazione';
                  selectedProject = _project;
                });
              }),
        )
      ],
    );
  }

  Widget standardFooter() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        selectedView == 'localizzazione'
            ? SizedBox(width: 150)
            : ElevatedButton(
            onPressed: () => _backButtonSetStateFunction(),
            style: ElevatedButton.styleFrom(
              elevation: 0,
              fixedSize: Size(150, 45),
              backgroundColor: Color(0xffe8e8e8),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            ),
            child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
              Transform.rotate(
                angle: pi,
                child: SvgPicture.asset(
                  'assets/icons/arrow.svg',
                  height: 16,
                  color: const Color(0xff6a6a6a),
                ),
              ),
              NarFormLabelWidget(
                label: 'Indietro',
                textColor: const Color(0xff6a6a6a),
                fontWeight: '600',
                letterSpacing: .5,
              ),
            ])),
        selectedView == 'pagamento'
            ? SizedBox(width: 150)
            : Row(
              children: [
                if (selectedView != 'indicazioni-speciali') ...[
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
                      });
                    },
                    style: ElevatedButton.styleFrom(
                      elevation: 0,
                      fixedSize: Size(150, 45),
                      backgroundColor: Colors.white,
                      side: BorderSide(color: Color(0xff6a6a6a)),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween, 
                      children: [
                        NarFormLabelWidget(
                          label: 'Salta',
                          textColor: Color(0xff6a6a6a),
                          fontWeight: '600',
                          letterSpacing: .5,
                        ),
                        SvgPicture.asset(
                          'assets/icons/arrow.svg',
                          height: 16,
                          color: Color(0xff6a6a6a),
                        ),
                      ] 
                    )
                  ),
                  SizedBox(width: 20),
                ],
                ElevatedButton(
                  onPressed: () => _forwardButtonConditions() ? _forwardButtonSetStateFunction() : _forwardButtonErrorFunction(),
                  style: ElevatedButton.styleFrom(
                    elevation: 0,
                    fixedSize: Size(150, 45),
                    backgroundColor: _forwardButtonConditions() ? Colors.black : Color.fromARGB(255, 192, 192, 192),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween, 
                    children: [
                      NarFormLabelWidget(
                        label: 'Avanti',
                        textColor: Colors.white,
                        fontWeight: '600',
                        letterSpacing: .5,
                      ),
                      SvgPicture.asset(
                        'assets/icons/arrow.svg',
                        height: 16,
                        color: Colors.white,
                      ),
                    ] 
                  )
                ),
              ],
            ),
      ],
    );
  }

  bool _forwardButtonConditions() {  
    Map conditions = {
      'localizzazione': selectedProject!.localizzazione(),
      'info-generali': selectedProject!.info_generali(),
      'descrizione': (selectedProject!.description != null) && (selectedProject!.description != ""),
      'caratteristiche': (characteristicsMap.values.any((value) => value == true)
          && ((selectedProject!.energyClass != null)&&(selectedProject!.energyClass != ""))
          && ((selectedProject!.constructionYear != null)&&(selectedProject!.constructionYear != ""))
          && (selectedProject!.externalEsposition.isNotEmpty)
      ),
      'planimetria': (selectedProject!.wantsNewarcPlanimetry) || (planimetryImages.isNotEmpty),
      'fotografie': (selectedProject!.wantsNewarcPictures) || (picturesImages.isNotEmpty && picturesImages.every((map) => map['tag'] != null) && picturesImages.every((map) => map['tag'] != "")),
      // 'indicazioni-speciali': (((selectedProject!.hasSpecialHints == null) || (selectedProject!.hasSpecialHints == false)) && (selectedProject!.propertyUpForSaleAnswer != null) && (selectedProject!.propertyUpForSaleAnswer != "")) ||
      //     ((selectedProject!.hasSpecialHints == true) && (selectedProject!.specialHints != null) && (selectedProject!.specialHints != "") && (selectedProject!.propertyUpForSaleAnswer != null) && (selectedProject!.propertyUpForSaleAnswer != "")),
    };
    bool indicazioniSpecialiCondition = (((selectedProject!.hasSpecialHints == null) || (selectedProject!.hasSpecialHints == false)) && (selectedProject!.propertyUpForSaleAnswer != null) && (selectedProject!.propertyUpForSaleAnswer != "")) ||
          ((selectedProject!.hasSpecialHints == true) && (selectedProject!.specialHints != null) && (selectedProject!.specialHints != "") && (selectedProject!.propertyUpForSaleAnswer != null) && (selectedProject!.propertyUpForSaleAnswer != ""));
    conditions["indicazioni-speciali"] = conditions.values.every((value) => value == true) && indicazioniSpecialiCondition;
    bool condition = conditions[selectedView] ?? false;
    return condition;
  }

  _backButtonSetStateFunction() {
    if (selectedView != 'localizzazione') {
      setState(() {
        selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) - 1];
      });
    }
  }

  _forwardButtonErrorFunction() {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (_) {
          return Center(
              child: BaseNewarcPopup(
                title: 'Errore',
                column: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    NarFormLabelWidget(
                      label: _errorMessage[selectedView],
                      textAlign: TextAlign.center,
                      fontWeight: '600',
                      letterSpacing: 1,
                    ),
                  ],
                ),
              ));
        });
  }

  _forwardButtonSetStateFunction() async {
    setState(() {
      _isFrozen = true;
    });
    String? currentView = selectedView;
    if (currentView == 'localizzazione') {
      if (selectedProject!.localizzazione()) {
        setState(() {
          selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
        });
      }
    } else if (currentView == 'info-generali') {
      if (selectedProject!.info_generali()) {
        if (selectedProject!.grossSquareFootage!<gSFUpperLimit){
          setState(() {
            selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
          });
        } else {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (_) {
            return Center(
              child: BaseNewarcPopup(
                title: 'Attenzione!',
                noButton: true,
                column: Container(
                  width: MediaQuery.of(context).size.width / 3,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      NarFormLabelWidget(
                        label: 'Stai effettuando una richiesta per una\ncasa di oltre ${gSFUpperLimit}mq!',
                        textAlign: TextAlign.center,
                        fontSize: 20,
                        fontWeight: '700',
                      ),
                      SizedBox(height: 30),
                      NarFormLabelWidget(
                        label: 'Newarc realizzerà un preventivo personalizzato per questo immobile e non sarà possibile utilizzare i crediti di un piano ad abbonamento.',
                        textAlign: TextAlign.center,
                        fontSize: 15,
                        fontWeight: '500',
                        textColor: Color(0xff696969),
                        overflow: TextOverflow.clip,
                      ),
                      SizedBox(height: 20),
                      NarFormLabelWidget(
                        label: 'Il preventivo comprenderà la cifra per la realizzazione del progetto Immagina + la Success Fee',
                        textAlign: TextAlign.center,
                        fontSize: 15,
                        fontWeight: '500',
                        textColor: Color(0xff696969),
                        overflow: TextOverflow.clip,
                      ),
                      SizedBox(height: 30),
                      BaseNewarcButton(
                        width: MediaQuery.of(context).size.width/6,
                        buttonText: 'Continua',
                        onPressed: () {
                          Navigator.of(context).pop();
                          setState(() {
                            selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
                          });
                        },
                      ),
                      SizedBox(height: 10),
                      BaseNewarcButton(
                        buttonText: 'Salva e chiudi',
                        width: MediaQuery.of(context).size.width/6,
                        textColor: Theme.of(context).primaryColor,
                        color: Colors.white,
                        onPressed: () {
                            Navigator.of(context).pop();
                            Navigator.of(context).pop();
                            widget.onClose();
                          },
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
        }
          
      }
    } else if (currentView == 'descrizione') {
      if ((selectedProject!.description != null) && (selectedProject!.description != "")) {
        setState(() {
          selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
        });
      }
    } else if (currentView == 'caratteristiche') {
      if (characteristicsMap.values.any((value) => value == true)
          && ((selectedProject!.energyClass != null)&&(selectedProject!.energyClass != ""))
          && ((selectedProject!.constructionYear != null)&&(selectedProject!.constructionYear != ""))
          && (selectedProject!.externalEsposition.isNotEmpty)) {
        saveCharacteristics();
        setState(() {
          selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
        });
      }
    } else if (currentView == 'planimetria') {
      if (selectedProject!.wantsNewarcPlanimetry) {
        setState(() {
          selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
        });
      } else if (planimetryImages.isNotEmpty) {
        await savePlanimetryImages();
        setState(() {
          selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
        });
      }
    } else if (currentView == 'fotografie') {
      if (selectedProject!.wantsNewarcPictures) {
        setState(() {
          selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
        });
      } else if (picturesImages.isNotEmpty) {
        if (!picturesImages.any((elem) => elem['tag'] == null)) {
          await savePicturesImages();
          setState(() {
            selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
          });
        }
      }
    } else if (currentView == 'indicazioni-speciali') {

      if ((selectedProject!.hasSpecialHints ?? false) && (selectedProject!.propertyUpForSaleAnswer != null) && (selectedProject!.propertyUpForSaleAnswer != "")) {
        if ((selectedProject!.specialHints != null) && (selectedProject!.specialHints != "") && (selectedProject!.propertyUpForSaleAnswer != null) && (selectedProject!.propertyUpForSaleAnswer != "")) {
          setState(() {
            selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
          });
        }
      } else {
        setState(() {
          selectedProject!.hasSpecialHints = false;
          selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
        });
      }
    }
    await FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).doc(selectedProject!.id).set(selectedProject!.toMap());
    setState(() {
      _isFrozen = false;
    });
  }

  Future<String> computeImmaginaProjectCode () async { 
    // create and assign internal code (projectId) for immaginaProject
    final FirebaseFirestore _db = FirebaseFirestore.instance;
    String _region = selectedProject!.addressInfo!.region!;
    String _regionCode = appConst.composeProjectCode['region']![_region]!;
    String _serviceCode = appConst.composeProjectCode['project']!['immagina']!;
    DateTime _createdDate = DateTime.fromMillisecondsSinceEpoch(selectedProject!.insertionTimestamp);
    String _yearCode = _createdDate.year.toString().substring(2); // Get last two digits of the year
    String _defaultProjectId = "P_${_serviceCode}${_regionCode}${_yearCode}0001";

    QuerySnapshot<Map<String, dynamic>> _immaginaProjectsRegionCollection = await _db
      .collection(appConfig.COLLECT_IMMAGINA_PROJECTS).where('region', isEqualTo: _region)
      .orderBy('insertionTimestamp', descending: true)
      .limit(1).get();
      
    if (_immaginaProjectsRegionCollection.docs.isNotEmpty) {
      Map<String, dynamic> lastDoc = _immaginaProjectsRegionCollection.docs[0].data();
      if (DateTime.fromMillisecondsSinceEpoch(lastDoc['insertionTimestamp']).year == DateTime.now().year) {
        RegExp matcher = RegExp(r"\d{4}$");
        final match = matcher.firstMatch(lastDoc['projectId']);
        if (match != null) {
          final matchedText = match.group(0);
          final seqNumber = int.parse(matchedText!) + 1;
          final stringSeqNumber = seqNumber.toString();
          final paddedStringSeqNumber = stringSeqNumber.padLeft(4, '0');
          _defaultProjectId =
          "P_${appConst.composeProjectCode['project']!['immagina']}${appConst.composeProjectCode['region']![_region]}${(DateTime.now().year) % 100}${paddedStringSeqNumber}";
        }
      }
    }
    return _defaultProjectId;
  }

  void saveCharacteristics() {
    selectedProject!.elevator = characteristicsMap['Ascensore'];
    selectedProject!.hasCantina = characteristicsMap['Cantina'];
    selectedProject!.terrace = characteristicsMap['Terrazzo'];
    selectedProject!.hasConcierge = characteristicsMap['Portineria'];
    selectedProject!.highEfficiencyFrames = characteristicsMap['Infissi ad alta efficienza'];
    selectedProject!.doubleEsposition = characteristicsMap['Doppia esposizione'];
    selectedProject!.tripleEsposition = characteristicsMap['Tripla esposizione'];
    selectedProject!.quadrupleEsposition = characteristicsMap['Quadrupla esposizione'];
    selectedProject!.centralizedHeating = characteristicsMap['Riscaldamento centralizzato'];
    selectedProject!.autonomousHeating = characteristicsMap['Riscaldamento autonomo'];
    selectedProject!.privateGarden = characteristicsMap['Giardino privato'];
    selectedProject!.sharedGarden = characteristicsMap['Giardino condominiale'];
    selectedProject!.surveiledBuilding = characteristicsMap['Stabile videosorvegliato'];
    selectedProject!.nobleBuilding = characteristicsMap['Stabile signorile'];
    selectedProject!.fiber = characteristicsMap['Fibra ottica'];
    selectedProject!.airConditioning = characteristicsMap['Pred. condizionatore'];
    selectedProject!.securityDoor = characteristicsMap['Porta blindata'];
    selectedProject!.tvStation = characteristicsMap['Impianto TV'];
    selectedProject!.alarm = characteristicsMap['Pred. antifurto'];
    selectedProject!.motorizedSunblind = characteristicsMap['Tapparelle motorizzate'];
    selectedProject!.domotizedSunblind = characteristicsMap['Tapparelle domotizzate'];
    selectedProject!.domotizedLights = characteristicsMap['Luci domotizzate'];
    selectedProject!.highFloor = characteristicsMap['Piano alto'];
    selectedProject!.metroVicinity = characteristicsMap['Vicinanza metro'];
    selectedProject!.bigBalconies = characteristicsMap['Ampi balconi'];
    selectedProject!.bigLiving = characteristicsMap['Grande zona living'];
    selectedProject!.doubleBathroom = characteristicsMap['Doppi servizi'];
    selectedProject!.swimmingPool = characteristicsMap['Piscina'];
    selectedProject!.hasGarage = characteristicsMap['Box o garage'];
    selectedProject!.solarPanel = characteristicsMap['Fotovoltaico'];
    selectedProject!.walkInCloset = characteristicsMap['Cabina armadio'];
  }

  Future savePlanimetryImages() async {
    Reference ref = FirebaseStorage.instance.ref('${appConfig.COLLECT_IMMAGINA_PROJECTS}/${selectedProject!.id}/planimetrie');
    final listResult = await ref.listAll();
    for (var element in listResult.items) {
      await FirebaseStorage.instance.ref(element.fullPath).delete();
    }
    selectedProject!.planimetry.clear();
    for (int i = 0; i < planimetryImages.length; i++) {
      String pictureFilename = 'planimetrie/planimetry_${i + 1}' + p.extension(planimetryImages[i].name);
      await uploadImageToStorage(appConfig.COLLECT_IMMAGINA_PROJECTS, selectedProject!.id, pictureFilename, planimetryImages[i]);
      selectedProject!.planimetry.add(pictureFilename);
    }
  }

  Future savePicturesImages() async {
    Reference ref = FirebaseStorage.instance.ref('${appConfig.COLLECT_IMMAGINA_PROJECTS}/${selectedProject!.id}/fotografie');
    final listResult = await ref.listAll();
    for (var element in listResult.items) {
      await FirebaseStorage.instance.ref(element.fullPath).delete();
    }
    selectedProject!.pictures.clear();
    for (int i = 0; i < picturesImages.length; i++) {
      String pictureFilename = 'fotografie/${picturesImages[i]['tag'] ?? 'notag'}_${i}' + p.extension(picturesImages[i]['file']!.name);
      await uploadImageToStorage(appConfig.COLLECT_IMMAGINA_PROJECTS, selectedProject!.id, pictureFilename, picturesImages[i]['file']);
      selectedProject!.pictures.add({'tag': picturesImages[i]['tag'], 'file': pictureFilename});
    }
  }

  Future<UploadTask?> uploadImageToStorage(String directory, String docId, String filename, XFile? file) async {
    if (file == null) {
      return null;
    }

    UploadTask uploadTask;

    Reference ref = FirebaseStorage.instance.ref().child(directory).child(docId).child(filename);

    final metadata = SettableMetadata(
      contentType: file.mimeType,
      customMetadata: {'picked-file-path': file.path},
    );

    uploadTask = ref.putData(await file.readAsBytes(), metadata);
    uploadTask.snapshotEvents.listen((TaskSnapshot taskSnapshot) {
      switch (taskSnapshot.state) {
        case TaskState.running:
          final progress = 100.0 * (taskSnapshot.bytesTransferred / taskSnapshot.totalBytes);
          // print("Upload is $progress% complete.");
          break;
        case TaskState.paused:
        // print("Upload is paused.");
          break;
        case TaskState.canceled:
        // print("Upload was canceled");
          break;
        case TaskState.error:
        // Handle unsuccessful uploads
          break;
        case TaskState.success:
        // Handle successful uploads on complete
        // ...
          break;
      }
    });

    return Future.value(uploadTask);
  }

  getFirestoreImages(directory) {
    if (selectedProject != null) {
      if (selectedProject!.pictures.isNotEmpty) {
        selectedProject!.pictures.forEach((pic) async {
          Reference ref = FirebaseStorage.instance.ref().child(directory + selectedProject!.id + "/" + pic['file']);
          Uint8List? data = await ref.getData();
          setState((){
            picturesImages.add({'tag': pic['tag'], 'file': XFile.fromData(data!, name: pic['file'])});
          });
        });
      }
      if (selectedProject!.planimetry.isNotEmpty) {
        selectedProject!.planimetry.forEach((plan) async {
          Reference ref = FirebaseStorage.instance.ref().child(directory + selectedProject!.id + "/" + plan);
          Uint8List? data = await ref.getData();
          setState((){
            planimetryImages.add(XFile.fromData(data!, name: plan));
          });
        });
      }
    }
  }

  selectView() {
    if (selectedView == null) {
      return NarFormLabelWidget(label: "view can't be null selected");
    } else if (selectedView == 'initial') {
      return initialDialog();
    } else if (selectedView == 'localizzazione') {
      return localizzazioneDialog();
    } else if (selectedView == 'info-generali') {
      return infoGeneraliDialog();
    } else if (selectedView == 'descrizione') {
      return descrizioneDialog();
    } else if (selectedView == 'caratteristiche') {
      return caratteristicheDialog();
    } else if (selectedView == 'planimetria') {
      return planimetriaDialog();
    } else if (selectedView == 'fotografie') {
      return fotografieDialog();
    } else if (selectedView == 'indicazioni-speciali') {
      return indicazioniSpecialiDialog();
    } else if (selectedView == 'pagamento') {
      return pagamentoDialog();
    } else {
      return Center(child: NarFormLabelWidget(label: "view $selectedView unknown"));
    }
  }

  Widget initialDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        Image.asset(
          'assets/logo_newarc_immagina_pro.png',
          height: 50,
        ),
        NarFormLabelWidget(
          label: 'Sei pronto per effettuare \nuna nuova richiesta di progetto?',
          overflow: TextOverflow.visible,
          textAlign: TextAlign.center,
          fontWeight: '800',
          fontSize: 30,
          textColor: Theme.of(context).disabledColor,
        ),
        Container(
          height: MediaQuery.of(context).size.height / 3.5,
          width: 600,
          decoration: BoxDecoration(color: const Color.fromARGB(255, 234, 234, 234), borderRadius: BorderRadius.all(Radius.circular(15))),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                height: 10,
              ),
              NarFormLabelWidget(
                label: 'COSA TI SERVIRÀ',
                fontWeight: '700',
                fontSize: 14,
                textColor: Color(0xff5f5f5f),
                letterSpacing: 2,
              ),
              SizedBox(
                height: 10,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  SizedBox(
                    width: 20,
                  ),
                  Expanded(
                    child: Column(
                      children: [
                        Image.asset(
                          'assets/icons/dati-immobile.png',
                          color: Color(0xFF737373),
                          height: MediaQuery.of(context).size.height / 10,
                        ),
                        SizedBox(
                          height: 5,
                        ),
                        NarFormLabelWidget(
                          label: 'Dati immobile',
                          fontWeight: '500',
                          fontSize: 13,
                          textColor: Colors.black,
                        ),
                        SizedBox(
                          height: 3,
                        ),
                        NarFormLabelWidget(
                          label: '',
                          fontWeight: '800',
                          fontSize: 10,
                          textColor: Theme.of(context).primaryColorLight,
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      children: [
                        Image.asset(
                          'assets/icons/planimetria.png',
                          color: Color(0xFF737373),
                          height: MediaQuery.of(context).size.height / 10,
                        ),
                        SizedBox(
                          height: 5,
                        ),
                        NarFormLabelWidget(
                          label: 'Planimetria di progetto',
                          fontWeight: '500',
                          fontSize: 13,
                          textColor: Colors.black,
                        ),
                        SizedBox(
                          height: 3,
                        ),
                        NarFormLabelWidget(
                          label: '',
                          fontWeight: '800',
                          fontSize: 10,
                          textColor: Color(0xff878787),
                          letterSpacing: 1,
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      children: [
                        Image.asset(
                          'assets/icons/pagamento.png',
                          color: Color(0xFF737373),
                          height: MediaQuery.of(context).size.height / 10,
                        ),
                        SizedBox(
                          height: 5,
                        ),
                        NarFormLabelWidget(
                          label: 'Carte di pagamento',
                          fontWeight: '500',
                          fontSize: 13,
                          textColor: Colors.black,
                        ),
                        SizedBox(
                          height: 3,
                        ),
                        NarFormLabelWidget(
                          label: '',
                          fontWeight: '800',
                          fontSize: 10,
                          textColor: Color(0xff878787),
                          letterSpacing: 1,
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 20,
                  ),
                ],
              ),
              SizedBox(
                height: 20,
              ),
            ],
          ),
        ),
        Column(children: [
          NarFormLabelWidget(
            label: 'Non hai tutto il materiale a disposizione?',
            fontWeight: '500',
            fontSize: 16,
            textColor: Theme.of(context).disabledColor,
          ),
          SizedBox(
            height: 5,
          ),
          NarFormLabelWidget(
            label: 'Non preoccuparti: potrai aggiungere in seguito il materiale mancante.',
            fontWeight: '500',
            fontSize: 14,
            textColor: Theme.of(context).disabledColor,
            letterSpacing: 0,
          )
        ]),
      ],
    );
  }

  Widget localizzazioneDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        NarFormLabelWidget(
          label: 'Localizzazione',
          fontWeight: '800',
          fontSize: 23,
          textColor: Theme.of(context).disabledColor,
        ),
        Padding(
            padding: EdgeInsets.only(top: MediaQuery.of(context).size.height / 10),
            child:
            Container(
                height: MediaQuery.of(context).size.height / 3,
                width: 400,
                child: Column(
                  children: [
                    AddressSearchBar(
                      label: 'Indirizzo',
                      onPlaceSelected: (selectedPlace) async {
                        if (BaseAddressInfo.fromMap(selectedPlace["place"]).isValidAddress()){
                          setState(() {
                            selectedProject!.streetName = selectedPlace["place"]["streetName"];
                            selectedProject!.streetNumber = selectedPlace["place"]["streetNumber"];
                            selectedProject!.city = selectedPlace["place"]["city"];
                            selectedProject!.postalCode = selectedPlace["place"]["postalCode"];
                            selectedProject!.province = selectedPlace["place"]["province"];
                            selectedProject!.region = selectedPlace["place"]["region"];
                            selectedProject!.country = selectedPlace["place"]["country"];
                            selectedProject!.latitude = selectedPlace["place"]["latitude"];
                            selectedProject!.longitude = selectedPlace["place"]["longitude"];
                            selectedProject!.fullAddress = selectedPlace["description"];
                            selectedProject!.addressInfo = BaseAddressInfo.fromMap(selectedPlace["place"]);
                          });
                          selectedProject!.projectId = await computeImmaginaProjectCode();
                        } else{
                          setState(() {
                            selectedProject!.streetName = null;
                            selectedProject!.streetNumber = null;
                            selectedProject!.city = null;
                            selectedProject!.postalCode = null;
                            selectedProject!.province = null;
                            selectedProject!.region = null;
                            selectedProject!.country = null;
                            selectedProject!.latitude = null;
                            selectedProject!.longitude = null;
                            selectedProject!.fullAddress = null;
                            selectedProject!.addressInfo = null;
                          });
                        }
                      },
                      initialAddress: selectedProject!.fullAddress,
                    ),
                    (cityMarketZoneMap.containsKey(selectedProject!.city)) ?
                    Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: 12,),
                          NarFormLabelWidget(
                            label: 'Zona',
                            textColor: Color(0xff696969),
                            fontSize: 13,
                            fontWeight: '600',
                            textAlign: TextAlign.left,
                          ),
                          SizedBox(height: 5,),
                          SearchSelectBox(
                              controller: filterMarketZone,
                              options: [
                                for (String value in cityMarketZoneMap[selectedProject!.city] ?? []) {'id': value, 'name': value.toTitleCase()}
                              ],
                              onSelection: (selected) {
                                setState(() {
                                  selectedProject!.marketZone = filterMarketZone.text;
                                });
                              }
                          )
                        ]
                    )
                        : SizedBox(),
                  ],
                )
            )
        ),
      ],
    );
  }

  Widget infoGeneraliDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        NarFormLabelWidget(
          label: 'Informazioni generali',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        Padding(
          padding: EdgeInsets.only(top: MediaQuery.of(context).size.height / 20),
          child: Container(
              height: MediaQuery.of(context).size.height / 2,
              width: 400,
              child: Column(mainAxisAlignment: MainAxisAlignment.center, crossAxisAlignment: CrossAxisAlignment.start, children: [
                Row(
                  children: [
                    Expanded(
                      child: NarSelectBoxWidget(
                        label: "Tipologia",
                        options: appConst.propertyTypesList,
                        controller: filterPropertyType,
                        onChanged: ((value) {
                          setState(() {
                            selectedProject!.propertyType = filterPropertyType.text;
                          });
                        }),
                      ),
                    ),
                    SizedBox(width: 10),
                    CustomTextFormField(
                      isNumber: true,
                      label: "Metri quadri commerciali",
                      controller: filterGSF,
                      onChangedCallback: (String _gSF) {
                        setState(() {
                          selectedProject!.grossSquareFootage = int.tryParse(filterGSF.text);
                        });
                      },
                    ),  
                  ],
                ),
                SizedBox(height: 25),
                Row(
                  children: [
                    Expanded(
                      child: NarSelectBoxWidget(
                        label: "Bagni",
                        options: appConst.bagniList,
                        controller: filterBathrooms,
                        onChanged: ((value) {
                          setState(() {
                            selectedProject!.numberOfBathrooms = int.tryParse(filterBathrooms.text.replaceAll(r'+', ''));
                          });
                        }),
                      ),
                    ),
                    SizedBox(width: 10),
                    Expanded(
                      child: NarSelectBoxWidget(
                        label: "Locali",
                        options: appConst.localiList,
                        controller: filterRooms,
                        onChanged: ((value) {
                          setState(() {
                            selectedProject!.rooms = int.tryParse(filterRooms.text.replaceAll(r'+', ''));
                          });
                        }),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 25),
                Row(
                  children: [
                    Expanded(
                      child: NarSelectBoxWidget(
                        label: "Piano",
                        options: appConst.pianoList,
                        controller: filterUnitFloor,
                        onChanged: ((value) {
                          setState(() {
                            selectedProject!.unitFloor = filterUnitFloor.text;
                          });
                        }),
                      ),
                    ),
                    SizedBox(width: 10),
                    CustomTextFormField(
                      label: "Richiesta",
                      isMoney: true,
                      // validator: (value) {35y4
                      //   if (value == null || value.isEmpty) {
                      //     return 'Inserisci un indirizzo';
                      //   }
                      //   return null;
                      // },
                      controller: filterListingPrice,
                      onChangedCallback: (String _price) {
                        setState(() {
                          selectedProject!.listingPrice = int.tryParse(filterListingPrice.text.replaceAll(r'.', ''));
                        });
                      },
                    ),
                  ],
                )
              ]
            )
          ),
        )
      ],
    );
  }

  Widget descrizioneDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        NarFormLabelWidget(
          label: 'Descrizione',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        Padding(
          padding: EdgeInsets.only(top: MediaQuery.of(context).size.height / 15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              NarFormLabelWidget(
                label: "Aggiungi una descrizione generale dell'immobile",
                textColor: Color(0xff696969),
                fontSize: 13,
                fontWeight: '600',
                textAlign: TextAlign.left,
              ),
              SizedBox(
                height: 5,
              ),
              Container(
                height: MediaQuery.of(context).size.height / 2,
                width: MediaQuery.of(context).size.width / 2,
                child: TextField(
                  textAlign: TextAlign.left,
                  textAlignVertical: TextAlignVertical.top,
                  minLines: null,
                  maxLines: null,
                  expands: true,
                  keyboardType: TextInputType.multiline,
                  controller: filterDescription,
                  onChanged: (desc) {
                    setState(() {
                      selectedProject!.description = filterDescription.text;
                    });
                  },
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 15,
                    fontFamily: 'Raleway-600',
                  ),
                  decoration: InputDecoration(
                      hintText: "Scrivi qui una descrizione generale per descrivere l'immobile.",
                      hintStyle: TextStyle(
                        color: Color.fromARGB(255, 150, 150, 150),
                        fontSize: 15,
                        fontFamily: 'Raleway-600',
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                        borderSide: BorderSide(color: Color(0xffdbdbdb)),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                        borderSide: BorderSide(color: Color(0xffdbdbdb)),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                        borderSide: BorderSide(color: Color(0xffdbdbdb)),
                      )),
                ),
              ),
            ],
          ),
        )
      ],
    );
  }

  /*Widget externalEspositionButton(String label){
    return Padding(
      padding: const EdgeInsets.only(right: 8.0),
      child: OutlinedButton(
        style: OutlinedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.0),
          ),
          side: BorderSide(
              color: selectedProject!.externalEsposition.contains(label)? Colors.transparent : Color(0xffdbdbdb)
          ),
          backgroundColor: selectedProject!.externalEsposition.contains(label)? Theme.of(context).primaryColor : Colors.transparent,
        ),
        onPressed: () {
          if (selectedProject!.externalEsposition.contains(label)) {
            setState(() {
              selectedProject!.externalEsposition.removeWhere((item) => item == label);
            });
          } else {
            setState(() {
              selectedProject!.externalEsposition.add(label);
            });
          }

        },
        child: Text(
          label,
          style: TextStyle(
            color: selectedProject!.externalEsposition.contains(label) ? Theme.of(context).unselectedWidgetColor : Theme.of(context).primaryColorDark,
            fontSize: 14,
            fontFamily: 'Raleway-600',
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }*/

  Widget caratteristicheDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        NarFormLabelWidget(
          label: 'Caratteristiche',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        Padding(
          padding: EdgeInsets.only(top: MediaQuery.of(context).size.height / 20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                // height: MediaQuery.of(context).size.height / 2,
                width: MediaQuery.of(context).size.width / 2,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    color: Color(0xffdbdbdb),
                  ),
                ),
                child: NarCheckboxWidget(
                  label: 'characteristics',
                  values: characteristicsMap,
                  columns: 4,
                  fontSize: 13,
                  childAspectRatio: 5,
                ),
              ),
              SizedBox(height: 20,),
              Container(
                width: MediaQuery.of(context).size.width / 2,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Expanded(
                      child: NarSelectBoxWidget(
                        label: 'Classe energetica',
                        options: appConst.energyClassList,
                        controller: filterEnergyClass,
                        onChanged: ((value) {
                          setState(() {
                            selectedProject!.energyClass = filterEnergyClass.text;
                          });
                        }),
                      ),
                    ),
                    SizedBox(width: 30,),
                    CustomTextFormField(
                      label: "Anno di costruzione",
                      controller: filterConstructionYear,
                      onChangedCallback: (String? _streetNum) {
                        if (_streetNum == null || _streetNum.isEmpty || int.tryParse(_streetNum) == null) {
                          setState(() {
                            selectedProject!.constructionYear = null;
                          });
                        } else {
                          setState(() {
                            selectedProject!.constructionYear = int.tryParse(filterConstructionYear.text);
                          });
                        };
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty || int.tryParse(value) == null) {
                          return 'Inserisci un anno di costruzione valido';
                        }
                        return null;
                      },
                    ),
                    SizedBox(width: 30,),
                    Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          NarFormLabelWidget(
                            label: 'Esposizione',
                            textColor: Color(0xff696969),
                            fontSize: 13,
                            fontWeight: '500',
                          ),
                          SizedBox(height: 15,),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              externalEspositionButton('Nord', selectedProject!, context, stateSetter: setState),
                              externalEspositionButton('Sud', selectedProject!, context, stateSetter: setState),
                              externalEspositionButton('Ovest', selectedProject!, context, stateSetter: setState),
                              externalEspositionButton('Est', selectedProject!, context, stateSetter: setState),
                            ],
                          )
                        ]
                    )
                  ],
                ),
              )
            ],
          ),
        )
      ],
    );
  }

  getPlanimetryFromGallery(
      BuildContext context,
      ) async {
    bool wrongExtension = false;
    bool wrongSize = false;
    await FilePicker.platform
        .pickFiles(
      allowMultiple: true,
      type: FileType.custom,
      allowedExtensions: allowedPlanimetryExtensions,
    )
        .then((filesList) {
      if (filesList != null) {
        filesList.files.forEach((file) {
          if (file.size < 10 * 1024 * 1024) {
            if (allowedPlanimetryExtensions.contains(file.extension)) {
              planimetryImages.add(file.xFile);
            } else {
              wrongExtension = true;
            }
          } else {
            wrongSize = true;
          }
        });
      }
      setState(() {});
      if (wrongExtension) {
        return showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return Center(
              child: BaseNewarcPopup(
                title: 'Errore: Estensione file non valida',
                column: Column(
                  children: [
                    Text('Inserisci file di planimetrie con estensione: ${allowedPlanimetryExtensions.join(', ')}'),
                  ],
                ),
              ),
            );
          },
        );
      }
      if (wrongSize) {
        return showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return Center(
              child: BaseNewarcPopup(
                title: 'Errore: Dimensione file non valida',
                column: Column(
                  children: [
                    Text('Inserisci file di planimetrie con dimensione massima 10 MB'),
                  ],
                ),
              ),
            );
          },
        );
      }
    });
  }

  _showUploadPlanimetryDialog(BuildContext context) {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) {
        return Center(
          child: BaseNewarcPopup(
              title: 'Carica immagini',
              noButton: true,
              column: StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
                return SingleChildScrollView(
                  child: Container(
                    height: 300,
                    width: 500,
                    child: Column(
                      children: [
                        Expanded(
                          flex: 40,
                          child: GestureDetector(
                            onTap: () async {
                              await getPlanimetryFromGallery(context);
                              setState(() {});
                              Navigator.of(context).pop(true);
                            },
                            child: MouseRegion(
                              cursor: SystemMouseCursors.click,
                              child: Container(
                                decoration: BoxDecoration(
                                    color: Color.fromRGBO(240, 240, 240, 1),
                                    //shape: BoxShape.circle,
                                    borderRadius: BorderRadius.all(Radius.circular(10))),
                                width: 400,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.upload_file,
                                      size: 60,
                                      color: Color.fromRGBO(128, 128, 128, 1),
                                    ),
                                    SizedBox(
                                      height: 30,
                                    ),
                                    NarFormLabelWidget(
                                      label: "Clicca per caricare",
                                      fontWeight: '700',
                                      fontSize: 18,
                                      textColor: Color.fromRGBO(128, 128, 128, 1),
                                      textAlign: TextAlign.center,
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 30,
                        ),
                      ],
                    ),
                  ),
                );
              })),
        );
      },
    );
  }

  Widget planimetriaDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        NarFormLabelWidget(
          label: 'Planimetria',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        SizedBox(
          height: 30,
        ),
        SizedBox(
          height: 50,
          width: 400,
          child: BarToggleButton(
            key: UniqueKey(),
            startingState: !(selectedProject!.wantsNewarcPlanimetry),
            onStateChanged: (selection) {
              setState(() {
                selectedProject!.wantsNewarcPlanimetry = !selection;
              });
            },
            trueStateText: 'Carica la tua planimetria',
            falseStateText: 'Richiedi rilievo a Newarc',
          ),
        ),
        Padding(
          padding: EdgeInsets.only(top: MediaQuery.of(context).size.height / 60),
          child: Container(
            height: MediaQuery.of(context).size.height / 2,
            child: Column(
              children: [
                SizedBox(
                  height: 10,
                ),
                ...!(selectedProject!.wantsNewarcPlanimetry)
                    ? [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Icon(
                              Icons.info,
                              size: 20,
                              color: Color(0xffbdbdbd),
                            ),
                            Expanded(      
                              child: RichText(
                                textAlign: TextAlign.center,
                                text: TextSpan(
                                  children: [
                                    TextSpan(
                                      text: 'La planimetria deve essere ben visibile e comprensibile in tutte le sue parti (con riferimento di scala grafica) e deve essere corrispondente allo stato reale, idealmente in fromato DWG, altrimenti JPEG, PNG o PDF. Consulta le ',
                                      style: TextStyle(
                                        fontFamily: 'Raleway-600',
                                        fontSize: 13,
                                        color: Color.fromRGBO(133, 133, 133, 1),
                                        height: 1.5,
                                      ),
                                    ), 
                                    TextSpan(
                                      text: 'linee guida',
                                      style: TextStyle(
                                        fontFamily: 'Raleway-600',
                                        fontSize: 13,
                                        color: Theme.of(context).primaryColor,
                                        decoration: TextDecoration.underline,
                                        height: 1.5,
                                      ),
                                      recognizer: new TapGestureRecognizer()
                                        ..onTap = () async {
                                          String url = 'https://www.newarc.it/guida-agenzie/#planimetria';
                                          await launchUrl(Uri.parse(url));
                                        },
                                    ),
                                    TextSpan(
                                      text: ' per maggiori informazioni.',
                                      style: TextStyle(
                                        fontFamily: 'Raleway-600',
                                        fontSize: 13,
                                        color: Color.fromRGBO(133, 133, 133, 1),
                                        height: 1.5,
                                      )
                                    ),
                                  ]
                                )
                              )
                            ),
                          ],
                        ),
                  SizedBox(
                    height: 20,
                  ),
                  // Image picker
                  Container(
                    height: (MediaQuery.of(context).size.height / 2.6),
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Color(0xffd3d3d3),
                      ),
                    ),
                    child: Stack(
                      children: [
                        SingleChildScrollView(
                          padding: EdgeInsets.only(top: 20, bottom: 20, left: 8, right: 100),
                          child: Wrap(
                            spacing: 12,
                            runSpacing: 20,
                            children: planimetryImages
                              .map((imageFile) => FutureBuilder<Uint8List>(
                                future: imageFile.readAsBytes(),
                                builder: (context, snapshot) {
                                  if (snapshot.connectionState == ConnectionState.done && snapshot.hasData) {
                                    return Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Stack(
                                          children: [
                                            ClipRRect(
                                              borderRadius: BorderRadius.circular(8),
                                              child: imageFile.name.split('.').last.toLowerCase() == 'dwg'
                                                  ? Image.asset(
                                                'assets/icons/dwg.png',
                                                color: Color(0xffa6a6a6),
                                                width: 150,
                                                height: 150,
                                                fit: BoxFit.cover,
                                              )
                                                  : imageFile.name.split('.').last.toLowerCase() == 'pdf'
                                                  ? Image.asset(
                                                'assets/icons/pdf.png',
                                                color: Color(0xffa6a6a6),
                                                width: 150,
                                                height: 150,
                                                fit: BoxFit.cover,
                                              )
                                                  : Image.memory(
                                                snapshot.data!,
                                                width: 150,
                                                height: 150,
                                                fit: BoxFit.cover,
                                              ),
                                            ),
                                            Positioned(
                                              top: 3,
                                              right: 3,
                                              child: GestureDetector(
                                                onTap: () {
                                                  planimetryImages.remove(imageFile);
                                                  setState(() {});
                                                },
                                                child: Container(
                                                  decoration: BoxDecoration(
                                                    shape: BoxShape.circle,
                                                    color: Theme.of(context).primaryColorDark,
                                                  ),
                                                  child: Padding(
                                                    padding: EdgeInsets.all(3),
                                                    child: SvgPicture.asset(
                                                      'assets/icons/close-popup.svg',
                                                      height: 10,
                                                      color: Theme.of(context).unselectedWidgetColor,
                                                    )
                                                  )
                                                )
                                              )
                                            ),
                                          ],
                                        ),
                                      ],
                                    );
                                  } else if (snapshot.connectionState == ConnectionState.waiting) {
                                    return Container(
                                      width: 80,
                                      height: 80,
                                      color: Colors.grey[300],
                                      child: Center(
                                        child: CircularProgressIndicator(),
                                      ),
                                    );
                                  } else {
                                    return Container(
                                      width: 80,
                                      height: 80,
                                      color: Colors.grey[300],
                                      child: Center(
                                        child: Icon(Icons.error, color: Colors.red),
                                      ),
                                    );
                                  }
                                },
                              )
                            ).toList(),
                          ),
                        ),
                        Positioned(
                          top: 8,
                          right: 8,
                          child: BaseNewarcButton(
                            color: Color(0xffE5E5E5),
                            textColor: Colors.black,
                            buttonText: "Carica",
                            fontWeight: '700',
                            fontSize: 14,
                            height: 35,
                            onPressed: () async {
                              await _showUploadPlanimetryDialog(context);
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ]
                    : [
                  Expanded(
                      child: Container(
                          width: MediaQuery.of(context).size.width / 2,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              SizedBox(
                                height: 20,
                              ),
                              Image.asset(
                                'assets/icons/planimetria.png',
                                height: 80,
                                color: Color(0xffa6a6a6),
                              ),
                              Text(
                                'Un nostro architetto si metterà in contatto con te \nper organizzare il rilievo',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: Theme.of(context).disabledColor,
                                  fontSize: 20,
                                  fontFamily: 'Raleway-700',
                                ),
                              ),
                              Text(
                                selectedProject!.grossSquareFootage! < gSFUpperLimit
                                ? 'Il servizio ha un costo aggiuntivo di: ${ImmaginaProjectEconomics(project: selectedProject!).computeNewarcRilieviFee()}€ + IVA'
                                : 'Il servizio ha un costo aggiuntivo da preventivare',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: Theme.of(context).primaryColor,
                                  fontSize: 15,
                                  fontFamily: 'Raleway-600',
                                ),
                              ),
                              SizedBox(
                                height: 20,
                              ),
                            ],
                          )))
                ]
              ],
            ),
          ),
        )
      ],
    );
  }

  Future<void> getPictureFromGallery(BuildContext context) async {
    bool wrongExtension = false;
    final filesList = await FilePicker.platform.pickFiles(
      allowMultiple: true,
      type: FileType.custom,
      allowedExtensions: allowedPicturesExtensions,
    );

    if (filesList != null) {
      for (final file in filesList.files) {
        if (allowedPicturesExtensions.contains(file.extension?.toLowerCase())) {
          final bytes = await file.xFile.readAsBytes();
          Uint8List convertedBlob = bytes;

          if (file.extension?.toLowerCase().contains("heic") ?? false) {
            convertedBlob = await heicToJpeg.HeicToJpegService().convertHeicToJpeg(bytes);
          }

          picturesImages.add({
            'tag': null,
            'file': XFile.fromData(
              convertedBlob,
              name: file.name,
              mimeType: 'image/jpeg',
            )
          });
        } else {
          wrongExtension = true;
        }
      }
      setState(() {});
      if (wrongExtension) {
        await showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return Center(
              child: BaseNewarcPopup(
                title: 'Errore: Estensione file non valida',
                column: Column(
                  children: [
                    Text('Inserisci file di fotografie con estensione: ${allowedPicturesExtensions.join(', ')}'),
                  ],
                ),
              ),
            );
          },
        );
      }
    }
  }

  _showUploadPicturesDialog(BuildContext context) {
    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!

      // barrierDismissible: ,
      builder: (_) {
        // builder: (context, setState) {

        return Center(
          child: BaseNewarcPopup(
              title: 'Carica immagini',
              noButton: true,
              column: StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
                return SingleChildScrollView(
                  child: Container(
                    height: 300,
                    width: 500,
                    child: Column(
                      children: [
                        Expanded(
                          flex: 40,
                          child: GestureDetector(
                            onTap: () async {
                              await getPictureFromGallery(context);
                              setState(() {});
                              Navigator.of(context).pop(true);
                            },
                            child: MouseRegion(
                              cursor: SystemMouseCursors.click,
                              child: Container(
                                decoration: BoxDecoration(
                                    color: Color.fromRGBO(240, 240, 240, 1),
                                    //shape: BoxShape.circle,
                                    borderRadius: BorderRadius.all(Radius.circular(10))),
                                width: 400,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.upload_file,
                                      size: 60,
                                      color: Color.fromRGBO(128, 128, 128, 1),
                                    ),
                                    SizedBox(
                                      height: 30,
                                    ),
                                    NarFormLabelWidget(
                                      label: "Clicca per caricare",
                                      fontWeight: '700',
                                      fontSize: 18,
                                      textColor: Color.fromRGBO(128, 128, 128, 1),
                                      textAlign: TextAlign.center,
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 30,
                        ),
                      ],
                    ),
                  ),
                );
              })),
        );
      },
    );
  }

  Widget fotografieDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        NarFormLabelWidget(
          label: 'Fotografie',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        SizedBox(
          height: 30,
        ),
        SizedBox(
          height: 50,
          width: 400,
          child: BarToggleButton(
            key: UniqueKey(),
            startingState: !(selectedProject!.wantsNewarcPictures),
            onStateChanged: (selection) {
              setState(() {
                selectedProject!.wantsNewarcPictures = !selection;
              });
            },
            trueStateText: 'Carica le tue foto',
            falseStateText: 'Richiedi servizio a Newarc',
          ),
        ),
        Padding(
          padding: EdgeInsets.only(top: MediaQuery.of(context).size.height / 60),
          child: Container(
            height: MediaQuery.of(context).size.height / 2,
            child: Column(
              children: [
                SizedBox(
                  height: 10,
                ),
                ...!(selectedProject!.wantsNewarcPictures)
                    ? [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.info,
                        size: 20,
                        color: Color(0xffbdbdbd),
                      ),
                      SizedBox(
                        width: 5,
                      ),
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: 'Le fotografie devono essere fornite in fromato JPEG e devono seguire le linee guida che trovi in ',
                              style: TextStyle(
                                fontFamily: 'Raleway-600',
                                fontSize: 13,
                                color: Color.fromRGBO(133, 133, 133, 1),
                              ),
                            ),
                            TextSpan(
                              text: 'questa guida',
                              style: TextStyle(
                                fontFamily: 'Raleway-600',
                                fontSize: 13,
                                color: Theme.of(context).primaryColor,
                                decoration: TextDecoration.underline,
                              ),
                              recognizer: new TapGestureRecognizer()
                                ..onTap = () async {
                                  String url = 'https://www.newarc.it/guida-agenzie/#immagini';
                                  await launchUrl(Uri.parse(url));
                                },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 20,
                  ),
                  // Image picker
                  Container(
                    height: MediaQuery.of(context).size.height / 2.6,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Color(0xffd3d3d3),
                      ),
                    ),
                    child: Stack(
                      children: [
                        SingleChildScrollView(
                          padding: EdgeInsets.only(top: 20, bottom: 20, left: 8, right: 100),
                          child: Wrap(
                            spacing: 12,
                            runSpacing: 20,
                            children: picturesImages
                                .map((imageFile) => FutureBuilder<Uint8List>(
                              future: imageFile['file'].readAsBytes(),
                              builder: (context, snapshot) {
                                TextEditingController tempController = TextEditingController(text: imageFile['tag'] ?? "");
                                if (snapshot.connectionState == ConnectionState.done && snapshot.hasData) {
                                  return Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Stack(
                                        children: [
                                          MouseRegion(
                                            cursor: SystemMouseCursors.click,
                                            child: GestureDetector(
                                              onTap: () {
                                                showEnlargedDownloadableImage(context, imageFile);
                                              },
                                              child: ClipRRect(
                                                borderRadius: BorderRadius.circular(8),
                                                child: Image.memory(
                                                  snapshot.data!,
                                                  width: 150,
                                                  height: 150,
                                                  fit: BoxFit.cover,
                                                ),
                                              ),
                                            ),
                                          ),
                                          Positioned(
                                              top: 3,
                                              right: 3,
                                              child: GestureDetector(
                                                  onTap: () {
                                                    setState(() {
                                                      picturesImages.remove(imageFile);
                                                    });
                                                  },
                                                  child: Container(
                                                      decoration: BoxDecoration(
                                                        shape: BoxShape.circle,
                                                        color: Theme.of(context).primaryColorDark,
                                                      ),
                                                      child: Padding(
                                                          padding: EdgeInsets.all(3),
                                                          child: SvgPicture.asset(
                                                            'assets/icons/close-popup.svg',
                                                            height: 10,
                                                            color: Theme.of(context).unselectedWidgetColor,
                                                          )
                                                      )
                                                  )
                                              )
                                          ),
                                        ],
                                      ),
                                      SizedBox(
                                        height: 5,
                                      ),
                                      Container(
                                        width: 150,
                                        child: NarSelectBoxWidget(
                                          label: 'Seleziona ambiente',
                                          controller: tempController,
                                          onChanged: (value) {
                                            if (tempController.text != ''){
                                              imageFile['tag'] = tempController.text;
                                            }
                                            setState(() {});
                                          },
                                          options: List.of(appConst.roomsList)..sort((a, b) => a.toLowerCase().compareTo(b.toLowerCase())),
                                          contentPadding: EdgeInsets.only(top: 10, bottom: 10, left: 10, right: 2),
                                        ),
                                      ),
                                    ],
                                  );
                                } else if (snapshot.connectionState == ConnectionState.waiting) {
                                  return Container(
                                    width: 80,
                                    height: 80,
                                    color: Colors.grey[300],
                                    child: Center(
                                      child: CircularProgressIndicator(),
                                    ),
                                  );
                                } else {
                                  return Container(
                                    width: 80,
                                    height: 80,
                                    color: Colors.grey[300],
                                    child: Center(
                                      child: Icon(Icons.error, color: Colors.red),
                                    ),
                                  );
                                }
                              },
                            ))
                                .toList(),
                          ),
                        ),
                        Positioned(
                          top: 8,
                          right: 8,
                          child: BaseNewarcButton(
                            color: Color(0xffE5E5E5),
                            textColor: Colors.black,
                            buttonText: "Carica",
                            fontWeight: '700',
                            fontSize: 14,
                            height: 35,
                            onPressed: () async {
                              await _showUploadPicturesDialog(context);
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ]
                    : [
                  Expanded(
                      child: Container(
                          width: MediaQuery.of(context).size.width / 2,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              SizedBox(
                                height: 20,
                              ),
                              SvgPicture.asset(
                                'assets/icons/photographer.svg',
                                height: 80,
                                color: Color(0xffa6a6a6),
                              ),
                              Text(
                                'Un nostro fotografo si metterà in contatto con te per organizzare il servizio fotografico',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: Theme.of(context).disabledColor,
                                  fontSize: 20,
                                  fontFamily: 'Raleway-700',
                                ),
                              ),
                              Text(
                                selectedProject!.grossSquareFootage! < gSFUpperLimit
                                ? 'Il servizio ha un costo aggiuntivo di: ${ImmaginaProjectEconomics(project: selectedProject!).computeNewarcMediaFee()}€ + IVA'
                                : 'Il servizio ha un costo aggiuntivo da preventivare',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: Theme.of(context).primaryColor,
                                  fontSize: 15,
                                  fontFamily: 'Raleway-600',
                                ),
                              ),
                              SizedBox(
                                height: 20,
                              ),
                            ],
                          )))
                ]
              ],
            ),
          ),
        )
      ],
    );
  }

  Future<Size> _calculateImageDimension(BuildContext context, String url) {
    Completer<Size> completer = Completer();
    Image image = Image.network(url);
    image.image.resolve(ImageConfiguration()).addListener(
      ImageStreamListener((ImageInfo info, bool _) {
        completer.complete(Size(
          info.image.width.toDouble(),
          info.image.height.toDouble(),
        ));
      }),
    );
    return completer.future;
  }

  downloadPictureFile(String url, String filename) {
    String nome_file = filename.replaceAll("fotografie/", "");
    final anchor = web.HTMLAnchorElement()
      ..href = url
      ..download = nome_file;
    anchor.click();
  }

  showEnlargedDownloadableImage(context, Map<String, dynamic> imageFile) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (_) {
        // builder: (context, setState) {
        return FutureBuilder<Size>(
          future: _calculateImageDimension(context, imageFile['file'].path),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      CircularProgressIndicator(
                        color: Theme.of(context).primaryColor,
                      ),
                      SizedBox(height: 5),
                      NarFormLabelWidget(
                        label: 'Loading...',
                        textColor: Colors.white,
                      )
                    ],
                  ));
            } else if (snapshot.hasError) {
              return AlertDialog(
                title: NarFormLabelWidget(label: 'Error'),
                content: NarFormLabelWidget(label: 'Could not load image'),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: NarFormLabelWidget(label: 'Close'),
                  ),
                ],
              );
            } else {
              final size = snapshot.data!;

              double imageWidth = 0;
              double imageHeight = 0;
              double displayWidth = 0;
              double displayHeight = 0;

              double maxImageWidth = MediaQuery.of(context).size.width;
              double maxImageHeight = MediaQuery.of(context).size.height;

              imageWidth = size.width.toDouble();
              imageHeight = size.height.toDouble();
              double aspectRatio = imageWidth / imageHeight;

              displayWidth = imageWidth;
              displayHeight = imageHeight;

              if (displayWidth > maxImageWidth) {
                displayWidth = maxImageWidth;
                displayHeight = displayWidth / aspectRatio;
              }

              if (displayHeight > maxImageHeight) {
                displayHeight = maxImageHeight;
                displayWidth = displayHeight * aspectRatio;
              }

              return Center(
                child: Wrap(
                  children: [
                    Material(
                      color: Colors.transparent,
                      child: Center(
                        child: Stack(
                          children: [
                            Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                    width: displayWidth,
                                    height: displayHeight,
                                    padding: const EdgeInsets.all(0),
                                    // decoration: BoxDecoration(
                                    //   borderRadius: BorderRadius.circular(15.0),
                                    // ),

                                    child: ListView(
                                      padding: EdgeInsets.all(0),
                                      shrinkWrap: true,
                                      children: [
                                        Card(
                                          color: const Color.fromRGBO(255, 255, 255, 1),
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(15.0),
                                          ),
                                          clipBehavior: Clip.hardEdge,
                                          child: Column(
                                            children: [
                                              Container(
                                                color: const Color.fromARGB(255, 228, 228, 228),
                                                width: displayWidth,
                                                height: displayHeight,
                                                child: Image.network(
                                                  imageFile['file'].path,
                                                  fit: BoxFit.cover,
                                                ),
                                              )
                                            ],
                                          ),
                                        ),
                                      ],
                                    )),
                              ],
                            ),
                            // Positioned(
                            //   child: Container(
                            //     color: Color.fromRGBO(255, 255, 255, 0.8),
                            //     child: Padding(
                            //       padding: const EdgeInsets.all(5),
                            //       child: NarFormLabelWidget(label: imageFile['file'].name),
                            //     ),
                            //   ),
                            //   top: 10,
                            //   left: 10,
                            // ),
                            Positioned(
                              top: 10,
                              right: 70,
                              child: Container(
                                height: 50,
                                width: 50,
                                margin: EdgeInsets.only(left: 10),
                                decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(25)),
                                child: Center(
                                  child: IconButton(
                                      onPressed: () {
                                        downloadPictureFile(imageFile['file'].path, imageFile['file'].name);
                                      },
                                      splashRadius: 20,
                                      icon: Icon(
                                        Icons.download,
                                        color: Colors.black,
                                      )),
                                ),
                              ),
                            ),
                            Positioned(
                              top: 10,
                              right: 10,
                              child: Container(
                                height: 50,
                                width: 50,
                                margin: EdgeInsets.only(left: 10),
                                decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(25)),
                                child: Center(
                                  child: IconButton(
                                      onPressed: () {
                                        Navigator.pop(context);
                                      },
                                      splashRadius: 20,
                                      icon: Icon(
                                        Icons.close,
                                        color: Colors.black,
                                      )),
                                ),
                              ),
                            ),
                            // Positioned(
                            //   left: 0,
                            //   top: displayHeight / 2,
                            //   child: widget.allFiles!.length > 1
                            //       ? previousButton(context, filename)
                            //       : SizedBox(height: 0),
                            // ),
                            // Positioned(
                            //   right: 0,
                            //   top: displayHeight / 2,
                            //   child: widget.allFiles!.length > 1
                            //       ? nextButton(context, filename)
                            //       : SizedBox(height: 0),
                            // )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }
          },
        );
      },
    );
  }

  Widget indicazioniSpecialiDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        NarFormLabelWidget(
          label: 'Altre indicazioni',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        SizedBox(height: 30),
        Container(
          height: MediaQuery.of(context).size.height / 1.7,
          width: MediaQuery.of(context).size.width / 2.5,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                "Hai indicazioni speciali da darci riguardo al progetto?",
                style: TextStyle(
                  fontFamily: 'Raleway-700',
                  fontSize: 15,
                  color: Theme.of(context).disabledColor,
                ),
                textAlign: TextAlign.left,
              ),
              SizedBox(height: 5),
              Text("Es: la colonna della sala è portante",
                  style: TextStyle(
                    fontFamily: 'Raleway-500',
                    fontSize: 13,
                    color: Theme.of(context).primaryColorLight,
                  )),
              SizedBox(height: 15),
              SiNoToggleButton(
                startingState: selectedProject!.hasSpecialHints ?? false,
                onStateChanged: (value) {
                  setState(() {
                    selectedProject!.hasSpecialHints = value;
                  });
                },
              ),
              SizedBox(height: 30),
              Container(
                child: CustomTextFormField(
                  isExpanded: false,
                  label: 'Scrivici qui le tue indicazioni speciali',
                  controller: filterSpecialHints,
                  onChangedCallback: (value) {
                    setState(() {
                      selectedProject!.specialHints = value;
                    });
                  },
                  enabled: selectedProject!.hasSpecialHints ?? false,
                  fillColor: (selectedProject!.hasSpecialHints ?? false) ? Theme.of(context).unselectedWidgetColor : Colors.grey[100],
                  minLines: 6,
                ),
              ),
              SizedBox(height: 30),
              NarFormLabelWidget(
                label: "Quando vorresti mettere in vendita l’immobile?",
                fontSize: 15,
                fontWeight: '700',
                textColor: Theme.of(context).disabledColor,
                textAlign: TextAlign.left,
              ),
              SizedBox(height: 10),
              SizedBox(
                height: 75,
                child: NarSelectBoxWidget(
                  label: "Seleziona una risposta",
                  options: [
                    "L'immobile è gia in vendita",
                    "Appena pronto il progetto Immagina",
                    "Entro un mese",
                    "Entro tre mesi",
                  ],
                  onChanged: (value) {
                    if(value != null && value.toString().isNotEmpty){
                      setState(() {
                        selectedProject!.propertyUpForSaleAnswer = value;
                      });
                    }
                  },
                  controller: filterPropertyUpForSaleAnswer,
                ),
              )
            ],
          ),
        )
      ],
    );
  }

  Widget pagamentoDialog() {
    // whether immagina projects exceeds gSF limit
    final bool gSFExceeded = selectedProject!.grossSquareFootage! >= gSFUpperLimit;
    // if gSFExceeded change project status and send email
    if (gSFExceeded && selectedProject!.requestStatus == 'da completare') {
      try {
        final int time_now = DateTime.now().millisecondsSinceEpoch;
        Future.delayed(Duration.zero, () async {
          // update project status on firebase
          final projectDocRef = FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).doc(selectedProject!.id);
          projectDocRef.update({
            'requestStatus': 'preventivazione',
            'isPaidWithCredits': false,
            'appliedSuccessFee': "0.2",
            'statusChangedDate': time_now,
          });
          // send email to agency
          sendEmail(
            templateId: CommonUtils.agencyNewOversizedRequestEmailTemplateId, 
            subject: CommonUtils.agencyNewOversizedRequestEmailSubject, 
            recipientEmail: widget.agencyUser.agency!.email,
            recipientName: widget.agencyUser.agency!.name,
          );
          // send email to ourselves 
          Map<String,dynamic> emailDataForUs = {
            "agencyname": widget.agencyUser.agency?.name,
            "immaginaprojectid": selectedProject!.projectId,
          };
          final renderistUsers = await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_USERS)
            .where('type', isEqualTo: 'newarc')
            .where('role', isEqualTo: 'master-renderist')
            .where('isActive', isEqualTo: true)
            .get();
          final masterUsers = await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_USERS)
            .where('type', isEqualTo: 'newarc')
            .where('role', isEqualTo: 'master')
            .where('isActive', isEqualTo: true)
            .get();
          for (var userDoc in renderistUsers.docs + masterUsers.docs) {
            final userData = userDoc.data();
            if (userData['email'] != null) {
              sendEmail(
                templateId: CommonUtils.agencyNewOversizedRequestForWorkSideEmailTemplateId, 
                subject: CommonUtils.agencyNewOversizedRequestForWorkSideEmailSubject, 
                variables: emailDataForUs, 
                recipientEmail: userData['email'], 
                recipientName: userData['firstName'] != null ? "${userData['firstName']} ${userData['lastName'] ?? ''}" : "Renderist",
              );
            }
          }
        });
        // update project status on client
        setState(() {
          selectedProject!.requestStatus = 'preventivazione';
          selectedProject!.isPaidWithCredits = false;
          selectedProject!.appliedSuccessFee = '0.2';
          selectedProject!.statusChangedDate = time_now;
        });
      } catch (e) {
        print("Error in pagamentoDialog while updating document and sending emails: $e");
      }
    }
    final int subscriptionServiceCountLeft = widget.agencyUser.agency?.subscriptionServiceCountLeft ?? 0;
    DateTime expiryDate = DateTime.fromMillisecondsSinceEpoch(widget.agencyUser.agency?.subscriptionEndDate ?? 0);
    DateTime now = DateTime.now();
    final bool isSubscriptionOver = (subscriptionServiceCountLeft <= 0 || now.isAfter(expiryDate));
    // Scenario #1: Agency has credits and uploaded required files
    final bool canCompleteUsingCredits = !isSubscriptionOver && (selectedProject?.wantsNewarcPlanimetry == false && selectedProject?.wantsNewarcPictures == false);
    // Scenario #2: Agency has credits but hasn't uploaded required files
    final bool needsToPay = !isSubscriptionOver && (selectedProject?.wantsNewarcPlanimetry == true || selectedProject?.wantsNewarcPictures == true);
    // Scenario #3: Agency has no credits or subscription expired
    print("isSubscriptionOver   ===> $isSubscriptionOver");
    print("canCompleteUsingCredits   ===> $canCompleteUsingCredits");
    print("needsToPay   ===> $needsToPay");
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        NarFormLabelWidget(
          label: 'Pagamento',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        Padding(
            padding: EdgeInsets.only(
              top: MediaQuery.of(context).size.height / 15,
            ),
            child: Container(
              height: MediaQuery.of(context).size.height / 2,
              width: MediaQuery.of(context).size.width / 2,
              child: gSFExceeded
                  ? Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    width: MediaQuery.of(context).size.width / 3,
                    child: Text(
                      "Date le caratteristiche speciali del tuo immobile procederemo a contattarti con un preventivo personalizzato. \nRiceverai al più presto un'email al tuo indirizzo ${widget.agencyUser.email}.",
                      textAlign: TextAlign.center,
                      style: TextStyle(fontFamily: 'Raleway-600', fontSize: 15, color: const Color.fromARGB(255, 100, 100, 100)),
                    ),
                  ),
                ],
              )
                  : Row(
                children: [
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        border: Border.all(
                          width: 1,
                          color: Color(0xffDBDBDB),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: !isSubscriptionOver ? _payBasedOnSelection() : _fullPay(),
                      ),
                    ),
                  ),
                  VerticalDivider(
                    thickness: .25,
                    color: Colors.grey[500],
                    indent: 30,
                    endIndent: 30,
                  ),
                  Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: loading
                            ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                CircularProgressIndicator(color: Theme.of(context).primaryColor),
                                SizedBox(
                                  height: 20,
                                ),
                                Text(
                                  "In attesa del pagamento, reindirizzamento al link per il pagamento.",
                                  textAlign: TextAlign.center,
                                  style: TextStyle(fontFamily: 'Raleway-600', fontSize: 15, color: Colors.grey[500]),
                                ),
                              ],
                            ))
                            : Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            if (paymentError) ...[
                              Text(
                                "Qualcosa è andato storto con il pagamento, riprova.",
                                textAlign: TextAlign.center,
                                style: TextStyle(fontFamily: 'Raleway-500', fontSize: 14, color: Colors.black),
                              ),
                              SizedBox(
                                height: 10,
                              )
                            ],
                            Column(
                              children: [
                                if (isSubscriptionOver || needsToPay)
                                  BaseNewarcButton(
                                      buttonText: selectedProject!.receivedPayment ? 'Pagamento già effettuato!' : 'Procedi con il pagamento',
                                      disableButton: selectedProject!.receivedPayment ? true : false,
                                      color: selectedProject!.receivedPayment ? Color(0xff489B79) : Theme.of(context).primaryColor,
                                      onPressed: selectedProject!.receivedPayment
                                      ? () {}
                                      : () async {
                                          setState(() {
                                          loading = true;
                                          _isFrozen = true;
                                        });

                                        // open tab before async calls so to avoid iOS popup blocking
                                        final newTab = web.window.open('', '_blank');
                                        try {
                                          
                                          // Check current payment status
                                          // avoids double payment
                                          final docRef = FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).doc(selectedProject!.id);
                                          var element = await docRef.get();
                                          ImmaginaProject _tmp = ImmaginaProject.fromDocument(element.data()!, element.id);
                                          if (_tmp.receivedPayment == true) {
                                            Navigator.of(context).pop();
                                            confirmationPopup(context);
                                            setState(() {
                                              loading = false;
                                              selectedProject = _tmp;
                                              paymentError = false;
                                            });
                                            return;
                                          }
                                          bool wasPaymentDetected = false;

                                          // Get payment link, redirect and set up freeze screen listener
                                          Map<String, dynamic> stripePriceIds = ImmaginaProjectEconomics(project: selectedProject!).getStripePriceIds(isSubscriptionOver);
                                          Map linkMap = await getStripeCheckoutLink(stripePriceIds, selectedProject!.id, widget.agencyUser.agencyId!);

                                          if (linkMap['link'] == null) {
                                            setState(() {
                                              loading = false;
                                              paymentError = true;
                                              _isFrozen = false;
                                            });
                                            newTab!.close();
                                            return;
                                          }

                                          // Assign the Stripe payment link to the tab
                                          newTab!.location.href = linkMap['link']!;

                                          // Check if the user closed the tab without completing payment
                                          Future.doWhile(() async {
                                            await Future.delayed(Duration(seconds: 1));
                                            return !wasPaymentDetected && newTab.closed == false;
                                          }).then((_) {
                                            if (!wasPaymentDetected) {
                                              setState(() {
                                                _isFrozen = false;
                                                loading = false;
                                                paymentError = true;
                                              });
                                            }
                                          });
                                          // Received payment listener
                                          StreamSubscription? subscription;
                                          subscription = docRef.snapshots().listen(
                                                (event) async {
                                              print('listening event');
                                              var temp = event.data();
                                              if (temp != null) {
                                                if (temp['receivedPayment'] != null) {
                                                  if (temp['receivedPayment'] == true) {
                                                    wasPaymentDetected = true;
                                                    setState(() {
                                                      loading = false;
                                                      _isFrozen = false;
                                                    });

                                                    Navigator.of(context).pop();
                                                    confirmationPopup(context);
                                                    await updateAgencyAndProject(
                                                      agencyId: widget.agencyUser.agencyId,
                                                      projectId: selectedProject?.id,
                                                      immaginaProjectId: selectedProject?.projectId,
                                                      isPaidWithCredits: !isSubscriptionOver,
                                                    );
                                                    subscription?.cancel();
                                                  }
                                                }
                                              }
                                            },
                                            onError: (error) {
                                              print("Listen failed: $error");
                                              setState(() {
                                                _isFrozen = false;
                                                loading = false;
                                                paymentError = true;
                                              });
                                              subscription?.cancel();
                                            },
                                            onDone: () async {
                                              print("Listen done");
                                              await docRef.get().then((DocumentSnapshot doc) {
                                                var temp = doc.data();
                                                print("temp: $temp");
                                              });
                                            },
                                          );
                                        } catch (e) {
                                          print('Error in payment button $e');
                                          setState(() {
                                            _isFrozen = false;
                                            loading = false;
                                            paymentError = true;
                                          });
                                          newTab!.close();
                                        }
                                      }),
                                if (canCompleteUsingCredits)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 20.0),
                                    child: BaseNewarcButton(
                                      buttonText: 'Clicca qui per inviare la richiesta',
                                      height: 50,
                                      disableButton: false,
                                      color: Theme.of(context).primaryColor,
                                      onPressed: () async {
                                        await updateAgencyAndProject(
                                          agencyId: widget.agencyUser.agencyId,
                                          projectId: selectedProject?.id,
                                          isPaidWithCredits: true,
                                          immaginaProjectId: selectedProject?.projectId,
                                        );
                                        Navigator.pop(context);
                                        confirmationPopup(context);
                                      },
                                    ),
                                  ),
                              ],
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            Text(
                              isSubscriptionOver
                                  ? "Verrà aperta una nuova finestra per il pagamento tramite Stripe."
                                  : canCompleteUsingCredits
                                  ? "Verrà scalato un credito da quelli disponibili."
                                  : needsToPay
                                  ? "Verrà scalato un credito da quelli disponibili e verrà aperta una nuova finestra per il pagamento dei servizi aggiuntivi tramite Stripe."
                                  : "",
                              textAlign: TextAlign.center,
                              style: TextStyle(fontFamily: 'Raleway-500', fontSize: 14, color: Color(0xff4A4A4A)),
                            ),
                          ],
                        ),
                      )),
                ],
              ),
            ))
      ],
    );
  }

  Column _payBasedOnSelection() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Completa il servizio',
          style: TextStyle(fontFamily: 'Raleway-800', fontSize: 18, color: Theme.of(context).disabledColor),
        ),
        SizedBox(height: 20),
        Text(
          'Locali: ${selectedProject!.rooms}',
          style: TextStyle(fontFamily: 'Raleway-500', fontSize: 12, color: Colors.grey[500]),
        ),
        Padding(
          padding: EdgeInsets.only(top: 10),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Progetto immagina:',
                style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
              ),
              Text(
                '1 credito',
                style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
              ),
            ],
          ),
        ),
        if (selectedProject?.wantsNewarcPlanimetry == true)
          Padding(
            padding: EdgeInsets.only(top: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Rilievi e planimetria:',
                  style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
                ),
                Text(
                  '${(((ImmaginaProjectEconomics(project: selectedProject!).computeNewarcRilieviFee()!) * 100).round() / 100).toStringAsFixed(2).replaceAll('.', ',')}€',
                  style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
                ),
              ],
            ),
          ),
        if (selectedProject?.wantsNewarcPictures == true)
          Padding(
            padding: EdgeInsets.only(top: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Fotografie e virtual tour:',
                  style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
                ),
                Text(
                  '${(((ImmaginaProjectEconomics(project: selectedProject!).computeNewarcMediaFee()!) * 100).round() / 100).toStringAsFixed(2).replaceAll('.', ',')}€',
                  style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
                ),
              ],
            ),
          ),
        if (selectedProject?.wantsNewarcPictures == true || selectedProject?.wantsNewarcPlanimetry == true)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 10),
                  Divider(
                    thickness: .25,
                    color: Color(0xffa6a6a6),
                    // indent: 30,
                    endIndent: 30,
                  ),
                  SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Totale progetto:',
                        style: TextStyle(fontFamily: 'Raleway-700', fontSize: 14, color: Theme.of(context).disabledColor),
                      ),
                      Text(
                        '${(((ImmaginaProjectEconomics(project: selectedProject!).computeNewarcMediaRilieviFeeTotalCost()! - ImmaginaProjectEconomics(project: selectedProject!).computeNewarcMediaRilieviFeeTotalVAT()!) * 100).round() / 100).toStringAsFixed(2).replaceAll('.', ',')}€',
                        style: TextStyle(fontFamily: 'Raleway-700', fontSize: 14, color: Theme.of(context).disabledColor),
                      ),
                    ],
                  ),
                  SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'IVA:',
                        style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
                      ),
                      Text(
                        '${(((ImmaginaProjectEconomics(project: selectedProject!).computeNewarcMediaRilieviFeeTotalVAT()!) * 100).round() / 100).toStringAsFixed(2).replaceAll('.', ',')}€',
                        style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
                      ),
                    ],
                  ),
                ],
              ),
              SizedBox(height: 10),
              Divider(
                thickness: .25,
                color: Color(0xffa6a6a6),
                // indent: 30,
                endIndent: 30,
              ),
              SizedBox(height: 10),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Totale:',
                    style: TextStyle(fontFamily: 'Raleway-700', fontSize: 14, color: Theme.of(context).disabledColor),
                  ),
                  Text(
                    '${(((ImmaginaProjectEconomics(project: selectedProject!).computeNewarcMediaRilieviFeeTotalCost()!) * 100).round() / 100).toStringAsFixed(2).replaceAll('.', ',')}€',
                    style: TextStyle(fontFamily: 'Raleway-700', fontSize: 14, color: Theme.of(context).disabledColor),
                  ),
                ],
              ),
            ],
          )
      ],
    );
  }

  Column _fullPay() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Completa il servizio',
          style: TextStyle(fontFamily: 'Raleway-800', fontSize: 18, color: Theme.of(context).disabledColor),
        ),
        SizedBox(height: 20),
        Text(
          'Locali: ${selectedProject!.rooms}',
          style: TextStyle(fontFamily: 'Raleway-500', fontSize: 12, color: Colors.grey[500]),
        ),
        Padding(
          padding: EdgeInsets.only(top: 10),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Progetto immagina:',
                style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
              ),
              Text(
                '${(((ImmaginaProjectEconomics(project: selectedProject!).computeRenderRequestFee()!) * 100).round() / 100).toStringAsFixed(2).replaceAll('.', ',')}€',
                style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
              ),
            ],
          ),
        ),
        Padding(
          padding: EdgeInsets.only(top: 10),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Rilievi e planimetria:',
                style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
              ),
              Text(
                '${(((ImmaginaProjectEconomics(project: selectedProject!).computeNewarcRilieviFee()!) * 100).round() / 100).toStringAsFixed(2).replaceAll('.', ',')}€',
                style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
              ),
            ],
          ),
        ),
        Padding(
          padding: EdgeInsets.only(top: 10),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Fotografie e virtual tour:',
                style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
              ),
              Text(
                '${(((ImmaginaProjectEconomics(project: selectedProject!).computeNewarcMediaFee()!) * 100).round() / 100).toStringAsFixed(2).replaceAll('.', ',')}€',
                style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
              ),
            ],
          ),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 10),
            Divider(
              thickness: .25,
              color: Color(0xffa6a6a6),
              // indent: 30,
              endIndent: 30,
            ),
            SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Totale progetto:',
                  style: TextStyle(fontFamily: 'Raleway-700', fontSize: 14, color: Theme.of(context).disabledColor),
                ),
                Text(
                  '${(((ImmaginaProjectEconomics(project: selectedProject!).computeTotalCost()! - ImmaginaProjectEconomics(project: selectedProject!).computeTotalVAT()!) * 100).round() / 100).toStringAsFixed(2).replaceAll('.', ',')}€',
                  style: TextStyle(fontFamily: 'Raleway-700', fontSize: 14, color: Theme.of(context).disabledColor),
                ),
              ],
            ),
            SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'IVA:',
                  style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
                ),
                Text(
                  '${(((ImmaginaProjectEconomics(project: selectedProject!).computeTotalVAT()!) * 100).round() / 100).toStringAsFixed(2).replaceAll('.', ',')}€',
                  style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
                ),
              ],
            ),
          ],
        ),
        SizedBox(height: 10),
        Divider(
          thickness: .25,
          color: Color(0xffa6a6a6),
          // indent: 30,
          endIndent: 30,
        ),
        SizedBox(height: 10),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Totale:',
              style: TextStyle(fontFamily: 'Raleway-700', fontSize: 14, color: Theme.of(context).disabledColor),
            ),
            Text(
              '${(((ImmaginaProjectEconomics(project: selectedProject!).computeTotalCost()!) * 100).round() / 100).toStringAsFixed(2).replaceAll('.', ',')}€',
              style: TextStyle(fontFamily: 'Raleway-700', fontSize: 14, color: Theme.of(context).disabledColor),
            ),
          ],
        ),
      ],
    );
  }

  Future<void> updateAgencyAndProject({required String? agencyId, required String? projectId, required bool isPaidWithCredits,required String? immaginaProjectId}) async {
    final FirebaseFirestore firestore = FirebaseFirestore.instance;

    try {
      await firestore.runTransaction((transaction) async {
        // Step 1: Get agency document
        final agencyDocRef = firestore.collection(appConfig.COLLECT_AGENCIES).doc(agencyId);
        final agencySnapshot = await transaction.get(agencyDocRef);

        if (!agencySnapshot.exists) {
          throw Exception("Agency does not exist");
        }

        final data = agencySnapshot.data();
        int subscriptionServiceCountLeft = data?['subscriptionServiceCountLeft'] ?? 0;

        // Computing
        String? agencySubscriptionId = data?['subscriptionId'];
        String agencySuccessFee = appConst.BASE_NEWARC_IMMAGINA_SUCCESS_FEE;
        if (isPaidWithCredits) {
          final subscriptionDocRef = firestore.collection(appConfig.COLLECT_IMMAGINA_SUBSCRIPTION).doc(agencySubscriptionId);
          final subscriptionSnapshot = await transaction.get(subscriptionDocRef);
          if (subscriptionSnapshot.exists) {
            final subscriptionData = subscriptionSnapshot.data();
            agencySuccessFee = subscriptionData!['successFee'];
          } else {
            throw Exception("Subscription does not exist");
          }
        }
        print("Success Fee: ${agencySuccessFee}");

        if (subscriptionServiceCountLeft > 0) {
          transaction.update(agencyDocRef, {
            'subscriptionServiceCountLeft': subscriptionServiceCountLeft - 1,
          });
        }
        Map<String,dynamic> emailVariable = {
          "agencyname": widget.agencyUser.agency?.name,
          "immaginaprojectid": immaginaProjectId,
        };
        // new request send notification mail
        sendEmail(
          templateId: CommonUtils.newRequestEmailTemplateId,  
          subject: CommonUtils.newRequestEmailSubject,
          recipientEmail: widget.agencyUser.agency!.email,
          recipientName: widget.agencyUser.agency!.name,
        );
        final renderistUsers = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_USERS)
          .where('type', isEqualTo: 'newarc')
          .where('role', isEqualTo: 'master-renderist')
          .where('isActive', isEqualTo: true)
          .get();
        final masterUsers = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_USERS)
          .where('type', isEqualTo: 'newarc')
          .where('role', isEqualTo: 'master')
          .where('isActive', isEqualTo: true)
          .get();
        for (var userDoc in renderistUsers.docs + masterUsers.docs) {
          final userData = userDoc.data();
          if (userData['email'] != null) {
            sendEmail(
              templateId: CommonUtils.agencyNewRequestForWorkSideEmailTemplateId, 
              subject: CommonUtils.agencyNewRequestForWorkSideEmailSubject, 
              variables: emailVariable, 
              recipientEmail: userData['email'], 
              recipientName: userData['firstName'] != null ? "${userData['firstName']} ${userData['lastName'] ?? ''}" : "Renderist",
            );
          }
        }
        final projectDocRef = firestore.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).doc(projectId);
        transaction.update(projectDocRef, {
          'requestStatus': 'in analisi',
          'statusChangedDate': DateTime.now().millisecondsSinceEpoch,
          'receivedPaymentDate': DateTime.now().millisecondsSinceEpoch,
          "receivedPayment": true,
          'isPaidWithCredits': isPaidWithCredits,
          'appliedSuccessFee': agencySuccessFee,
        });
      });

      print('Transaction completed successfully');
    } catch (e) {
      print('Transaction failed: $e');
    }
  }

  dynamic confirmationPopup(context) {
    return showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return Center(
              child: Container(
                width: 800,
                child: Material(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(15),
                  child: Container(
                    width: MediaQuery.of(context).size.width / 1.8,
                    height: MediaQuery.of(context).size.height / 2,
                    padding: EdgeInsets.all(30),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SvgPicture.asset(
                          'assets/icons/check.svg',
                          color: Theme.of(context).primaryColor,
                        ),
                        SizedBox(
                          height: 30,
                        ),
                        Text(
                          "Richiesta di progetto inviata!",
                          textAlign: TextAlign.center,
                          style: TextStyle(fontFamily: 'Raleway-700', fontSize: 30, color: Colors.black),
                        ),
                        SizedBox(
                          height: 30,
                        ),
                        Container(
                          width: MediaQuery.of(context).size.width / 2.5,
                          child: Column(
                            children: [
                              Text(
                                "Il tuo progetto sarà completato entro i prossimi 5 giorni lavorativi, a partire dal prossimo giorno lavorativo. ",
                                textAlign: TextAlign.center,
                                style: TextStyle(fontFamily: 'Raleway-500', fontSize: 14, color: Color(0xff4A4A4A)),
                              ),
                              SizedBox(
                                height: 30,
                              ),
                              Text(
                                "In caso di eventuali problemi legati alla progettazione ti contatteremo per la risoluzione. In questo caso potrebbero essere necessari più giorni lavorativi al completamento del progetto.",
                                textAlign: TextAlign.center,
                                style: TextStyle(fontFamily: 'Raleway-500', fontSize: 14, color: Colors.grey[500]),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 50,
                        ),
                        SizedBox(
                          width: 200,
                          child: BaseNewarcButton(
                            buttonText: 'OK',
                            onPressed: () async {
                              Navigator.of(context).pop();
                              widget.onClose();
                            },
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ));
        });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: 20,
        vertical: 20,
      ),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        child: Stack(
          children: [
            Positioned(
              top: 18,
              right: 20,
              child: Container(
                height: 20,
                width: 20,
                child: MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                      child: SvgPicture.asset(
                        'assets/icons/close-popup.svg',
                        width: 18,
                        color: Color(0xffb3b3b3),
                      ),
                      onTap: () {
                        if (selectedView == 'initial') {
                          Navigator.of(context).pop();
                        } else if (selectedView == 'pagamento' && selectedProject!.grossSquareFootage!>=gSFUpperLimit) {
                          Navigator.of(context).pop();
                          widget.onClose();
                        } else {
                          showDialog(
                              context: context,
                              barrierDismissible: false,
                              builder: (BuildContext context) {
                                return Center(
                                    child: Container(
                                      width: 400,
                                      child: BaseNewarcPopup(
                                        title: 'Vuoi davvero uscire \ndalla procedura?',
                                        noButton: true,
                                        isShowCloseIcon: true,
                                        closeIconColor: Color(0xFFB3B3B3),
                                        titleTextAlign: TextAlign.center,
                                        column: StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
                                          return Column(
                                            mainAxisAlignment: MainAxisAlignment.end,
                                            children: [
                                              ...!_isFrozen ? [BaseNewarcButton(
                                                buttonText: 'Rimani',
                                                onPressed: () {
                                                  Navigator.of(context).pop();
                                                },
                                                textColor: Colors.white,
                                                color: Colors.black,
                                                disableButton: _isFrozen,
                                              ),
                                              SizedBox(
                                                height: 15,
                                              ),
                                              BaseNewarcButton(
                                                buttonText: 'Salva e chiudi',
                                                onPressed: () async {
                                                  setState(() {
                                                    _isFrozen = true;
                                                    loading = true;
                                                  });
                                                  // save to selectedProject features not saved onChange
                                                  saveCharacteristics();
                                                  await savePlanimetryImages();
                                                  await savePicturesImages();
                                                  // save selectedProject to firestore
                                                  await FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).doc(selectedProject!.id).set(selectedProject!.toMap());
                                                  setState(() {
                                                    _isFrozen = false;
                                                    loading = false;
                                                  });
                                                  Navigator.of(context).pop();
                                                  Navigator.of(context).pop();
                                                  widget.onClose();
                                                },
                                                textColor: Colors.black,
                                                color: Colors.white,
                                                disableButton: _isFrozen,
                                              )]
                                              : [Center(child: CircularProgressIndicator())]
                                            ],
                                          );
                                        }),
                                      ),
                                    ));
                              });
                        }
                      }),
                ),
              ),
            ),
            Form(
              key: _formKey,
              child: Padding(
                padding: const EdgeInsets.only(bottom: 20.0, top: 20),
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Padding(
                    //   padding: EdgeInsets.symmetric(
                    //     horizontal: 20,
                    //   ),
                    //   child: NarFormLabelWidget(
                    //     label: 'Nuova richiesta progetto',
                    //     textColor: Color(0xFF565656),
                    //     fontSize: 18,
                    //     fontWeight: '600',
                    //   ),
                    // ),
                    Padding(
                      padding: const EdgeInsets.only(
                        left: 40.0,
                        right: 40.0,
                        // top: 25,
                      ),
                      child: Container(
                        width: MediaQuery.of(context).size.width / 1.5,
                        height: MediaQuery.of(context).size.height / 1.5,
                        child: selectView(),
                      ),
                    ),
                    SizedBox(height: 10),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        formErrorMessage.length == 0 || formErrorMessage[0] == ''
                            ? Container()
                            : NarFormLabelWidget(
                          label: formErrorMessage.length > 0 ? formErrorMessage.join('') : '',
                          fontSize: 12,
                          fontWeight: 'bold',
                        ),
                      ],
                    ),
                    SizedBox(height: 5),
                    Container(
                      width: MediaQuery.of(context).size.width / 1.5, 
                      height: MediaQuery.of(context).size.height / 10, 
                      child: Center(
                        child: selectFooter()
                      )
                    ),
                  ],
                ),
              ),
            ),
            if (_isFrozen)
              Positioned.fill(
                child: Container(
                  color: Colors.black54,
                  child: Center(
                    child: !loading ? CircularProgressIndicator() : null,
                  ), // Semi-transparent overlay
                ),
              ),
          ],
        ),
      ),
    );
  }
}

Future<void> deleteArchivedProject(String projectId) async {
    try {
      await FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).doc(projectId).update({'isArchived': true});
      print("Project ${projectId} successfully marked as archived.");
    } catch (e) {
      print("Error archiving project $projectId: $e");
    }
  }
