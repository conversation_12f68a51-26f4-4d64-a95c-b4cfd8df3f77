
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/classes/supplier.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/image-select-box.dart';

class ProjectAssignVendor extends StatefulWidget {
  final NewarcProject? project;
  final List<Supplier>? suppliers;

  const ProjectAssignVendor({Key? key, this.project, this.suppliers})
      : super(key: key);

  @override
  State<ProjectAssignVendor> createState() => _ProjectAssignVendorState();
}

class _ProjectAssignVendorState extends State<ProjectAssignVendor> {
  String progressMessage = '';
  bool loading = false;
  TextEditingController? vendorId = new TextEditingController();
  TextEditingController? txtconStartDate = new TextEditingController();
  TextEditingController? txtconEndDate = new TextEditingController();
  TextEditingController? agreedPenalty = new TextEditingController();
  TextEditingController? effectivePenalty = new TextEditingController();

  Map userRoleIndex = {
    'scouter': 0,
    'renovator': 1,
    'renderist': 2,
    'geometra': 3,
    'media_creator': 4
  };

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    setInitialValues();
    // fetchSuppliers();
  }

  @protected
  void didUpdateWidget(ProjectAssignVendor oldWidget) {
    setInitialValues();
    super.didUpdateWidget(oldWidget);
  }

  setInitialValues() {
    NewarcProject _proj = widget.project!;
    if (_proj.assignedVendor!.length > 0) {
      if (_proj.assignedVendor!['supplierId'] != null) {
        vendorId!.text = _proj.assignedVendor!['supplierId'];
      } else {
        vendorId!.text = '';
      }

      if (_proj.assignedVendor!['startDate'] != null) {
        txtconStartDate!.text = _proj.assignedVendor!['startDate'];
      }

      if (_proj.assignedVendor!['endDate'] != null) {
        txtconEndDate!.text = _proj.assignedVendor!['endDate'];
      }

      if (_proj.assignedVendor!['agreedPenalty'] != null) {
        agreedPenalty!.text = _proj.assignedVendor!['agreedPenalty'];
      }

      if (_proj.assignedVendor!['effectivePenalty'] != null) {
        effectivePenalty!.text = _proj.assignedVendor!['effectivePenalty'];
      }
    }
  }

  formatDateForParsing(String dateString) {
    List splittedDate = dateString.split('/');
    // print(splittedDate);
    return splittedDate[2] + '-' + splittedDate[1] + '-' + splittedDate[0];
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: double.infinity,
      child: loading == true
          ? NarFormLabelWidget(label: 'Loading')
          : Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Expanded(
                  child: ListView(
                    // crossAxisAlignment: CrossAxisAlignment.start,
                    // mainAxisAlignment: MainAxisAlignment.start,
                    // mainAxisSize: MainAxisSize.max,
                    children: [
                      SizedBox(height: 20),
                      NarFormLabelWidget(
                        label: 'Assegna Ditta',
                        fontSize: 20,
                        fontWeight: 'bold',
                      ),
                      SizedBox(height: 30),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Expanded(
                            flex: 1,
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                NarFormLabelWidget(
                                  label: "Seleziona ditta",
                                  textColor: Color(0xff696969),
                                  fontSize: 14,
                                  fontWeight: '600',
                                ),
                                SizedBox(height: 4),
                                NarImageSelectBoxWidget(
                                  options: widget.suppliers!
                                      .map((e) =>
                                          {'value': e.id, 'label': e.name})
                                      .toList(),
                                  controller: vendorId,
                                  validationType: 'required', 
                                  parametersValidate: 'Required!',
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            width: 15,
                          ),
                          Expanded(
                            child: SizedBox(
                              height: 0,
                            ),
                            flex: 1,
                          )
                        ],
                      ),
                      SizedBox(
                        height: 20,
                      ),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CustomTextFormField(
                            label: "Data inizio lavori concordata",
                            controller: txtconStartDate,
                            suffixIcon: Image.asset(
                              'assets/icons/calendar.png',
                              height: 17,
                              width: 17,
                            ),
                            // validationMessage: 'Required!',
                            validator: (value) {
                              if (value == '') {
                                return 'Required!';
                              }

                              return null;
                            },
                            onTap: () async {
                              DateTime? pickedDate = await showDatePicker(
                                  context: context,
                                  initialDate: txtconStartDate!.text == ''
                                      ? DateTime.now()
                                      : DateTime.tryParse(formatDateForParsing(
                                          txtconStartDate!.text))!,
                                  firstDate: DateTime(1950),
                                  lastDate: DateTime(2300));

                              if (pickedDate != null) {
                                String formattedDate =
                                    DateFormat('dd/MM/yyyy').format(pickedDate);
                                setState(() {
                                  txtconStartDate!.text =
                                      formattedDate; //set output date to TextField value.
                                });
                              } else {}
                            },
                          ),
                          SizedBox(
                            width: 15,
                          ),
                          CustomTextFormField(
                            label: "Data fine lavori concordata",
                            controller: txtconEndDate,
                            suffixIcon: Image.asset(
                              'assets/icons/calendar.png',
                              height: 17,
                              width: 17,
                            ),
                            // validationMessage: 'Required!',
                            validator: (value) {
                              if (value == '') {
                                return 'Required!';
                              }

                              return null;
                            },
                            onTap: () async {
                              DateTime? pickedDate = await showDatePicker(
                                  context: context,
                                  initialDate: txtconEndDate!.text == ''
                                      ? DateTime.now()
                                      : DateTime.tryParse(formatDateForParsing(
                                          txtconEndDate!.text))!,
                                  firstDate: DateTime(1950),
                                  lastDate: DateTime(2300));

                              if (pickedDate != null) {
                                String formattedDate =
                                    DateFormat('dd/MM/yyyy').format(pickedDate);
                                setState(() {
                                  txtconEndDate!.text =
                                      formattedDate; //set output date to TextField value.
                                });
                              } else {}
                            },
                          ),
                          Expanded(flex: 1, child: SizedBox(height: 0))
                        ],
                      ),
                      SizedBox(height: 20),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CustomTextFormField(
                            label: "Penale concordata",
                            controller: agreedPenalty,
                            suffixIcon: Column(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    NarFormLabelWidget(
                                      label: '€/giorno ',
                                      fontSize: 14,
                                      textColor:
                                          Color.fromRGBO(123, 123, 123, 1),
                                    ),
                                    SizedBox(
                                      width: 5,
                                    )
                                  ],
                                ),
                              ],
                            ),
                            // validationMessage: 'Required!',
                            validator: (value) {
                              if (value == '') {
                                return 'Required!';
                              }

                              return null;
                            },
                            onTap: () async {},
                          ),
                          SizedBox(
                            width: 15,
                          ),
                          CustomTextFormField(
                            label: "Penale effettiva",
                            controller: effectivePenalty,
                            suffixIcon: Column(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                NarFormLabelWidget(
                                  label: '€',
                                  fontSize: 14,
                                  textColor: Color.fromRGBO(123, 123, 123, 1),
                                ),
                              ],
                            ),
                            // validationMessage: 'Required!',
                            validator: (value) {
                              if (value == '') {
                                return 'Required!';
                              }

                              return null;
                            },
                            onTap: () async {},
                          ),
                          Expanded(flex: 1, child: SizedBox(height: 0))
                        ],
                      ),
                      SizedBox(height: 6),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    NarFormLabelWidget(label: progressMessage),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          BaseNewarcButton(
                            buttonText: "Salva",
                            onPressed: () async {
                              setState(() {
                                progressMessage = 'Salvataggio in corso...';
                              });

                              Map<String, dynamic> vendorData = {
                                'supplierName': widget.suppliers!
                                    .where((supplier) =>
                                        supplier.id == vendorId!.text)
                                    .first
                                    .name,
                                'supplierId': vendorId!.text,
                                'startDate': txtconStartDate!.text,
                                'endDate': txtconEndDate!.text,
                                'agreedPenalty': agreedPenalty!.text,
                                'effectivePenalty': effectivePenalty!.text,
                              };

                              // print(vendorData);
                              // return;``

                              widget.project!.assignedVendor = vendorData;
                              final FirebaseFirestore _db =
                                  FirebaseFirestore.instance;

                              try {
                                await _db
                                    .collection(
                                        appConfig.COLLECT_NEWARC_PROJECTS)
                                    .doc(widget.project!.id)
                                    .update(widget.project!.toMap());

                                setState(() {
                                  progressMessage = 'Saved!';
                                });
                              } catch (e) {
                                setState(() {
                                  progressMessage = 'Errore: ' + e.toString();
                                });
                              }
                            },
                          )
                        ],
                      ),
                    ),
                  ],
                )
              ],
            ),
    );
  }
}
