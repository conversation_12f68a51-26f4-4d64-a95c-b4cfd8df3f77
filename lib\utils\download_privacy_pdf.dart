
import 'dart:developer';
import 'package:flutter/services.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import '../classes/renovationQuotation.dart';






Future<Uint8List?> downloadPrivacyPDF({required RenovationQuotation quotation,required isPDFDownload})async{
  try{

    final ByteData fontMediumData = await rootBundle.load('assets/fonts/Raleway-Medium.ttf');
    final ByteData fontLightData = await rootBundle.load('assets/fonts/Raleway-Regular.ttf');
    final ByteData fontBoldData = await rootBundle.load('assets/fonts/Raleway-Bold.ttf');

    final ralewayMedium = pw.Font.ttf(fontMediumData.buffer.asByteData());
    final ralewayBold = pw.Font.ttf(fontBoldData.buffer.asByteData());
    final ralewayLight = pw.Font.ttf(fontLightData.buffer.asByteData());

    final Uint8List pdfLogoData = await loadImage('assets/rq-pdf-cover-logo.png');
    final pdfLogo = pw.MemoryImage(pdfLogoData);

    final pdf = pw.Document();

    List<pw.Widget> privacyPage = [];

    pw.Widget pageFooter(String pagenumber){
      return pw.Container(
        child: pw.Row(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          mainAxisAlignment: pw.MainAxisAlignment.center,
          children: [
            pw.Text(
              "Newarc Srl - Sede Operativa: Corso Ferrucci, 36, 10138, Torino, IT - Sede Legale: Via V.Emanuele II, 29, 10023, Chieri, TO, IT \nP.Iva 12533550013 - <EMAIL> - www.newarc.it - Tel 011 026 38 50",
              textAlign: pw.TextAlign.center,
              style: pw.TextStyle(
                font: ralewayLight,
                fontSize: 7,
                color: PdfColors.black,
              )
            ),
          ]
        )
      );
    }

    pw.Widget _pointText(String text,{pw.TextAlign? textAlign}){
      return pw.Text(
          text,
          textAlign: textAlign,
          style: pw.TextStyle(
              font: ralewayLight,
              fontSize: 8,
              color: PdfColors.black,
              lineSpacing: 3,
              height: 13
          )
      );
    }

    pw.Widget _headerText(String text){
      return pw.Text(
          text,
          style: pw.TextStyle(
              font: ralewayBold,
              fontSize: 8,
              color: PdfColors.black,
              lineSpacing: 3,
              height: 13
          )
      );
    }

    privacyPage.add(
      pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.SizedBox(height: 11),
          pw.Center(
            child: pw.Image(pdfLogo, height: 44,width: 115)
          ),
          pw.SizedBox(height: 36),
          pw.Center(
            child: pw.Text(
                "Modulo di Consenso per il Trattamento dei Dati Personali",
                style: pw.TextStyle(
                    font: ralewayBold,
                    fontSize: 19,
                    color: PdfColors.black
                )
            )
          ),
          pw.SizedBox(height: 5),
          pw.Center(
              child: pw.Text(
                  "Ai sensi del Regolamento UE 2016/679 (“GDPR”)",
                  style: pw.TextStyle(
                      font: ralewayBold,
                      fontSize: 16,
                      color: PdfColors.black
                  )
              )
          ),
          pw.SizedBox(height: 20),
          pw.Row(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              //---First
              pw.Expanded(
                child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      _headerText("Finalità del trattamento"),
                      pw.SizedBox(height: 5),
                      _pointText("I dati personali forniti saranno trattati esclusivamente per le seguenti\nfinalità:"),

                      pw.SizedBox(height: 5),
                      _headerText("1. Gestione del contratto e obblighi di legge"),
                      pw.SizedBox(height: 5),
                      _pointText("• Esecuzione del contratto di subappalto e adempimenti fiscali e\namministrativi connessi;"),
                      _pointText("• Verifica dell’idoneità tecnico-professionale ai sensi della normativa\nvigente in materia di sicurezza sul lavoro (D.Lgs. 81/2008)."),

                      pw.SizedBox(height: 5),
                      _headerText("2. Marketing e finalità promozionali (facoltativo)"),
                      pw.SizedBox(height: 5),
                      _pointText("Utilizzo di immagini e/o video del cantiere per scopi promozionali,\ninclusa la pubblicazione su siti web, social media, brochure e altri\nmateriali di marketing di Newarc."),

                      pw.SizedBox(height: 15),
                      _headerText("Base giuridica del trattamento"),
                      pw.SizedBox(height: 5),
                      _pointText("Il trattamento dei dati per la gestione del contratto è necessario ai sensi\ndell’art. 6(1)(b) e 6(1)(c) del GDPR.\nIl trattamento dei dati per finalità di marketing è basato sul consenso\ndell’interessato, ai sensi dell’art. 6(1)(a) del GDPR."),

                      pw.SizedBox(height: 15),
                      _headerText("Destinatari dei dati personali"),
                      pw.SizedBox(height: 5),
                      _pointText("I dati personali potranno essere comunicati a:\n• Enti pubblici per obblighi di legge;\n• Consulenti e fornitori incaricati da Newarc per la gestione amministrativa\ne operativa;\n• Altri soggetti coinvolti nella filiera del contratto, ove necessario per\nl’esecuzione delle attività previste."),
                    ]
                )
              ),
              pw.SizedBox(width: 10),
              //--- SEC
              pw.Expanded(
                  child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        _headerText("Durata del trattamento"),
                        pw.SizedBox(height: 5),
                        _pointText("I dati personali saranno conservati per il tempo strettamente necessario\nall’esecuzione del contratto e successivamente per il periodo previsto\ndalle normative applicabili in materia fiscale e amministrativa.\nPer il trattamento delle immagini/video a fini promozionali, i dati saranno\nconservati fino a revoca del consenso."),
                        pw.SizedBox(height: 15),

                        _headerText("Diritti dell’interessato"),
                        pw.SizedBox(height: 5),
                        _pointText("L’interessato può esercitare i diritti previsti dal GDPR (artt. 15-22), tra cui:\n• Accesso, rettifica o cancellazione dei dati personali;\n• Limitazione del trattamento;\n• Opposizione al trattamento;\n• Portabilità dei dati;\n• Revoca del consenso per il trattamento dei dati per finalità di marketing\nsenza pregiudicare la liceità del trattamento basato sul consenso prima\ndella revoca;\n• Reclamo all’Autorità Garante per la Protezione dei Dati Personali."),
                        pw.SizedBox(height: 5),
                      ]
                  )
              ),
            ]
          ),
          pw.SizedBox(height: 15),
          pw.Container(
            width: double.infinity,
            height: 1,
            color: PdfColors.black
          ),
          pw.SizedBox(height: 15),

          pw.Center(
            child: pw.Text(
                "Consenso al trattamento dei dati personali",
                style: pw.TextStyle(
                    font: ralewayBold,
                    fontSize: 16,
                    color: PdfColors.black
                )
            )
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.center,
            children: [
              pw.Text(
                  "CONSENSI OBBLIGATORI",
                  style: pw.TextStyle(
                      font: ralewayBold,
                      fontSize: 12,
                      color: PdfColors.black
                  )
              ),
              pw.Text(
                  " (in caso di diniego il contratto non può essere eseguito)",
                  style: pw.TextStyle(
                      font: ralewayLight,
                      fontSize: 12,
                      color: PdfColors.black
                  )
              )
            ]
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.center,
            children: [
              pw.Container(
                height: 12,
                width: 12,
                decoration: pw.BoxDecoration(
                  color: PdfColor.fromHex("#EBEBEB"),
                  border: pw.Border.all(
                    color: PdfColor.fromHex("#B6B6B6"),
                    width: 0.5,
                  ),
                ),
                alignment: pw.Alignment.center,
                child: pw.Center(
                  child: pw.Text(
                    "{{CHECKBOX_MANDATORY}}",
                    style: pw.TextStyle(
                      fontSize: 1,
                      color: PdfColors.white,
                      font: ralewayBold,
                    ),
                    textAlign: pw.TextAlign.center,
                  ),
                ),
              ),
              pw.SizedBox(width: 10),
              pw.Text(
                  "ACCONSENTO",
                  style: pw.TextStyle(
                      font: ralewayBold,
                      fontSize: 12,
                      color: PdfColors.black
                  )
              ),

              pw.SizedBox(width: 30),

              pw.Container(
                  height: 12,
                  width: 12,
                  alignment: pw.Alignment.center,
                  decoration: pw.BoxDecoration(
                      color: PdfColor.fromHex("#EBEBEB"),
                      border: pw.Border.all(color: PdfColor.fromHex("#B6B6B6"),width: 0.5)
                  ),
              ),
              pw.SizedBox(width: 10),
              pw.Text(
                  "NON ACCONSENTO",
                  style: pw.TextStyle(
                      font: ralewayBold,
                      fontSize: 12,
                      color: PdfColors.black
                  )
              ),
            ]
          ),
          pw.SizedBox(height: 5),
          pw.Center(
            child: pw.Text(
                "al trattamento dei miei dati personali per le finalità di gestione del contratto.",
                style: pw.TextStyle(
                    font: ralewayLight,
                    fontSize: 11,
                    color: PdfColors.black
                )
            )
          ),
          pw.SizedBox(height: 10),
          pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.center,
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                    "CONSENSI FACOLTATIVI",
                    style: pw.TextStyle(
                        font: ralewayBold,
                        fontSize: 12,
                        color: PdfColors.black
                    )
                ),
                pw.Flexible(
                  child: pw.Text(
                      " (il diniego non pregiudica l’esecuzione del contratto, ma comporta\nl’impossibilità di utilizzare l’applicazione fornita per il monitoraggio dei lavori)",
                      softWrap: true,
                      overflow: pw.TextOverflow.visible,
                      textAlign: pw.TextAlign.start,
                      style: pw.TextStyle(
                          font: ralewayLight,
                          fontSize: 12,
                          color: PdfColors.black
                      )
                  )
                )
              ]
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.center,
            children: [
              pw.Container(
                height: 12,
                width: 12,
                decoration: pw.BoxDecoration(
                  color: PdfColor.fromHex("#EBEBEB"),
                  border: pw.Border.all(
                    color: PdfColor.fromHex("#B6B6B6"),
                    width: 0.5,
                  ),
                ),
                alignment: pw.Alignment.center,
                child: pw.Center(
                  child: pw.Text(
                    "{{CHECKBOX_OPTIONAL_ACCEPT}}",
                    style: pw.TextStyle(
                      fontSize: 1,
                      color: PdfColors.white,
                      font: ralewayBold,
                    ),
                    textAlign: pw.TextAlign.center,
                  ),
                ),
              ),
              pw.SizedBox(width: 10),
              pw.Text(
                "ACCONSENTO",
                style: pw.TextStyle(
                  font: ralewayBold,
                  fontSize: 12,
                  color: PdfColors.black,
                ),
              ),

              pw.SizedBox(width: 30),

              pw.Container(
                height: 12,
                width: 12,
                decoration: pw.BoxDecoration(
                  color: PdfColor.fromHex("#EBEBEB"),
                  border: pw.Border.all(
                    color: PdfColor.fromHex("#B6B6B6"),
                    width: 0.5,
                  ),
                ),
                alignment: pw.Alignment.center,
                child: pw.Center(
                  child: pw.Text(
                    "{{CHECKBOX_OPTIONAL_REJECT}}",
                    style: pw.TextStyle(
                      fontSize: 1,
                      color: PdfColors.white,
                      font: ralewayBold,
                    ),
                    textAlign: pw.TextAlign.center,
                  ),
                ),
              ),
              pw.SizedBox(width: 10),
              pw.Text(
                "NON ACCONSENTO",
                style: pw.TextStyle(
                  font: ralewayBold,
                  fontSize: 12,
                  color: PdfColors.black,
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 10),
          _pointText("al trattamento di materiale fotografico e video esclusivamente relativo al cantiere e/o alle lavorazioni, ai fini dell’utilizzo nelle seguenti modalità:\n    • Per rendere possibile l’esperienza interattiva dell’App Newarc, che consente di seguire l’andamento dei lavori da remoto;\n    • Per attività promozionali e di marketing di Newarc, inclusa la condivisione di immagini e video (senza riferimenti identificabili del cantiere o alle persone\ncoinvolte) su siti web, social media e altri canali di comunicazione, ad esempio per eventuali confronti “prima e dopo” delle lavorazioni.",textAlign: pw.TextAlign.center),
          pw.SizedBox(height: 20),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.center,
            children: [
              pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                        "Luogo e data",
                        style: pw.TextStyle(
                            font: ralewayMedium,
                            fontSize: 12,
                            color: PdfColors.black
                        )
                    ),
                    pw.Container(
                        margin: pw.EdgeInsets.only(top: 10,bottom: 1),
                        height: 30,
                        alignment: pw.Alignment.bottomCenter,
                        child: pw.Text(
                            "{{DATE_HERE_1}}",
                            style: pw.TextStyle(
                                font: ralewayMedium,
                                fontSize: 1,
                                color: PdfColors.white
                            )
                        )
                    ),
                    pw.Text(
                        "......................................",
                        tightBounds: true,
                        style: pw.TextStyle(
                            font: ralewayMedium,
                            fontSize: 12,
                            color: PdfColors.black
                        )
                    ),
                  ]
              ),
              pw.SizedBox(width: 20),
              pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                        "Firma per accettazione",
                        style: pw.TextStyle(
                            font: ralewayMedium,
                            fontSize: 12,
                            color: PdfColors.black
                        )
                    ),
                    pw.Container(
                        height: 30,
                        margin: pw.EdgeInsets.only(top: 10,bottom: 1),
                        alignment: pw.Alignment.bottomCenter,
                        child: pw.Text(
                            "{{SIGN_HERE_1}}",
                            style: pw.TextStyle(
                                font: ralewayMedium,
                                fontSize: 1,
                                color: PdfColors.white
                            )
                        )
                    ),
                    pw.Text(
                        ".......................................................",
                        tightBounds: true,
                        style: pw.TextStyle(
                            font: ralewayMedium,
                            fontSize: 12,
                            color: PdfColors.black
                        )
                    ),
                  ]
              ),
            ]
          )
        ]
      )
    );

    pdf.addPage(
        pw.MultiPage(
          theme: pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
          pageFormat: PdfPageFormat(
            PdfPageFormat.a4.width,
            930
          ),
          orientation: pw.PageOrientation.portrait,
          margin: const pw.EdgeInsets.only(left: 30,right: 30,top: 26,bottom: 20),
          build: (pw.Context context) => privacyPage,
          footer: (pw.Context context) => pageFooter(context.pageNumber.toString()),
        )
    );

    // Save PDF to bytes
    final pdfBytes = await pdf.save();
    if(isPDFDownload){
      await downloadPdf(pdfBytes: pdfBytes,fileName: 'Modulo_Privacy_${quotation.code}_V${quotation.version}_R${quotation.revision}.pdf');
    }else{
      return pdfBytes;
    }


  }catch(e,s){
    log("Error while downloading privacy PDF ----> ${e.toString()}");
    log("Stacktrace while downloading privacy PDF ----> ${s.toString()}");
  }
  return null;
}


