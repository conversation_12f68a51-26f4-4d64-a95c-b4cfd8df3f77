
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/reportAcquirente.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import '../../../widget/UI/image_dropdown.dart';

class BuyerReportRequestSource extends DataTableSource {
  BuyerReportRequestSource({
    required this.projects,
    required this.onOpenAzioniDialog,
  });

  List<ReportAcquirente> projects = [];
  final Function(ReportAcquirente project,String type) onOpenAzioniDialog;

  @override
  DataRow? getRow(int index) {
    if (index < projects.length) {
      final project = projects[index];
      var address = "";
      if (project.addressInfo != null) {
        address = project.addressInfo!.toShortAddress();
      }
      return DataRow(
        cells: [
          ///Indirizzo
          DataCell(NarFormLabelWidget(
              label: address,
              fontSize: 12,
              fontWeight: '700',
              textColor: AppColor.black,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          ///Creazione
          DataCell(
            NarFormLabelWidget(
              label: getFormattedDate(project.insertionTimestamp),
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),
          ),

          DataCell(_buildActionsDropdown(project)),

        ],
      );
    }

    return null;
  }

  Widget _buildActionsDropdown(ReportAcquirente project) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 5.0),
      child: NarImageDropdown(
        controller: TextEditingController(),
        customButton: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 10.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              NarFormLabelWidget(
                label: "Azioni",
                fontSize: 11,
                fontWeight: '600',
                textColor: AppColor.greyColor,
              ),
              Icon(
                Icons.keyboard_arrow_down,
                color: AppColor.iconGreyColor,
                size: 15,
              )
            ],
          ),
        ),
        options: [
          {
            'value': 'edit',
            'label': 'Modifica',
            'image': 'assets/icons/edit.svg',
            'iconColor': AppColor.greyColor
          },
          // {
          //   'value': 'download_pdf',
          //   'label': 'Scarica PDF',
          //   'image': 'assets/icons/download.png',
          //   'iconColor': AppColor.greyColor
          // },
            {
              'value': 'delete',
              'label': 'Elimina',
              'image': 'assets/icons/trash-process.png',
              'iconColor': AppColor.redColor,
              'labelColor': AppColor.redColor
            },
        ],
        iconSize: 15,
        hintText: "Azioni",
        onChanged: (value) {
          if (value == null || value['value'] == null) return;
          onOpenAzioniDialog(project, value['value'].toString().trim());
        },
      ),
    );
  }


  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => projects.length;

  @override
  int get selectedRowCount => 0;
}
