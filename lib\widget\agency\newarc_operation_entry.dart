import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/operation.dart';
import 'package:newarc_platform/utils/various.dart';

class NewarcOperationEntry extends StatefulWidget {
  final Operation operation;
  final Function showAddEditOperationPopup;
  final AgencyUser agencyUser;
  const NewarcOperationEntry(
      {Key? key,
      required this.operation,
      required this.agencyUser,
      required this.showAddEditOperationPopup})
      : super(key: key);

  @override
  State<NewarcOperationEntry> createState() => _NewarcOperationEntryState();
}

class _NewarcOperationEntryState extends State<NewarcOperationEntry> {
  bool isExpanded = false;

  @override
  void initState() {
    print(widget.operation.toJson());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(width: 5),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.operation.addressString!,
                        style: TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                      Text(
                        widget.operation.city!,
                        style: TextStyle(
                          color: Colors.grey,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              Row(
                children: [
                  Text(
                    "Stato vendita:",
                    style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(width: 8),
                  getSaleBadge(widget.operation.saleState!),
                  SizedBox(width: 18),
                  Text(
                    "Stato lavori:",
                    style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(width: 8),
                  getStatusBadge(widget.operation.workState!),
                  widget.agencyUser.role == "master"
                      ? Row(
                          children: [
                            SizedBox(width: 10),
                            IconButton(
                              onPressed: () {
                                setState(() {
                                  widget.showAddEditOperationPopup(
                                      widget.operation, true);
                                });
                              },
                              icon: Icon(
                                Icons.edit,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        )
                      : Container(),
                  SizedBox(width: 10),
                  IconButton(
                    onPressed: () {
                      setState(() {
                        isExpanded = !isExpanded;
                      });
                    },
                    icon: Icon(
                      isExpanded
                          ? Icons.keyboard_arrow_up
                          : Icons.keyboard_arrow_down,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ],
          ),
          Container(
            height: 1,
            margin: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            color: Colors.grey,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Stack(
                alignment: Alignment.center,
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                    decoration: BoxDecoration(
                      border: Border.all(color: Theme.of(context).primaryColor),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Column(
                          children: [
                            Text(
                              "Acquisto",
                              style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 1,
                                  color: widget.operation.firstCommission!
                                              .commissionState !=
                                          CommissionState.paid
                                      ? Color(0xff6A6A6A)
                                      : Theme.of(context).primaryColor),
                            ),
                            Row(
                              children: [
                                Image.asset(
                                  "assets/icons/acquisto.png",
                                  height: 15,
                                ),
                                SizedBox(width: 5),
                                Text(
                                  widget.operation.firstCommission!
                                              .commissionState ==
                                          CommissionState.toBeDefined
                                      ? "-"
                                      : formatPrice(widget
                                          .operation.firstCommission!.amount!),
                                  style: TextStyle(
                                      fontSize: 22,
                                      fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                            Text(
                              getCommissionStateString(widget
                                  .operation.firstCommission!.commissionState!),
                              style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 1,
                                  color: widget.operation.firstCommission!
                                              .commissionState !=
                                          CommissionState.paid
                                      ? Color(0xff6A6A6A)
                                      : Theme.of(context).primaryColor),
                            ),
                          ],
                        ),
                        SizedBox(width: 20),
                        Column(
                          children: [
                            Text(
                              "Rivendita",
                              style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 1,
                                  color: widget.operation.secondCommission!
                                              .commissionState !=
                                          CommissionState.paid
                                      ? Color(0xff6A6A6A)
                                      : Theme.of(context).primaryColor),
                            ),
                            Row(
                              children: [
                                Image.asset(
                                  "assets/icons/vendita.png",
                                  height: 15,
                                ),
                                SizedBox(width: 5),
                                Text(
                                  widget.operation.secondCommission!
                                              .commissionState ==
                                          CommissionState.toBeDefined
                                      ? "-"
                                      : formatPrice(widget
                                          .operation.secondCommission!.amount!),
                                  style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 22),
                                ),
                              ],
                            ),
                            Text(
                              getCommissionStateString(widget.operation
                                  .secondCommission!.commissionState!),
                              style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 1,
                                  color: widget.operation.secondCommission!
                                              .commissionState !=
                                          CommissionState.paid
                                      ? Color(0xff6A6A6A)
                                      : Theme.of(context).primaryColor),
                            )
                          ],
                        ),
                        SizedBox(width: 20),
                        Column(
                          children: [
                            Text(
                              "Bonus",
                              style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 1,
                                  color: Theme.of(context).primaryColor),
                            ),
                            Text(
                              widget.operation.bonus!.commissionState ==
                                      CommissionState.toBeDefined
                                  ? "-"
                                  : "${widget.operation.bonus!.amount!}%",
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color:
                                    widget.operation.bonus!.commissionState ==
                                            CommissionState.toBeDefined
                                        ? Color(0xff6A6A6A)
                                        : Theme.of(context).primaryColor,
                                fontSize: 22,
                              ),
                            ),
                            Text(
                              getCommissionStateString(
                                  widget.operation.bonus!.commissionState!),
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                letterSpacing: 1,
                                color:
                                    widget.operation.bonus!.commissionState ==
                                            CommissionState.toBeDefined
                                        ? Color(0xff6A6A6A)
                                        : Theme.of(context).primaryColor,
                              ),
                            )
                          ],
                        )
                      ],
                    ),
                  ),
                  Positioned(
                    top: 0,
                    left: 10,
                    child: Container(
                      color: Colors.white,
                      child: Text(
                        "  Provvigioni  ",
                        style: TextStyle(
                          color: Theme.of(context).primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  Container(height: 90),
                ],
              ),
              Row(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Prezzo annuncio",
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 1,
                          color: Color(0xff6A6A6A),
                        ),
                      ),
                      SizedBox(height: 10),
                      Text(
                        formatPrice(widget.operation.adPrice!),
                        style: TextStyle(
                            fontSize: 20, fontWeight: FontWeight.bold),
                      )
                    ],
                  ),
                  SizedBox(width: 20),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Proposta minima",
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 1,
                          color: Color(0xff6A6A6A),
                        ),
                      ),
                      SizedBox(height: 10),
                      Text(
                        formatPrice(widget.operation.minSalePrice!),
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Color(0xff6A6A6A),
                        ),
                      )
                    ],
                  ),
                  SizedBox(width: 20),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Obiettivo vendita",
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 1,
                          color: Color(0xff6A6A6A),
                        ),
                      ),
                      SizedBox(height: 10),
                      Text(
                        formatPrice(widget.operation.salePriceGoal!),
                        style: TextStyle(
                          color: Color(0xff6A6A6A),
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
          !isExpanded
              ? Container()
              : Column(
                  children: [
                    Container(
                      height: 1,
                      margin:
                          EdgeInsets.symmetric(horizontal: 20, vertical: 15),
                      color: Colors.grey,
                    ),
                    Text(
                      "Informazioni rivendita".toUpperCase(),
                      style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                          color: Colors.grey,
                          letterSpacing: 1),
                    ),
                    SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Expanded(
                          child: Column(
                            children: [
                              Text(
                                "Atto Acquisto".toUpperCase(),
                                style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 10,
                                    color: Colors.grey,
                                    wordSpacing: 1),
                              ),
                              SizedBox(height: 8),
                              Text(
                                getFormattedDate(
                                    widget.operation.purchaseDeedTimestamp),
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            children: [
                              Text(
                                "In vendita dal".toUpperCase(),
                                style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 1,
                                    color: Colors.grey),
                              ),
                              SizedBox(height: 8),
                              Text(
                                getFormattedDate(
                                    widget.operation.onSaleFromTimestamp),
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text(
                                "Giorni in vendita".toUpperCase(),
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 1,
                                    color: Colors.grey),
                              ),
                              SizedBox(height: 8),
                              Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      "${getDaysCounter(widget.operation.onSaleFromTimestamp, DateTime.now().millisecondsSinceEpoch)}",
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 18,
                                        color: getDaysOnSaleColor(
                                            widget
                                                .operation.onSaleFromTimestamp,
                                            widget
                                                .operation.endMandateTimestamp),
                                      ),
                                    ),
                                    Text(
                                      "/${getDaysCounter(widget.operation.onSaleFromTimestamp, widget.operation.endMandateTimestamp)}",
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    )
                                  ]),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text(
                                "Scadenza mandato".toUpperCase(),
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 1,
                                    color: Colors.grey),
                              ),
                              SizedBox(height: 8),
                              Text(
                                getFormattedDate(
                                    widget.operation.endMandateTimestamp),
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            children: [
                              Text(
                                "Atto Vendita".toUpperCase(),
                                style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 1,
                                    color: Colors.grey),
                              ),
                              SizedBox(height: 8),
                              Text(
                                getFormattedDate(
                                    widget.operation.saleDeedTimestamp),
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 10)
                  ],
                ),
        ],
      ),
    );
  }

  Widget getSaleBadge(SaleState saleState) {
    Color color = Theme.of(context).primaryColor;
    if (saleState == SaleState.forSale) {
      color = Color(0xffFFC633);
    } else if (saleState == SaleState.toSell) {
      color = Color(0xffFF5E53);
    }
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 3),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(5),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 3, bottom: 3),
            child: Image.asset(
              "assets/icon.png",
              color: Colors.white,
              width: 15,
            ),
          ),
          SizedBox(width: 5),
          Text(
            getSaleStateString(saleState),
            style: TextStyle(color: Colors.white),
          )
        ],
      ),
    );
  }

  Widget getStatusBadge(WorkState workState) {
    Color color = Theme.of(context).primaryColor;
    if (workState == WorkState.inProgress) {
      color = Color(0xffFFC633);
    } else if (workState == WorkState.toStart) {
      color = Color(0xffFF5E53);
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 3),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(5),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.construction, color: Colors.white),
          SizedBox(width: 5),
          Text(
            getWorkStateString(workState),
            style: TextStyle(color: Colors.white),
          )
        ],
      ),
    );
  }

  int getDaysCounter(int? t1Ms, int? t2Ms) {
    if (t1Ms == null || t2Ms == null) {
      return 0;
    }
    int days = DateTime.fromMillisecondsSinceEpoch(t2Ms)
        .difference(DateTime.fromMillisecondsSinceEpoch(t1Ms))
        .inDays;
    if (days < 0) {
      days = 0;
    }
    return days;
  }

  Color getDaysOnSaleColor(int? onSaleFromTimestamp, int? endMandate) {
    if (onSaleFromTimestamp == null || endMandate == endMandate) {
      return Colors.grey;
    }
    int firstNumber = getDaysCounter(widget.operation.onSaleFromTimestamp,
        DateTime.now().millisecondsSinceEpoch);
    int secondNumber = getDaysCounter(widget.operation.onSaleFromTimestamp,
        widget.operation.endMandateTimestamp);

    double percentage = firstNumber / secondNumber;

    if (percentage <= 0.5)
      return Theme.of(context).primaryColor;
    else if (percentage < 0.75)
      return Color(0xffFFC633);
    else
      return Color(0xffFF5E53);
  }
}
