
import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/supplier.dart';
import 'package:newarc_platform/classes/professionals.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/pages/register_page.dart';
import 'package:newarc_platform/pages/successful_payment_page.dart';
import 'package:newarc_platform/pages/404_page.dart';
import 'package:newarc_platform/routes/app_router.dart';
import 'package:newarc_platform/routes/professional_routes.dart';
import 'package:newarc_platform/routes/work_routes.dart';
import 'package:newarc_platform/utils/firestore.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/functions/various.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/custom_icon_button.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:provider/provider.dart';
import 'package:web/web.dart' as web;
import '../routes/agency_routes.dart';
import '../routes/user_provider.dart';

class LoginPage extends StatefulWidget {
  // final String loginType;

  const LoginPage({
    Key? key, 
    // required this.loginType
  }) : super(key: key);

  // static const String route = '/login';

  @override
  _LoginPageState createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  String email = "";
  String password = "";
  User? user;
  bool loading = false;
  bool pwdVisible = true;
  bool resetMode = false;
  final _formKey = GlobalKey<FormState>();
  bool isVerified = true;
  bool isMaster = false;

  String loginType = 'agency';

  TextEditingController passwordTextController = new TextEditingController();
  TextEditingController usernameTextController = new TextEditingController();
  bool isValidPassword = true;

  @override
  void initState() {
    // checkLoggedUser();
    initLoginPageType();
    super.initState();
  }

  // checkLoggedUser() async {
  //   if (isVerified) {
  //     FirebaseAuth.instance.authStateChanges().listen((firebaseUser) async {
  //       if (firebaseUser == null) {
  //         setState(() {
  //           loading = false;
  //         });
  //         return;
  //       }
  //       // Check if we're on the successful payment page
  //       // final currentRoute = ModalRoute.of(context)?.settings.name;
  //       // get current url
  //       final currentRoute = Uri.parse(web.window.location.href);
  //       const authBypassRoutes = [
  //         SuccessfulPaymentPage.route,
  //         Page404.route,
  //       ];
  //       if (authBypassRoutes.contains(currentRoute.path)) {
  //         // Skip authentication check for the successful payment page
  //         setState(() {
  //           loading = false;
  //         });
  //         return;
  //       }
  //
  //       dynamic user = await getUser(firebaseUser.uid);
  //
  //       if (user == null) {
  //         setState(() {
  //           loading = false;
  //         });
  //         return;
  //       }
  //
  //       if (user != null &&
  //           (user.runtimeType == NewarcUser || user.runtimeType == SupplierUser || firebaseUser.emailVerified)) {
  //         //AgencyUser agencyUser =
  //         //  await getAgencyUser(FirebaseAuth.instance.currentUser!.uid);
  //
  //         goHome(context,user, widget.loginType);
  //         setState(() {
  //           user = firebaseUser;
  //           loading = false;
  //         });
  //       } else {
  //         setState(() {
  //           loading = false;
  //         });
  //       }
  //     }).onDone(() {
  //       loading = false;
  //     });
  //   }
  //
  //   setState(() {
  //     loading = false;
  //   });
  // }

  void initLoginPageType(){
    final currentRoute = Uri.parse(web.window.location.href);
    if (currentRoute.host.contains('work.newarc.it')) {
      loginType = 'work';
    } else if (currentRoute.host.contains('pro.newarc.it')) {
      loginType = 'professionals';
    } else if (currentRoute.host.contains('agenzie.newarc.it')) {
      loginType = 'agency';
    } else {
      loginType = 'agency';
    }
    debugPrint('Login Type: $loginType');
  }

  @override
  Widget build(BuildContext context) {   
    return Scaffold(
      backgroundColor: Theme.of(context).primaryColorDark,
      body: LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          bool responsive = constraints.maxWidth < 600;
          return Center(
            child: loading
                ? CircularProgressIndicator(
                    color: Theme.of(context).primaryColor,
                  )
                : Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        loginType == 'work'
                          ? 'assets/logo.png'
                          : loginType == 'professionals'
                            ? 'assets/newarc_professionals_white.png'
                            : 'assets/logo-agenzie-white.png',
                        width: 197,
                      ),
                      SizedBox(height: 40),
                      Form(
                        key: _formKey,
                        child: Container(
                          width: responsive
                              ? MediaQuery.of(context).size.width * 0.8
                              : 360,
                          child: AutofillGroup(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    CustomTextFormField(
                                      label: "Email",
                                      labelColor: Colors.white,
                                      autoFillHints: [AutofillHints.email],
                                      controller: usernameTextController,
                                      onChangedCallback: (String _email) {
                                        setState(() {
                                          email = _email;
                                        });
                                      },
                                      onFieldSubmitted: (String input) async {
                                        showLoginError();
                                      },
                                      validator: (value) {
                                        if (value == null || value.isEmpty) {
                                          return 'Inserisci una mail valida';
                                        }
                                        return null;
                                      },
                                    ),
                                  ],
                                ),
                                SizedBox(height: 10),
                                resetMode
                                    ? Container()
                                    : Row(
                                        children: [
                                          CustomTextFormField(
                                            label: "Password",
                                            labelColor: Colors.white,
                                            autoFillHints: [
                                              AutofillHints.password
                                            ],
                                            controller: passwordTextController,
                                            isObscureText: pwdVisible,
                                            suffixIcon: IconButton(
                                              icon: pwdVisible
                                                  ? Icon(
                                                      Icons.visibility_rounded,
                                                      color: Theme.of(context)
                                                          .primaryColor,
                                                    )
                                                  : Icon(
                                                      Icons
                                                          .visibility_off_rounded,
                                                      color: Colors.grey),
                                              onPressed: () {
                                                setState(() {
                                                  pwdVisible = !pwdVisible;
                                                });
                                              },
                                            ),
                                            validator: (value) {
                                              if (value == null ||
                                                  value.isEmpty) {
                                                return 'Inserisci una password valida';
                                              }
                                              return null;
                                            },
                                            onChangedCallback:
                                                (String _password) {
                                              setState(() {
                                                password = _password;
                                              });
                                            },
                                            onFieldSubmitted:
                                                (String input) async {
                                              showLoginError();
                                            },
                                          ),
                                        ],
                                      ),
                                SizedBox(height: 0),
                                TextButton(
                                  onPressed: () async {
                                    setState(() {
                                      resetMode = !resetMode;
                                    });
                                  },
                                  child: NarFormLabelWidget(
                                    label: resetMode
                                        ? "Torna alla schermata d'accesso"
                                        : "Password dimenticata?",
                                    textColor: Colors.white,
                                    fontSize: 14,
                                    fontWeight: '600',
                                    textDecoration: TextDecoration.underline,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: 30),
                      Container(
                        width: 200,
                        child: CustomIconButton(
                          label: resetMode ? "Reimposta password" : "Accedi",
                          textStyle: TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.w600),
                          width: 200,
                          height: 50,
                          icon: Container(),
                          color: Theme.of(context).primaryColor,
                          function: () {
                            TextInput.finishAutofillContext();
                            if (resetMode) {
                              resetPassword();
                            } else {
                              login();
                            }
                          },
                        ),
                      ),
                      SizedBox(height: 10),
                      loginType != 'agency' && loginType != 'professionals'
                          ? Container()
                          : TextButton(
                              onPressed: () {
                                context.go("/register/${loginType}");
                                // Navigator.of(context).pushNamed(
                                //   RegisterPage.route,
                                //   arguments: loginType,
                                // );
                              },
                              child: NarFormLabelWidget(
                                label: "Sei un${loginType == 'agency' ? "'agenzia" : " professionista"}? Registrati qui",
                                textColor: Colors.white,
                                fontSize: 16,
                                fontWeight: '700',
                                textDecoration: TextDecoration.underline,
                              ),
                            ),
                    ],
                  ),
          );
        },
      ),
    );
  }

  showLoginError() async {
    login();
  }

  // Future<void> login() async {
  Future<void> login() async {
    // print()
    if (_formKey.currentState!.validate()) {
      setState(() {
        loading = true;
      });

      try {
        UserCredential userCredential = await FirebaseAuth.instance
            .signInWithEmailAndPassword(email: email, password: password);

        isVerified = userCredential.user!.emailVerified;
        if (!appConfig.isProduction) {isVerified = true;};
        
        dynamic user = await getUser(userCredential.user!.uid);

        if (user == null) {
          setState(() {
            loading = false;
          });

          return;
        }

        /*if( isMaster == true ) {
          goHome(user, widget.isWork);
        } else if (user.runtimeType == AgencyUser && isVerified) {
          goHome(user, widget.isWork);
        } else if (user.runtimeType == NewarcUser) {
          goHome(user, widget.isWork);
        }*/
        // goHome(user, widget.isWork);

        //AgencyUser agencyUser = await getAgencyUser(userCredential.user!.uid);
        //if (agencyUser.agency != null && !agencyUser.agency!.isActive!) {
        //  isVerified = false;
        //}
        if (isMaster == true ||
            (user.runtimeType == AgencyUser && isVerified) ||
            user.runtimeType == NewarcUser || user.runtimeType == SupplierUser ||
            (user.runtimeType == ProfessionalsUser && isVerified)) {
          goHome(context, user);
        } else {
          User? _user = FirebaseAuth.instance.currentUser;

          // Logout the user before calling the verification link
          await FirebaseAuth.instance.signOut();

          // Verify link email
          await sendVerificationLinkEmail(_user!, loginType);

          customDialogWithActions(
              context,
              "Utente non verificato",
              "La tua mail non è ancora stata verificata.\n\nTi abbiamo re-inviato una mail di verifica all'indirizzo di posta elettronica registrato.\n\nPer ulteriori domande contatta l'assistenza Newarc.",
              []);

          setState(() {
            loading = false;
          });

          return;
        }
      } on FirebaseAuthException catch (e) {
        if (e.code == 'weak-password') {
          await showAlertDialog(
              context, "Errore", 'La password inserita è troppo debole');
        } else if (e.code == 'wrong-password') {
          await showAlertDialog(context, "Errore",
              "Le informazioni di accesso non sono corrette");
        } else {
          await showAlertDialog(context, "Errore Server", e.message!);
          // print('Error');
          // print(e);
        }
        setState(() {
          loading = false;
        });
      } catch (e) {
        await showAlertDialog(context, "Errore Generico", e.toString());

        // print('catch');
        // print({e, s});
        setState(() {
          loading = false;
        });
      }
    }
  }

  Future<dynamic> getUser(String uid) async {
    // Returns a AgencyUser, NewarcUser, ProfessionalsUser or SupplierUser depending on the userCredential uid

    if (uid == '') return null;

    DocumentSnapshot<Map<String, dynamic>?> firestoreUserDoc = await fetchDocument('users/${uid}');
    Map<String, dynamic>? firestoreUser = firestoreUserDoc.data();

    // print(firestoreUser);

    if (firestoreUser != null) {
      if (firestoreUser['type'] == 'agency') {
        return await _getAgencyUser(firestoreUser, uid);
      } else if (firestoreUser['type'] == 'newarc') {
        return await _getNewarcUser(firestoreUser, uid);
      } else if (firestoreUser['type'] == 'professionals') {
        return await _getProfessionalsUser(firestoreUser, uid);
      } else if (firestoreUser['type'] == 'supplier') {
        return await _getSupplierUser(firestoreUser, uid);
      } else if (firestoreUser['type'] == 'master') {
        isMaster = true;
        if (loginType == 'work') {
          return await _getNewarcUser(firestoreUser, uid);
        } else {
          return await _getAgencyUser(firestoreUser, uid);
        }
      }
    }
  }

  Future<ProfessionalsUser> _getProfessionalsUser(
      Map<String, dynamic>? firestoreUser, String uid) async {
    DocumentSnapshot<Map<String, dynamic>?> firestoreProfessionalsDoc =
        await fetchDocument('${appConfig.COLLECT_PROFESSIONALS}/${firestoreUser!["professionalId"]}');
    Map<String, dynamic>? firestoreProfessionals = firestoreProfessionalsDoc.data();

    ProfessionalsUser professionalsUser = ProfessionalsUser(firestoreUser, uid, firestoreProfessionals!, firestoreProfessionalsDoc.id);

    return professionalsUser;
  }


  Future<SupplierUser> _getSupplierUser(
      Map<String, dynamic>? firestoreUser, String uid) async {
    DocumentSnapshot<Map<String, dynamic>?> firestoreSupplierDoc =
        await fetchDocument('${appConfig.COLLECT_SUPPLIERS}/${firestoreUser!["supplierId"]}');
    Map<String, dynamic>? firestoreSupplier = firestoreSupplierDoc.data();

    SupplierUser supplierUser =
        SupplierUser(firestoreUser, uid, firestoreSupplier!, firestoreSupplierDoc.id);

    return supplierUser;
  }

  Future<AgencyUser> _getAgencyUser(
      Map<String, dynamic>? firestoreUser, String uid) async {
    DocumentSnapshot<Map<String, dynamic>?> firestoreAgencyDoc =
        await fetchDocument('${appConfig.COLLECT_AGENCIES}/${firestoreUser!["agencyId"]}');
    Map<String, dynamic>? firestoreAgency = firestoreAgencyDoc.data();

    AgencyUser agencyUser =
        AgencyUser(firestoreUser, uid, firestoreAgency!, firestoreAgencyDoc.id);

    return agencyUser;
  }

  @deprecated
  Future<AgencyUser> getAgencyUser(String uid) async {
    DocumentSnapshot<Map<String, dynamic>?> firestoreUserDoc =
        await fetchDocument('users/${uid}');
    Map<String, dynamic>? firestoreUser = firestoreUserDoc.data();

    DocumentSnapshot<Map<String, dynamic>?> firestoreAgencyDoc =
        await fetchDocument('agencies/${firestoreUser!["agencyId"]}');
    Map<String, dynamic>? firestoreAgency = firestoreAgencyDoc.data();

    AgencyUser agencyUser = AgencyUser(firestoreUser, firestoreUserDoc.id,
        firestoreAgency!, firestoreAgencyDoc.id);

    return agencyUser;
  }

  Future<NewarcUser> _getNewarcUser(
      Map<String, dynamic>? firestoreUser, String uid) async {
    NewarcUser newarcUser = NewarcUser.fromDocument(firestoreUser!, uid);

    return newarcUser;
  }

  @deprecated
  Future<NewarcUser> getNewarcUser(String uid) async {
    DocumentSnapshot<Map<String, dynamic>?> firestoreUserDoc =
        await fetchDocument('users/${uid}');
    Map<String, dynamic>? firestoreUser = firestoreUserDoc.data();

    NewarcUser newarcUser =
        NewarcUser.fromDocument(firestoreUser!, firestoreUserDoc.id);

    return newarcUser;
  }

  void goHome(BuildContext context, dynamic user) {
    final userProvider = context.read<UserProvider>();
    userProvider.setUser(user);
    if (user.runtimeType == NewarcUser) {
      context.go(WorkRoutes.workRenovationProject);
    } else if (user.runtimeType == ProfessionalsUser) {
      context.go(ProfessionalRoutes.professionalImmagina("progetti-attivi"));
    } else if (user.runtimeType == SupplierUser) {
      // context.go('/home-supplier/dashboard');
    } else {
      context.go(AgencyRoutes.agencyImmagina('immagina-pro'));
    }
  }

  Future<void> resetPassword() async {
    if ((email != null) && (email!="")) {
      try {
        // show circular progress indicator
        setState(() {
          loading = true;
        });
        var requestStatus = await sendResetPasswordEmail(email);
        if (requestStatus["status"]!=200) {
          throw FirebaseAuthException(code: requestStatus["msg"]["errorInfo"]["code"], message: requestStatus["msg"]["errorInfo"]["message"]);
        }
        await showAlertDialog(
          context,
          "Ok!",
          "Abbiamo inviato una mail per ripristinare la tua password al seguente indirizzo di posta elettronica: $email.",
        );
      } on FirebaseAuthException catch (e) {
        if (e.code == 'invalid-email') {
          await showAlertDialog(
              context, "Errore", "La mail fornita non è valida");
        }
        if (e.code == 'invalid-email') {
          await showAlertDialog(
              context, "Errore", "La mail fornita non è valida");
        } 
        if (e.code == 'auth/email-not-found'){
          await showAlertDialog(
              context, "Errore", "La mail fornita non è iscritta al portale");
        } else {
          await showAlertDialog(context, "Errore", "Si è verificato un errore");
        }
      } finally {
        setState(() {
          loading = false;
        });
      }
    } else {
      await showAlertDialog(context, "Errore",
          "Inserisci una mail valida prima di iniziare la procedura di ripristino password.");
    }
  }
}
