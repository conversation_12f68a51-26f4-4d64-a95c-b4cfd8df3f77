import 'package:newarc_platform/classes/baseAddressInfo.dart';

class BasePersonInfo{
  String? phone;
  String? email;
  String? name;
  String? surname;

  toFullName() {
    return this.name! +' '+ this.surname!;
  }

  BasePersonInfo(Map<String, dynamic> userMap) {
    this.name = userMap['name'];
    this.surname = userMap['surname'];
    this.phone = userMap['phone'];
    this.email = userMap['email'] ;
  }

  BasePersonInfo.empty() {
    this.name = null;
    this.surname = null;
    this.phone = null;
    this.email = null;
  }

  BasePersonInfo.fromMap(Map<String, dynamic> data) {
    this.name = data['name'];
    this.surname = data['surname'];
    this.phone = data['phone'];
    this.email = data['email'];
  }

  Map<String, dynamic> toMap() {
    return {
      'name': this.name,
      'surname': this.surname,
      'phone': this.phone,
      'email': this.email,
    };
  }
}

class LegalRepresentativePersonInfo extends BasePersonInfo {
  String? fiscalCode;
  String? sex;
  String? birthDate;
  String? birthCountry;
  String? birthProvince;
  String? birthCity;
  String? pec;
  BaseAddressInfo? residentialAddressInfo;

  LegalRepresentativePersonInfo.empty() : super({}) {
    this.fiscalCode = null;
    this.sex = null;
    this.birthDate = null;
    this.birthCountry = null;
    this.birthProvince = null;
    this.birthCity = null;
    this.pec = null;
    this.residentialAddressInfo = BaseAddressInfo.empty();
  }

  LegalRepresentativePersonInfo.fromMap(Map<String, dynamic> data) : super(data) {
    this.fiscalCode = data["fiscalCode"];
    this.sex = data["sex"];
    this.birthDate = data["birthDate"];
    this.birthCountry = data["birthCountry"];
    this.birthProvince = data["birthProvince"];
    this.birthCity = data["birthCity"];
    this.pec = data["pec"];
    this.residentialAddressInfo = (data.containsKey('residentialAddressInfo') && data['residentialAddressInfo'] != null) ? BaseAddressInfo.fromMap(data['residentialAddressInfo']) : null;
  }

  Map<String, dynamic> toMap() {
    Map<String, dynamic> baseMap = super.toMap();
    baseMap.addAll({
      'fiscalCode': this.fiscalCode,
      'sex': this.sex,
      'birthDate': this.birthDate,
      'birthCountry': this.birthCountry,
      'birthProvince': this.birthProvince,
      'birthCity': this.birthCity,
      'pec': this.pec,
      'residentialAddressInfo': this.residentialAddressInfo?.toMap(),
    });
    return baseMap;
  }
}