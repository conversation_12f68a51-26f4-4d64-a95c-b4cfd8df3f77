name: newarc_platform
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: ">=3.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations: # Add this line
    sdk: flutter   

  google_fonts: ^6.3.0
  cupertino_icons: ^1.0.2
  google_maps_flutter_web: ^0.5.8
  csv: ^6.0.0
  provider: ^6.1.2
  http: ^1.0.0
  collection: ^1.15.0
  cloud_functions: ^6.0.0
  firebase_core: ^4.0.0
  async: ^2.8.1
  dropdown_button2: ^2.3.9
  cloud_firestore: ^6.0.0
  firebase_auth: ^6.0.0
  url_launcher: ^6.1.3
  data_table_2: ^2.6.0
  mask_text_input_formatter: ^2.4.0
  url_strategy: ^0.3.0
  flutter_svg: ^2.0.13
  clipboard: ^2.0.2
  fluttertoast: ^8.1.2
  image_picker: ^1.1.2
  firebase_storage: ^13.0.0
  google_api_headers: ^4.5.7
  google_maps_flutter: ^2.1.1
  geocoding: ^4.0.0
  fl_chart: ^1.0.0
  image_compression_flutter: ^1.0.4
  path_provider: ^2.1.3
  win32: ^5.5.1
  file_picker: ^10.3.1
  syncfusion_flutter_pdfviewer: ^30.2.6
  video_player: ^2.10.0
  pdf: ^3.11.0
  flutter_image_stack: ^0.0.7
  country_picker: ^2.0.27
  get: ^4.6.6
  intl:
  js: ^0.7.2
  qr_flutter: ^4.1.0
  flutter_colorpicker: ^1.1.0
  mime: ^1.0.6
  go_router: ^16.2.1





dev_dependencies:
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/icons/
    - assets/icons/map_icons/
    - assets/various/
    - assets/character/
    - .env.production
    - .env.staging

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Raleway-300
      fonts:
        - asset: assets/fonts/Raleway-Light.ttf
    - family: Raleway-400
      fonts:
        - asset: assets/fonts/Raleway-Regular.ttf
    - family: Raleway-500
      fonts:
        - asset: assets/fonts/Raleway-Medium.ttf
    - family: Raleway-600
      fonts:
        - asset: assets/fonts/Raleway-Medium.ttf
    - family: Raleway-normal
      fonts:
        - asset: assets/fonts/Raleway-Medium.ttf
    - family: Raleway-700
      fonts:
        - asset: assets/fonts/Raleway-Bold.ttf

    #italic 700 bold
    - family: Raleway-700_italic
      fonts:
        - asset: assets/fonts/Raleway-BoldItalic.ttf


    - family: Raleway-800
      fonts:
        - asset: assets/fonts/Raleway-Bold.ttf
    - family: Raleway-bold
      fonts:
        - asset: assets/fonts/Raleway-Bold.ttf
    - family: Raleway-900
      fonts:
        - asset: assets/fonts/Raleway-ExtraBold.ttf
    - family: semi-bold
      fonts:
        - asset: assets/fonts/Raleway-SemiBold.ttf
    - family: light-italic
      fonts:
        - asset: assets/fonts/Raleway-LightItalic.ttf
    - family: Tinos-700
      fonts:
        - asset: assets/fonts/Tinos-Bold.ttf
    - family: Tinos-bold
      fonts:
        - asset: assets/fonts/Tinos-Bold.ttf
    - family: Tinos-400
      fonts:
        - asset: assets/fonts/Tinos-Regular.ttf
    - family: Tinos-500
      fonts:
        - asset: assets/fonts/Tinos-Regular.ttf
    - family: Tinos-Bold-Italic
      fonts:
        - asset: assets/fonts/Tinos-BoldItalic.ttf
      
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages