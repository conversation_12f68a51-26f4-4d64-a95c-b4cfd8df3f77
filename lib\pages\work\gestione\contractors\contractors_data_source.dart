import 'dart:developer';

import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:newarc_platform/classes/supplier.dart';
import 'package:newarc_platform/pages/work/gestione/contractors/contractors_controller.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:intl/intl.dart';

class ContractorsDataSource extends DataTableSource {
  final List<Supplier> contractors;
  final BuildContext context;
  final Function(Supplier) onContractorTap;
  final controller = Get.put<ContractorsController>(ContractorsController());

  NumberFormat localCurrencyFormatMain = NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 0);

  ContractorsDataSource({
    required this.context,
    required this.contractors,
    required this.onContractorTap,
  });

  @override
  DataRow? getRow(int index) {
    if (index < contractors.length){
      Supplier cont = contractors[index];

      final uniqueProjectIds = cont.assignedProject
          ?.map((e) => e["project_id"])
          .whereType<String>()
          .toSet();

      return DataRow2(
        specificRowHeight: 50,
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
                color: AppColor.borderColor,
                width: 1,
            ),
          ),
        ),
        cells: [
          // Ditta
          DataCell(
            NarLinkWidget(
              text: "${cont.name} ${cont.formationType}",
              fontSize: 12,
              fontWeight: '800',
              textAlign: TextAlign.left,
              textColor: Colors.black,
              onClick: (){
                onContractorTap(cont);
              },
            )
          ),
          // Categorie
          DataCell(
            NarFormLabelWidget(
              label: "${cont.activities?.join(' - ') ?? ""}",
              fontSize: 12,
              fontWeight: '600',
              textAlign: TextAlign.start,
              textColor: Colors.black,
              maxLines: 3,
            )
          ),
          // Provincia base
          DataCell(
            NarFormLabelWidget(
              label: cont.operativeAddressInfo?.province ?? "${cont.city}",
              fontSize: 12,
              fontWeight: '600',
              textAlign: TextAlign.start,
              textColor: Colors.black,
            )
          ),
          // Prog. assegnati
          DataCell(
            NarFormLabelWidget(
              label: "${uniqueProjectIds?.length ?? ""}",
              fontSize: 12,
              fontWeight: '600',
              textAlign: TextAlign.start,
              textColor: Colors.black,
            )
          ),
        ],
      );
    }
    return null;
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => contractors.length;

  @override
  int get selectedRowCount => 0;
}

