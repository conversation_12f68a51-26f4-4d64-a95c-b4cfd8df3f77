import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:newarc_platform/classes/NAMaterial.dart';
import 'package:newarc_platform/classes/newarcAdConfiguration.dart';
import 'package:newarc_platform/classes/newarcAdElementConfiguration.dart';
import 'package:newarc_platform/classes/newarcAdOptionalCategoryConfiguration.dart';
import 'package:newarc_platform/classes/newarcAdRoomConfiguration.dart';
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/button.dart';
import 'package:newarc_platform/classes/property.dart';
import '../../../../app_const.dart';
import '../../../../classes/NewarcMaterialManufacturer.dart';
import '../../../../classes/NewarcMaterialManufacturerCollection.dart';
import '../../../../classes/NewarcMaterialVariant.dart';
import '../../../../classes/newarcAdMaterialConfiguration.dart';
import '../../../../classes/newarcAdOptionalConfiguration.dart';
import '../../../UI/base_newarc_popup.dart';
import '../../../UI/custom_textformfield.dart';
import '../../../UI/file-picker.dart';
import '../../../UI/link.dart';
import '../../../UI/multi-select-dropdown.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import '../../../UI/select-box.dart';
import '../../../UI/tab/common_icon_button.dart';

class ConfigurationForm extends StatefulWidget {
  final NewarcProject? project;
  final Property? property;
  final Function? initialFetchProperties;
  final List<bool>? isInputChangeDetected;

  ConfigurationForm({
    Key? key,
    this.project,
    this.property,
    this.initialFetchProperties,
    this.isInputChangeDetected,
  }) : super(key: key);

  static const String route = '/property/add';

  @override
  _ConfigurationFormState createState() => _ConfigurationFormState();
}

class _ConfigurationFormState extends State<ConfigurationForm> {
  bool loading = true;

  List<Map<String, dynamic>> selectedConfigurableList = [];

  String formProgressMessage = '';

  List configurableList = [
    {'label': "Pavimenti", 'value': "pavimenti"},
    {'label': "Rivestimenti", 'value': "rivestimenti"},
    {'label': "Tinte", 'value': "tinte"},
  ];

  List<NewarcAdRoomConfiguration> newarcAdRoomConfigurationList = [];
  List<NewarcAdOptionalConfiguration> newarcAdOptionalConfigurationList = [];
  List<NewarcAdOptionalConfiguration> newarcAdOptionalConfigurationListForDropdown = [];

  NewarcAdConfiguration? newarcAdConfiguration ;

  NumberFormat localCurrencyFormatMain = NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 2);

  @override
  void initState() {
    log("widget.property?.firebaseId ===> ${widget.property?.firebaseId}");
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      await initialFetchConfiguration();
      if(newarcAdConfiguration?.firebaseId?.isEmpty ?? false){
        DocumentReference adConfigRef = await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_NEWARCADCONFIGURATION)
            .doc();
        Map<String, dynamic> data = {
          "isActive": false,
          "newarcAdRoomConfiguration": [],
          "newarcAdOptionalConfiguration": [],
        };
        newarcAdConfiguration = NewarcAdConfiguration.fromDocument(data, adConfigRef.id);
      }
    });
    super.initState();
  }

  Future<void> initialFetchConfiguration({bool force = false}) async {
    if ((newarcAdConfiguration?.firebaseId?.isNotEmpty ?? false) && !force) return;
    try{
      Query<Map<String, dynamic>> collectionSnapshotQuery;
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;

      collectionSnapshotQuery = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_NEWARCADCONFIGURATION);

      collectionSnapshotQuery = collectionSnapshotQuery.where('newarcHomesId', isEqualTo: widget.property?.firebaseId);

      collectionSnapshot = await collectionSnapshotQuery.get();

      NewarcAdConfiguration? _newarcAdConfiguration;
      List<NewarcAdOptionalConfiguration> optionalConfigurations = [];
      List<NewarcAdRoomConfiguration> rooomConfigurations = [];

      if(collectionSnapshot.docs.isNotEmpty){
        for (var doc in collectionSnapshot.docs) {
          _newarcAdConfiguration = NewarcAdConfiguration.fromDocument(doc.data(), doc.id);
          List? optionalConfigIds = _newarcAdConfiguration.newarcAdOptionalConfigurationIDS ?? [];
          List? roomConfigIds = _newarcAdConfiguration.newarcAdRoomConfigurationIDS ?? [];


          if (roomConfigIds.isNotEmpty) {
            for (String id in roomConfigIds) {
              DocumentSnapshot<Map<String, dynamic>> roomDoc =
              await FirebaseFirestore.instance.collection(appConfig.COLLECT_NEWARCADROOMCONFIGURATION).doc(id).get();

              if (roomDoc.exists) {

                NewarcAdRoomConfiguration room = NewarcAdRoomConfiguration.fromDocument(roomDoc.data()!);

                List<String> elementConfigIds = List.from(room.newarcAdElementConfigurationIDS?.reversed ?? []);
                List<NewarcAdElementConfiguration> elementConfigurations = [];

                if (elementConfigIds.isNotEmpty) {
                  QuerySnapshot<Map<String, dynamic>> elementSnapshot = await FirebaseFirestore.instance
                      .collection(appConfig.COLLECT_NEWARCADELEMENTCONFIGURATION)
                      .where(FieldPath.documentId, whereIn: elementConfigIds)
                      .get();

                  for (var doc in elementSnapshot.docs) {
                    NewarcAdElementConfiguration element = NewarcAdElementConfiguration.fromDocument(doc.data());

                    List? materialConfigIds = List.from(element.newarcAdMaterialConfigurationIDS?.reversed ?? []);
                    List<NewarcAdMaterialConfiguration> materialConfigurations = [];

                    if (materialConfigIds.isNotEmpty) {
                      QuerySnapshot<Map<String, dynamic>> materialSnapshot = await FirebaseFirestore.instance
                          .collection(appConfig.COLLECT_NEWARCADMATERIALCONFIGURATION)
                          .where(FieldPath.documentId, whereIn: materialConfigIds)
                          .get();

                      materialConfigurations = materialSnapshot.docs.map(
                            (matDoc) => NewarcAdMaterialConfiguration.fromDocument(matDoc.data()),
                      ).toList();
                    }

                    element.newarcAdMaterialConfiguration = materialConfigurations;
                    elementConfigurations.add(element);
                  }
                }

                room.newarcAdElementConfiguration = elementConfigurations;

                rooomConfigurations.add(room);
              }
            }
          }

          if (optionalConfigIds.isNotEmpty) {
            for (String id in optionalConfigIds) {
              DocumentSnapshot<Map<String, dynamic>> optionalDoc =
              await FirebaseFirestore.instance.collection(appConfig.COLLECT_NEWARCADOPTIONALCONFIGURATION).doc(id).get();

              if (optionalDoc.exists) {
                optionalConfigurations.add(
                  NewarcAdOptionalConfiguration.fromDocument(optionalDoc.data()!),
                );
              }
            }
          }

        }
      }else{
        _newarcAdConfiguration = NewarcAdConfiguration.empty();
      }

      setState(() {
        newarcAdConfiguration = _newarcAdConfiguration;
        newarcAdOptionalConfigurationList = optionalConfigurations;
        newarcAdRoomConfigurationList = rooomConfigurations;
      });
    }catch(e){
      log("Error while initialFetchConfiguration ------> ${e.toString()}");
    }finally{
      loading = false;
    }
  }

  @override
  void dispose() {
    super.dispose();
    newarcAdRoomConfigurationList = [];
    newarcAdOptionalConfigurationList = [];
    newarcAdConfiguration = NewarcAdConfiguration.empty();
    newarcAdOptionalConfigurationListForDropdown = [];
  }

  @override
  Widget build(BuildContext context) {
    return loading ? Center(
        child: CircularProgressIndicator(),
    )  : Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            NarFormLabelWidget(
              label: 'Configuratore',
              fontSize: 22,
              fontWeight: '700',
              textColor: AppColor.black,
            ),
            Padding(
              padding: const EdgeInsets.only(right: 0),
              child: Row(
                children: [
                  NarFormLabelWidget(
                    label: "Attiva/disattiva",
                    textColor: Color(0xff696969),
                    fontSize: 17,
                    fontWeight: '600',
                  ),
                  SizedBox(width: 5),
                  Switch(
                    value: newarcAdConfiguration?.isActive ?? false,
                    activeTrackColor: Theme.of(context).primaryColor,
                    activeThumbColor: AppColor.white,
                    inactiveThumbColor: AppColor.white,
                    inactiveTrackColor: Color(0xffA9A9A9),
                    onChanged: (val) async {
                      setState(() {
                        newarcAdConfiguration?.isActive = val;
                      });
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
        SizedBox(height: 15),

        //------ Section
        Container(

            padding: EdgeInsets.all(15),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: Color(0xffC8C8C8))),
            child: ListView(shrinkWrap: true, children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  NarFormLabelWidget(
                    label: 'Stanze',
                    fontSize: 20,
                    fontWeight: '700',
                    textColor: AppColor.black,
                  ),
                  NarButtonWidget(
                    trailingIcon: Icon(
                      Icons.add,
                      color: Theme.of(context).primaryColor,
                    ),
                    textHeight: 1,
                    color: Color(0xffDAEAE3),
                    hoverColor: Color(0xffDAEAE3),
                    borderRadius: 7,
                    fontWeight: '600',
                    fontSize: 15,
                    splashColor: Color(0xffDAEAE3),
                    textColor: Theme.of(context).primaryColor,
                    borderSideColor: Colors.transparent,
                    text: 'Aggiungi stanza',
                    elevation: 0.0,
                    height: 53,
                    minWidth: 192,
                    buttonPadding:
                        EdgeInsets.only(left: 15, right: 15, bottom: 0),
                    onClick: () {
                      setState(() {
                        selectedConfigurableList = [];
                      });
                      showAddRoomPopup();
                    },
                  ),
                ],
              ),
              SizedBox(
                height: 20,
              ),
              ListView(
                shrinkWrap: true,
                children: newarcAdRoomConfigurationList.map((room) {
                  return Container(
                    margin: EdgeInsets.only(bottom: 10),
                    padding: EdgeInsets.all(15),
                    decoration: BoxDecoration(
                      color: Color(0xFFF7F7F7),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: ExpansionTile(
                      title: Row(
                        children: [
                          // Up Move
                          Container(
                            height: 30,
                            width: 30,
                            decoration: BoxDecoration(
                              color: Color.fromRGBO(227, 227, 227, 1),
                              borderRadius: BorderRadius.circular(7.0),
                            ),
                            child: IconButton(
                              onPressed: () {
                                final index = newarcAdRoomConfigurationList.indexOf(room);
                                if (index > 0) {
                                  setState(() {
                                    final temp = newarcAdRoomConfigurationList[index - 1];
                                    newarcAdRoomConfigurationList[index - 1] = newarcAdRoomConfigurationList[index];
                                    newarcAdRoomConfigurationList[index] = temp;
                                  });
                                }
                              },
                              style: ButtonStyle(
                                overlayColor: WidgetStateProperty.all(Colors.transparent),
                              ),
                              icon: Transform.rotate(
                                angle: 3.14,
                                child: Image.asset(
                                  'assets/icons/download.png',
                                  height: 18,
                                  color: Color(0xff5b5b5b),
                                ),
                              ),
                            ),
                          ),
                          SizedBox(width: 5),
                          // Down Move
                          Container(
                            height: 30,
                            width: 30,
                            decoration: BoxDecoration(
                              color: Color.fromRGBO(227, 227, 227, 1),
                              borderRadius: BorderRadius.circular(7.0),
                            ),
                            child: IconButton(
                              onPressed: () {
                                final index = newarcAdRoomConfigurationList.indexOf(room);
                                if (index < newarcAdRoomConfigurationList.length - 1) {
                                  setState(() {
                                    final temp = newarcAdRoomConfigurationList[index + 1];
                                    newarcAdRoomConfigurationList[index + 1] = newarcAdRoomConfigurationList[index];
                                    newarcAdRoomConfigurationList[index] = temp;
                                  });
                                }
                              },
                              style: ButtonStyle(
                                overlayColor: WidgetStateProperty.all(Colors.transparent),
                              ),
                              icon: Image.asset(
                                'assets/icons/download.png',
                                height: 18,
                                color: Color(0xff5b5b5b),
                              ),
                            ),
                          ),
                          SizedBox(width: 10),
                          NarFormLabelWidget(
                            label: room.roomName ?? "",
                            fontSize: 15,
                            fontWeight: '700',
                            textColor: AppColor.black,
                          ),
                        ],
                      ),
                      clipBehavior: Clip.none,
                      iconColor: Colors.black,
                      shape: Border.all(color: Colors.transparent),
                      collapsedShape: Border.all(color: Colors.transparent),
                      onExpansionChanged: (val) {},
                      initiallyExpanded: true,
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            color: Color(0xFFF7F7F7),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          width: double.infinity,
                          padding: EdgeInsets.all(15),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              NarFormLabelWidget(
                                label: 'Immagine principale',
                                fontSize: 13,
                                fontWeight: '600',
                                textColor: AppColor.black,
                              ),
                              SizedBox(
                                height: 10,
                              ),
                                  NarFilePickerWidget(
                                      allowMultiple: false,
                                      imageContainerBoxDecoration: BoxDecoration(
                                        color: Colors.transparent
                                      ),
                                      filesToDisplayInList: 0,
                                      removeButton: false,
                                      isDownloadable: false,
                                      bottomActionSpace: 8,
                                      showTitle: false,
                                      removeButtonText: 'Elimina',
                                      removeButtonTextColor: Color(0xff797979),
                                      uploadButtonPosition: 'back',
                                      showMoreButtonText: '+ espandi',
                                      actionButtonPosition: 'bottom',
                                      displayFormat: 'button',
                                      containerWidth: 145,
                                      containerHeight: 145,
                                      containerBorderRadius: 8,
                                      borderRadius: 7,
                                      fontSize: 11,
                                      fontWeight: '600',
                                      text: 'Carica',
                                      borderSideColor: Theme.of(context).primaryColor,
                                      hoverColor: Color.fromRGBO(133, 133, 133, 1),
                                      allFiles: room.coverImageList,
                                      pageContext: context,
                                      storageDirectory: "newArcAdRoomConfiguration/cover/${room.id}",
                                      removeExistingOnChange: true,
                                      progressMessage: [''],
                                      notAccent: true,
                                      splashColor: Color(0xffE5E5E5),
                                      height: 35,
                                      buttonWidth: 125,
                                      buttonTextColor: Colors.black,
                                      onUploadCompleted: () {
                                        setState(() {
                                          room.coverImage?["location"] = room
                                                      .coverImageList
                                                      ?.isNotEmpty ??
                                                  false
                                              ? "newArcAdRoomConfiguration/cover/${room.id}"
                                              : "";
                                          room.coverImage?["fileName"] =
                                              room.coverImageList?.isNotEmpty ??
                                                      false
                                                  ? room.coverImageList![0]
                                                  : "";
                                        });
                                      },
                                    ),
                            ],
                          ),
                        ),
                        ...?room.newarcAdElementConfiguration
                            ?.map<Widget>((element) {
                          return Container(
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            width: double.infinity,
                            padding: EdgeInsets.all(15),
                            margin: EdgeInsets.symmetric(vertical: 10),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        // Up Move
                                        Container(
                                          height: 30,
                                          width: 30,
                                          decoration: BoxDecoration(
                                            color: Color.fromRGBO(227, 227, 227, 1),
                                            borderRadius: BorderRadius.circular(7.0),
                                          ),
                                          child: IconButton(
                                            onPressed: () {
                                              final roomIndex = newarcAdRoomConfigurationList.indexOf(room);
                                              final elements = newarcAdRoomConfigurationList[roomIndex].newarcAdElementConfiguration;

                                              final elementIndex = elements?.indexOf(element) ?? -1;
                                              if (elementIndex > 0) {
                                                setState(() {
                                                  final temp = elements![elementIndex - 1];
                                                  elements[elementIndex - 1] = elements[elementIndex];
                                                  elements[elementIndex] = temp;
                                                });
                                              }
                                            },
                                            style: ButtonStyle(
                                              overlayColor: WidgetStateProperty.all(Colors.transparent),
                                            ),
                                            icon: Transform.rotate(
                                              angle: 3.14,
                                              child: Image.asset(
                                                'assets/icons/download.png',
                                                height: 18,
                                                color: Color(0xff5b5b5b),
                                              ),
                                            ),
                                          ),
                                        ),
                                        SizedBox(width: 5),
                                        // Down Move
                                        Container(
                                          height: 30,
                                          width: 30,
                                          decoration: BoxDecoration(
                                            color: Color.fromRGBO(227, 227, 227, 1),
                                            borderRadius: BorderRadius.circular(7.0),
                                          ),
                                          child: IconButton(
                                            onPressed: () {
                                              final roomIndex = newarcAdRoomConfigurationList.indexOf(room);
                                              final elements = newarcAdRoomConfigurationList[roomIndex].newarcAdElementConfiguration;

                                              final elementIndex = elements?.indexOf(element) ?? -1;
                                              if (elementIndex < (elements?.length ?? 1) - 1) {
                                                setState(() {
                                                  final temp = elements![elementIndex + 1];
                                                  elements[elementIndex + 1] = elements[elementIndex];
                                                  elements[elementIndex] = temp;
                                                });
                                              }
                                            },
                                            style: ButtonStyle(
                                              overlayColor: WidgetStateProperty.all(Colors.transparent),
                                            ),
                                            icon: Image.asset(
                                              'assets/icons/download.png',
                                              height: 18,
                                              color: Color(0xff5b5b5b),
                                            ),
                                          ),
                                        ),
                                        SizedBox(width: 10),
                                        SizedBox(
                                          width: 10,
                                        ),
                                        NarFormLabelWidget(
                                          label: element.configurationType
                                              ?.capitalizeFirst,
                                          fontSize: 13,
                                          fontWeight: '600',
                                          textColor: AppColor.black,
                                        ),
                                      ],
                                    ),
                                    NarButtonWidget(
                                      textHeight: 1,
                                      color: Color(0xffE5E5E5),
                                      hoverColor: Color(0xffE5E5E5),
                                      borderRadius: 7,
                                      fontWeight: '600',
                                      fontSize: 13,
                                      splashColor: Color(0xffE5E5E5),
                                      textColor: AppColor.black,
                                      borderSideColor: Colors.transparent,
                                      text: 'Gestisci',
                                      elevation: 0.0,
                                      height: 53,
                                      minWidth: 192,
                                      buttonPadding: EdgeInsets.only(
                                          left: 15, right: 15, bottom: 0),
                                      onClick: () {
                                        showManageMaterialsPopup(
                                            materialType: element.configurationType ?? "",
                                            elementConfigurationId: element.id ?? "",
                                            newarcAdMaterialConfigurationList: element.newarcAdMaterialConfiguration,
                                            roomConfigurationId: element.roomConfigurationId ?? "",
                                        );
                                      },
                                    ),
                                  ],
                                ),
                                if (element.newarcAdMaterialConfiguration?.isNotEmpty ?? false)
                                  Wrap(
                                    alignment: WrapAlignment
                                        .start, // Ensures proper alignment
                                    spacing:
                                        10, // Adds spacing between elements
                                    runSpacing: 10,
                                    children: element
                                        .newarcAdMaterialConfiguration!
                                        .map<Widget>((material) {
                                      return Container(
                                        padding: const EdgeInsets.all(8.0),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            //Material Image
                                            NarFilePickerWidget(
                                              allowMultiple: false,
                                              filesToDisplayInList: 0,
                                              removeButton: false,
                                              isDownloadable: false,
                                              removeButtonText: 'Elimina',
                                              removeButtonTextColor:
                                                  Color(0xff797979),
                                              uploadButtonPosition: 'back',
                                              showMoreButtonText: '+ espandi',
                                              actionButtonPosition: 'bottom',
                                              displayFormat: 'inline-widget',
                                              containerWidth: 145,
                                              containerHeight: 145,
                                              containerBorderRadius: 8,
                                              borderRadius: 7,
                                              fontSize: 11,
                                              fontWeight: '600',
                                              text: 'Carica',
                                              borderSideColor: Theme.of(context)
                                                  .primaryColor,
                                              hoverColor: Color.fromRGBO(
                                                  133, 133, 133, 1),
                                              allFiles: material.coverImageList,
                                              storageDirectory: "newArcAdMaterialConfiguration/cover/${material.id}",
                                              pageContext: context,
                                              removeExistingOnChange: false,
                                              progressMessage: [''],
                                              notAccent: true,
                                              splashColor: Color(0xffE5E5E5),
                                              height: 35,
                                              buttonWidth: 125,
                                              buttonTextColor: Colors.black,
                                              onUploadCompleted: () {
                                                setState(() {});
                                              },
                                            ),
                                            SizedBox(
                                              width: 10,
                                            ),
                                            NarFormLabelWidget(
                                              label: material.code ?? "",
                                              fontSize: 12,
                                              fontWeight: '600',
                                              textColor: Color(0xff717171),
                                            ),
                                          ],
                                        ),
                                      );
                                    }).toList(),
                                  ),
                                Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    NarFormLabelWidget(
                                      label: "Inserisci mq",
                                      fontSize: 15,
                                      fontWeight: '500',
                                      textColor: AppColor.black,
                                    ),
                                    SizedBox(
                                      width: 10,
                                    ),
                                    SizedBox(
                                      width: 120,
                                      height: 50,
                                      child: CustomTextFormField(
                                        inputFormatters: [
                                          FilteringTextInputFormatter.digitsOnly
                                        ],
                                        isExpanded: false,
                                        controller: element.sizeController,
                                        label: "",
                                        hintText: "",
                                        suffixIcon: SizedBox(
                                          width: 25,
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              NarFormLabelWidget(
                                                label: 'mq',
                                                fontSize: 14,
                                                textColor: Colors.black,
                                              ),
                                              SizedBox(
                                                width: 5,
                                              )
                                            ],
                                          ),
                                        ),
                                        onChangedCallback: (String newValue) async {
                                          setState(() {
                                            element.size = double.tryParse(
                                                    newValue.replaceAll(
                                                        ',', '.')) ??
                                                0.0;
                                          });
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                        SizedBox(height: 10,),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            IconButtonWidget(
                              onTap: () {
                                List<Map<String, dynamic>> newList = [];
                                room.newarcAdElementConfiguration?.forEach((element){
                                  newList.add({'value':element.configurationType,'label':element.configurationType?.capitalizeFirst});
                                });
                                setState(() {
                                  selectedConfigurableList = newList;
                                });
                                showAddRoomPopup(newarcAdRoomConfigurationVal: room,isEdit: true);
                              },
                              iconPadding: EdgeInsets.all(8),
                              isSvgIcon: false,
                              icon: 'assets/icons/edit.png',
                              iconColor: AppColor.greyColor,
                            ),
                            SizedBox(width: 4),
                            IconButtonWidget(
                              iconPadding: EdgeInsets.all(8),
                              onTap: () {
                                setState(() {
                                  newarcAdRoomConfigurationList.remove(room);
                                });
                              },
                              isSvgIcon: false,
                              icon:
                              'assets/icons/trash-process.png',
                              iconColor: AppColor.greyColor,
                            ),
                          ],
                        )
                      ],
                    ),
                  );
                }).toList(),
              ),
            ])),
        SizedBox(height: 15),

        //----Optional Section
        Container(
            padding: EdgeInsets.all(15),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: Color(0xffC8C8C8))),
            child: ListView(shrinkWrap: true, children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  NarFormLabelWidget(
                    label: 'Optional',
                    fontSize: 20,
                    fontWeight: '700',
                    textColor: AppColor.black,
                  ),
                  Row(
                    children: [
                      NarButtonWidget(
                        textHeight: 1,
                        color: Color(0xffE5E5E5),
                        hoverColor: Color(0xffE5E5E5),
                        borderRadius: 7,
                        fontWeight: '600',
                        fontSize: 15,
                        splashColor: Color(0xffE5E5E5),
                        textColor: AppColor.black,
                        borderSideColor: Colors.transparent,
                        text: 'Seleziona optional',
                        elevation: 0.0,
                        height: 53,
                        minWidth: 192,
                        buttonPadding:
                        EdgeInsets.only(left: 15, right: 15, bottom: 0),
                        onClick: () async {
                          selectOptionalPopup();
                        },
                      ),
                      SizedBox(width: 10,),
                      NarButtonWidget(
                        trailingIcon: Icon(
                          Icons.add,
                          color: Theme.of(context).primaryColor,
                        ),
                        textHeight: 1,
                        color: Color(0xffDAEAE3),
                        hoverColor: Color(0xffDAEAE3),
                        borderRadius: 7,
                        fontWeight: '600',
                        fontSize: 15,
                        splashColor: Color(0xffDAEAE3),
                        textColor: Theme.of(context).primaryColor,
                        borderSideColor: Colors.transparent,
                        text: 'Nuovo optional',
                        elevation: 0.0,
                        height: 53,
                        minWidth: 192,
                        buttonPadding:
                            EdgeInsets.only(left: 15, right: 15, bottom: 0),
                        onClick: () async {
                          DocumentReference optionalConfigRef =
                              await FirebaseFirestore.instance
                                  .collection(
                                      appConfig.COLLECT_NEWARCADROOMCONFIGURATION)
                                  .doc();
                          Map<String, dynamic> data = {
                            "id": optionalConfigRef.id,
                            "productName": '',
                            "price": 0.0,
                            "isActive": false,
                            "coverImages": [],
                            "adOptionalCategoryName": '',
                            "adOptionalCategoryId": '',
                            "description": '',
                          };
                          showOptionalPopup(
                              newarcAdOptionalConfiguration:
                                  NewarcAdOptionalConfiguration.fromDocument(data));
                        },
                      ),
                    ],
                  ),
                ],
              ),
              SizedBox(
                height: 20,
              ),
              if (newarcAdOptionalConfigurationList.isNotEmpty)
                ...groupBy(
                    newarcAdOptionalConfigurationList,
                    (NewarcAdOptionalConfiguration item) =>
                        item.adOptionalCategoryName ?? '').entries.map((entry) {
                  final categoryName = entry.key;
                  List<NewarcAdOptionalConfiguration> categoryItems =
                      entry.value;
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      NarFormLabelWidget(
                        label: categoryName,
                        fontSize: 18,
                        fontWeight: '700',
                        textColor: AppColor.black,
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      ListView(
                        shrinkWrap: true,
                        children: categoryItems.map((optional) {
                          return Container(
                            margin: EdgeInsets.only(bottom: 10),
                            padding: EdgeInsets.all(15),
                            decoration: BoxDecoration(
                              color: Color(0xffF1F1F1),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: ExpansionTile(
                              title: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Row(
                                    children: [
                                      Switch(
                                        value: optional.isActive ?? false,
                                        activeThumbColor: AppColor.white,
                                        activeTrackColor: Theme.of(context).primaryColor,
                                        onChanged: (bool value) async {
                                          setState(() {
                                            optional.isActive = value;
                                          });
                                        },
                                      ),
                                      SizedBox(
                                        width: 10,
                                      ),
                                      NarFormLabelWidget(
                                        label: optional.productName ?? "",
                                        fontSize: 15,
                                        fontWeight: '600',
                                        textColor: AppColor.black,
                                      ),
                                    ],
                                  ),
                                  NarFormLabelWidget(
                                    label: "${localCurrencyFormatMain.format(optional.price ?? 0.0)}€",
                                    fontSize: 15,
                                    fontWeight: '600',
                                    textColor: AppColor.black,
                                  )
                                ],
                              ),
                              iconColor: Colors.black,
                              clipBehavior: Clip.none,
                              shape: Border.all(color: Colors.transparent),
                              collapsedShape:
                                  Border.all(color: Colors.transparent),
                              onExpansionChanged: (val) {},
                              initiallyExpanded: true,
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    color: Color(0xffF1F1F1),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  width: double.infinity,
                                  padding: EdgeInsets.all(15),
                                  margin: EdgeInsets.symmetric(vertical: 10),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Flexible(
                                            child: NarFormLabelWidget(
                                              label: optional.description ?? "",
                                              fontSize: 13,
                                              fontWeight: '600',
                                              overflow: TextOverflow.visible,
                                              textColor: Color(0xff6B6B6B),
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(
                                        height: 10,
                                      ),
                                      NarFilePickerWidget(
                                        allowMultiple: false,
                                        filesToDisplayInList: 0,
                                        removeButton: false,
                                        isDownloadable: false,
                                        removeButtonText: 'Elimina',
                                        removeButtonTextColor:
                                            Color(0xff797979),
                                        uploadButtonPosition: 'back',
                                        showMoreButtonText: '+ espandi',
                                        actionButtonPosition: 'bottom',
                                        displayFormat: 'inline-widget',
                                        containerWidth: 90,
                                        containerHeight: 90,
                                        containerBorderRadius: 8,
                                        borderRadius: 7,
                                        fontSize: 11,
                                        fontWeight: '600',
                                        text: 'Carica',
                                        borderSideColor:
                                            Theme.of(context).primaryColor,
                                        hoverColor:
                                            Color.fromRGBO(133, 133, 133, 1),
                                        allFiles: optional.coverImageList,
                                        storageDirectory:
                                            "newArcAdOptionalConfiguration/cover/${optional.id}",
                                        pageContext: context,
                                        removeExistingOnChange: false,
                                        progressMessage: [''],
                                        notAccent: true,
                                        splashColor: Color(0xffE5E5E5),
                                        height: 35,
                                        buttonWidth: 125,
                                        buttonTextColor: Colors.black,
                                        onUploadCompleted: () {
                                          setState(() {});
                                        },
                                      ),
                                      Row(
                                        mainAxisAlignment: MainAxisAlignment.end,
                                        children: [
                                          IconButtonWidget(
                                            onTap: () {
                                              showOptionalPopup(
                                                  newarcAdOptionalConfiguration:
                                                      optional);
                                            },
                                            iconPadding: EdgeInsets.all(8),
                                            isSvgIcon: false,
                                            icon: 'assets/icons/edit.png',
                                            iconColor: AppColor.greyColor,
                                          ),
                                          SizedBox(width: 4),
                                          IconButtonWidget(
                                            iconPadding: EdgeInsets.all(8),
                                            onTap: () {
                                              setState(() {
                                                newarcAdOptionalConfigurationList
                                                    .remove(optional);
                                              });
                                            },
                                            isSvgIcon: false,
                                            icon:
                                                'assets/icons/trash-process.png',
                                            iconColor: AppColor.greyColor,
                                          ),
                                        ],
                                      )
                                    ],
                                  ),
                                )
                              ],
                            ),
                          );
                        }).toList(),
                      ),
                    ],
                  );
                })
            ])),
        SizedBox(height: 15),
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTap: () async {
                  setState(() {
                    formProgressMessage = "Salvataggio in corso...";
                  });
                  try {
                    List<String> roomsData = [];
                    List<String> optionsData = [];

                    for (NewarcAdRoomConfiguration room in newarcAdRoomConfigurationList) {
                      // Step 2: Create a reference for each Room Configuration
                      DocumentReference roomRef = FirebaseFirestore.instance
                          .collection(
                              appConfig.COLLECT_NEWARCADROOMCONFIGURATION)
                          .doc(room.id);

                      List<String> elementsData = [];

                      for (NewarcAdElementConfiguration element
                          in room.newarcAdElementConfiguration ?? []) {
                        // Step 3: Create a reference for each Element Configuration
                        DocumentReference elementRef = FirebaseFirestore
                            .instance
                            .collection(
                                appConfig.COLLECT_NEWARCADELEMENTCONFIGURATION)
                            .doc(element.id);

                        List<String> materialsData = [];

                        for (NewarcAdMaterialConfiguration material in element.newarcAdMaterialConfiguration ?? []) {
                          // Step 4: Create a reference for each Material Configuration
                          DocumentReference materialRef = FirebaseFirestore
                              .instance
                              .collection(appConfig
                                  .COLLECT_NEWARCADMATERIALCONFIGURATION)
                              .doc(material.id);

                          // Save Material Configuration in Firestore
                          await materialRef.set({
                            'materialType': material.materialType,
                            'naMaterialId': material.naMaterialId,
                            'materialManufacturerId': material.materialManufacturerId,
                            'materialCollectionId': material.materialCollectionId,
                            'materialVariantId': material.materialVariantId,
                            'coverImage': material.coverImage,
                            'code': material.code,
                            'elementConfigurationId': element.id,
                            'id': material.id,
                          });

                          // Add material id to the list
                          materialsData.add(material.id ?? "");
                        }

                        // Save Element Configuration in Firestore
                        await elementRef.set({
                          'configurationType': element.configurationType,
                          'size': element.size,
                          'isActive': element.isActive,
                          'roomConfigurationId': room.id, // Link to Room
                          'id': element.id,
                          'newarcAdMaterialConfigurationIDS': materialsData,
                        });

                        // Add element id to the list
                        elementsData.add(element.id ?? "");
                      }

                      // Save Room Configuration in Firestore
                      await roomRef.set({
                        'roomName': room.roomName,
                        'coverImage': room.coverImage,
                        'id': room.id,
                        'newarcAdElementConfigurationIDS': elementsData,
                      });

                      // Add room id to the list
                      roomsData.add(room.id ?? "");
                    }

                    for (NewarcAdOptionalConfiguration option in newarcAdOptionalConfigurationList) {
                      // Step 2: Create a reference for each Options Configuration
                      DocumentReference optionRef = FirebaseFirestore.instance
                          .collection(appConfig.COLLECT_NEWARCADOPTIONALCONFIGURATION)
                          .doc(option.id);

                      // Save Options Configuration in Firestore
                      await optionRef.set({
                        'productName': option.productName,
                        'isActive': option.isActive,
                        'adOptionalCategoryId': option.adOptionalCategoryId,
                        'adOptionalCategoryName': option.adOptionalCategoryName,
                        'description': option.description,
                        'price': option.price,
                        'coverImages': option.coverImages?.map((images) => images).toList(),
                        'id': optionRef.id,
                      });

                      // Add Options to the list
                      optionsData.add(optionRef.id);
                    }

                    DocumentReference adRef = FirebaseFirestore.instance
                        .collection(appConfig.COLLECT_NEWARCADCONFIGURATION)
                        .doc(newarcAdConfiguration?.firebaseId ?? "");

                    // Save Ad Configuration in Firestore
                    await adRef.set({
                      'newarcAdRoomConfigurationIDS': roomsData,
                      'insertTimestamp': DateTime.now().millisecondsSinceEpoch,
                      'modificationTimestamp': DateTime.now().millisecondsSinceEpoch,
                      'isActive': newarcAdConfiguration?.isActive ?? false,
                      'newarcHomesId': widget.property?.firebaseId,
                      'newarcAdOptionalConfigurationIDS': optionsData,
                    });

                    setState(() {
                      formProgressMessage = "Salvato";
                    });
                  } catch (e) {
                    setState(() {
                      formProgressMessage = "Si è verificato un errore.";
                    });
                    log("Error While Adding Ad Configuration -----> ${e.toString()}");
                  }
                },
                child: Container(
                  height: 38,
                  width: 140,
                  padding: const EdgeInsets.symmetric(horizontal: 20.0),
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withOpacity(
                        formProgressMessage == "Salvataggio in corso..."
                            ? 0.5
                            : 1.0),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    "Salva",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 13,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 5),
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            NarFormLabelWidget(
              label: formProgressMessage,
              fontSize: 14,
              fontWeight: '600',
              textColor: AppColor.black,
            ),
          ],
        ),
      ],
    );
  }

  Future<void> showAddRoomPopup({NewarcAdRoomConfiguration? newarcAdRoomConfigurationVal,bool isEdit = false}) async {
    TextEditingController roomNameController = TextEditingController(text: newarcAdRoomConfigurationVal?.roomName ?? "");
    List<String> formErrorMessage = [];
    await showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (__context, _setState) {
            return Center(
                child: BaseNewarcPopup(
              title: isEdit ? "Modifica stanza" : "Aggiungi stanza",
              buttonText: isEdit ? "Modifica" : "Aggiungi",
              formErrorMessage: formErrorMessage,
              onPressed: () async {
                _setState(() {
                  formErrorMessage.clear();
                  formErrorMessage.add("Salvataggio in corso...");
                });
                try {
                  DocumentReference roomRef = await FirebaseFirestore.instance
                      .collection(appConfig.COLLECT_NEWARCADROOMCONFIGURATION)
                      .doc();

                  List<NewarcAdElementConfiguration> newElements = [];


                  await Future.forEach(selectedConfigurableList, (Map<String, dynamic> val) async {

                    if (isEdit && val["value"] != null) {
                      //  Find the matching room that contains the element
                      NewarcAdRoomConfiguration? matchingRoom = newarcAdRoomConfigurationVal;

                      if (matchingRoom?.id != null && matchingRoom?.id != "") {
                        // Find the element inside the room
                        int elementIndex = matchingRoom?.newarcAdElementConfiguration?.indexWhere(
                              (element) => element.configurationType == val["value"],
                        ) ?? -1;

                        if (elementIndex != -1) {
                          // Update existing element
                          NewarcAdElementConfiguration? updatedElement = matchingRoom?.newarcAdElementConfiguration![elementIndex];

                          Map<String, dynamic> data = {
                            "configurationType": val["value"] ?? "",
                            "size": updatedElement?.size,
                            "isActive": updatedElement?.isActive ?? false,
                            "roomConfigurationId": updatedElement?.roomConfigurationId,
                            "id": updatedElement?.id,
                            "newarcAdMaterialConfiguration": updatedElement?.newarcAdMaterialConfiguration
                                ?.map((e) => e.toMap())
                                .toList(),
                          };
                          newElements.add(NewarcAdElementConfiguration.fromDocument(data));
                        } else {
                          DocumentReference elementRef = FirebaseFirestore.instance
                              .collection(appConfig.COLLECT_NEWARCADELEMENTCONFIGURATION)
                              .doc();

                          Map<String, dynamic> data = {
                            "configurationType": val["value"] ?? "",
                            "size": null,
                            "isActive": false,
                            "roomConfigurationId": matchingRoom?.id,
                            "id": elementRef.id,
                          };

                          NewarcAdElementConfiguration newElement = NewarcAdElementConfiguration.fromDocument(data);
                          //matchingRoom?.newarcAdElementConfiguration?.add(newElement);
                          newElements.add(newElement);
                        }
                      }
                    } else {
                      // New element creation (when not in edit mode)
                      DocumentReference elementRef = FirebaseFirestore.instance
                          .collection(appConfig.COLLECT_NEWARCADELEMENTCONFIGURATION)
                          .doc();

                      Map<String, dynamic> data = {
                        "configurationType": val["value"] ?? "",
                        "size": null,
                        "isActive": false,
                        "roomConfigurationId": roomRef.id,
                        "id": elementRef.id
                      };

                      newElements.add(NewarcAdElementConfiguration.fromDocument(data));
                    }
                  });

                  Map<String, dynamic> roomData = {
                    "id": roomRef.id,
                    "roomName": roomNameController.text.trim(),
                    "isActive": false,
                    'coverImage': {
                      "fileName": "",
                      "location": "",
                    },
                    "newarcAdElementConfiguration": newElements.map((e) => e.toMap()).toList(),
                  };

                  _setState(() {
                    formErrorMessage.clear();
                    formErrorMessage.add("Salvato");
                  });
                  setState(() {
                    if (isEdit) {
                      int index = newarcAdRoomConfigurationList.indexWhere((room) => room.id == newarcAdRoomConfigurationVal?.id);
                      if (index != -1) {
                        setState(() {
                          newarcAdRoomConfigurationList[index].roomName = roomNameController.text.trim();
                          newarcAdRoomConfigurationList[index].newarcAdElementConfiguration = newElements.map((e){
                            return e;
                          }).toList();
                        });
                      }
                    } else {
                      setState(() {
                        newarcAdRoomConfigurationList.add(NewarcAdRoomConfiguration.fromDocument(roomData));
                      });
                    }
                  });
                } catch (e,s) {
                  _setState(() {
                    formErrorMessage.clear();
                    formErrorMessage.add("Si è verificato un errore.");
                  });
                  log("Error While Adding new room -----> ${e.toString()}");
                  log("Stack Trace While Adding new room -----> ${s.toString()}");
                }
              },
              buttonColor: Theme.of(context).primaryColor,
              column: Container(
                width: 400,
                child: ListView(
                  shrinkWrap: true,
                  children: [
                    NarSelectBoxWidget(
                      label: "Assegna nome alla stanza",
                      options: List.of(roomConfiguratorList)..sort((a, b) => a.toLowerCase().compareTo(b.toLowerCase())),
                      onChanged: (value) {},
                      controller: roomNameController,
                    ),
                    SizedBox(
                      height: 20,
                    ),
                    NarFormLabelWidget(
                      label: 'Seleziona configurabili',
                      fontSize: 13,
                      fontWeight: '600',
                      textColor: Color(0xff696969),
                    ),
                    SizedBox(
                      height: 5,
                    ),
                    MultiSelectDropdownWidget(
                      options: configurableList,
                      initialValue: selectedConfigurableList,
                      validationType: 'required',
                      parametersValidate: 'Obbligatorio',
                      onChanged: (List<dynamic> selectedValues) {
                        setState(() {
                          List<Map<String, dynamic>> uniqueList = [];

                          for (var item in selectedValues) {
                            if (!uniqueList.any((element) => element["value"] == item["value"])) {
                              uniqueList.add(Map<String, dynamic>.from(item));
                            }
                          }

                          selectedConfigurableList = uniqueList;
                        });
                        _setState(() {});
                      },
                    ),
                    SizedBox(
                      height: 10,
                    ),
                  ],
                ),
              ),
            ));
          });
        });
  }

  Future<void> showManageMaterialsPopup({required String materialType, required String elementConfigurationId, List<NewarcAdMaterialConfiguration>? newarcAdMaterialConfigurationList, required String roomConfigurationId}) async {
    List<String> formErrorMessage = [];

    List<NewarcAdMaterialConfiguration> newNewarcAdMaterialConfigurationList =
        newarcAdMaterialConfigurationList?.map((item) {
          NewarcAdMaterialConfiguration material =
          NewarcAdMaterialConfiguration.fromDocument(item.toMap());

          // Assign Futures (example functions - replace with actual fetching functions)
          material.collectionFuture = fetchNewarcProductCollection(manufacturerId: material.materialManufacturerId ?? "");
          material.naMaterialFuture = fetchNAMaterial(manufacturerId: material.materialManufacturerId ?? "",collectionId: material.materialCollectionId ?? "");

          return material;
        }).toList() ?? [];

    await showDialog(
      context: context,
      builder: (BuildContext _context) {
        return StatefulBuilder(builder: (__context, _setState) {
          return Center(
            child: BaseNewarcPopup(
              title: "Gestisci ${materialType.capitalizeFirst}",
              buttonText: "Conferma",
              formErrorMessage: formErrorMessage,
              onPressed: () async {
                _setState(() {
                  formErrorMessage.clear();
                  formErrorMessage.add("Salvataggio in corso...");
                });

                try {
                  // Update material list inside newarcAdRoomConfigurationList

                  for (NewarcAdRoomConfiguration room
                      in newarcAdRoomConfigurationList) {
                    if (room.id == roomConfigurationId) {
                      for (NewarcAdElementConfiguration element
                          in room.newarcAdElementConfiguration ?? []) {
                        if (element.id == elementConfigurationId &&
                            element.configurationType == materialType) {
                          element.newarcAdMaterialConfiguration =
                              newNewarcAdMaterialConfigurationList;
                        }
                      }
                    }
                  }
                  _setState(() {
                    formErrorMessage.clear();
                    formErrorMessage.add("Salvato");
                  });
                  setState(() {});
                } catch (e) {
                  _setState(() {
                    formErrorMessage.clear();
                    formErrorMessage.add("Si è verificato un errore.");
                  });
                  log("Error While Saving -----> ${e.toString()}");
                }
              },
              buttonColor: Theme.of(context).primaryColor,
              column: SingleChildScrollView(
                child: Container(
                  height: MediaQuery.of(context).size.height * 0.7,
                  width: 650,
                  child: Column(
                    children: [
                      Expanded(
                        child: ListView.builder(
                          shrinkWrap: true,
                          itemCount: newNewarcAdMaterialConfigurationList.length,
                          itemBuilder: (context, index) {
                            return Padding(
                              padding: const EdgeInsets.only(bottom: 10),
                              child: Container(
                                padding: EdgeInsets.all(20),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  border: Border.all(
                                      width: 1, color: Color(0xffD7D7D7)),
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(10)),
                                ),
                                width: 600,
                                child: Column(
                                  children: [
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Column(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            NarFormLabelWidget(
                                              label: 'Carica PNG',
                                              fontSize: 13,
                                              fontWeight: '600',
                                              textColor: Colors.black,
                                            ),
                                            SizedBox(height: 10),
                                            newNewarcAdMaterialConfigurationList[index].coverImageList?.isNotEmpty ?? false
                                                ? NarFilePickerWidget(
                                                    allowMultiple: false,
                                                    filesToDisplayInList: 0,
                                                    allowedExtensions: ['png'],
                                                    removeButton: true,
                                                    isDownloadable: false,
                                                    removeButtonText: 'Elimina',
                                                    removeButtonTextColor:
                                                        Color(0xff797979),
                                                    uploadButtonPosition:
                                                        'back',
                                                    showMoreButtonText:
                                                        '+ espandi',
                                                    actionButtonPosition:
                                                        'bottom',
                                                    displayFormat:
                                                        'inline-widget',
                                                    containerWidth: 145,
                                                    containerHeight: 145,
                                                    containerBorderRadius: 8,
                                                    borderRadius: 7,
                                                    fontSize: 11,
                                                    fontWeight: '600',
                                                    text: 'Carica',
                                                    borderSideColor:
                                                        Theme.of(context)
                                                            .primaryColor,
                                                    hoverColor: Color.fromRGBO(
                                                        133, 133, 133, 1),
                                                    allFiles:
                                                        newNewarcAdMaterialConfigurationList[
                                                                index]
                                                            .coverImageList,
                                                    pageContext: context,
                                                    storageDirectory:
                                                        "newArcAdMaterialConfiguration/cover/${newNewarcAdMaterialConfigurationList[index].id}",
                                                    removeExistingOnChange:
                                                        false,
                                                    progressMessage: [''],
                                                    notAccent: true,
                                                    splashColor:
                                                        Color(0xffE5E5E5),
                                                    height: 35,
                                                    buttonWidth: 125,
                                                    buttonTextColor:
                                                        Colors.black,
                                                    onUploadCompleted: () {
                                                      _setState(() {});
                                                    },
                                                  )
                                                : NarFilePickerWidget(
                                                    allowMultiple: false,
                                                    filesToDisplayInList: 0,
                                                    allowedExtensions: ['png'],
                                                    removeButton: true,
                                                    isDownloadable: false,
                                                    removeButtonText: 'Elimina',
                                                    removeButtonTextColor:
                                                        Color(0xff797979),
                                                    uploadButtonPosition:
                                                        'back',
                                                    showMoreButtonText:
                                                        '+ espandi',
                                                    actionButtonPosition:
                                                        'bottom',
                                                    displayFormat: 'button',
                                                    containerWidth: 65,
                                                    containerHeight: 65,
                                                    containerBorderRadius: 8,
                                                    borderRadius: 7,
                                                    fontSize: 11,
                                                    fontWeight: '600',
                                                    text: 'Carica',
                                                    borderSideColor:
                                                        Theme.of(context)
                                                            .primaryColor,
                                                    hoverColor: Color.fromRGBO(
                                                        133, 133, 133, 1),
                                                    allFiles:
                                                        newNewarcAdMaterialConfigurationList[
                                                                index]
                                                            .coverImageList,
                                                    pageContext: context,
                                                    storageDirectory:
                                                        "newArcAdMaterialConfiguration/cover/${newNewarcAdMaterialConfigurationList[index].id}",
                                                    removeExistingOnChange:
                                                        false,
                                                    progressMessage: [''],
                                                    notAccent: true,
                                                    splashColor:
                                                        Color(0xffE5E5E5),
                                                    height: 35,
                                                    buttonWidth: 125,
                                                    buttonTextColor:
                                                        Colors.black,
                                                    onUploadCompleted: () {
                                                      _setState(() {
                                                        newNewarcAdMaterialConfigurationList[
                                                                    index]
                                                                .coverImage?[
                                                            "location"] = newNewarcAdMaterialConfigurationList[
                                                                        index]
                                                                    .coverImageList
                                                                    ?.isNotEmpty ??
                                                                false
                                                            ? "newArcAdMaterialConfiguration/cover/${newNewarcAdMaterialConfigurationList[index].id}"
                                                            : "";
                                                        newNewarcAdMaterialConfigurationList[
                                                                    index]
                                                                .coverImage?[
                                                            "fileName"] = newNewarcAdMaterialConfigurationList[
                                                                        index]
                                                                    .coverImageList
                                                                    ?.isNotEmpty ??
                                                                false
                                                            ? newNewarcAdMaterialConfigurationList[
                                                                    index]
                                                                .coverImageList![0]
                                                            : "";
                                                      });
                                                    },
                                                  ),
                                          ],
                                        ),
                                        SizedBox(width: 50),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              NarFormLabelWidget(
                                                label: 'Associa il materiale',
                                                fontSize: 13,
                                                fontWeight: '600',
                                                textColor: Colors.black,
                                              ),
                                              SizedBox(height: 10),
                                              //-------- Manufacturer Dropdown
                                              SizedBox(
                                                width: 350,
                                                height: 51,
                                                child: FutureBuilder<List>(
                                                    future: fetchNewarcManufacturer(),
                                                    builder: (context, snapshot) {
                                                      if (snapshot.hasData) {
                                                        return DropdownButtonFormField<
                                                            String>(
                                                          isExpanded: true,
                                                          initialValue: newNewarcAdMaterialConfigurationList[
                                                                          index]
                                                                      .materialManufacturerId
                                                                      ?.isNotEmpty ??
                                                                  false
                                                              ? newNewarcAdMaterialConfigurationList[
                                                                      index]
                                                                  .materialManufacturerId
                                                              : null,
                                                          icon: Padding(
                                                            padding:
                                                                EdgeInsets.only(
                                                                    right: 8),
                                                            child: SvgPicture
                                                                .asset(
                                                              'assets/icons/arrow_down.svg',
                                                              width: 12,
                                                              color: Color(
                                                                  0xff7e7e7e),
                                                            ),
                                                          ),
                                                          style: TextStyle(
                                                            overflow:
                                                                TextOverflow
                                                                    .ellipsis,
                                                            color: Colors.black,
                                                            fontSize: 12.0,
                                                            fontWeight:
                                                                FontWeight.bold,
                                                            fontStyle: FontStyle
                                                                .normal,
                                                          ),
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(8),
                                                          decoration:
                                                              InputDecoration(
                                                            border:
                                                                OutlineInputBorder(
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .all(Radius
                                                                          .circular(
                                                                              8)),
                                                              borderSide:
                                                                  BorderSide(
                                                                color: Color
                                                                    .fromRGBO(
                                                                        227,
                                                                        227,
                                                                        227,
                                                                        1),
                                                                width: 1,
                                                              ),
                                                            ),
                                                            hintStyle:
                                                                TextStyle(
                                                              color:
                                                                  Colors.grey,
                                                              fontSize: 15.0,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w800,
                                                              fontStyle:
                                                                  FontStyle
                                                                      .normal,
                                                              letterSpacing: 0,
                                                            ),
                                                            focusedBorder:
                                                                OutlineInputBorder(
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .all(Radius
                                                                          .circular(
                                                                              8)),
                                                              borderSide:
                                                                  BorderSide(
                                                                color: Color
                                                                    .fromRGBO(
                                                                        227,
                                                                        227,
                                                                        227,
                                                                        1),
                                                                width: 1,
                                                              ),
                                                            ),
                                                            contentPadding:
                                                                EdgeInsets
                                                                    .symmetric(
                                                                        horizontal:
                                                                            12,
                                                                        vertical:
                                                                            8),
                                                            fillColor:
                                                                Colors.white,
                                                          ),
                                                          onChanged: (String?
                                                              value) async {
                                                            if (value != null &&
                                                                value
                                                                    .isNotEmpty) {
                                                              _setState(() {
                                                                newNewarcAdMaterialConfigurationList[
                                                                            index]
                                                                        .materialManufacturerId =
                                                                    value;
                                                                newNewarcAdMaterialConfigurationList[
                                                                        index]
                                                                    .materialCollectionId = "";
                                                                newNewarcAdMaterialConfigurationList[
                                                                        index]
                                                                    .naMaterialId = "";
                                                                newNewarcAdMaterialConfigurationList[
                                                                        index]
                                                                    .materialVariantId = "";
                                                                newNewarcAdMaterialConfigurationList[
                                                                        index]
                                                                    .code = "";
                                                                newNewarcAdMaterialConfigurationList[
                                                                            index]
                                                                        .collectionFuture =
                                                                    Future
                                                                        .value(
                                                                            []);
                                                                newNewarcAdMaterialConfigurationList[
                                                                            index]
                                                                        .naMaterialFuture =
                                                                    Future
                                                                        .value(
                                                                            []);
                                                              });

                                                              final newCollectionData = await fetchNewarcProductCollection(manufacturerId: value);

                                                              // Step 3: Update the state with the fetched data
                                                              _setState(() {
                                                                newNewarcAdMaterialConfigurationList[index].collectionFuture =
                                                                    Future.value(newCollectionData);
                                                              });
                                                            }
                                                          },
                                                          validator: (value) {
                                                            if (value == null ||
                                                                value.isEmpty) {
                                                              return "Required!";
                                                            }
                                                            return null;
                                                          },
                                                          dropdownColor:
                                                              Colors.white,
                                                          items: snapshot.data?.map<
                                                              DropdownMenuItem<
                                                                  String>>((item) {
                                                            return DropdownMenuItem<
                                                                String>(
                                                              value:
                                                                  item['value'],
                                                              child:
                                                                  NarFormLabelWidget(
                                                                label: item[
                                                                    'label']!,
                                                                textColor:
                                                                    Colors
                                                                        .black,
                                                                fontSize: 14,
                                                                fontWeight:
                                                                    '600',
                                                              ),
                                                            );
                                                          }).toList(),
                                                        );
                                                      } else if (snapshot
                                                          .hasError) {
                                                        return Container(
                                                          width: 30,
                                                          height: 30,
                                                          alignment:
                                                              Alignment.center,
                                                          decoration:
                                                              BoxDecoration(
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        100),
                                                            color: const Color
                                                                .fromARGB(255,
                                                                19, 17, 17),
                                                          ),
                                                        );
                                                      }
                                                      return Center(
                                                        child:
                                                            CircularProgressIndicator(
                                                          color:
                                                              Theme.of(context)
                                                                  .primaryColor,
                                                        ),
                                                      );
                                                    }),
                                              ),
                                              SizedBox(height: 10),

                                              //-------- Collection Dropdown
                                              SizedBox(
                                                width: 350,
                                                height: 51,
                                                child: FutureBuilder<List>(
                                                  future: newNewarcAdMaterialConfigurationList[index].collectionFuture,
                                                  builder: (context, snapshot) {
                                                    if (snapshot
                                                            .connectionState ==
                                                        ConnectionState
                                                            .waiting) {
                                                      return Center(
                                                        child:
                                                            CircularProgressIndicator(
                                                          color:
                                                              Theme.of(context)
                                                                  .primaryColor,
                                                        ),
                                                      );
                                                    }

                                                    if (snapshot.hasError) {
                                                      return Center(
                                                        child: Text(
                                                          "Error loading collections",
                                                          style: TextStyle(
                                                              color:
                                                                  Colors.red),
                                                        ),
                                                      );
                                                    }

                                                    List<
                                                            DropdownMenuItem<
                                                                String>>
                                                        dropdownItems = [];
                                                    if (snapshot.hasData &&
                                                        snapshot
                                                            .data!.isNotEmpty) {
                                                      dropdownItems = snapshot
                                                          .data!
                                                          .map<
                                                              DropdownMenuItem<
                                                                  String>>((item) =>
                                                              DropdownMenuItem<
                                                                  String>(
                                                                value: item[
                                                                    'value'],
                                                                child:
                                                                    NarFormLabelWidget(
                                                                  label: item[
                                                                      'label']!,
                                                                  textColor:
                                                                      Colors
                                                                          .black,
                                                                  fontSize: 14,
                                                                  fontWeight:
                                                                      '600',
                                                                ),
                                                              ))
                                                          .toList();
                                                    }
                                                    return DropdownButtonFormField<
                                                        String>(
                                                      isExpanded: true,
                                                      initialValue: newNewarcAdMaterialConfigurationList[
                                                                      index]
                                                                  .materialCollectionId
                                                                  ?.isNotEmpty ??
                                                              false
                                                          ? newNewarcAdMaterialConfigurationList[
                                                                  index]
                                                              .materialCollectionId
                                                          : null,
                                                      icon: Padding(
                                                        padding:
                                                            EdgeInsets.only(
                                                                right: 8),
                                                        child: SvgPicture.asset(
                                                          'assets/icons/arrow_down.svg',
                                                          width: 12,
                                                          color:
                                                              Color(0xff7e7e7e),
                                                        ),
                                                      ),
                                                      style: TextStyle(
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                        color: Colors.black,
                                                        fontSize: 12.0,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        fontStyle:
                                                            FontStyle.normal,
                                                      ),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              8),
                                                      decoration:
                                                          InputDecoration(
                                                        border:
                                                            OutlineInputBorder(
                                                          borderRadius:
                                                              BorderRadius.all(
                                                                  Radius
                                                                      .circular(
                                                                          8)),
                                                          borderSide:
                                                              BorderSide(
                                                            color:
                                                                Color.fromRGBO(
                                                                    227,
                                                                    227,
                                                                    227,
                                                                    1),
                                                            width: 1,
                                                          ),
                                                        ),
                                                        hintStyle: TextStyle(
                                                          color: Colors.grey,
                                                          fontSize: 15.0,
                                                          fontWeight:
                                                              FontWeight.w800,
                                                          fontStyle:
                                                              FontStyle.normal,
                                                          letterSpacing: 0,
                                                        ),
                                                        focusedBorder:
                                                            OutlineInputBorder(
                                                          borderRadius:
                                                              BorderRadius.all(
                                                                  Radius
                                                                      .circular(
                                                                          8)),
                                                          borderSide:
                                                              BorderSide(
                                                            color:
                                                                Color.fromRGBO(
                                                                    227,
                                                                    227,
                                                                    227,
                                                                    1),
                                                            width: 1,
                                                          ),
                                                        ),
                                                        contentPadding:
                                                            EdgeInsets
                                                                .symmetric(
                                                                    horizontal:
                                                                        12,
                                                                    vertical:
                                                                        8),
                                                        fillColor: Colors.white,
                                                      ),
                                                      onChanged: (String?
                                                          value) async {
                                                        if (value != null) {
                                                          _setState(() {
                                                            newNewarcAdMaterialConfigurationList[
                                                                        index]
                                                                    .materialCollectionId =
                                                                value;
                                                            newNewarcAdMaterialConfigurationList[
                                                                    index]
                                                                .naMaterialId = "";
                                                            newNewarcAdMaterialConfigurationList[
                                                                    index]
                                                                .materialVariantId = "";
                                                            newNewarcAdMaterialConfigurationList[
                                                                    index]
                                                                .code = "";
                                                            newNewarcAdMaterialConfigurationList[
                                                                        index]
                                                                    .naMaterialFuture =
                                                                Future.value(
                                                                    []);
                                                          });
                                                        }
                                                        final newNAMaterialData =
                                                            await fetchNAMaterial(
                                                                manufacturerId:
                                                                    newNewarcAdMaterialConfigurationList[index]
                                                                            .materialManufacturerId ??
                                                                        "",
                                                                collectionId:
                                                                    value ??
                                                                        "");

                                                        // Step 3: Update the state with the fetched data
                                                        _setState(() {
                                                          newNewarcAdMaterialConfigurationList[
                                                                      index]
                                                                  .naMaterialFuture =
                                                              Future.value(
                                                                  newNAMaterialData);
                                                        });
                                                      },
                                                      validator: (value) {
                                                        if (value == null ||
                                                            value.isEmpty) {
                                                          return "Required!";
                                                        }
                                                        return null;
                                                      },
                                                      dropdownColor:
                                                          Colors.white,
                                                      items: dropdownItems,
                                                    );
                                                  },
                                                ),
                                              ),
                                              SizedBox(height: 10),

                                              //-------- NAMaterial
                                              SizedBox(
                                                width: 350,
                                                height: 51,
                                                child: FutureBuilder<List>(
                                                  future:
                                                      newNewarcAdMaterialConfigurationList[
                                                              index]
                                                          .naMaterialFuture,
                                                  builder: (context, snapshot) {
                                                    if (snapshot
                                                            .connectionState ==
                                                        ConnectionState
                                                            .waiting) {
                                                      return Center(
                                                        child:
                                                            CircularProgressIndicator(
                                                          color:
                                                              Theme.of(context)
                                                                  .primaryColor,
                                                        ),
                                                      );
                                                    }
                                                    if (snapshot.hasError) {
                                                      return Center(
                                                        child: Text(
                                                          "Error loading materials",
                                                          style: TextStyle(
                                                              color:
                                                                  Colors.red),
                                                        ),
                                                      );
                                                    }
                                                    List<
                                                            DropdownMenuItem<
                                                                String>>
                                                        dropdownItems = [];
                                                    if (snapshot.hasData &&
                                                        snapshot
                                                            .data!.isNotEmpty) {
                                                      dropdownItems = snapshot
                                                          .data!
                                                          .map<
                                                              DropdownMenuItem<
                                                                  String>>((item) =>
                                                              DropdownMenuItem<
                                                                  String>(
                                                                value: item[
                                                                    'value'],
                                                                child:
                                                                    NarFormLabelWidget(
                                                                  label: item[
                                                                      'label']!,
                                                                  textColor:
                                                                      Colors
                                                                          .black,
                                                                  fontSize: 14,
                                                                  fontWeight:
                                                                      '600',
                                                                ),
                                                              ))
                                                          .toList();
                                                    }
                                                    return DropdownButtonFormField<
                                                        String>(
                                                      isExpanded: true,
                                                      initialValue: newNewarcAdMaterialConfigurationList[
                                                                      index]
                                                                  .naMaterialId
                                                                  ?.isNotEmpty ??
                                                              false
                                                          ? "${newNewarcAdMaterialConfigurationList[index].naMaterialId ?? "null"}|${newNewarcAdMaterialConfigurationList[index].materialVariantId}"
                                                          : null,
                                                      icon: Padding(
                                                        padding:
                                                            EdgeInsets.only(
                                                                right: 8),
                                                        child: SvgPicture.asset(
                                                          'assets/icons/arrow_down.svg',
                                                          width: 12,
                                                          color:
                                                              Color(0xff7e7e7e),
                                                        ),
                                                      ),
                                                      style: TextStyle(
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                        color: Colors.black,
                                                        fontSize: 12.0,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        fontStyle:
                                                            FontStyle.normal,
                                                      ),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              8),
                                                      decoration:
                                                          InputDecoration(
                                                        border:
                                                            OutlineInputBorder(
                                                          borderRadius:
                                                              BorderRadius.all(
                                                                  Radius
                                                                      .circular(
                                                                          8)),
                                                          borderSide:
                                                              BorderSide(
                                                            color:
                                                                Color.fromRGBO(
                                                                    227,
                                                                    227,
                                                                    227,
                                                                    1),
                                                            width: 1,
                                                          ),
                                                        ),
                                                        hintStyle: TextStyle(
                                                          color: Colors.grey,
                                                          fontSize: 15.0,
                                                          fontWeight:
                                                              FontWeight.w800,
                                                          fontStyle:
                                                              FontStyle.normal,
                                                          letterSpacing: 0,
                                                        ),
                                                        focusedBorder:
                                                            OutlineInputBorder(
                                                          borderRadius:
                                                              BorderRadius.all(
                                                                  Radius
                                                                      .circular(
                                                                          8)),
                                                          borderSide:
                                                              BorderSide(
                                                            color:
                                                                Color.fromRGBO(
                                                                    227,
                                                                    227,
                                                                    227,
                                                                    1),
                                                            width: 1,
                                                          ),
                                                        ),
                                                        contentPadding:
                                                            EdgeInsets
                                                                .symmetric(
                                                                    horizontal:
                                                                        12,
                                                                    vertical:
                                                                        8),
                                                        fillColor: Colors.white,
                                                      ),
                                                      onChanged:
                                                          (String? value) {
                                                        if (value != null) {
                                                          String?
                                                              selectedLabel =
                                                              snapshot.data!.firstWhere(
                                                                  (item) =>
                                                                      item[
                                                                          'value'] ==
                                                                      value,
                                                                  orElse: () =>
                                                                      {
                                                                        'label':
                                                                            ''
                                                                      })['label'];

                                                          List<String> parts =
                                                              value.split('|');
                                                          String materialId =
                                                              parts[0];
                                                          String variantId =
                                                              parts.length > 1
                                                                  ? parts[1]
                                                                  : "null";

                                                          // Extract code from selectedLabel
                                                          List<String>
                                                              labelParts =
                                                              selectedLabel?.split(
                                                                      ' - ') ??
                                                                  [];
                                                          String prodName =
                                                              labelParts
                                                                      .isNotEmpty
                                                                  ? labelParts[
                                                                      0]
                                                                  : "";
                                                          String code =
                                                              labelParts.length >
                                                                      1
                                                                  ? labelParts[
                                                                      1]
                                                                  : "";

                                                          _setState(() {
                                                            newNewarcAdMaterialConfigurationList[
                                                                        index]
                                                                    .naMaterialId =
                                                                materialId;
                                                            newNewarcAdMaterialConfigurationList[
                                                                        index]
                                                                    .materialVariantId =
                                                                variantId;
                                                            newNewarcAdMaterialConfigurationList[
                                                                    index]
                                                                .code = code;
                                                          });
                                                        }
                                                      },
                                                      validator: (value) {
                                                        if (value == null ||
                                                            value.isEmpty) {
                                                          return "Required!";
                                                        }
                                                        return null;
                                                      },
                                                      dropdownColor:
                                                          Colors.white,
                                                      items: dropdownItems,
                                                    );
                                                  },
                                                ),
                                              ),
                                            ],
                                          ),
                                        )
                                      ],
                                    ),
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        MouseRegion(
                                          cursor: SystemMouseCursors.click,
                                          child: GestureDetector(
                                            onTap: () {
                                              _setState(() {
                                                newNewarcAdMaterialConfigurationList
                                                    .removeAt(index);
                                              });
                                            },
                                            child: Row(
                                              children: [
                                                NarFormLabelWidget(
                                                  label: 'Elimina',
                                                  textColor: Color(0xffB5B4B4),
                                                  fontSize: 13,
                                                  textDecoration:
                                                      TextDecoration.underline,
                                                  fontWeight: '600',
                                                ),
                                                SizedBox(width: 5),
                                                SvgPicture.asset(
                                                  "assets/icons/trash.svg",
                                                  color: Color(0xffB5B4B4),
                                                  height: 20,
                                                ),
                                              ],
                                            ),
                                          ),
                                        )
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      NarButtonWidget(
                        trailingIcon: Icon(Icons.add, color: Color(0xff696969)),
                        textHeight: 1,
                        color: Color(0xffE8E8E8),
                        hoverColor: Color(0xffE8E8E8),
                        borderRadius: 7,
                        fontWeight: '600',
                        fontSize: 15,
                        splashColor: Color(0xffE8E8E8),
                        textColor: Color(0xff696969),
                        borderSideColor: Colors.transparent,
                        text: 'Aggiungi',
                        elevation: 0.0,
                        height: 53,
                        minWidth: 192,
                        buttonPadding: EdgeInsets.all(15),
                        onClick: () async {
                          DocumentReference docRefNewarchAdElement =
                              await FirebaseFirestore.instance
                                  .collection(appConfig
                                      .COLLECT_NEWARCADMATERIALCONFIGURATION)
                                  .doc();
                          String adMaterialConfigFirebaseId =
                              docRefNewarchAdElement.id;
                          Map<String, dynamic> data = {
                            'materialType': materialType,
                            'naMaterialId': "",
                            'materialManufacturerId': "",
                            'materialCollectionId': "",
                            'materialVariantId': "",
                            'coverImage': {
                              "fileName": "",
                              "location": "",
                            },
                            'code': "",
                            'elementConfigurationId': elementConfigurationId,
                            'id': adMaterialConfigFirebaseId,
                          };
                          _setState(() {
                            newNewarcAdMaterialConfigurationList.add(
                                NewarcAdMaterialConfiguration.fromDocument(
                                    data));
                          });
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        });
      },
    );
  }

  Future<List> fetchNewarcManufacturer() async {
    try {
      List<NewarcMaterialManufacturer> _newarcManufacturer = [];
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance
          .collection(appConfig.COLLECT_NEWARCMATERIALMANUFACTURER);

      collectionSnapshot = await collectionSnapshotQuery
          .orderBy('insertTimestamp', descending: true)
          .get();

      if (collectionSnapshot.docs.length > 0) {
        for (var element in collectionSnapshot.docs) {
          try {
            NewarcMaterialManufacturer _tmp =
                NewarcMaterialManufacturer.fromDocument(
                    element.data(), element.id);
            _newarcManufacturer.add(_tmp);
          } catch (e) {
            print("ERROR fetchNewarcManufacturer ---> $e");
          }
        }
      }

      // if(mounted) {
      //   setState(() {
      //     controller.newarcManufacturer = _newarcManufacturer;
      //   });
      // };

      if (_newarcManufacturer.length > 0) {
        return _newarcManufacturer.map((e) {
          return {'value': e.firebaseId, 'label': e.name};
        }).toList();
      } else {
        return [
          {'value': '', 'label': ''}
        ];
      }
    } catch (e, s) {
      print('error: $e');
      print(s);
      return [
        {'value': '', 'label': ''}
      ];
    }
  }

  Future<List> fetchNewarcOptionalCategory() async {
    try {
      List<NewarcAdOptionalCategoryConfiguration>
          _newarcAdOptionalCategoryConfiguration = [];
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance
          .collection(appConfig.COLLECT_NEWARCADOPTIONALCATEGORYCONFIGURATION);

      collectionSnapshot = await collectionSnapshotQuery
          .orderBy('insertTimestamp', descending: true)
          .get();

      if (collectionSnapshot.docs.length > 0) {
        for (var element in collectionSnapshot.docs) {
          try {
            NewarcAdOptionalCategoryConfiguration _tmp =
                NewarcAdOptionalCategoryConfiguration.fromDocument(
                    element.data());
            _newarcAdOptionalCategoryConfiguration.add(_tmp);
          } catch (e) {
            print("ERROR fetchNewarcOptionalCategory ---> $e");
          }
        }
      }
      if (_newarcAdOptionalCategoryConfiguration.length > 0) {
        return _newarcAdOptionalCategoryConfiguration.map((e) {
          return {'value': e.id, 'label': e.name};
        }).toList();
      } else {
        return [
          {'value': '', 'label': ''}
        ];
      }
    } catch (e, s) {
      print('error: $e');
      print(s);
      return [
        {'value': '', 'label': ''}
      ];
    }
  }

  Future<List> fetchNewarcOptionalProduct({required String categoryId}) async {
    try {
      List<NewarcAdOptionalConfiguration> _newarcAdOptionalConfiguration = [];
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance
          .collection(appConfig.COLLECT_NEWARCADOPTIONALCONFIGURATION);


      collectionSnapshot = await collectionSnapshotQuery
          .where('adOptionalCategoryId',isEqualTo: categoryId)
          .get();

      if (collectionSnapshot.docs.length > 0) {
        for (var element in collectionSnapshot.docs) {
          try {
            NewarcAdOptionalConfiguration _tmp = NewarcAdOptionalConfiguration.fromDocument(element.data());
            _newarcAdOptionalConfiguration.add(_tmp);
          } catch (e) {
            print("ERROR fetchNewarcOptionalProduct ---> $e");
          }
        }
      }
      setState(() {
        newarcAdOptionalConfigurationListForDropdown = _newarcAdOptionalConfiguration;
      });
      if (_newarcAdOptionalConfiguration.length > 0) {
        return _newarcAdOptionalConfiguration.map((e) {
          return {'value': e.id, 'label': e.productName};
        }).toList();
      } else {
        return [
          {'value': '', 'label': ''}
        ];
      }
    } catch (e, s) {
      print('error: $e');
      print(s);
      return [
        {'value': '', 'label': ''}
      ];
    }
  }

  Future<List> fetchNewarcProductCollection({required String manufacturerId}) async {
    try {
      List<NewarcMaterialManufacturerCollection> _newarcProductCollection = [];
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance
          .collection(appConfig.COLLECT_NEWARCMATERIALMANUFACTURERCOLLECTION);

      collectionSnapshot = await collectionSnapshotQuery
          .orderBy('insertTimestamp', descending: true)
          .where('newarcManufacturerId', isEqualTo: manufacturerId)
          .get();

      if (collectionSnapshot.docs.length > 0) {
        for (var element in collectionSnapshot.docs) {
          try {
            NewarcMaterialManufacturerCollection _tmp =
                NewarcMaterialManufacturerCollection.fromDocument(
                    element.data(), element.id);
            _newarcProductCollection.add(_tmp);
          } catch (e) {
            print("ERROR fetchNewarcProductCollection Pavimenti ---> $e");
          }
        }
      }

      // setState(() {
      //   controller.newarcProductCollection = _newarcProductCollection;
      // });

      if (_newarcProductCollection.length > 0) {
        return _newarcProductCollection.map((e) {
          return {'value': e.firebaseId, 'label': e.name};
        }).toList();
      } else {
        return [];
      }
    } catch (e, s) {
      print('error: $e');
      print(s);
      return [];
    }
  }

  Future<List<Map>> fetchNAMaterial({required String manufacturerId, required String collectionId}) async {
    try {
      List<NAMaterial> _newarcNAMaterial = [];
      List<Map> result = [];

      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;

      // Fetch NAMaterial based on manufacturerId and collectionId
      collectionSnapshot = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_NEWARCHMATERIAL)
          .where('newarcManufacturerID', isEqualTo: manufacturerId)
          .where('newarcProductCollectionID', isEqualTo: collectionId)
          .orderBy('insertTimestamp', descending: true)
          .get();

      if (collectionSnapshot.docs.isNotEmpty) {
        for (var element in collectionSnapshot.docs) {
          try {
            NAMaterial material =
                NAMaterial.fromDocument(element.data(), element.id);

            // Fetch material variants
            QuerySnapshot<Map<String, dynamic>> materialVariantSnapshot =
                await FirebaseFirestore.instance
                    .collection(appConfig.COLLECT_NEWARCMATERIALVARIANT)
                    .where('naMaterialId', isEqualTo: material.firebaseId)
                    .orderBy('insertTimestamp', descending: true)
                    .get();

            List<NewarcMaterialVariant> variants = materialVariantSnapshot.docs
                .map((e) => NewarcMaterialVariant.fromDocument(e.data(), e.id))
                .toList();

            material.newarcMaterialVariants = variants;
            _newarcNAMaterial.add(material);
          } catch (e) {
            print("ERROR fetchNAMaterial ---> $e");
          }
        }
      }

      // Prepare the final result list
      for (var material in _newarcNAMaterial) {
        if (material.newarcMaterialVariants != null &&
            material.newarcMaterialVariants!.isNotEmpty) {
          for (var variant in material.newarcMaterialVariants!) {
            result.add({
              'value':
                  '${material.firebaseId ?? 'null'}|${variant.firebaseId ?? 'null'}',
              'label':
                  "${material.name} - ${variant.code ?? material.code ?? ''}",
            });
          }
        } else {
          result.add({
            'value': '${material.firebaseId ?? "null"}|${'null'}',
            'label': "${material.name} - ${material.code ?? ''}",
          });
        }
      }
      return result;
    } catch (e, s) {
      print('Error in fetchNAMaterial: $e');
      print(s);
      return [];
    }
  }

  Future<void> showOptionalPopup({required NewarcAdOptionalConfiguration?newarcAdOptionalConfiguration}) async {
    List<String> formErrorMessage = [];

    NewarcAdOptionalConfiguration newNewarcAdOptionalConfiguration =
        newarcAdOptionalConfiguration ?? NewarcAdOptionalConfiguration.empty();

    TextEditingController productNameController = TextEditingController(
        text: newNewarcAdOptionalConfiguration.productName ?? "");
    TextEditingController descriptionController = TextEditingController(
        text: newNewarcAdOptionalConfiguration.description ?? "");
    TextEditingController priceController = TextEditingController(
        text: newNewarcAdOptionalConfiguration.price != 0.0
            ? newNewarcAdOptionalConfiguration.price.toString()
            : "");
    TextEditingController categoryController = TextEditingController(
        text: newNewarcAdOptionalConfiguration.adOptionalCategoryId ?? "");

    double price = newNewarcAdOptionalConfiguration.price ?? 0.0;

    Future<void>? fetchNewarcOptinalCategoryFuture;

    fetchNewarcOptinalCategoryFuture ?? fetchNewarcOptionalCategory();

    await showDialog(
      context: context,
      builder: (BuildContext _context) {
        return StatefulBuilder(builder: (__context, _setState) {
          void refreshNewarcOptinalCategoryFuture() {
            _setState(() {
              fetchNewarcOptinalCategoryFuture ?? fetchNewarcOptionalCategory();
            });
          }

          return Center(
            child: BaseNewarcPopup(
              title: "Nuovo optional",
              buttonText: "Aggiungi",
              formErrorMessage: formErrorMessage,
              onPressed: () async {
                _setState(() {
                  formErrorMessage.clear();
                  formErrorMessage.add("Salvataggio in corso...");
                });

                try {
                  newNewarcAdOptionalConfiguration.price = price;
                  newNewarcAdOptionalConfiguration.productName = productNameController.text.trim();
                  newNewarcAdOptionalConfiguration.description = descriptionController.text.trim();

                  if (newNewarcAdOptionalConfiguration.coverImageList?.isNotEmpty ?? false) {
                    newNewarcAdOptionalConfiguration.coverImages = newNewarcAdOptionalConfiguration.coverImageList!.map((file) {
                      return {
                        "fileName": file,
                        "location": "newArcAdOptionalConfiguration/cover/${newNewarcAdOptionalConfiguration.id}"
                      };
                    }).toList();
                  } else {
                    newNewarcAdOptionalConfiguration.coverImages = [];
                    newNewarcAdOptionalConfiguration.coverImageList = [];
                  }


                  int index = newarcAdOptionalConfigurationList.indexWhere((element) =>
                  element.id == newNewarcAdOptionalConfiguration.id);

                  setState(() {
                    if (index != -1) {
                      // Update existing
                      newarcAdOptionalConfigurationList[index] = newNewarcAdOptionalConfiguration;

                    } else {
                      // Add new
                      newarcAdOptionalConfigurationList.add(newNewarcAdOptionalConfiguration);
                    }
                  });
                  _setState(() {
                    formErrorMessage.clear();
                    formErrorMessage.add("Salvato");
                  });
                  setState(() {});
                } catch (e) {
                  _setState(() {
                    formErrorMessage.clear();
                    formErrorMessage.add("Si è verificato un errore.");
                  });
                  log("Error While Saving -----> ${e.toString()}");
                }
              },
              buttonColor: Theme.of(context).primaryColor,
              column: Container(
                height: MediaQuery.of(context).size.height * 0.6,
                width: 650,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        //-------- Category Dropdown
                        Flexible(
                          flex: 1,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SizedBox(
                                width: 250,
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    NarFormLabelWidget(
                                      label: "Seleziona categoria",
                                      fontSize: 13,
                                      fontWeight: '600',
                                      textColor: AppColor.greyColor,
                                    ),
                                    NarLinkWidget(
                                      fontSize: 12,
                                      fontWeight: '600',
                                      textColor: Theme.of(context).primaryColor,
                                      text: 'Nuova categoria',
                                      textDecoration: TextDecoration.underline,
                                      onClick: () {
                                        showAddOptionalCategoryPopup(
                                            onCategoryAdded:
                                                refreshNewarcOptinalCategoryFuture);
                                      },
                                    )
                                  ],
                                ),
                              ),
                              SizedBox(height: 8,),
                              SizedBox(
                                height: 50,
                                child: FutureBuilder<List>(
                                    future: fetchNewarcOptionalCategory(),
                                    builder: (context, snapshot) {
                                      if (snapshot.hasData) {
                                        return DropdownButtonFormField<String>(
                                          isExpanded: true,
                                          initialValue: (newNewarcAdOptionalConfiguration
                                                      .adOptionalCategoryId
                                                      ?.isNotEmpty ??
                                                  false)
                                              ? newNewarcAdOptionalConfiguration
                                                  .adOptionalCategoryId
                                              : null,
                                          icon: Padding(
                                            padding: EdgeInsets.only(right: 8),
                                            child: SvgPicture.asset(
                                              'assets/icons/arrow_down.svg',
                                              width: 12,
                                              color: Color(0xff7e7e7e),
                                            ),
                                          ),
                                          style: TextStyle(
                                            overflow: TextOverflow.ellipsis,
                                            color: Colors.black,
                                            fontSize: 12.0,
                                            fontWeight: FontWeight.bold,
                                            fontStyle: FontStyle.normal,
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          decoration: InputDecoration(
                                            border: OutlineInputBorder(
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(8)),
                                              borderSide: BorderSide(
                                                color: Color.fromRGBO(
                                                    227, 227, 227, 1),
                                                width: 1,
                                              ),
                                            ),
                                            hintStyle: TextStyle(
                                              color: Colors.grey,
                                              fontSize: 15.0,
                                              fontWeight: FontWeight.w800,
                                              fontStyle: FontStyle.normal,
                                              letterSpacing: 0,
                                            ),
                                            focusedBorder: OutlineInputBorder(
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(8)),
                                              borderSide: BorderSide(
                                                color: Color.fromRGBO(
                                                    227, 227, 227, 1),
                                                width: 1,
                                              ),
                                            ),
                                            contentPadding:
                                                EdgeInsets.symmetric(
                                                    horizontal: 12,
                                                    vertical: 8),
                                            fillColor: Colors.white,
                                          ),
                                          onChanged: (String? value) async {
                                            if (value != null &&
                                                value.isNotEmpty) {
                                              var selectedItem =
                                                  snapshot.data!.firstWhere(
                                                (item) =>
                                                    item['value'] == value,
                                                orElse: () =>
                                                    {"label": "", "value": ""},
                                              );
                                              _setState(() {
                                                categoryController.text = value;
                                                newNewarcAdOptionalConfiguration
                                                        .adOptionalCategoryId =
                                                    value;
                                                newNewarcAdOptionalConfiguration
                                                        .adOptionalCategoryName =
                                                    selectedItem['label'] ?? "";
                                              });
                                            }
                                          },
                                          validator: (value) {
                                            if (value == null ||
                                                value.isEmpty) {
                                              return "Required!";
                                            }
                                            return null;
                                          },
                                          dropdownColor: Colors.white,
                                          items: snapshot.data
                                              ?.map<DropdownMenuItem<String>>(
                                                  (item) {
                                            return DropdownMenuItem<String>(
                                              value: item['value'],
                                              child: NarFormLabelWidget(
                                                label: item['label']!,
                                                textColor: Colors.black,
                                                fontSize: 14,
                                                fontWeight: '600',
                                              ),
                                            );
                                          }).toList(),
                                        );
                                      } else if (snapshot.hasError) {
                                        return Container(
                                          width: 30,
                                          height: 30,
                                          alignment: Alignment.center,
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(100),
                                            color: const Color.fromARGB(
                                                255, 19, 17, 17),
                                          ),
                                        );
                                      }
                                      return Center(
                                        child: CircularProgressIndicator(
                                          color: Theme.of(context).primaryColor,
                                        ),
                                      );
                                    }),
                              ),

                            ],
                          ),
                        ),

                        SizedBox(
                          width: 20,
                        ),

                        //-------- Product Name
                        Flexible(
                          flex: 1,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SizedBox(
                                width: 250,
                                height: 36,
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    NarFormLabelWidget(
                                      label: "Nome prodotto",
                                      fontSize: 13,
                                      fontWeight: '600',
                                      textColor: AppColor.greyColor,
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                height: 8,
                              ),
                              SizedBox(
                                height: 50,
                                child: CustomTextFormField(
                                  isExpanded: false,
                                  controller: productNameController,
                                  label: "",
                                  hintText: "",
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return "Required!";
                                    }
                                    return null;
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: 15),

                    SizedBox(
                      width: double.infinity,
                      child: CustomTextFormField(
                        isExpanded: false,
                        minLines: 4,
                        controller: descriptionController,
                        label: "Descrizione prodotto",
                        hintText: "",
                      ),
                    ),

                    SizedBox(height: 15),

                    //------ Price
                    SizedBox(
                      width: 300,
                      height: 75,
                      child: CustomTextFormField(
                        isExpanded: false,
                        label: "Prezzo",
                        suffixIcon: Container(
                          width: 50,
                          padding: const EdgeInsets.only(right: 5),
                          child: Align(
                              alignment: Alignment.centerRight,
                              child: NarFormLabelWidget(
                                label: "€",
                                textColor: AppColor.greyColor,
                                fontWeight: "500",
                              )),
                        ),
                        isMoney: true,
                        validator: (value) {
                          if (value == '') {
                            return 'Required!';
                          }
                          return null;
                        },
                        onChangedCallback: (String value) {
                          double priceOnchange = double.tryParse(value
                                  .replaceAll('.', '')
                                  .replaceAll(',', '.')
                                  .toString()) ??
                              0.0;
                          _setState(() {
                            price = priceOnchange;
                          });
                        },
                        controller: priceController,
                      ),
                    ),

                    SizedBox(height: 15),

                    //------- Image
                    NarFormLabelWidget(
                      label: 'Immagini',
                      fontSize: 13,
                      fontWeight: '600',
                      textColor: Colors.black,
                    ),
                    SizedBox(height: 10),
                    Container(
                      padding: EdgeInsets.all(15.0),
                      width: double.infinity,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10.0),
                          border: Border.all(color: Color(0xffDBDBDB))),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: NarFilePickerWidget(
                              allowMultiple: true,
                              filesToDisplayInList: 0,
                              removeButton: true,
                              isDownloadable: false,
                              removeButtonText: 'Elimina',
                              removeButtonTextColor: Color(0xff797979),
                              uploadButtonPosition: 'back',
                              showMoreButtonText: '+ espandi',
                              actionButtonPosition: 'bottom',
                              displayFormat: 'inline-widget',
                              containerWidth: 65,
                              containerHeight: 65,
                              containerBorderRadius: 8,
                              borderRadius: 7,
                              fontSize: 11,
                              fontWeight: '600',
                              text: 'Carica',
                              borderSideColor: Theme.of(context).primaryColor,
                              hoverColor: Color.fromRGBO(133, 133, 133, 1),
                              allFiles: newNewarcAdOptionalConfiguration.coverImageList,
                              pageContext: context,
                              storageDirectory: "newArcAdOptionalConfiguration/cover/${newNewarcAdOptionalConfiguration.id}",
                              removeExistingOnChange: false,
                              progressMessage: [''],
                              notAccent: true,
                              splashColor: Color(0xffE5E5E5),
                              height: 35,
                              buttonWidth: 125,
                              buttonTextColor: Colors.black,
                              onUploadCompleted: () {
                                _setState(() {});
                              },
                            ),
                          ),
                          NarFilePickerWidget(
                            allowMultiple: true,
                            filesToDisplayInList: 0,
                            removeButton: true,
                            isDownloadable: false,
                            removeButtonText: 'Elimina',
                            removeButtonTextColor: Color(0xff797979),
                            uploadButtonPosition: 'back',
                            showMoreButtonText: '+ espandi',
                            actionButtonPosition: 'bottom',
                            displayFormat: 'inline-button',
                            containerWidth: 65,
                            containerHeight: 65,
                            containerBorderRadius: 8,
                            borderRadius: 7,
                            fontSize: 11,
                            fontWeight: '600',
                            text: 'Carica',
                            borderSideColor: Theme.of(context).primaryColor,
                            hoverColor: Color.fromRGBO(133, 133, 133, 1),
                            allFiles: newNewarcAdOptionalConfiguration.coverImageList,
                            pageContext: context,
                            storageDirectory: "newArcAdOptionalConfiguration/cover/${newNewarcAdOptionalConfiguration.id}",
                            removeExistingOnChange: false,
                            progressMessage: [''],
                            notAccent: true,
                            splashColor: Color(0xffE5E5E5),
                            height: 35,
                            buttonWidth: 125,
                            buttonTextColor: Colors.black,
                            onUploadCompleted: () {
                              _setState(() {});
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        });
      },
    );
  }

  Future<void> showAddOptionalCategoryPopup({required VoidCallback onCategoryAdded}) async {
    TextEditingController nameController = TextEditingController();
    List<String> formErrorMessage = [];
    await showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (__context, _setState) {
            return Center(
                child: BaseNewarcPopup(
              title: "Nuova categoria",
              buttonText: "Aggiungi",
              formErrorMessage: formErrorMessage,
              onPressed: () async {
                _setState(() {
                  formErrorMessage.clear();
                  formErrorMessage.add("Salvataggio in corso...");
                });
                try {
                  DocumentReference categoryRef = await FirebaseFirestore
                      .instance
                      .collection(appConfig
                          .COLLECT_NEWARCADOPTIONALCATEGORYCONFIGURATION)
                      .doc();

                  Map<String, dynamic> data = {
                    "name": nameController.text.trim(),
                    "id": categoryRef.id,
                    "insertTimestamp": DateTime.now().millisecondsSinceEpoch,
                  };

                  await categoryRef.set(data);

                  onCategoryAdded();

                  _setState(() {
                    formErrorMessage.clear();
                    formErrorMessage.add("Salvato");
                  });
                } catch (e) {
                  _setState(() {
                    formErrorMessage.clear();
                    formErrorMessage.add("Si è verificato un errore.");
                  });
                  log("Error While adding new Optional Category -----> ${e.toString()}");
                }
              },
              buttonColor: Theme.of(context).primaryColor,
              column: Container(
                width: 400,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: 250,
                      height: 75,
                      child: CustomTextFormField(
                        isExpanded: false,
                        controller: nameController,
                        label: "categoria noma",
                        hintText: "",
                      ),
                    ),
                    SizedBox(
                      height: 20,
                    ),
                  ],
                ),
              ),
            ));
          });
        });
  }

  Future<void> selectOptionalPopup() async {
    List<String> formErrorMessage = [];


    TextEditingController optionalCategoryController = TextEditingController();
    TextEditingController optionalProductController = TextEditingController();

    Future<List>? fetchNewarcOptinalFutureByCategory;


    await showDialog(
      context: context,
      builder: (BuildContext _context) {
        return StatefulBuilder(builder: (__context, _setState) {

          return Center(
            child: BaseNewarcPopup(
              title: "Seleziona optional",
              buttonText: "Aggiungi",
              formErrorMessage: formErrorMessage,
              onPressed: () async {
                _setState(() {
                  formErrorMessage.clear();
                  formErrorMessage.add("Salvataggio in corso...");
                });

                try {

                  NewarcAdOptionalConfiguration newNewarcAdOptionalConfiguration = newarcAdOptionalConfigurationListForDropdown.firstWhere(
                        (element) => element.id == optionalProductController.text,
                  );


                  if ((newNewarcAdOptionalConfiguration.id?.isNotEmpty ?? false) && !newarcAdOptionalConfigurationList.any((item) => item.id == newNewarcAdOptionalConfiguration.id)) {
                    newarcAdOptionalConfigurationList.add(newNewarcAdOptionalConfiguration);
                  }

                  _setState(() {
                    formErrorMessage.clear();
                    formErrorMessage.add("Salvato");
                  });
                  setState(() {});
                } catch (e) {
                  _setState(() {
                    formErrorMessage.clear();
                    formErrorMessage.add("Si è verificato un errore.");
                  });
                  log("Error While Saving -----> ${e.toString()}");
                }
              },
              buttonColor: Theme.of(context).primaryColor,
              column: Container(
                width: 500,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(
                          width: 350,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              NarFormLabelWidget(
                                label: "Seleziona categoria",
                                fontSize: 13,
                                fontWeight: '600',
                                textColor: AppColor.greyColor,
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 8,),
                        SizedBox(
                          height: 50,
                          child: FutureBuilder<List>(
                              future: fetchNewarcOptionalCategory(),
                              builder: (context, snapshot) {
                                if (snapshot.hasData) {
                                  return DropdownButtonFormField<String>(
                                    isExpanded: true,
                                    initialValue: (optionalCategoryController.text.isNotEmpty)
                                        ? optionalCategoryController.text
                                        : null,
                                    icon: Padding(
                                      padding: EdgeInsets.only(right: 8),
                                      child: SvgPicture.asset(
                                        'assets/icons/arrow_down.svg',
                                        width: 12,
                                        color: Color(0xff7e7e7e),
                                      ),
                                    ),
                                    style: TextStyle(
                                      overflow: TextOverflow.ellipsis,
                                      color: Colors.black,
                                      fontSize: 12.0,
                                      fontWeight: FontWeight.bold,
                                      fontStyle: FontStyle.normal,
                                    ),
                                    borderRadius:
                                    BorderRadius.circular(8),
                                    decoration: InputDecoration(
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(8)),
                                        borderSide: BorderSide(
                                          color: Color.fromRGBO(
                                              227, 227, 227, 1),
                                          width: 1,
                                        ),
                                      ),
                                      hintStyle: TextStyle(
                                        color: Colors.grey,
                                        fontSize: 15.0,
                                        fontWeight: FontWeight.w800,
                                        fontStyle: FontStyle.normal,
                                        letterSpacing: 0,
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(8)),
                                        borderSide: BorderSide(
                                          color: Color.fromRGBO(
                                              227, 227, 227, 1),
                                          width: 1,
                                        ),
                                      ),
                                      contentPadding:
                                      EdgeInsets.symmetric(
                                          horizontal: 12,
                                          vertical: 8),
                                      fillColor: Colors.white,
                                    ),
                                    onChanged: (String? value) async {
                                      if (value != null &&
                                          value.isNotEmpty) {
                                        var selectedItem =
                                        snapshot.data!.firstWhere((item) =>
                                        item['value'] == value,
                                          orElse: () => {"label": "", "value": ""},
                                        );
                                        _setState(() {
                                          optionalCategoryController.text = value;
                                        });

                                        final newCollectionData = await fetchNewarcOptionalProduct(categoryId: value);

                                        // Step 3: Update the state with the fetched data
                                        _setState(() {
                                          fetchNewarcOptinalFutureByCategory = Future.value(newCollectionData);
                                        });
                                      }
                                    },
                                    validator: (value) {
                                      if (value == null ||
                                          value.isEmpty) {
                                        return "Required!";
                                      }
                                      return null;
                                    },
                                    dropdownColor: Colors.white,
                                    items: snapshot.data
                                        ?.map<DropdownMenuItem<String>>(
                                            (item) {
                                          return DropdownMenuItem<String>(
                                            value: item['value'],
                                            child: NarFormLabelWidget(
                                              label: item['label']!,
                                              textColor: Colors.black,
                                              fontSize: 14,
                                              fontWeight: '600',
                                            ),
                                          );
                                        }).toList(),
                                  );
                                } else if (snapshot.hasError) {
                                  return Container(
                                    width: 30,
                                    height: 30,
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      borderRadius:
                                      BorderRadius.circular(100),
                                      color: const Color.fromARGB(
                                          255, 19, 17, 17),
                                    ),
                                  );
                                }
                                return Center(
                                  child: CircularProgressIndicator(
                                    color: Theme.of(context).primaryColor,
                                  ),
                                );
                              }),
                        ),

                      ],
                    ),


                    SizedBox(height: 15),

                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(
                          width: 350,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              NarFormLabelWidget(
                                label: "Seleziona prodotto",
                                fontSize: 13,
                                fontWeight: '600',
                                textColor: AppColor.greyColor,
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 8,),

                        SizedBox(
                          height: 51,
                          child: FutureBuilder<List>(
                            future: fetchNewarcOptinalFutureByCategory ?? null,
                            builder: (context, snapshot) {
                              if (snapshot.connectionState == ConnectionState.waiting) {
                                return Center(
                                  child:
                                  CircularProgressIndicator(
                                    color:
                                    Theme.of(context)
                                        .primaryColor,
                                  ),
                                );
                              }

                              if (snapshot.hasError) {
                                return Center(
                                  child: Text(
                                    "Error loading product",
                                    style: TextStyle(
                                        color:
                                        Colors.red),
                                  ),
                                );
                              }

                              List<DropdownMenuItem<String>>dropdownItems = [];
                              if (snapshot.hasData && snapshot.data!.isNotEmpty) {
                                dropdownItems = snapshot.data!.map<DropdownMenuItem<String>>((item) =>
                                    DropdownMenuItem<
                                        String>(value: item['value'], child:
                                      NarFormLabelWidget(
                                        label: item['label']!,
                                        textColor:
                                        Colors.black,
                                        fontSize: 14,
                                        fontWeight:
                                        '600',
                                      ),
                                    ))
                                    .toList();
                              }
                              return DropdownButtonFormField<
                                  String>(
                                isExpanded: true,
                                initialValue: optionalProductController.text.isNotEmpty
                                    ? optionalProductController.text
                                    : null,
                                icon: Padding(
                                  padding:
                                  EdgeInsets.only(
                                      right: 8),
                                  child: SvgPicture.asset(
                                    'assets/icons/arrow_down.svg',
                                    width: 12,
                                    color:
                                    Color(0xff7e7e7e),
                                  ),
                                ),
                                style: TextStyle(
                                  overflow: TextOverflow
                                      .ellipsis,
                                  color: Colors.black,
                                  fontSize: 12.0,
                                  fontWeight:
                                  FontWeight.bold,
                                  fontStyle:
                                  FontStyle.normal,
                                ),
                                borderRadius:
                                BorderRadius.circular(
                                    8),
                                decoration:
                                InputDecoration(
                                  border:
                                  OutlineInputBorder(
                                    borderRadius:
                                    BorderRadius.all(
                                        Radius
                                            .circular(
                                            8)),
                                    borderSide:
                                    BorderSide(
                                      color:
                                      Color.fromRGBO(
                                          227,
                                          227,
                                          227,
                                          1),
                                      width: 1,
                                    ),
                                  ),
                                  hintStyle: TextStyle(
                                    color: Colors.grey,
                                    fontSize: 15.0,
                                    fontWeight:
                                    FontWeight.w800,
                                    fontStyle:
                                    FontStyle.normal,
                                    letterSpacing: 0,
                                  ),
                                  focusedBorder:
                                  OutlineInputBorder(
                                    borderRadius:
                                    BorderRadius.all(
                                        Radius
                                            .circular(
                                            8)),
                                    borderSide:
                                    BorderSide(
                                      color:
                                      Color.fromRGBO(
                                          227,
                                          227,
                                          227,
                                          1),
                                      width: 1,
                                    ),
                                  ),
                                  contentPadding:
                                  EdgeInsets
                                      .symmetric(
                                      horizontal:
                                      12,
                                      vertical:
                                      8),
                                  fillColor: Colors.white,
                                ),
                                onChanged: (String? value) async {
                                  if (value != null &&
                                      value.isNotEmpty) {
                                    var selectedItem =
                                    snapshot.data!.firstWhere((item) =>
                                    item['value'] == value,
                                      orElse: () => {"label": "", "value": ""},
                                    );
                                    _setState(() {
                                      optionalProductController.text = value;
                                    });
                                  }
                                },
                                validator: (value) {
                                  if (value == null ||
                                      value.isEmpty) {
                                    return "Required!";
                                  }
                                  return null;
                                },
                                dropdownColor:
                                Colors.white,
                                items: dropdownItems,
                              );
                            },
                          ),
                        ),

                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        });
      },
    );
  }
}
