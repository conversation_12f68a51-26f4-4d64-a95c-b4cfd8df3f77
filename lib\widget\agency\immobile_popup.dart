import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/acquiredContact.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

class ImmobilePopup extends StatefulWidget {
  const ImmobilePopup({
    Key? key,
    required this.acquiredContact,
  }) : super(key: key);

  final AcquiredContact acquiredContact;

  @override
  State<ImmobilePopup> createState() => _ImmobilePopupState();
}

class _ImmobilePopupState extends State<ImmobilePopup> {
  TextStyle sectionTitleStyle =
      TextStyle(fontSize: 16, fontWeight: FontWeight.bold);

  @override
  void initState() {
    print(widget.acquiredContact.toMap());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      key: Key(widget.acquiredContact.firebaseId!),
      height: 600,
      width: 850, //MediaQuery.of(context).size.width * 0.6,
      margin: EdgeInsets.symmetric(horizontal: 20),
      padding: EdgeInsets.symmetric(horizontal: 30, vertical: 20),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        child: ListView(
          children: [
            Column(
              children: [
                SizedBox(height: 20),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 18.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      IconButton(
                        onPressed: () {},
                        icon: Container(),
                      ),
                      Text(
                        "${widget.acquiredContact.address} ${widget.acquiredContact.streetNumber}, ${widget.acquiredContact.postalCode}, ${widget.acquiredContact.city}",
                        style: TextStyle(
                            color: Theme.of(context).primaryColor,
                            fontSize: 20,
                            fontWeight: FontWeight.bold),
                      ),
                      IconButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        icon: Icon(Icons.close),
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 20),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 27),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      NarFormLabelWidget(
                        label: 'Dati Immobile',
                        fontSize: 15,
                        fontWeight: 'bold',
                        textColor: Colors.black,
                      ),
                      SelectableText(
                          'ID: ' + widget.acquiredContact.firebaseId!,
                          style: TextStyle(color: Colors.grey, fontSize: 11))
                    ],
                  ),
                ),
                SizedBox(height: 10),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 27.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        height: 120,
                        width: 160,
                        padding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                        decoration: BoxDecoration(
                            color: Color(0xffE3E3E3),
                            borderRadius: BorderRadius.circular(8)),
                        child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Image.asset(
                                "assets/icons/building.png",
                                width: 30,
                                color: Theme.of(context).primaryColor,
                              ),
                              SizedBox(height: 8),
                              NarFormLabelWidget(
                                label: "${widget.acquiredContact.typology}",
                                fontSize: 13,
                                fontWeight: 'bold',
                                textColor: Colors.black,
                              ),
                            ]),
                      ),
                      Container(
                        height: 120,
                        width: 160,
                        padding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                        decoration: BoxDecoration(
                            color: Color(0xffE3E3E3),
                            borderRadius: BorderRadius.circular(8)),
                        child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Image.asset(
                                "assets/icons/mq.png",
                                width: 30,
                                color: Theme.of(context).primaryColor,
                              ),
                              SizedBox(height: 8),
                              NarFormLabelWidget(
                                label: "${widget.acquiredContact.surface}mq",
                                fontSize: 13,
                                fontWeight: 'bold',
                                textColor: Colors.black,
                              ),
                            ]),
                      ),
                      Container(
                        height: 120,
                        width: 160,
                        padding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                        decoration: BoxDecoration(
                            color: Color(0xffE3E3E3),
                            borderRadius: BorderRadius.circular(8)),
                        child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Image.asset(
                                "assets/icons/stairs.png",
                                height: 30,
                                color: Theme.of(context).primaryColor,
                              ),
                              SizedBox(height: 8),
                              NarFormLabelWidget(
                                label: "${widget.acquiredContact.floor}° piano",
                                fontSize: 13,
                                fontWeight: 'bold',
                                textColor: Colors.black,
                              ),
                            ]),
                      ),
                      Container(
                        height: 120,
                        width: 160,
                        padding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                        decoration: BoxDecoration(
                            color: Color(0xffE3E3E3),
                            borderRadius: BorderRadius.circular(8)),
                        child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Image.asset(
                                "assets/icons/locali.png",
                                width: 30,
                                color: Theme.of(context).primaryColor,
                              ),
                              SizedBox(height: 8),
                              NarFormLabelWidget(
                                label:
                                    "${widget.acquiredContact.locali} locale/i",
                                fontSize: 13,
                                fontWeight: 'bold',
                                textColor: Colors.black,
                              ),
                            ]),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 15),
                Container(
                  height: 120,
                  margin: EdgeInsets.symmetric(horizontal: 27),
                  padding: EdgeInsets.symmetric(horizontal: 12),
                  decoration: BoxDecoration(
                      color: Color(0xffE3E3E3),
                      borderRadius: BorderRadius.circular(8)),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          NarFormLabelWidget(
                            label: "Ascensore: " +
                                (widget.acquiredContact.hasElevator!
                                    ? 'Sì'
                                    : 'No'),
                            fontSize: 13,
                            textColor: Color(0xff636363),
                            fontWeight: '600',
                          ),
                          SizedBox(height: 3),
                          NarFormLabelWidget(
                            label: "Balconi: " +
                                (widget.acquiredContact.hasElevator!
                                    ? widget.acquiredContact.numBalcony!
                                        .toString()
                                    : 'No'),
                            fontSize: 13,
                            textColor: Color(0xff636363),
                            fontWeight: '600',
                          ),
                          SizedBox(height: 3),
                          NarFormLabelWidget(
                            label: "Terrazzo: " +
                                (widget.acquiredContact.hasTerrace!
                                    ? 'Sì'
                                    : 'No'),
                            fontSize: 13,
                            textColor: Color(0xff636363),
                            fontWeight: '600',
                          ),
                          SizedBox(height: 3),
                          NarFormLabelWidget(
                            label:
                                "Garage: ${widget.acquiredContact.hasGarage! ? widget.acquiredContact.garageCount : 'Nessuno'}",
                            fontSize: 13,
                            textColor: Color(0xff636363),
                            fontWeight: '600',
                          ),
                        ],
                      ),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          NarFormLabelWidget(
                            label: "Cantina: " +
                                (widget.acquiredContact.hasCantina!
                                    ? 'Sì'
                                    : 'No'),
                            fontSize: 13,
                            textColor: Color(0xff636363),
                            fontWeight: '600',
                          ),
                          SizedBox(height: 3),
                          NarFormLabelWidget(
                            label: "Ripostiglio: " +
                                (widget.acquiredContact.hasCloset!
                                    ? 'Sì'
                                    : 'No'),
                            fontSize: 13,
                            textColor: Color(0xff636363),
                            fontWeight: '600',
                          ),
                          SizedBox(height: 3),
                          NarFormLabelWidget(
                            label: "Giardino condominiale: " +
                                (widget.acquiredContact.hasSharedGarden!
                                    ? 'Sì'
                                    : 'No'),
                            fontSize: 13,
                            textColor: Color(0xff636363),
                            fontWeight: '600',
                          ),
                          SizedBox(height: 3),
                          NarFormLabelWidget(
                            label: "Portineria: " +
                                (widget.acquiredContact.hasConcierge!
                                    ? 'Sì'
                                    : 'No'),
                            fontSize: 13,
                            textColor: Color(0xff636363),
                            fontWeight: '600',
                          ),
                        ],
                      ),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          NarFormLabelWidget(
                            label:
                                "Anno costruzione: ${widget.acquiredContact.yearBuilt != null ? widget.acquiredContact.yearBuilt : "Non so"}",
                            fontSize: 13,
                            textColor: Color(0xff636363),
                            fontWeight: '600',
                          ),
                          SizedBox(height: 3),
                          NarFormLabelWidget(
                            label: "Classe energetica: " +
                                (widget.acquiredContact.energyClass != null
                                    ? widget.acquiredContact.energyClass!
                                    : 'Non so'),
                            fontSize: 13,
                            textColor: Color(0xff636363),
                            fontWeight: '600',
                          ),
                          SizedBox(height: 3),
                          NarFormLabelWidget(
                            label: "Esposizione: " +
                                (widget.acquiredContact.exposition!.length != 0
                                    ? widget.acquiredContact.exposition!
                                        .join(', ')
                                    : 'Non so'),
                            fontSize: 13,
                            textColor: Color(0xff636363),
                            fontWeight: '600',
                          ),
                          SizedBox(height: 3),
                          NarFormLabelWidget(
                            label:
                                "Riscaldamento; ${widget.acquiredContact.heating!}",
                            fontSize: 13,
                            textColor: Color(0xff636363),
                            fontWeight: '600',
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 15),

                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 27.0),
                  child: Row(
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          NarFormLabelWidget(
                            label: 'Valutazione',
                            fontSize: 15,
                            fontWeight: 'bold',
                            textColor: Colors.black,
                          ),
                          SizedBox(height: 10),
                          Container(
                            height: 120,
                            width: 296,
                            padding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 10),
                            decoration: BoxDecoration(
                                color: Color(0xffE3E3E3),
                                borderRadius: BorderRadius.circular(8)),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    NarFormLabelWidget(
                                      label:
                                          "${formatPrice(widget.acquiredContact.prezzoAcquistoPrevisto)}",
                                      fontSize: 22,
                                      textColor: Theme.of(context).primaryColor,
                                      fontWeight: '700',
                                    ),
                                    SizedBox(height: 5),
                                    NarFormLabelWidget(
                                      label:
                                          "€/Mq ${formatPrice(widget.acquiredContact.prezzoMqPrevisto)}",
                                      fontSize: 12,
                                      textColor: Color(0xff797979),
                                      fontWeight: '500',
                                    ),
                                  ],
                                ),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    NarFormLabelWidget(
                                      label:
                                          "Min. ${formatPrice(widget.acquiredContact.prezzoAcquistoPrevisto * 0.95)}",
                                      fontSize: 14,
                                      textColor: Colors.black,
                                      fontWeight: '700',
                                    ),
                                    NarFormLabelWidget(
                                      label:
                                          "Max. ${formatPrice(widget.acquiredContact.prezzoAcquistoPrevisto * 1.05)}",
                                      fontSize: 14,
                                      textColor: Colors.black,
                                      fontWeight: '700',
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(width: 20),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          NarFormLabelWidget(
                            label: 'Info aggiuntive',
                            fontSize: 15,
                            fontWeight: 'bold',
                            textColor: Colors.black,
                          ),
                          SizedBox(height: 10),
                          Container(
                            height: 120,
                            width: 420,
                            padding: EdgeInsets.symmetric(horizontal: 8),
                            decoration: BoxDecoration(
                                color: Color(0xffE3E3E3),
                                borderRadius: BorderRadius.circular(8)),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                widget.acquiredContact.currentAvailability ==
                                        null
                                    ? Container()
                                    : NarFormLabelWidget(
                                        label:
                                            "Attualmente l'immobile è: ${widget.acquiredContact.currentAvailability}",
                                        fontSize: 13,
                                        textColor: Color(0xff636363),
                                        fontWeight: '600',
                                      ),
                                SizedBox(height: 5),
                                NarFormLabelWidget(
                                  label:
                                      "Condizioni dell'immobile: ${widget.acquiredContact.status}",
                                  fontSize: 13,
                                  textColor: Color(0xff636363),
                                  fontWeight: '600',
                                ),
                                SizedBox(height: 5),
                                NarFormLabelWidget(
                                  label:
                                      "Tempo di vendita auspicato: ${widget.acquiredContact.desiredSellTime}",
                                  fontSize: 13,
                                  textColor: Color(0xff636363),
                                  fontWeight: '600',
                                ),
                                SizedBox(height: 5),
                                NarFormLabelWidget(
                                  label: "Di proprietà del richiedente?: " +
                                      (widget.acquiredContact
                                              .isPropertyOfSubmitter!
                                          ? 'Sì'
                                          : 'No'),
                                  fontSize: 13,
                                  textColor: Color(0xff636363),
                                  fontWeight: '600',
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                )
                //ROBA VECCHIA
                /*
                Text("Valutazione", style: sectionTitleStyle),
                SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '€' +
                          (widget.acquiredContact.prezzoAcquistoPrevisto * 0.95)
                              .toStringAsFixed(0),
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(width: 30),
                    Text(
                      '€' +
                          widget.acquiredContact.prezzoAcquistoPrevisto
                              .toString(),
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 36,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(width: 30),
                    Text(
                      '€' +
                          (widget.acquiredContact.prezzoAcquistoPrevisto * 1.05)
                              .toStringAsFixed(0),
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '€/Mq ' +
                          widget.acquiredContact.prezzoMqPrevisto.toString(),
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Container(
                    height: 1,
                    margin: EdgeInsets.symmetric(vertical: 20, horizontal: 20),
                    color: Colors.grey),
                Text(
                  "Informazioni aggiuntive",
                  style: sectionTitleStyle,
                ),
                SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Expanded(
                      child: Column(children: [
                        Text(
                          "Attualmente l'immobile è",
                          style: TextStyle(fontSize: 12),
                        ),
                        SizedBox(height: 10),
                        Text(
                          "${widget.acquiredContact.currentAvailability}",
                          style: TextStyle(
                              fontSize: 18, fontWeight: FontWeight.bold),
                        )
                      ]),
                    ),
                    Expanded(
                      child: Column(children: [
                        Text(
                          "Condizioni dell'immobile",
                          style: TextStyle(fontSize: 12),
                        ),
                        SizedBox(height: 10),
                        Text(
                          "${widget.acquiredContact.status}",
                          style: TextStyle(
                              fontSize: 18, fontWeight: FontWeight.bold),
                        )
                      ]),
                    ),
                    Expanded(
                      child: Column(children: [
                        Text(
                          "Tempo di vendita auspicato",
                          style: TextStyle(fontSize: 12),
                        ),
                        SizedBox(height: 10),
                        Text(
                          "${widget.acquiredContact.desiredSellTime}",
                          style: TextStyle(
                              fontSize: 18, fontWeight: FontWeight.bold),
                        )
                      ]),
                    ),
                    Expanded(
                      child: Column(children: [
                        Text(
                          "Di proprietà del richiedente",
                          style: TextStyle(fontSize: 12),
                        ),
                        SizedBox(height: 10),
                        Text(
                          widget.acquiredContact.isPropertyOfSubmitter!
                              ? "Sì"
                              : "No",
                          style: TextStyle(
                              fontSize: 18, fontWeight: FontWeight.bold),
                        )
                      ]),
                    )
                  ],
                ),
                SizedBox(height: 10),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    widget.acquiredContact.explicitConversionSource == null
                        ? Container()
                        : Expanded(
                            child: Column(children: [
                              Text(
                                "Fonte conversione",
                                style: TextStyle(fontSize: 12),
                              ),
                              SizedBox(height: 20),
                              Text(
                                "${widget.acquiredContact.explicitConversionSource}",
                                style: TextStyle(
                                    fontSize: 18, fontWeight: FontWeight.bold),
                              )
                            ]),
                          ),
                    widget.acquiredContact.submitterIsLookingForAHouse == null
                        ? Container()
                        : Expanded(
                            child: Column(
                              children: [
                                Text(
                                  "Il richiedente cerca casa?",
                                  style: TextStyle(fontSize: 12),
                                ),
                                SizedBox(height: 10),
                                Text(
                                  "${widget.acquiredContact.submitterIsLookingForAHouse!}",
                                  style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                          ),
                    widget.acquiredContact.sellerProfession == null
                        ? Container()
                        : Expanded(
                            child: Column(
                              children: [
                                Text(
                                  "Professione richiedente",
                                  style: TextStyle(fontSize: 12),
                                ),
                                SizedBox(height: 10),
                                Text(
                                  "${widget.acquiredContact.sellerProfession!}",
                                  style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                          ),
                    widget.acquiredContact.whyAskEvaluation == null
                        ? Container()
                        : Expanded(
                            child: Column(
                              children: [
                                Text(
                                  "Motivo valutazione",
                                  style: TextStyle(fontSize: 12),
                                ),
                                SizedBox(height: 10),
                                Text(
                                  "${widget.acquiredContact.whyAskEvaluation!}",
                                  style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                          ),
                  ],
                ),
                SizedBox(height: 20),*/
              ],
            ),
          ],
        ),
      ),
    );
  }
}
