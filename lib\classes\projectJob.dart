import 'package:newarc_platform/classes/user.dart';

class ProjectJob{
  int? indexPlace;
  String? uid;
  String? activity;
  String? activityCategory;
  String? vendorUserId;
  String? vendor;
  String? status;
  String? quality;
  int? startDate;
  int? endDate;
  List? images;
  String? virtualTourLink;
  List<JobComments>? jobComments = [];
  String? timeline;
  int? hypotheticalStartDate;
  bool? showInApp;
  bool? showPaymentRows;
  bool? paymentConnected;
  String? associatedPaymentTitle;
  String? associatedPaymentID;
  String? selectedCoverImage;


  
  ProjectJob(Map<String, dynamic> fixedProperty) {
    this.uid = fixedProperty['uid'];
    this.indexPlace = fixedProperty['indexPlace'];
    this.activity = fixedProperty['activity'] == null ? '' : fixedProperty['activity'];
    this.activityCategory = fixedProperty['activityCategory']??'';
    this.vendorUserId = fixedProperty['vendorUserId'];
    this.vendor = fixedProperty['vendor']??'';
    this.status = fixedProperty['status'] == null ? '' : fixedProperty['status'];
    this.quality = fixedProperty['quality'] == null ? '' : fixedProperty['quality'];
    this.startDate = fixedProperty['startDate'] == null ? 0 : fixedProperty['startDate'];
    this.endDate = fixedProperty['endDate'] == null ? 0 : fixedProperty['endDate'];
    this.images = fixedProperty['images'] == null ? [] : fixedProperty['images'];
    this.jobComments = fixedProperty['jobComments'] == null ? [] : fixedProperty['jobComments'] as List<JobComments>;
    this.timeline = fixedProperty['timeline']??'';
    this.hypotheticalStartDate = fixedProperty['hypotheticalStartDate']??0;
    this.showInApp = fixedProperty['showInApp']??false;
    this.virtualTourLink = fixedProperty['virtualTourLink']??'';
    this.showPaymentRows = fixedProperty['showPaymentRows']??false;
    this.paymentConnected = fixedProperty['paymentConnected']??false;
    this.associatedPaymentTitle = fixedProperty['associatedPaymentTitle']??'';
    this.associatedPaymentID = fixedProperty['associatedPaymentID']??'';
    this.selectedCoverImage = fixedProperty['selectedCoverImage'] ?? '';
  }

  ProjectJob.empty(){
    this.uid = '';
    this.indexPlace = -1;
    this.activity = '';
    this.activityCategory = '';
    this.vendorUserId = '';
    this.vendor = '';
    this.status = '';
    this.quality = '';
    this.startDate = 0;
    this.endDate = 0;
    this.images = [];
    this.jobComments = [];
    this.timeline = '';
    this.hypotheticalStartDate = 0;
    this.showInApp = false;
    this.virtualTourLink = '';
    this.showPaymentRows = false;
    this.paymentConnected = false;
    this.associatedPaymentTitle = '';
    this.associatedPaymentID = '';
    this.selectedCoverImage = '';
  }


  Map<String, dynamic> toMap() {
    return {
      'uid': this.uid,
      'indexPlace': this.indexPlace,
      'activity': this.activity,
      'activityCategory': this.activityCategory,
      'vendorUserId': this.vendorUserId,
      'vendor': this.vendor??'',
      'status': this.status??'',
      'quality': this.quality??'',
      'startDate': this.startDate??0,
      'endDate': this.endDate??0,
      'images': this.images,
      'timeline': this.timeline??'',
      'hypotheticalStartDate': this.hypotheticalStartDate??0,
      'showInApp': this.showInApp??false,
      'jobComments': this.jobComments != null ? this.jobComments!.map((e) => e.toMap()).toList() : [],
      'virtualTourLink': this.virtualTourLink??'',
      'showPaymentRows': this.showPaymentRows??false,
      'paymentConnected': this.paymentConnected??false,
      'associatedPaymentTitle': this.associatedPaymentTitle??'',
      'associatedPaymentID': this.associatedPaymentID??'',
      'selectedCoverImage': this.selectedCoverImage ?? ''
    };
  }

  ProjectJob.fromDocument(Map<String, dynamic> data, int index) {

    try {
      
      this.uid = data['uid'];
      this.indexPlace = index;
      this.activity = data['activity'] == '' ? '' : data['activity'];
      this.vendorUserId = data['vendorUserId'];
      this.vendor = data['vendor'];
      this.status = data['status'] == '' ? '' : data['status'];
      this.quality = data['quality'] == '' ? '' : data['quality'];
      this.startDate = data['startDate'] == '' ? 0 : data['startDate'];
      this.endDate = data['endDate'] == '' ? 0 : data['endDate'];
      this.images = data['images'];
      this.timeline = data['timeline'];
      this.hypotheticalStartDate = data['hypotheticalStartDate']??0;
      this.showInApp = data['showInApp']??false;
      this.activityCategory = data['activityCategory']??'';
      this.virtualTourLink = data['virtualTourLink']??'';
      this.showPaymentRows = data['showPaymentRows']??false;
      this.paymentConnected = data['paymentConnected']??false;
      this.associatedPaymentTitle = data['associatedPaymentTitle']??'';
      this.associatedPaymentID = data['associatedPaymentID']??'';
      this.selectedCoverImage = data['selectedCoverImage'] ?? '';

      if( data['jobComments'] != null ) {

        if( data['jobComments'].length > 0 ) {
          for (var i = 0; i < data['jobComments'].length ; i++) {
            this.jobComments!.add( JobComments.fromDocument(data['jobComments'][i], i ) );
          }
        } else {
          this.jobComments = [];
        }
        
      } else {
        
      }

      
    } catch (e) {
      // print({'projectJob.dart', e, s});
    }
    
  }

}

class JobComments {

  int? index;
  int? date;
  String? commentorId;
  String? message;
  NewarcUser? newarcUser;
  String? profilePicture;
  

  JobComments(Map<String, dynamic> fixedProperty) {
    
    this.index = fixedProperty['index'] == '' ? 0 : fixedProperty['index'];
    this.date = fixedProperty['date'] == null ? 0 : fixedProperty['date'];
    this.commentorId = fixedProperty['commentorId'] == null ? '' : fixedProperty['commentorId'];
    this.message =  fixedProperty['message'] == null ? '' : fixedProperty['message'];

  }

  JobComments.empty(){
    this.index = 0;
    this.date = 0;
    this.commentorId = '';
    this.message = '';
    
  }

  Map<String, dynamic> toMap() {
    return {
      'index': this.index,
      'date': this.date,
      'commentorId': this.commentorId,
      'message': this.message
    };
  }

  JobComments.fromDocument(Map<String, dynamic> data, int index) {
    this.index = index;
    this.date = data['date'];
    this.commentorId = data['commentorId'];
    this.message = data['message'] != null ? data['message'] : '';
  }

}