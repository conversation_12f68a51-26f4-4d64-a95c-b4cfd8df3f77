
import 'dart:developer';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import '../classes/CMEInformation.dart';
import '../classes/renovationQuotation.dart';


NumberFormat localCurrencyFormatMain = NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 2);



Future<void> computoMatrixPdfDesign({
  required RenovationQuotation  quotation,
  required Map<String, Map<String, List<Map<dynamic, dynamic>>>> renovationData,
  required Map subCategoryTotal,
  required List<String> selectedLavoriSubCategory,
  required List<String> selectedFornitureSubCategory
})async{
  try{

    //? ------------------------------ Fonts and Images-----------------------------
    final ByteData fontMediumData = await rootBundle.load('assets/fonts/Raleway-Medium.ttf');
    final ralewayMedium = pw.Font.ttf(fontMediumData.buffer.asByteData());

    final ByteData fontBoldData = await rootBundle.load('assets/fonts/Raleway-Bold.ttf');
    final ralewayBold = pw.Font.ttf(fontBoldData.buffer.asByteData());

    final ByteData fontLightData = await rootBundle.load('assets/fonts/Raleway-Regular.ttf');
    final ralewayLight = pw.Font.ttf(fontLightData.buffer.asByteData());

    final Uint8List coverbgImageData =
        await loadImage('assets/renovation-quotation-pdf-bg.jpg');
    final coverbg = pw.MemoryImage(coverbgImageData);

    final Uint8List coverLogoImageData = await loadImage('assets/rq-pdf-cover-logo.png');
    final coverLogo = pw.MemoryImage(coverLogoImageData);

    String qrImageUrl = quotation.cmeInformation?.virtualTourURL ?? "";

    List<Map<String, dynamic>> imageFiles = [];
    List<Map<String, dynamic>> pdfFiles = [];

    for (ExecutiveGraphicDrawing eg in quotation.cmeInformation?.executiveGraphicDrawingList ?? []) {
      if (eg.filePath?.isEmpty ?? true) continue;

      for(var fileP in eg.filePath!){
        String fileUrl = await printUrl(fileP["location"], "", fileP["fileName"]);
        String extension = fileP["fileName"]!.split('.').last.toLowerCase();
        bool isPDF = extension == 'pdf';

        final file = await getNetworkImage(fileUrl, isPDF: isPDF);
        final uploadMap = {
          "file": file,
          "isPDF": isPDF,
          "type": eg.type,
        };

        if (isPDF) {
          pdfFiles.add(uploadMap);
        } else {
          imageFiles.add(uploadMap);
        }
      }


    }

    // Merge image files first, then PDFs
    List<Map<String, dynamic>> uploadList = [...imageFiles, ...pdfFiles];

    //?------------------------------------------------------------------------

    final pdf = pw.Document();

    ;

    //? -----------------------------------------------------------
    //? ------------------------------ Cover Page -----------------------------
    //? -----------------------------------------------------------

    pw.Widget footer(String pageNumber){
      return  pw.Container(
          height: 110,
          child: pw.Column(
            mainAxisAlignment: pw.MainAxisAlignment.start,
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.SizedBox(height: 10),
              pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  mainAxisAlignment: pw.MainAxisAlignment.start,
                  children: [
                    pw.Text("Firma per accettazione",
                        overflow: pw.TextOverflow.visible,
                        style: pw.TextStyle(
                            font: ralewayBold,
                            fontSize: 11,
                            color: PdfColors.black)),
                    pw.Container(
                        height: 1,
                        width: 250,
                        margin: pw.EdgeInsets.only(top: 30),
                        decoration: pw.BoxDecoration(
                          color: PdfColors.black,
                        ))
                  ]),
              pw.SizedBox(height: 30),
              pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                  children: [
                    pw.Expanded(
                      flex: 1,
                      child: pw.Image(coverLogo, height: 25),
                    ),
                    pw.Expanded(
                      flex: 5,
                      child: pw.Padding(
                          padding: pw.EdgeInsets.symmetric(horizontal: 20),
                          child: pw.Text(
                              "NEWARC SRL - Sede Legale Via Vittorio Emanuele II 29, Chieri - Sede Operativa Corso Ferrucci 36, Torino - P.Iva 12533550013 - <EMAIL> - 011.02.63.850",
                              overflow: pw.TextOverflow.visible,
                              style: pw.TextStyle(
                                  font: ralewayMedium,
                                  fontSize: 7,
                                  color: PdfColor.fromHex('6E6E6E')))),
                    ),
                    pw.Expanded(
                        flex: 1,
                        child: pw.Text('${pageNumber}',
                            textAlign: pw.TextAlign.right,
                            style: pw.TextStyle(
                                font: ralewayBold,
                                fontSize: 13,
                                color: PdfColor.fromHex('000'))))
                  ]),
            ],
          ));
    }

    pdf.addPage(
      pw.Page(
        theme: pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(0),
        build: (pw.Context context) => pw.Center(
            child: pw.Stack(children: [
              pw.Positioned(
                  child: pw.Image(
                    coverbg,
                    fit: pw.BoxFit.cover,
                  ),
                  left: 0,
                  top: 0,
                  right: 0,
                  bottom: 0),
              pw.Container(
                  width: double.infinity,
                  height: double.infinity,
                  padding: pw.EdgeInsets.only(top: 100, left: 50, right: 25, bottom: 30),
                  child: pw.Column(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Container(
                          child: pw.Column(
                              crossAxisAlignment: pw.CrossAxisAlignment.start,
                              children: [
                                pw.Text('COMPUTO METRICO',
                                    style: pw.TextStyle(
                                        fontSize: 20,
                                        letterSpacing: 1,
                                        font: ralewayMedium,
                                        color: PdfColor.fromHex('ffffff'))),
                                pw.SizedBox(height: 10),
                                //---------- Street Address----------
                                pw.Text(
                                  "${quotation.renovationContactAddress!.addressInfo!.streetName!} ${quotation.renovationContactAddress!.addressInfo!.streetNumber!}",
                                  style: pw.TextStyle(
                                        font: ralewayBold,
                                        fontSize: 40,
                                        color: PdfColor.fromHex('ffffff'))),
                              ])),
                      pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          quotation.cmeInformation?.virtualTourURL?.isNotEmpty ?? false ?
                          pw.Container(
                            height: 159,
                            width: 337,
                            padding: pw.EdgeInsets.all(20),
                            margin: pw.EdgeInsets.only(bottom: 50),
                            decoration: pw.BoxDecoration(
                              borderRadius: pw.BorderRadius.circular(10),
                              border: pw.Border.all(width: 1,color:PdfColor.fromHex('ffffff')),
                            ),
                            child: pw.Row(
                              crossAxisAlignment: pw.CrossAxisAlignment.center,
                              children: [
                                pw.Container(
                                  width: 120,
                                  height: 120,
                                  padding: pw.EdgeInsets.all(10),
                                  decoration: pw.BoxDecoration(
                                    color: PdfColors.white,
                                    borderRadius: pw.BorderRadius.circular(10),
                                  ),
                                  child: pw.BarcodeWidget(
                                    barcode: pw.Barcode.qrCode(),
                                    data: qrImageUrl,
                                    width: 190,
                                    height: 190,
                                  ),
                                ),
                                pw.SizedBox(width: 20),
                                pw.Text('Visita l’immobile\nin anteprima',
                                    style: pw.TextStyle(
                                        fontSize: 17,
                                        font: ralewayMedium,
                                        color: PdfColor.fromHex('#000000'))),

                              ]
                            )
                          ) : pw.SizedBox(),

                          pw.Row(
                            crossAxisAlignment: pw.CrossAxisAlignment.start,
                            children: [
                              pw.Expanded(child: pw.Image(coverLogo, height: 56)),
                              pw.SizedBox(width: 45),
                              pw.Expanded(
                                  child: pw.Column(
                                      crossAxisAlignment:
                                      pw.CrossAxisAlignment.start,
                                      children: [
                                        pw.Row(children: [
                                          pw.Expanded(
                                              child: pw.Column(
                                                  mainAxisAlignment:
                                                  pw.MainAxisAlignment.start,
                                                  crossAxisAlignment:
                                                  pw.CrossAxisAlignment.start,
                                                  children: [
                                                    pw.Text('DATA',
                                                        style: pw.TextStyle(
                                                            fontSize: 8,
                                                            letterSpacing: 1,
                                                            color:
                                                            PdfColor.fromHex('000'))),
                                                    pw.SizedBox(height: 5),

                                                    //-----created date
                                                    pw.Text(
                                                        "${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}",
                                                        style: pw.TextStyle(
                                                            font: ralewayBold,
                                                            fontSize: 11,
                                                            color:
                                                            PdfColor.fromHex('000'))),
                                                  ])),
                                          pw.SizedBox(width: 1),
                                          pw.Expanded(
                                              child: pw.Column(
                                                  mainAxisAlignment:
                                                  pw.MainAxisAlignment.start,
                                                  crossAxisAlignment:
                                                  pw.CrossAxisAlignment.start,
                                                  children: [
                                                    pw.Text('CODICE',
                                                        style: pw.TextStyle(
                                                            fontSize: 8,
                                                            letterSpacing: 1,
                                                            color:
                                                            PdfColor.fromHex('000000'))),
                                                    pw.SizedBox(height: 5),

                                                    //---------- quotation code
                                                    pw.Text(
                                                        "${quotation.code!}_V${quotation.version!}_R${quotation.revision!}",
                                                        style: pw.TextStyle(
                                                            font: ralewayBold,
                                                            fontSize: 11,
                                                            color:
                                                            PdfColor.fromHex('000000'))),
                                                  ]))
                                        ]),
                                      ]))
                            ],
                          )
                        ]
                      )
                    ],
                  )),
            ])),
      ),
    );

    //? -----------------------------------------------------------
    //? ------------------------------ Dati del progetto page -----------------------------
    //? -----------------------------------------------------------

    List<pw.Widget> pdfProjectDetail = [];

    pw.Widget _commonColumnWidget({required String title,required String value}){
      return pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            title,
            style: pw.TextStyle(
                font: ralewayLight,
                fontSize: 10,
                color: PdfColor.fromHex('#6D6D6D')),
          ),
          pw.SizedBox(height: 8),
          pw.Text(
            value,
            style: pw.TextStyle(
                font: ralewayMedium,
                fontSize: 11,
                color: PdfColors.black,
            ),
          ),
        ]
      );
    }

    pdfProjectDetail.add(pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text('Dati del progetto',
            style: pw.TextStyle(
                font: ralewayBold,
                fontSize: 18,
                color: PdfColor.fromHex('000')),
        ),
        pw.SizedBox(height: 29),
        
        pw.Container(
          width: double.infinity,
          padding: pw.EdgeInsets.symmetric(horizontal: 26,vertical: 19),
          decoration: pw.BoxDecoration(
            borderRadius: pw.BorderRadius.circular(10),
            border: pw.Border.all(color: PdfColor.fromHex('#E1E1E1'),width: 1)
          ),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text('Dati generali',
                style: pw.TextStyle(
                    font: ralewayBold,
                    fontSize: 14,
                    color: PdfColor.fromHex('000')),
              ),
              pw.SizedBox(height: 15),
              pw.Row(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Expanded(
                    child: pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.start,
                        children: [
                          _commonColumnWidget(title: "Tipologia", value: quotation.propertyType ?? "")
                        ],
                    )
                  ),
                  pw.Expanded(
                      child: pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.start,
                          children: [
                            _commonColumnWidget(title: "Metri quadri", value: quotation.areaMq ?? "")
                          ]
                      )
                  ),
                  pw.Expanded(
                      child: pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.start,
                          children: [
                            _commonColumnWidget(title: "Piano", value: quotation.floor ?? "")
                          ]
                      )
                  ),
                  pw.Expanded(
                      child: pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.start,
                          children: [
                            _commonColumnWidget(title: "Bagni", value: quotation.bathroom ?? "")
                          ]
                      )
                  ),
                ]
              )
            ]
          )
        ),

        pw.SizedBox(height: 16),

        pw.Container(
            width: double.infinity,
            padding: pw.EdgeInsets.symmetric(horizontal: 26,vertical: 19),
            decoration: pw.BoxDecoration(
                borderRadius: pw.BorderRadius.circular(10),
                border: pw.Border.all(color: PdfColor.fromHex('#E1E1E1'),width: 1)
            ),
            child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text('Descrizione del progetto',
                    style: pw.TextStyle(
                        font: ralewayBold,
                        fontSize: 14,
                        color: PdfColor.fromHex('000')),
                  ),
                  pw.SizedBox(height: 15),
                  pw.Row(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Flexible(
                          child: pw.Text(
                            quotation.cmeInformation?.projectDescription ?? "",
                            overflow: pw.TextOverflow.visible,
                            style: pw.TextStyle(
                                font: ralewayLight,
                                fontSize: 12,
                                height: 13,
                                lineSpacing: 2,
                                color: PdfColor.fromHex('000')),
                          )
                        ),
                      ]
                  )
                ]
            )
        ),

        pw.SizedBox(height: 16),

        pw.Container(
            width: double.infinity,
            padding: pw.EdgeInsets.symmetric(horizontal: 26,vertical: 19),
            decoration: pw.BoxDecoration(
                borderRadius: pw.BorderRadius.circular(10),
                border: pw.Border.all(color: PdfColor.fromHex('#E1E1E1'),width: 1)
            ),
            child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                mainAxisAlignment: pw.MainAxisAlignment.start,
                children: [
                  pw.Text('Informazioni utili',
                    style: pw.TextStyle(
                        font: ralewayBold,
                        fontSize: 14,
                        color: PdfColor.fromHex('000')),
                  ),
                  pw.SizedBox(height: 20),
                  pw.Row(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Expanded(
                            child: pw.Row(
                              mainAxisAlignment: pw.MainAxisAlignment.start,
                              children: [
                                _commonColumnWidget(title: "ZTL", value: quotation.cmeInformation?.limitedTrafficZone ?? "")
                              ],
                            )
                        ),
                        pw.Expanded(
                            child: pw.Row(
                              mainAxisAlignment: pw.MainAxisAlignment.start,
                              children: [
                                _commonColumnWidget(title: "Strisce blu", value: quotation.cmeInformation?.blueStripes ?? "")
                              ],
                            )
                        ),
                        pw.Expanded(
                            child: pw.Row(
                              mainAxisAlignment: pw.MainAxisAlignment.start,
                              children: [
                                _commonColumnWidget(title: "Accesso carrabile", value: quotation.cmeInformation?.drivewayAccess ?? "")
                              ],
                            )
                        ),
                        pw.Expanded(
                            child: pw.Row(
                              mainAxisAlignment: pw.MainAxisAlignment.start,
                              children: [
                                _commonColumnWidget(title: "Poss. montacarichi", value: quotation.cmeInformation?.possibilityOfFreightElevator ?? "")
                              ],
                            )
                        ),
                      ]
                  ),
                  pw.SizedBox(height: 20),
                  pw.Row(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Expanded(
                            child: pw.Row(
                              mainAxisAlignment: pw.MainAxisAlignment.start,
                              children: [
                                _commonColumnWidget(title: "Poss. cassone macerie", value: quotation.cmeInformation?.possibilityOfRubbleContainer ?? "")
                              ],
                            )
                        ),
                        pw.Expanded(
                            child: pw.Row(
                              mainAxisAlignment: pw.MainAxisAlignment.start,
                              children: [
                                _commonColumnWidget(title: "Mq. calpestabili", value: quotation.cmeInformation?.walkableSquareMeters != null ? "${quotation.cmeInformation?.walkableSquareMeters.toString()} mq" : "")
                              ],
                            )
                        ),
                        pw.Expanded(
                            child: pw.Row(
                              mainAxisAlignment: pw.MainAxisAlignment.start,
                              children: [
                                _commonColumnWidget(title: "Altezza locali", value: quotation.cmeInformation?.roomHeight != null ?  "${quotation.cmeInformation?.roomHeight.toString()} cm" : "")
                              ],
                            )
                        ),
                        pw.Expanded(
                            child: pw.Row(
                              mainAxisAlignment: pw.MainAxisAlignment.start,
                              children: [
                                _commonColumnWidget(title: "Ascensore", value: quotation.cmeInformation?.elevator ?? "")
                              ],
                            )
                        ),
                      ]
                  ),
                ]
            )
        ),
        pw.SizedBox(height: 16),

        pw.Container(
            width: double.infinity,
            padding: pw.EdgeInsets.symmetric(horizontal: 26,vertical: 19),
            decoration: pw.BoxDecoration(
                borderRadius: pw.BorderRadius.circular(10),
                border: pw.Border.all(color: PdfColor.fromHex('#E1E1E1'),width: 1)
            ),
            child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text('Specifiche sui materiali da applicare',
                    style: pw.TextStyle(
                        font: ralewayBold,
                        fontSize: 14,
                        color: PdfColor.fromHex('000')),
                  ),
                  pw.SizedBox(height: 15),
                  pw.Row(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Flexible(
                            child: pw.Text(
                              quotation.cmeInformation?.specificationDescription ?? "",
                              overflow: pw.TextOverflow.visible,
                              style: pw.TextStyle(
                                  font: ralewayLight,
                                  fontSize: 12,
                                  height: 13,
                                  lineSpacing: 2,
                                  color: PdfColor.fromHex('000')),
                            )
                        ),
                      ]
                  )
                ]
            )
        ),
      ]
    ));

    pdf.addPage(
      pw.MultiPage(
      theme: pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
      pageFormat: PdfPageFormat.a4,
      footer: (context) => footer(context.pageNumber.toString()),
      margin: const pw.EdgeInsets.all(25),
      build: (pw.Context context) => pdfProjectDetail,
    ));



    //? -----------------------------------------------------------
    //? ------------------------------ computo matrico page -----------------------------
    //? -----------------------------------------------------------

    List<pw.Widget> pdfDataWidgetsForComputoMatrico = [];

    //---------- page title ------------
    pdfDataWidgetsForComputoMatrico.add(
        pw.Text('Computo Metrico',
        style: pw.TextStyle(
            font: ralewayBold,
            fontSize: 18,
            color: PdfColor.fromHex('000'))),
    );
    pdfDataWidgetsForComputoMatrico.add(
      pw.SizedBox(height: 20),
    );


    //------sub category Wise Data

    renovationData.keys.map((category) {

      if(category == "C - Lavori interni" || category == "M - Forniture"){

        List<String> filteredSubcategories = [];

        if (category == "C - Lavori interni") {
          filteredSubcategories = renovationData[category]!.keys
              .where((subcategory) => selectedLavoriSubCategory.contains(subcategory))
              .toList();
        } else if (category == "M - Forniture") {
          filteredSubcategories = renovationData[category]!.keys
              .where((subcategory) => selectedFornitureSubCategory.contains(subcategory))
              .toList();
        }

        if(filteredSubcategories.isNotEmpty){
          pdfDataWidgetsForComputoMatrico.add(
            pw.Container(
                width: double.infinity,
                padding: pw.EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                decoration: pw.BoxDecoration(
                  color: PdfColor.fromHex('F2FAF7'),
                  borderRadius: pw.BorderRadius.circular(7),
                ),
                child: pw.Text(
                    category,
                    style: pw.TextStyle(
                        font: ralewayBold,
                        fontSize: 13,
                        color: PdfColor.fromHex('489B79')))
            ),
          );
        }


        pdfDataWidgetsForComputoMatrico.add(
          pw.SizedBox(height: 10),
        );

        filteredSubcategories.forEach((subcategory) {
          pdfDataWidgetsForComputoMatrico.add(pw.Container(
            padding: pw.EdgeInsets.only(left: 15, right: 15, bottom: 5, top: 3),
            child: pw.Text(
              subcategory,
              style: pw.TextStyle(
                font: ralewayBold,
                fontSize: 11,
                color: PdfColor.fromHex('489B79'),
              ),
            ),
          ));

          pdfDataWidgetsForComputoMatrico.add(headRow(ralewayMedium, ralewayBold));

          renovationData[category]![subcategory]!.forEach((entry) {
            pdfDataWidgetsForComputoMatrico.add(dataRow(category, entry, ralewayMedium, ralewayBold));
          });
        });

        pdfDataWidgetsForComputoMatrico.add(pw.SizedBox(
          height: 10,
        ));
      }
    }).toList();



    pdf.addPage(pw.MultiPage(
      theme: pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),

      pageFormat: PdfPageFormat.a4,
      footer: (context) => footer(context.pageNumber.toString()),
      margin: const pw.EdgeInsets.all(25),
      build: (pw.Context context) => pdfDataWidgetsForComputoMatrico,
    ));

    //? -----------------------------------------------------------
    //? ------------------------------ Prezzi -----------------------------
    //? -----------------------------------------------------------

    List<pw.Widget> pdfDataWidgetsForPrezzi = [];

    //---------- page title ------------
    pdfDataWidgetsForPrezzi.add(pw.Text('Prezzi',
        style: pw.TextStyle(
            font: ralewayBold,
            fontSize: 18,
            color: PdfColor.fromHex('000'))));
    pdfDataWidgetsForPrezzi.add(
      pw.SizedBox(height: 20),
    );

    pdfDataWidgetsForPrezzi.add(
      pw.Container(
        padding: pw.EdgeInsets.all(10),
        decoration: pw.BoxDecoration(
          border: pw.Border.all(
            color: PdfColor.fromHex('D3D3D3'), // Green border color
            width: 1, // Border thickness
          ),
          borderRadius: pw.BorderRadius.circular(7), // Rounded corners
        ),
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            ...renovationData.keys
                .where((category) => category == "C - Lavori interni" || category == "M - Forniture")
                .expand((category) {
              final selectedSubList = category == "C - Lavori interni"
                  ? selectedLavoriSubCategory
                  : selectedFornitureSubCategory;

              return renovationData[category]!.keys
                  .where((subcategory) => selectedSubList.contains(subcategory))
                  .map((subcategory) {
                return pw.Container(
                  width: double.infinity,
                  margin: pw.EdgeInsets.symmetric(vertical: 5),
                  padding: pw.EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                  decoration: pw.BoxDecoration(
                    color: PdfColor.fromHex('F2FAF7'),
                    borderRadius: pw.BorderRadius.circular(7),
                  ),
                  child: pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: pw.CrossAxisAlignment.end,
                    children: [
                      pw.Text(
                        subcategory,
                        style: pw.TextStyle(
                          font: ralewayMedium,
                          fontSize: 10,
                          color: PdfColor.fromHex('489B79'),
                        ),
                      ),
                      pw.Text(
                        "...........................€",
                        style: pw.TextStyle(
                          font: ralewayMedium,
                          fontSize: 10,
                          color: PdfColor.fromHex('489B79'),
                        ),
                      ),
                    ],
                  ),
                );
              });
            }),
          ],
        )
      ),
    );

    pdf.addPage(pw.MultiPage(
      theme:
      pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
      pageFormat: PdfPageFormat.a4,
      footer: (context) => pw.Container(
          height: 110,
          child: pw.Column(
            mainAxisAlignment: pw.MainAxisAlignment.start,
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.SizedBox(height: 10),
              pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  mainAxisAlignment: pw.MainAxisAlignment.start,
                  children: [
                    pw.Text("Firma per accettazione",
                        overflow: pw.TextOverflow.visible,
                        style: pw.TextStyle(
                            font: ralewayBold,
                            fontSize: 11,
                            color: PdfColors.black)),
                    pw.Container(
                        height: 1,
                        width: 250,
                        margin: pw.EdgeInsets.only(top: 30),
                        decoration: pw.BoxDecoration(
                          color: PdfColors.black,
                        ))
                  ]),
              pw.SizedBox(height: 30),
              pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                  children: [
                    pw.Expanded(
                      flex: 1,
                      child: pw.Image(coverLogo, height: 25),
                    ),
                    pw.Expanded(
                      flex: 5,
                      child: pw.Padding(
                          padding: pw.EdgeInsets.symmetric(horizontal: 20),
                          child: pw.Text(
                              "NEWARC SRL - Sede Legale Via Vittorio Emanuele II 29, Chieri - Sede Operativa Corso Ferrucci 36, Torino - P.Iva 12533550013 - <EMAIL> - 011.02.63.850",
                              overflow: pw.TextOverflow.visible,
                              style: pw.TextStyle(
                                  font: ralewayMedium,
                                  fontSize: 7,
                                  color: PdfColor.fromHex('6E6E6E')))),
                    ),
                    pw.Expanded(
                        flex: 1,
                        child: pw.Text('${context.pageNumber}',
                            textAlign: pw.TextAlign.right,
                            style: pw.TextStyle(
                                font: ralewayBold,
                                fontSize: 13,
                                color: PdfColor.fromHex('000'))))
                  ]),
            ],
          )),
      margin: const pw.EdgeInsets.all(25),
      build: (pw.Context context) => pdfDataWidgetsForPrezzi,
    ));

    //? -----------------------------------------------------------
    //? ------------------------------ UPLOADS -----------------------------
    //? -----------------------------------------------------------

    Future<void> addPdfPagesFromImages(Uint8List pdfBytes, pw.Document pdf) async {
      final imagePages = await convertPdfBytesInFlutter(pdfBytes);

      for (final imageBytes in imagePages) {
        final image = pw.MemoryImage(imageBytes);
        pdf.addPage(
          pw.Page(
            pageFormat: PdfPageFormat.undefined,
            build: (context) => pw.Image(image, fit: pw.BoxFit.contain),
          ),
        );
      }
    }

    for (final value in uploadList) {
      final isPDF = value["isPDF"];
      if (isPDF) {
        await addPdfPagesFromImages(value["file"], pdf);
      } else {
        pdf.addPage(
          pw.Page(
            pageFormat: PdfPageFormat.a4,
            margin: const pw.EdgeInsets.all(25),
            build: (context) => pw.SizedBox(
              width: double.infinity,
              height: 841.89,
              child: pw.Column(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        value["type"],
                        style: pw.TextStyle(
                          font: ralewayBold,
                          fontSize: 18,
                          color: PdfColor.fromHex('000'),
                        ),
                      ),
                      pw.SizedBox(height: 29),
                      pw.Image(value["file"], fit: pw.BoxFit.contain,height: 600,),
                    ],
                  ),
                  pw.Container(
                    height: 110,
                    child: pw.Column(
                      mainAxisAlignment: pw.MainAxisAlignment.start,
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.SizedBox(height: 10),
                        pw.Text("Firma per accettazione",
                            style: pw.TextStyle(
                                font: ralewayBold,
                                fontSize: 11,
                                color: PdfColors.black)),
                        pw.Container(
                            height: 1,
                            width: 250,
                            margin: pw.EdgeInsets.only(top: 30),
                            color: PdfColors.black),
                        pw.SizedBox(height: 30),
                        pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                          children: [
                            pw.Expanded(child: pw.Image(coverLogo, height: 25)),
                            pw.Expanded(
                              flex: 5,
                              child: pw.Padding(
                                padding: pw.EdgeInsets.symmetric(horizontal: 20),
                                child: pw.Text(
                                  "NEWARC SRL - Sede Legale Via Vittorio Emanuele II 29, Chieri - Sede Operativa Corso Ferrucci 36, Torino - P.Iva 12533550013 - <EMAIL> - 011.02.63.850",
                                  style: pw.TextStyle(
                                      font: ralewayMedium,
                                      fontSize: 7,
                                      color: PdfColor.fromHex('6E6E6E')),
                                ),
                              ),
                            ),
                            pw.Expanded(
                              child: pw.Text(
                                '${context.pageNumber}',
                                textAlign: pw.TextAlign.right,
                                style: pw.TextStyle(
                                    font: ralewayBold,
                                    fontSize: 13,
                                    color: PdfColor.fromHex('000')),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }
    }




    //? -----------------------------------------------------------
    //? ------------------------------ Save and Download PDF -----------------------------
    //? -----------------------------------------------------------

    // Save PDF to bytes
    final pdfBytes = await pdf.save();

    await downloadPdf(pdfBytes: pdfBytes,fileName: '${quotation.code!}_V${quotation.version!}_R${quotation.revision!}.pdf');

  }catch(error,stackTrace){
    log("Error While Crating Computo Matrix Pdf ${error.toString()}");
    log("StackTrace While Crating Computo Matrix Pdf ${stackTrace.toString()}");
  }
}

pw.Widget headRow(ralewayMedium, ralewayBold) {
  return pw.Container(
    margin: pw.EdgeInsets.only(bottom: 5),
    padding: const pw.EdgeInsets.only(left: 15, right: 15, bottom: 0, top: 5),
    child: pw.Row(
      mainAxisSize: pw.MainAxisSize.max,
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Expanded(
          flex: 6,
          child: pw.Container(
          ),
        ),
        pw.SizedBox(
          width: 5,
        ),
        pw.Expanded(
          flex: 1,
          child: pw.Text('Unità',
              textAlign: pw.TextAlign.right,

              style: pw.TextStyle(fontSize: 8, color: PdfColor.fromHex('6E6E6E'), letterSpacing: 0.3 )),
        ),
        pw.SizedBox(
          width: 5,
        ),
        pw.Expanded(
            flex: 1,
            child: pw.Text('Quantità',
                textAlign: pw.TextAlign.right,
                style: pw.TextStyle(
                    fontSize: 8, color: PdfColor.fromHex('6E6E6E'), letterSpacing: 0.3))),
        pw.SizedBox(
          width: 5,
        ),
        pw.Expanded(
            flex: 1,
            child: pw.Text('Prezzo',
                textAlign: pw.TextAlign.right,
                style: pw.TextStyle(
                    fontSize: 8, color: PdfColor.fromHex('6E6E6E'), letterSpacing: 0.3))),
      ],
    ),
  );
}

pw.Widget dataRow(String category, Map rowData, ralewayMedium, ralewayBold) {
  return pw.Container(
    margin: pw.EdgeInsets.only(bottom: 5),
    padding: const pw.EdgeInsets.only(left: 15, right: 15, bottom: 0, top: 0),
    child: pw.Row(
      mainAxisSize: pw.MainAxisSize.max,
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Expanded(
          flex: 6,
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            mainAxisAlignment: pw.MainAxisAlignment.start,
            children: [
              pw.Text(
                  rowData['code'] +
                      (rowData['code'] != '' ? ' - ' : '') +
                      rowData['title'],
                  style: pw.TextStyle(fontSize: 8, color: PdfColor.fromHex('000'))),
              pw.SizedBox(
                height: 5,
              ),
              pw.Padding(
                padding: const pw.EdgeInsets.only(right: 15),
                child: pw.Text(rowData['comment'],
                    style: pw.TextStyle(
                        fontSize: 8,
                        color: PdfColor.fromHex('6e6e6e'),
                        letterSpacing: 0.3)),
              ),
            ],
          ),
        ),
        pw.SizedBox(
          width: 5,
        ),
        pw.Expanded(
          flex: 1,
          child: pw.Text(rowData['measurementUnit'],
              textAlign: pw.TextAlign.right,
              style: pw.TextStyle(fontSize: 9, color: PdfColor.fromHex('000'))),
        ),
        pw.SizedBox(
          width: 5,
        ),
        pw.Expanded(
            flex: 1,
            child: pw.Text(rowData['quantity'].toString(),
                textAlign: pw.TextAlign.right,
                style: pw.TextStyle(
                    fontSize: 9, color: PdfColor.fromHex('000'))),
        ),
        pw.SizedBox(
          width: 5,
        ),
        pw.Expanded(
          flex: 1,
          child: pw.Text("..............",
              textAlign: pw.TextAlign.right,
              style: pw.TextStyle(
                  fontSize: 9, color: PdfColor.fromHex('000'))),
        ),
      ],
    ),
  );
}

