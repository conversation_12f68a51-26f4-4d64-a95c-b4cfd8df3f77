import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:newarc_platform/classes/basePersonInfo.dart';
import 'package:newarc_platform/classes/lead.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/classes/property.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/utils/various.dart';

class WpFormsSubmission {
  
  String? id;
  String? userId;
  String? agencyId;
  int? insertTimestamp;
  List? newarcHomesId = []; 
  List? projectId = [];
  String? tag;
  BasePersonInfo? basePersonInfo;
  String? status;
  Property? adData;
  
  /* To be removed in future */
  /*String? name;
  String? email;
  String? phone;*/
  
  WpFormsSubmission.empty() {
    this.id = '';
    this.userId = '';
    this.agencyId = '';
    this.insertTimestamp = 0;
    this.newarcHomesId = [];
    this.projectId = [];
    this.tag = '';
    this.basePersonInfo = new BasePersonInfo.empty();
    this.status = '';
    this.adData = Property.empty();
    /*this.name = '';
    this.email = '';
    this.phone = '';*/
  }

  WpFormsSubmission.fromDocument(Map<String, dynamic> data, String id) {
    

    try {
      this.id = id;
      this.userId = data['userId'];
      this.agencyId = data['agencyId'];
      this.insertTimestamp = data['insertTimestamp'];
      this.newarcHomesId = data['newarcHomesId']??'';
      this.projectId = data['projectId']??'';
      this.tag = data['tag']??'';
      this.basePersonInfo = (data.containsKey('basePersonInfo') && data['basePersonInfo'] != null) ? new BasePersonInfo.fromMap(data['basePersonInfo']) : new BasePersonInfo.empty();
      
      /*this.name = data['name']??'';
      this.email = data['email']??'';
      this.phone = data['phone'];*/
      this.status = data['status']??'Da contattare';
      // fallbackUserId();
      // fallbackAddToLead();
      
    } catch (e, s) {
      print({ 'web lead error', e, s});
      // print(id);
    }
  }


  Map<String, dynamic> toMap() {
    return {
      'agencyId': agencyId,
      'userId': userId,
      'insertTimestamp': insertTimestamp,
      'newarcHomesId': newarcHomesId,
      'projectId': projectId,
      'tag': tag,
      'basePersonInfo': basePersonInfo,
      /*'name': name,
      'email': email,
      'phone': phone,*/
      'status': status,
    };
  }

  // fallbackUserId() async {
  //   ''' fallback to change user_id to userId ''';
  //   await FirebaseFirestore.instance
  //     .collection(appConfig.COLLECT_WP_FORM_SUBMISSION)
  //     .doc(this.id)
  //     .update(this.toMap());
  // }

  // fallbackAddToLead() async {

  //   print('updating wpform');
  //   // BasePersonInfo personInfo = BasePersonInfo.empty();
  //   /* Capture the name, surname, email, phone in BasePersonInfo format if it's not available*/
  //   /*if( this.basePersonInfo!.email == null ) {
      
  //     List fullName = this.name!.split(' ');

      
  //     personInfo = new BasePersonInfo({
  //       'name': fullName[0]??'',
  //       'surname': fullName[1]??'',
  //       'phone': this.phone,
  //       'email': this.email

  //     });

  //     await FirebaseFirestore.instance
  //     .collection(appConfig.COLLECT_WP_FORM_SUBMISSION)
  //     .doc(this.id)
  //     .update({ 
  //       'basePersonInfo': personInfo.toMap()
  //     });

  //   } else {
  //     personInfo = this.basePersonInfo!;
  //   }*/

  //   /*personInfo = this.basePersonInfo!;

  //   QuerySnapshot<Map<String, dynamic>> checkLeads = await FirebaseFirestore.instance
  //   .collection(appConfig.COLLECT_LEADS)
  //   .where('basePersonInfo.email', isEqualTo: personInfo.email)
  //   .get();

  //   if( checkLeads.docs.length > 0 ) return;

  //   DocumentReference formRef = FirebaseFirestore.instance.collection(appConfig.COLLECT_WP_FORM_SUBMISSION).doc(this.id);

  //   Lead leadData = new Lead({
  //     'basePersonInfo': personInfo,
  //     'insertTimestamp': Timestamp.now().millisecondsSinceEpoch,
  //     'source': 'wordpress',
  //     'sourceReference': formRef
  //   });

  //   DocumentReference<Map<String, dynamic>> leadRef = await FirebaseFirestore.instance
  //   .collection(appConfig.COLLECT_LEADS)
  //   .add(leadData.toMap());*/

  //   if( this.user_id != '' ) return;

  //   BasePersonInfo personInfo = this.basePersonInfo!;

  //   /* Get users with the email address */
  //   QuerySnapshot<Map<String, dynamic>> checkUser = await FirebaseFirestore.instance
  //   .collection(appConfig.COLLECT_USERS)
  //   .where('email', isEqualTo: personInfo.email)
  //   .get();

  //   /* If a user exists with the email address then update the user_id in the Valuation Submission */
  //   if( checkUser.docs.length > 0 ) {
  //     // NewarcUser nu = new NewarcUser.fromDocument(checkUser.docs.first.data(), checkUser.docs.first.id);
  //     // if( nu. )
  //     await FirebaseFirestore.instance
  //     .collection(appConfig.COLLECT_WP_FORM_SUBMISSION)
  //     .doc(this.id)
  //     .update({ 
  //       'user_id': checkUser.docs.first.id
  //     });

  //     return;

  //   } 

  //   registerWordpressContact(this).then(( uid ) async {
  //     // if( uid == false ) {
  //     //   QuerySnapshot<Map<String, dynamic>> userRef =  await FirebaseFirestore.instance
  //     //   .collection(
  //     //   appConfig.COLLECT_USERS)
  //     //   .where('email', isEqualTo: this.basePersonInfo!.email)
  //     //   .get();

  //     //   print({'userRef', this.basePersonInfo!.email, userRef.docs.length});
  //     //   return;
  //     //   // return;               
  //     // }

  //     if( uid == false ) {
  //       QuerySnapshot<Map<String, dynamic>> userRef =  await FirebaseFirestore.instance
  //       .collection(
  //       appConfig.COLLECT_USERS)
  //       .where('email', isEqualTo: this.basePersonInfo!.email)
  //       .get();

  //       print({'userRef', this.basePersonInfo!.email, userRef.docs.length});
  //       if( userRef.docs.length == 0 ) {
  //         uid = 'randomuid';
  //       } else {
  //         print({'had user document', userRef.docs[0].id });
  //         return;
  //       }
        
  //       // return;               
  //     }

  //     // createLead(this, 'direct', uid);
  //     NewarcUser newarcUser = new NewarcUser({
  //       'id': uid,
  //       'name': personInfo.name,
  //       'surname': personInfo.surname,
  //       'email': personInfo.email,
  //       'source': 'wordpress',
  //       'role': 'web-client',
  //       'type': 'web-client',
  //       'isActive': false,
  //       'isArchived': true,
  //       'isHiredInternally': false,
  //       'menuAccess': {},
  //       'projectTabAccess': {},
  //       'isFilterPerAccountEnabled': false,
  //       'phone': personInfo.phone,
  //       'insertTimestamp': this.insertTimestamp
  //     });

      
      
  //     DocumentReference<Map<String, dynamic>> userRef =  await FirebaseFirestore.instance
  //     .collection(
  //     appConfig.COLLECT_USERS)
  //     .add(newarcUser.toMap());

  //     await FirebaseFirestore.instance
  //     .collection(appConfig.COLLECT_WP_FORM_SUBMISSION)
  //     .doc(this.id)
  //     .update({ 
  //       'user_id': userRef.id
  //     });

  //   });


  // }
}