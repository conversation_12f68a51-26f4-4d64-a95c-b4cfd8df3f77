import 'dart:async';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:image_compression_flutter/image_compression_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/classes/renderImage.dart';
import 'dart:math';
import 'package:newarc_platform/app_const.dart' as appConst;
import 'package:image/image.dart' as img;
import 'package:path/path.dart' as path;


class NarBlockFilePickerWidget extends StatefulWidget {
  
  final String? displayType;
  final int? containerCount;
  final bool? roomsSelection;
  final num? filesToDisplayInList;
  final Function onUploadCompleted;
  final Function? onImageSelection;
  final List<String>? progressMessage;
  final String selectionType;
  final double? containerHeight;
  final double? containerWidth;
  final double? containerBorderRadius;
  final String? dialogTitle; 
  

  final String? invokerButtonText;
  final List<RenderImageList> renderImageList;
  final List<RenderImage> gallery;
  final String storageLocation;
  final Color? buttonColor;
  final Color? buttonTextColor;
  final Color? buttonBorderColor;
  final bool? buttonNotAccent;
  final double? buttonHeight;
  final double? buttonWidth;

  //---for image resize pass isResizeImage == true and resizeImageSize = [[width,height,thumbnail]]
  final bool? isResizeImage;
  final List? resizeImageSize;

  NarBlockFilePickerWidget({
      this.displayType = 'button',
      this.containerCount = 1,
      this.roomsSelection = false,
      this.containerHeight = 100,
      this.containerWidth = 100,
      this.containerBorderRadius = 10,
      this.filesToDisplayInList = 0,
      required this.onUploadCompleted,
      this.onImageSelection,
      this.progressMessage = const [''],
      required this.selectionType,
      this.dialogTitle = 'Render',
      this.invokerButtonText = 'Gestisci',
      required this.renderImageList,
      required this.storageLocation,
      this.gallery = const [],
      this.buttonColor = Colors.grey,
      this.buttonTextColor = Colors.black,
      this.buttonBorderColor = Colors.transparent,
      this.buttonNotAccent = false,
      this.buttonHeight = 35,
      this.buttonWidth = 125,
      this.isResizeImage = false,
      this.resizeImageSize,

  });

  //selectiongType : none, beforeAfter, dayNight

  @override
  _NarBlockFilePickerWidgetState createState() => _NarBlockFilePickerWidgetState();
}

class _NarBlockFilePickerWidgetState extends State<NarBlockFilePickerWidget> {
  
  List<String> formMessages = [''];
  
  @override
  void initState() {
    super.initState();
    updateFiles();
    
    
  }

  @protected
  void didUpdateWidget(NarBlockFilePickerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  showImageGallery( int currentIndex, ___popupSetState ) {

    showDialog(
      context: context, 
      builder: (BuildContext _bc1) {
          return StatefulBuilder(
            builder: (BuildContext _bc2, StateSetter _setState) {
              return Center(
                child: Material(
                  borderRadius: BorderRadius.circular(20), 
                  child: Container(
                    width: 750,
                    height: MediaQuery.of(context).size.height * 0.98,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20), 
                      color: Colors.white
                    ),
                    padding: EdgeInsets.only( top: 20, bottom: 30, left: 50, right: 50),
                  
                    child: Stack(
                      clipBehavior: Clip.none,
                      children: [
                        Positioned(
                          top: 0,
                          right: 0,
                          child: Container(
                            height: 15,
                            width: 15,
                            child: MouseRegion(
                              cursor: SystemMouseCursors.click,
                              child: GestureDetector(
                                  child: SvgPicture.asset(
                                    'assets/icons/close-popup.svg',
                                    width: 15,
                                  ),
                                  onTap: () {
                                    Navigator.pop(context);
                                  }),
                            ),
                          ),
                        ),
                        Column(
                                          
                          children: [
                            NarFormLabelWidget(
                              label: 'Seleziona un render',
                              textColor: Colors.black,
                              fontSize: 20,
                              fontWeight: 'bold',
                            ),
                        
                            Container(
                              height: MediaQuery.of(context).size.height * 0.7,
                              padding: EdgeInsets.only(top: 20),
                              child: ListView(
                                shrinkWrap: true,
                                children: [
                                  Wrap(
                                    
                                    children: [
                                                          
                                      ...widget.gallery.asMap().entries.map((_galleryImage) {

                                        RenderImage galleryImage = _galleryImage.value;
                                        int galleryIndex = _galleryImage.key;
                                        
                                        BoxDecoration decoration = BoxDecoration(
                                            borderRadius: BorderRadius.circular(widget.containerBorderRadius??7),
                                            border: Border.all(
                                              width: 3, 
                                              color: galleryImage.isSelected! ? Theme.of(context).primaryColor : Colors.transparent
                                            ),
                                            image: DecorationImage( 
                                              image: NetworkImage(galleryImage.filenameUrl! ),
                                              fit: BoxFit.cover,
                                            )
                                          );
                                          
                                        
                                        
                                      
                                        return Container(
                                          width: widget.containerWidth,
                                          margin: EdgeInsets.only(right: 15, bottom: 15),
                                          child: MouseRegion(
                                            cursor: SystemMouseCursors.click,
                                            child: GestureDetector(
                                              onTap: () async{
                                                
                                                if( widget.selectionType == 'beforeAfter' ) {
                                                  widget.renderImageList[currentIndex].renderImageSecond!.filename = ''; 
                                                  widget.renderImageList[currentIndex].renderImageSecond!.filenameUrl = ''; 
                                                  widget.renderImageList[currentIndex].renderImageSecond!.room = ''; 
                                                  widget.renderImageList[currentIndex].renderImageSecond!.location = ''; 
                                                  widget.renderImageList[currentIndex].renderImageSecond!.isNetworkImage = false; 
                                                } else {
                                                  widget.renderImageList[currentIndex].renderImageFirst!.filename = ''; 
                                                  widget.renderImageList[currentIndex].renderImageFirst!.filenameUrl = ''; 
                                                  widget.renderImageList[currentIndex].renderImageFirst!.room = ''; 
                                                  widget.renderImageList[currentIndex].renderImageFirst!.location = ''; 
                                                  widget.renderImageList[currentIndex].renderImageFirst!.isNetworkImage = false; 
                                                }
                                                

                                                if( widget.gallery[galleryIndex].isSelected == true ) {
                                                  widget.gallery[galleryIndex].isSelected = false;
                                                } else {
                                                  for( int rd = 0; rd < widget.gallery.length; rd++ ) {
                                                    widget.gallery[rd].isSelected = false;
                                                  }
                                                  widget.gallery[galleryIndex].isSelected = true;

                                                  if( widget.selectionType == 'beforeAfter' ) {
                                                    widget.renderImageList[currentIndex].renderImageSecond!.filename = widget.gallery[galleryIndex].filename; 
                                                    widget.renderImageList[currentIndex].renderImageSecond!.filenameUrl = widget.gallery[galleryIndex].filenameUrl; 
                                                    widget.renderImageList[currentIndex].renderImageSecond!.room = widget.gallery[galleryIndex].room; 
                                                    widget.renderImageList[currentIndex].renderImageSecond!.location = widget.gallery[galleryIndex].location; 
                                                    widget.renderImageList[currentIndex].renderImageSecond!.isNetworkImage = widget.gallery[galleryIndex].isNetworkImage; 
                                                    
                                                  } else if( widget.selectionType == 'dayNight' ||  widget.selectionType == 'singleImage' ) {
                                                    widget.renderImageList[currentIndex].renderImageFirst!.filename = widget.gallery[galleryIndex].filename; 
                                                    widget.renderImageList[currentIndex].renderImageFirst!.filenameUrl = widget.gallery[galleryIndex].filenameUrl; 
                                                    widget.renderImageList[currentIndex].renderImageFirst!.room = widget.gallery[galleryIndex].room; 
                                                    widget.renderImageList[currentIndex].renderImageFirst!.location = widget.gallery[galleryIndex].location; 
                                                    widget.renderImageList[currentIndex].renderImageFirst!.isNetworkImage = widget.gallery[galleryIndex].isNetworkImage; 
                                                  
                                                  }

                                                  

                                                  
                                                }
                                                
                                                
                                                _setState((){});
                                                
                                              },
                                              child: Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  
                                                  Container(
                                                    decoration: decoration,
                                                    height: widget.containerHeight,
                                                    width: widget.containerWidth,
                                                  ),
                                                  SizedBox(height: 5,),
                                                  NarFormLabelWidget(
                                                    label: galleryImage.room,
                                                    fontSize: 12,
                                                    textColor: Color(0xff717171),
                                                    fontWeight: '600',
                                                  ),
                                                  
                                              
                                                ],
                                              ),
                                            ),
                                          ),
                                        );
                                      }).toList(),
                                  
                                      // emptyImageAddBlockTypeNone(),
                                    ],
                                  ),
                                ],
                              ),
                              
                            ),
                            
                            SizedBox(height: 25,), 
                        
                            BaseNewarcButton(
                              width: 200,
                              onPressed: () async {
                                Navigator.pop(context);
                                ___popupSetState((){}); 

                                if( widget.onImageSelection != null) widget.onImageSelection!();
                                
                                setState((){});

                                // Unselect all Gallery Image
                                for( int rd = 0; rd < widget.gallery.length; rd++ ) {
                                  widget.gallery[rd].isSelected = false;
                                }
                              },
                              buttonText: 'Conferma',
                              textColor: Colors.white,
                              fontSize: 18,
                              fontWeight: 'bold',
                              
                            )
                                          
                                          
                          ],
                        ),
                      ],
                    ),
                  
                  
                  ),
                ),
              );
            }
          );
      }
    );

  
  }

  updateFiles() async {
    
    if( widget.renderImageList.isNotEmpty ) {

      for (var rd = 0; rd < widget.renderImageList.length; rd++) {
        
        if (widget.renderImageList[rd].renderImageFirst?.filename != null && widget.renderImageList[rd].renderImageFirst!.filename != '') {
          RenderImage _fRD = widget.renderImageList[rd].renderImageFirst!;
          widget.renderImageList[rd].renderImageFirst!.filenameUrl = await printUrl(_fRD.location, '', _fRD.filename);
        }

        if (widget.renderImageList[rd].renderImageSecond?.filename != null && widget.renderImageList[rd].renderImageSecond!.filename != '') {
          RenderImage _fRD = widget.renderImageList[rd].renderImageSecond!;
          widget.renderImageList[rd].renderImageSecond!.filenameUrl = await printUrl(_fRD.location, '', _fRD.filename);
        }
      }

    }
  
  }

  Row sortingArrows( int currentIndex, _setState ) {

    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: (){
              if( currentIndex == 0 ) return;
              
              widget.renderImageList[currentIndex].index = widget.renderImageList[currentIndex].index! - 1;
              widget.renderImageList[currentIndex-1].index = widget.renderImageList[currentIndex-1].index! + 1;

              widget.renderImageList.sort((a, b) => a.index!.compareTo(b.index!));

              _setState((){});
              widget.onUploadCompleted();

              // widget.renderImageList[currentIndex] = widget.renderImageList[currentIndex -1];
            },
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5),
                color: Color(0xffE6E6E6)
              ),
              height: 20,
              width: 20,
              child: Center(
                child: Transform.rotate(
                  angle: pi * 1.5,
                  child: SvgPicture.asset(
                    'assets/icons/arrow.svg',
                    width: 10,
                    color: const Color(0xff6E6E6E),
                  ),
                ),
              )
            ),
          ),
        ),
                
        SizedBox(width: 5),
                
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: (){
              if( currentIndex == (widget.renderImageList.length - 1 ) ) return;
              
              widget.renderImageList[currentIndex].index = widget.renderImageList[currentIndex].index! + 1;
              widget.renderImageList[currentIndex+1].index = widget.renderImageList[currentIndex+1].index! - 1;

              widget.renderImageList.sort((a, b) => a.index!.compareTo(b.index!));
              _setState((){});
              widget.onUploadCompleted();
            },
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5),
                color: Color(0xffE6E6E6)
              ),
              height: 20,
              width: 20,
              child: Center(
                child: Transform.rotate(
                  angle: pi * 2.5,
                  child: SvgPicture.asset(
                    'assets/icons/arrow.svg',
                    width: 10,
                    color: const Color(0xff6E6E6E),
                  ),
                ),
              )
            ),
          ),
        ),
                
      ],
    );
  }

  removeRow( int currentIndex, _setState ){
    
    showDialog(
      context: context, 
      builder: (BuildContext _bc1) {
        return Center(
          child: Container(
            width: 500,
            child: BaseNewarcPopup(
              title: 'Attenzione!', 
              column: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  NarFormLabelWidget(label: 'Once deleted the item can not be recovered. Are you sure?', overflow: TextOverflow.visible,)
                ],
              ),
              buttonText: 'Proceed',
              onPressed: () {
                
                widget.renderImageList.removeAt(currentIndex);
                for( int i = 0; i < widget.renderImageList.length; i++ ) {
                  widget.renderImageList[i].index = i;
                }
            
                _setState((){});
                widget.onUploadCompleted();
            
              },
            ),
          ),
        );
      }
    );
  }

  imageDialogTypeNone() {
    formMessages.clear();
    formMessages.add('');
    showDialog(
      context: context, 
      builder: (BuildContext _bc1) {
          return StatefulBuilder(
            builder: (BuildContext _bc2, StateSetter _setState) {
              return Center(
                child: Material(
                  borderRadius: BorderRadius.circular(20), 
                  child: Container(
                    width: 750,
                    height: MediaQuery.of(context).size.height * 0.98,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20), 
                      color: Colors.white
                    ),
                    padding: EdgeInsets.only( top: 20, bottom: 30, left: 50, right: 50),
                  
                    child: Stack(
                      clipBehavior: Clip.none,
                      children: [
                        Positioned(
                          top: 0,
                          right: 0,
                          child: Container(
                            height: 15,
                            width: 15,
                            child: MouseRegion(
                              cursor: SystemMouseCursors.click,
                              child: GestureDetector(
                                  child: SvgPicture.asset(
                                    'assets/icons/close-popup.svg',
                                    width: 15,
                                  ),
                                  onTap: () {
                                    Navigator.pop(context);
                                  }),
                            ),
                          ),
                        ),
                        Column(
                                          
                          children: [
                            NarFormLabelWidget(
                              label: widget.dialogTitle,
                              textColor: Colors.black,
                              fontSize: 20,
                              fontWeight: 'bold',
                            ),
                        
                            Container(
                              height: MediaQuery.of(context).size.height * 0.7,
                              child: ListView(
                                shrinkWrap: true,
                                children: [
                        
                                  ...widget.renderImageList.map((renderImage) {
                        
                                    
                                    BoxDecoration decoration;
                                    
                        
                                    int currentIndex = widget.renderImageList.indexWhere((l) => l.index == renderImage.index);
                        
                                    if( widget.renderImageList[currentIndex].renderImageFirst!.tmpFile != null ) {
                        
                                      decoration  = BoxDecoration(
                                        borderRadius: BorderRadius.circular(widget.containerBorderRadius??7),
                                        // border: Border.all(width: 1, color: Color(0xff999999)),
                                        image: DecorationImage( 
                                          image: MemoryImage( widget.renderImageList[currentIndex].renderImageFirst!.imageBytes! ),
                                          fit: BoxFit.cover,
                                        )
                                      );
                                    } else if( widget.renderImageList[currentIndex].renderImageFirst!.isNetworkImage == true ) {
                                      decoration  = BoxDecoration(
                                        borderRadius: BorderRadius.circular(widget.containerBorderRadius??7),
                                        // border: Border.all(width: 1, color: Color(0xff999999)),
                                        image: DecorationImage( 
                                          image: NetworkImage(widget.renderImageList[currentIndex].renderImageFirst!.filenameUrl! ),
                                          fit: BoxFit.cover,
                                        )
                                      );
                                      
                                    } else {
                                      
                                      decoration  = BoxDecoration(
                                        borderRadius: BorderRadius.circular(widget.containerBorderRadius??7),
                                        border: Border.all(width: 1, color: Color(0xff999999)),
                                        
                                      );
                                    }
                                  
                                    return Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(10),
                                        border: Border.all( width: 1, color: Color(0xffD9D9D9) )
                                      ),
                                      margin: EdgeInsets.only(bottom: 15),
                                      padding: EdgeInsets.all(10),
                                      child: Column(
                                        children: [
                                          Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              sortingArrows( currentIndex, _setState ),
                                              Column(
                                                children: [
                                                  Row(
                                                    children: [
                                                      NarFormLabelWidget(
                                                        label: 'Annuncio',
                                                        fontSize: 13,
                                                        fontWeight: '600',
                                                      ),
                                                      Transform.scale(
                                                        scale: 0.7,
                                                        child: SizedBox(
                                                          width: 50,
                                                          child: Switch(
                                                            activeThumbColor: Theme.of(context).primaryColor,
                                                            value: widget.renderImageList[currentIndex].renderImageFirst!.isEnabledForWebsite!, 
                                                            onChanged: ( value ) {
                                                              _setState((){
                                                                widget.renderImageList[currentIndex].renderImageFirst!.isEnabledForWebsite = value; 
                                                              });
                                                            }
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  SizedBox(height:5),
                                                  Row(
                                                    children: [
                                                      NarFormLabelWidget(
                                                        label: 'Brochure',
                                                        fontSize: 13,
                                                        fontWeight: '600',
                                                      ),
                                                      Transform.scale(
                                                        scale: 0.7,
                                                        child: SizedBox(
                                                          width: 50,
                                                          child: Switch(
                                                            activeThumbColor: Theme.of(context).primaryColor,
                                                            value: widget.renderImageList[currentIndex].renderImageFirst!.isBrochure!, 
                                                            onChanged: ( value ) {
                                                              _setState((){
                                                                widget.renderImageList[currentIndex].renderImageFirst!.isBrochure = value; 
                                                              });
                                                            }
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              )
                                            ],
                                          ),
                                          
                                          Row(
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            children: [
                                              Container(
                                                width: widget.containerWidth,
                                                child: Column(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  children: [
                                                    
                                                    MouseRegion(
                                                      cursor: SystemMouseCursors.click,
                                                      child: GestureDetector(
                                                        onTap: () async{
                                                          widget.renderImageList[currentIndex].renderImageFirst!.tmpFile = await widget.renderImageList[currentIndex].renderImageFirst!.picker.pickImage(source: ImageSource.gallery);
                                                          if( widget.renderImageList[currentIndex].renderImageFirst!.tmpFile != null ) {
                                                            widget.renderImageList[currentIndex].renderImageFirst!.imageBytes = await widget.renderImageList[currentIndex].renderImageFirst!.tmpFile!.readAsBytes();
                                                          }
                                                          
                        
                                                          _setState((){});
                                                        },
                                                        child: Container(
                                                          decoration: decoration,
                                                          height: widget.containerHeight,
                                                          width: widget.containerWidth,
                                                          child: widget.renderImageList[currentIndex].renderImageFirst!.tmpFile == null && widget.renderImageList[currentIndex].renderImageFirst!.isNetworkImage == false
                                                          ? Center(
                                                            child: Transform.rotate(
                                                              angle: pi/4,
                                                              child: SvgPicture.asset(
                                                                'assets/icons/close-popup.svg',
                                                                width: 20,
                                                                color: const Color(0xff999999),
                                                              ),
                                                            ),
                                                          )
                                                          : Container(),
                                                        
                                                        ),
                                                      ),
                                                    ),
                                                    SizedBox(height: 5,),
                                                    NarFormLabelWidget(
                                                      label: 'Stanza',
                                                      fontSize: 12,
                                                      textColor: Color(0xff717171),
                                                      fontWeight: '600',
                                                    ),
                                                    SizedBox(height: 4,),
                                                    Row(
                                                      mainAxisAlignment: MainAxisAlignment.start,
                                                      children: [
                                                        Expanded(
                                                          child: NarSelectBoxWidget(
                                                            options: List.of(appConst.roomsList)..sort((a, b) => a.toLowerCase().compareTo(b.toLowerCase())),
                                                            controller: widget.renderImageList[currentIndex].renderImageFirst!.contRoom,
                                                            contentPadding: EdgeInsets.only(top: 10, bottom: 8, right: 2, left: 10),
                                                            dropdownFontSize: 11,
                                                          ),
                                                        ),
                                                      ],
                                                    )
                                                
                                                  ],
                                                ),
                                              )
                                                      
                                            ],
                                          ),
                                
                                          MouseRegion(
                                            cursor: SystemMouseCursors.click,
                                            child: GestureDetector(
                                              onTap: (){
                                                removeRow(currentIndex, _setState );
                                              },
                                              child: Row(
                                                crossAxisAlignment: CrossAxisAlignment.center,
                                                mainAxisAlignment: MainAxisAlignment.end,
                                                mainAxisSize: MainAxisSize.max,
                                                children: [
                                                  NarFormLabelWidget(
                                                    label: 'Elimina',
                                                    textColor: Color(0xffB5B4B4),
                                                    fontSize: 13,
                                                    textDecoration: TextDecoration.underline,
                                                    fontWeight: '600',
                                                  ),
                                                  SizedBox(width:5),
                                                  SvgPicture.asset(
                                                    "assets/icons/trash.svg",
                                                    color: Color(0xffB5B4B4),
                                                    height: 20,
                                                  ),
                                                ],
                                              ),
                                            ),
                                          )
                                        ],
                                      ),
                                    );
                                  }).toList(),
                              
                                  // emptyImageAddBlockTypeNone(),
                                ],
                              ),
                              
                            ),
                            
                            SizedBox(height: 10,),
                        
                            BaseNewarcButton(
                              width: 190,
                              onPressed: (){
                                RenderImageList tmpList = RenderImageList.empty();
                                tmpList.index = widget.renderImageList.length;
                                widget.renderImageList.add( tmpList );
                                _setState((){});
                              },
                              buttonText: 'Aggiungi',
                              textColor: Color(0xff636363),
                              fontSize: 18,
                              fontWeight: 'bold',
                              notAccent: true,
                        
                            ),
                        
                            SizedBox(height: 25,), 
                        
                            if( formMessages[0] != '' ) Container(
                              margin: EdgeInsets.only(bottom: 5),
                              child: NarFormLabelWidget(label: formMessages[0])
                            ),
                        
                            BaseNewarcButton(
                              width: 200,
                              onPressed: () async {
                                formMessages.clear();
                                formMessages.add('Saving...');
                                _setState(() {});

                                await Future.forEach(widget.renderImageList, (RenderImageList _renderList) async {
                                  int currentIndex = widget.renderImageList.indexWhere((e) => _renderList.index == e.index);

                                  if (_renderList.renderImageFirst!.tmpFile != null) {
                                    await uploadFileDelayed(
                                      widget.storageLocation,
                                      _renderList.renderImageFirst!.tmpFile!.name,
                                      _renderList.renderImageFirst!.tmpFile!,
                                    );
                                    if (widget.isResizeImage ?? false) {
                                      if (widget.resizeImageSize?.isNotEmpty ?? false) {
                                        for (int i = 0; i < widget.resizeImageSize!.length; i++) {
                                          List size = widget.resizeImageSize![i];
                                          final resizedFile = await resizeXFile(
                                            _renderList.renderImageFirst!.tmpFile!,
                                            width: size[0],
                                            height: size[1],
                                            quality: 50,
                                            thumbnailTitle: size[2]
                                          );

                                          await uploadFileDelayed(
                                            widget.storageLocation,
                                            resizedFile.name,
                                            resizedFile,
                                          );
                                        }
                                      }
                                    }

                                    widget.renderImageList[currentIndex].renderImageFirst!.filename =
                                        _renderList.renderImageFirst!.tmpFile!.name;
                                    widget.renderImageList[currentIndex].renderImageFirst!.location = widget.storageLocation;
                                    widget.renderImageList[currentIndex].renderImageFirst!.tmpFile = null;
                                    widget.renderImageList[currentIndex].renderImageFirst!.imageBytes = null;
                                    widget.renderImageList[currentIndex].renderImageFirst!.isNetworkImage = true;
                                    widget.renderImageList[currentIndex].renderImageFirst!.room =
                                        widget.renderImageList[currentIndex].renderImageFirst!.contRoom!.text;

                                    widget.renderImageList[currentIndex].renderImageFirst!.filenameUrl =
                                        await printUrl(widget.storageLocation, '', widget.renderImageList[currentIndex].renderImageFirst!.filename);


                                  }

                                  if (widget.renderImageList[currentIndex].renderImageFirst!.filename == '') {
                                    widget.renderImageList.removeAt(currentIndex);
                                  }

                                  _setState(() {});
                                });

                                for (int i = 0; i < widget.renderImageList.length; i++) {
                                  widget.renderImageList[i].index = i;
                                }

                                widget.onUploadCompleted();
                                if (mounted) {
                                  Navigator.pop(context); // Ensures widget is still in tree before popping
                                }
                              },
                              buttonText: 'Conferma',
                              textColor: Colors.white,
                              fontSize: 18,
                              fontWeight: 'bold',
                            )
                                          
                                          
                          ],
                        ),
                      ],
                    ),
                  
                  
                  ),
                ),
              );
            }
          );
      }
    );
  
  }

  Future<XFile> resizeXFile(XFile originalFile, {int width = 670, int height = 195, int quality = 70,String thumbnailTitle = "thumbnail"}) async {
    final bytes = await originalFile.readAsBytes();
    final image = img.decodeImage(bytes);

    if (image == null) throw Exception("Could not decode image.");

    img.Image resized;


    if (image.width > width || image.height > height) {
      // Always fixed width, auto height to maintain aspect ratio
      final double aspectRatio = image.height / image.width;
      final int calculatedHeight = (width * aspectRatio).floor();

      resized = img.copyResize(image, width: width, height: calculatedHeight, maintainAspect: true,interpolation: img.Interpolation.average);
    } else {
      resized = image;
    }

    final ext = path.extension(originalFile.name).toLowerCase();
    final baseName = path.basenameWithoutExtension(originalFile.name);
    Uint8List compressedBytes;


    if (ext == '.png') {
      compressedBytes = Uint8List.fromList(img.encodePng(resized, level: 0));
    } else {
      compressedBytes = Uint8List.fromList(img.encodeJpg(resized, quality: quality));
    }

    return XFile.fromData(
      compressedBytes,
      name: "${baseName}_$thumbnailTitle${ext}",
      mimeType: originalFile.mimeType,
    );
  }


  imageDialogTypeDayNight() {
    formMessages.clear();
    formMessages.add('');
    showDialog(
      context: context, 
      builder: (BuildContext _bc1) {
          return StatefulBuilder(
            builder: (BuildContext _bc2, StateSetter _setState) {
              return Center(
                child: Material(
                  borderRadius: BorderRadius.circular(20), 
                  child: Container(
                    width: 750,
                    height: MediaQuery.of(context).size.height * 0.98,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20), 
                      color: Colors.white
                    ),
                    padding: EdgeInsets.only( top: 20, bottom: 30, left: 50, right: 50),
                  
                    child: Stack(
                      clipBehavior: Clip.none,
                      children: [
                        Positioned(
                          top: 0,
                          right: 0,
                          child: Container(
                            height: 15,
                            width: 15,
                            child: MouseRegion(
                              cursor: SystemMouseCursors.click,
                              child: GestureDetector(
                                  child: SvgPicture.asset(
                                    'assets/icons/close-popup.svg',
                                    width: 15,
                                  ),
                                  onTap: () {
                                    Navigator.pop(context);
                                  }),
                            ),
                          ),
                        ),
                        Column(
                                          
                          children: [
                            NarFormLabelWidget(
                              label: widget.dialogTitle,
                              textColor: Colors.black,
                              fontSize: 20,
                              fontWeight: 'bold',
                            ),
                        
                            Container(
                              height: MediaQuery.of(context).size.height * 0.7,
                              child: ListView(
                                shrinkWrap: true,
                                children: [
                        
                                  ...widget.renderImageList.map((renderImage) {
                        
                                    
                                    BoxDecoration decoration;
                                    BoxDecoration decoration2;

                                    int currentIndex = widget.renderImageList.indexWhere((l) => l.index == renderImage.index);
                        
                                    if( widget.renderImageList[currentIndex].renderImageFirst!.isNetworkImage == true ) {
                                      decoration  = BoxDecoration(
                                        borderRadius: BorderRadius.circular(widget.containerBorderRadius??7),
                                        // border: Border.all(width: 1, color: Color(0xff999999)),
                                        image: DecorationImage( 
                                          image: NetworkImage(widget.renderImageList[currentIndex].renderImageFirst!.filenameUrl! ),
                                          fit: BoxFit.cover,
                                        )
                                      );
                                      
                                    } else {
                                      
                                      decoration  = BoxDecoration(
                                        borderRadius: BorderRadius.circular(widget.containerBorderRadius??7),
                                        border: Border.all(width: 1, color: Color(0xff999999)),
                                        
                                      );
                                    }

                                    // Second box decoration
                                    if( widget.renderImageList[currentIndex].renderImageSecond!.tmpFile != null ) {
                        
                                      decoration2  = BoxDecoration(
                                        borderRadius: BorderRadius.circular(widget.containerBorderRadius??7),
                                        // border: Border.all(width: 1, color: Color(0xff999999)),
                                        image: DecorationImage( 
                                          image: MemoryImage( widget.renderImageList[currentIndex].renderImageSecond!.imageBytes! ),
                                          fit: BoxFit.cover,
                                        )
                                      );
                                    } else if( widget.renderImageList[currentIndex].renderImageSecond!.isNetworkImage == true ) {
                                      decoration2  = BoxDecoration(
                                        borderRadius: BorderRadius.circular(widget.containerBorderRadius??7),
                                        // border: Border.all(width: 1, color: Color(0xff999999)),
                                        image: DecorationImage( 
                                          image: NetworkImage(widget.renderImageList[currentIndex].renderImageSecond!.filenameUrl! ),
                                          fit: BoxFit.cover,
                                        )
                                      );
                                      
                                    } else {
                                      
                                      decoration2  = BoxDecoration(
                                        borderRadius: BorderRadius.circular(widget.containerBorderRadius??7),
                                        border: Border.all(width: 1, color: Colors.transparent),
                                        color: Color(0xffEDEDED)
                                      );
                                    }
                                  
                                    return Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(10),
                                        border: Border.all( width: 1, color: Color(0xffD9D9D9) )
                                      ),
                                      margin: EdgeInsets.only(bottom: 15),
                                      padding: EdgeInsets.all(10),
                                      child: Column(
                                        children: [
                                          sortingArrows( currentIndex, _setState ),
                                          
                                          Row(
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            children: [
                                              
                                              Container(
                                                width: widget.containerWidth,
                                                child: Column(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  children: [
                                                    
                                                    MouseRegion(
                                                      cursor: SystemMouseCursors.click,
                                                      child: GestureDetector(
                                                        onTap: () async{
                                                          showImageGallery(currentIndex, _setState);
                                                          
                                                        },
                                                        child: Container(
                                                          decoration: decoration,
                                                          height: widget.containerHeight,
                                                          width: widget.containerWidth,
                                                          child: widget.renderImageList[currentIndex].renderImageFirst!.isNetworkImage == false
                                                          ? Center(
                                                            child: Transform.rotate(
                                                              angle: pi/4,
                                                              child: SvgPicture.asset(
                                                                'assets/icons/close-popup.svg',
                                                                width: 20,
                                                                color: const Color(0xff999999),
                                                              ),
                                                            ),
                                                          )
                                                          : Container(),
                                                        
                                                        ),
                                                      ),
                                                    ),
                                                    
                                                
                                                  ],
                                                ),
                                              ),
                                              
                                              // Second Box

                                              Opacity(
                                                opacity: widget.renderImageList[currentIndex].renderImageFirst!.isNetworkImage!
                                                ? 1
                                                : 0.5,

                                                child: Container(
                                                  width: widget.containerWidth,
                                                  margin: EdgeInsets.only(left:20),
                                                  child: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      
                                                      MouseRegion(
                                                        cursor: SystemMouseCursors.click,
                                                        child: GestureDetector(
                                                          onTap: () async{
                                                            
                                                            widget.renderImageList[currentIndex].renderImageSecond!.tmpFile = await widget.renderImageList[currentIndex].renderImageSecond!.picker.pickImage(source: ImageSource.gallery);
                                                            if( widget.renderImageList[currentIndex].renderImageSecond!.tmpFile != null ) {
                                                              widget.renderImageList[currentIndex].renderImageSecond!.imageBytes = await widget.renderImageList[currentIndex].renderImageSecond!.tmpFile!.readAsBytes();
                                                            }
                                                            
                                                                        
                                                            _setState((){});
                                                
                                                            _setState((){});
                                                          },
                                                          child: Container(
                                                            decoration: decoration2,
                                                            height: widget.containerHeight,
                                                            width: widget.containerWidth,
                                                            child: widget.renderImageList[currentIndex].renderImageSecond!.tmpFile == null && widget.renderImageList[currentIndex].renderImageSecond!.isNetworkImage == false
                                                            ? Center(
                                                              child: Transform.rotate(
                                                                angle: pi/4,
                                                                child: SvgPicture.asset(
                                                                  'assets/icons/close-popup.svg',
                                                                  width: 20,
                                                                  color: widget.renderImageList[currentIndex].renderImageFirst!.isNetworkImage!
                                                                  ? const Color(0xff999999)
                                                                  : Colors.white,
                                                                ),
                                                              ),
                                                            )
                                                            : Container(),
                                                          
                                                          ),
                                                        ),
                                                      ),
                                                      
                                                  
                                                    ],
                                                  ),
                                                ),
                                              )
                                                      
                                            ],
                                          ),
                                
                                          MouseRegion(
                                            cursor: SystemMouseCursors.click,
                                            child: GestureDetector(
                                              onTap: (){
                                                removeRow(currentIndex, _setState );
                                              },
                                              child: Row(
                                                crossAxisAlignment: CrossAxisAlignment.center,
                                                mainAxisAlignment: MainAxisAlignment.end,
                                                mainAxisSize: MainAxisSize.max,
                                                children: [
                                                  NarFormLabelWidget(
                                                    label: 'Elimina',
                                                    textColor: Color(0xffB5B4B4),
                                                    fontSize: 13,
                                                    textDecoration: TextDecoration.underline,
                                                    fontWeight: '600',
                                                  ),
                                                  SizedBox(width:5),
                                                  SvgPicture.asset(
                                                    "assets/icons/trash.svg",
                                                    color: Color(0xffB5B4B4),
                                                    height: 20,
                                                  ),
                                                ],
                                              ),
                                            ),
                                          )
                                        ],
                                      ),
                                    );
                                  }).toList(),
                              
                                  // emptyImageAddBlockTypeNone(),
                                ],
                              ),
                              
                            ),
                            
                            SizedBox(height: 10,),
                        
                            BaseNewarcButton(
                              width: 190,
                              onPressed: (){
                                RenderImageList tmpList = RenderImageList.empty();
                                tmpList.index = widget.renderImageList.length;
                                widget.renderImageList.add( tmpList );
                                _setState((){});
                              },
                              buttonText: 'Aggiungi',
                              textColor: Color(0xff636363),
                              fontSize: 18,
                              fontWeight: 'bold',
                              notAccent: true,
                        
                            ),
                        
                            SizedBox(height: 25,), 
                        
                            if( formMessages[0] != '' ) Container(
                              margin: EdgeInsets.only(bottom: 5),
                              child: NarFormLabelWidget(label: formMessages[0])
                            ),
                        
                            BaseNewarcButton(
                              width: 200,
                              onPressed: () async {
                                print("==============> Block File Picker");
                                
                                formMessages.clear();
                                formMessages.add('Saving...');
                                _setState((){});

                                final renderListToUpload = widget.renderImageList
                                    .where((e) => e.renderImageSecond?.tmpFile != null).toList();

                                await Future.forEach(renderListToUpload, (RenderImageList _renderList) async {

                                  int currentIndex = widget.renderImageList.indexWhere((e) => _renderList.index == e.index );
                                  if( _renderList.renderImageSecond!.tmpFile != null ) {
                                    await uploadFileDelayed( widget.storageLocation, _renderList.renderImageSecond!.tmpFile!.name, _renderList.renderImageSecond!.tmpFile! );
                                    if (widget.isResizeImage ?? false) {
                                      if (widget.resizeImageSize?.isNotEmpty ?? false) {
                                        for (int i = 0; i < widget.resizeImageSize!.length; i++) {
                                          List size = widget.resizeImageSize![i];
                                          final resizedFile = await resizeXFile(
                                              _renderList.renderImageSecond!.tmpFile!,
                                              width: size[0],
                                              height: size[1],
                                              quality: 50,
                                              thumbnailTitle: size[2]
                                          );

                                          await uploadFileDelayed(
                                            widget.storageLocation,
                                            resizedFile.name,
                                            resizedFile,
                                          );
                                        }
                                      }
                                    }
                                    widget.renderImageList[currentIndex].renderImageSecond!.filename = _renderList.renderImageSecond!.tmpFile!.name;
                                    widget.renderImageList[currentIndex].renderImageSecond!.location = widget.storageLocation;
                                    widget.renderImageList[currentIndex].renderImageSecond!.tmpFile = null;
                                    widget.renderImageList[currentIndex].renderImageSecond!.imageBytes = null;
                                    widget.renderImageList[currentIndex].renderImageSecond!.isNetworkImage = true;
                                    widget.renderImageList[currentIndex].renderImageSecond!.room = widget.renderImageList[currentIndex].renderImageSecond!.contRoom!.text;
                        
                                    widget.renderImageList[currentIndex].renderImageSecond!.filenameUrl = await printUrl( widget.storageLocation, '', widget.renderImageList[currentIndex].renderImageSecond!.filename );
                                    
                                  }

                                  if( widget.renderImageList[currentIndex].renderImageSecond!.filename == '' ) {
                                    widget.renderImageList.removeAt(currentIndex);
                                  }


                                  
                                  // widget.onUploadCompleted();
                                  // Navigator.pop(context);
                                  // Navigator.pop(context);
                                  
                                });
                                formMessages.clear();
                                formMessages.add('Saved!');

                                _setState((){});

                                for( int i = 0; i < widget.renderImageList.length; i++ ) {
                                  widget.renderImageList[i].index = i;
                                }

                                widget.onUploadCompleted();
                                if (mounted) {
                                  Navigator.pop(context); // Ensures widget is still in tree before popping
                                }

                              
                              },
                              buttonText: 'Conferma',
                              textColor: Colors.white,
                              fontSize: 18,
                              fontWeight: 'bold',
                              
                            )
                                          
                                          
                          ],
                        ),
                      ],
                    ),
                  
                  
                  ),
                ),
              );
            }
          );
      }
    );
  
  }


  setImageUrl() async {

    for( int i = 0; i < widget.renderImageList.length; i++ ) {
      
      if( widget.renderImageList[i].renderImageFirst!.filenameUrl != '' ) continue;
      
      widget.renderImageList[i].renderImageFirst!.filenameUrl = await printUrl( widget.renderImageList[i].renderImageFirst!.location, '', widget.renderImageList[i].renderImageFirst!.filename ); 
      
      setState((){});

      
    }
    
  }
  singleImageContainer() {

    // widget.renderImageList.map((e) {
    //   await printUrl( widget.storageLocation, '', widget.renderImageList[currentIndex].renderImageFirst!.filename );
    // } );

    setImageUrl();

    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: List.generate(widget.containerCount!, (index) {

        BoxDecoration decoration;
        int currentIndex = index;
        Color plusColor = Color(0xff999999);
        if( widget.renderImageList[currentIndex].renderImageFirst!.isNetworkImage == true && widget.renderImageList[currentIndex].renderImageFirst!.filename != '' ) {
          decoration  = BoxDecoration(
            borderRadius: BorderRadius.circular(widget.containerBorderRadius??7),
            border: Border.all(width: 1, color: Color(0xff999999)),
            image: DecorationImage( 
              image: NetworkImage(widget.renderImageList[currentIndex].renderImageFirst!.filenameUrl! ),
              fit: BoxFit.cover,
            )
          );

          plusColor = Color(0xffffffff);
          
        } else {
          
          decoration  = BoxDecoration(
            borderRadius: BorderRadius.circular(widget.containerBorderRadius??7),
            border: Border.all(width: 1, color: Color(0xff999999)),
            
          );
        }
        return Container(
          width: widget.containerWidth,
          margin: EdgeInsets.only(right: 10),
          child: Column(

            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              
              MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                  onTap: () async{
                    showImageGallery(currentIndex, setState);
                    
                  },
                  child: Container(
                    decoration: decoration,
                    height: widget.containerHeight,
                    width: widget.containerWidth,
                    child: Center(
                      child: Transform.rotate(
                        angle: pi/4,
                        child: SvgPicture.asset(
                          'assets/icons/close-popup.svg',
                          width: 20,
                          color: plusColor,
                        ),
                      ),
                    )
                  
                  ),
                ),
              ),
              if( widget.roomsSelection! ) Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 5,),
                  NarFormLabelWidget(
                    label: 'Stanza',
                    fontSize: 12,
                    textColor: Color(0xff717171),
                    fontWeight: '600',
                  ),
                  SizedBox(height: 4,),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Expanded(
                        child: NarSelectBoxWidget(
                          options: List.of(appConst.roomsList)..sort((a, b) => a.toLowerCase().compareTo(b.toLowerCase())),
                          controller: widget.renderImageList[currentIndex].renderImageFirst!.contRoom,
                          contentPadding: EdgeInsets.only(top: 10, bottom: 8, right: 2, left: 10),
                          dropdownFontSize: 11,
                        ),
                      ),
                    ],
                  )
                ],
              )
              
              
          
            ],
          ),
        );
      })
      
      
    );
    

    
  
  }  
  
  imageDialogTypeBeforeAfter() {
    formMessages.clear();
    formMessages.add('');

    showDialog(
      context: context, 
      builder: (BuildContext _bc1) {
          return StatefulBuilder(
            builder: (BuildContext _bc2, StateSetter _setState) {
              return Center(
                child: Material(
                  borderRadius: BorderRadius.circular(20), 
                  child: Container(
                    width: 750,
                    height: MediaQuery.of(context).size.height * 0.98,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20), 
                      color: Colors.white
                    ),
                    padding: EdgeInsets.only( top: 20, bottom: 30, left: 50, right: 50),
                  
                    child: Stack(
                      clipBehavior: Clip.none,
                      children: [
                        Positioned(
                          top: 0,
                          right: 0,
                          child: Container(
                            height: 15,
                            width: 15,
                            child: MouseRegion(
                              cursor: SystemMouseCursors.click,
                              child: GestureDetector(
                                  child: SvgPicture.asset(
                                    'assets/icons/close-popup.svg',
                                    width: 15,
                                  ),
                                  onTap: () {
                                    Navigator.pop(context);
                                  }),
                            ),
                          ),
                        ),
                        Column(
                                          
                          children: [
                            NarFormLabelWidget(
                              label: widget.dialogTitle,
                              textColor: Colors.black,
                              fontSize: 20,
                              fontWeight: 'bold',
                            ),
                        
                            Container(
                              height: MediaQuery.of(context).size.height * 0.7,
                              child: ListView(
                                shrinkWrap: true,
                                children: [
                        
                                  ...widget.renderImageList.map((renderImage) {
                        
                                    
                                    BoxDecoration decoration;
                                    BoxDecoration decoration2;

                                    int currentIndex = widget.renderImageList.indexWhere((l) => l.index == renderImage.index);
                        
                                    if( widget.renderImageList[currentIndex].renderImageSecond!.isNetworkImage == true ) {
                                      decoration2  = BoxDecoration(
                                        borderRadius: BorderRadius.circular(widget.containerBorderRadius??7),
                                        // border: Border.all(width: 1, color: Color(0xff999999)),
                                        image: DecorationImage( 
                                          image: NetworkImage(widget.renderImageList[currentIndex].renderImageSecond!.filenameUrl! ),
                                          fit: BoxFit.cover,
                                        )
                                      );
                                      
                                    } else {
                                      
                                      decoration2  = BoxDecoration(
                                        borderRadius: BorderRadius.circular(widget.containerBorderRadius??7),
                                        border: Border.all(width: 1, color: Color(0xff999999)),
                                        
                                      );
                                    }

                                    // first box decoration
                                    if( widget.renderImageList[currentIndex].renderImageFirst!.tmpFile != null ) {
                        
                                      decoration  = BoxDecoration(
                                        borderRadius: BorderRadius.circular(widget.containerBorderRadius??7),
                                        // border: Border.all(width: 1, color: Color(0xff999999)),
                                        image: DecorationImage( 
                                          image: MemoryImage( widget.renderImageList[currentIndex].renderImageFirst!.imageBytes! ),
                                          fit: BoxFit.cover,
                                        )
                                      );
                                    } else if( widget.renderImageList[currentIndex].renderImageFirst!.isNetworkImage == true ) {
                                      decoration  = BoxDecoration(
                                        borderRadius: BorderRadius.circular(widget.containerBorderRadius??7),
                                        // border: Border.all(width: 1, color: Color(0xff999999)),
                                        image: DecorationImage( 
                                          image: NetworkImage(widget.renderImageList[currentIndex].renderImageFirst!.filenameUrl! ),
                                          fit: BoxFit.cover,
                                        )
                                      );
                                      
                                    } else {
                                      
                                      decoration  = BoxDecoration(
                                        borderRadius: BorderRadius.circular(widget.containerBorderRadius??7),
                                        border: Border.all(width: 1, color: Color(0xff999999)),
                                        
                                      );
                                    }
                                  
                                    return Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(10),
                                        border: Border.all( width: 1, color: Color(0xffD9D9D9) )
                                      ),
                                      margin: EdgeInsets.only(bottom: 15),
                                      padding: EdgeInsets.all(10),
                                      child: Column(
                                        children: [
                                          Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              sortingArrows( currentIndex, _setState ),
                                              Row(
                                                children: [
                                                  NarFormLabelWidget(
                                                    label: 'Brochure',
                                                    fontSize: 13,
                                                    fontWeight: '600',
                                                  ),
                                                  Transform.scale(
                                                    scale: 0.7,
                                                    child: SizedBox(
                                                      width: 50,
                                                      child: Switch(
                                                        activeThumbColor: Theme.of(context).primaryColor,
                                                        value: widget.renderImageList[currentIndex].renderImageFirst!.isBrochure!, 
                                                        onChanged: ( value ) {
                                                          _setState((){
                                                            widget.renderImageList[currentIndex].renderImageFirst!.isBrochure = value; 
                                                          });
                                                        }
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              )
                                            ],
                                          ),
                                          
                                          Row(
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              
                                              Container(
                                                width: widget.containerWidth,
                                                child: Column(
                                                  children: [
                                                    Container(
                                                      height: 30,
                                                    ),
                                                    Container(
                                                      width: widget.containerWidth,
                                                      // margin: EdgeInsets.only(left:20),
                                                      child: Column(
                                                        crossAxisAlignment: CrossAxisAlignment.start,
                                                        children: [
                                                          
                                                          MouseRegion(
                                                            cursor: SystemMouseCursors.click,
                                                            child: GestureDetector(
                                                              onTap: () async{
                                                                
                                                                widget.renderImageList[currentIndex].renderImageFirst!.tmpFile = await widget.renderImageList[currentIndex].renderImageFirst!.picker.pickImage(source: ImageSource.gallery);
                                                                if( widget.renderImageList[currentIndex].renderImageFirst!.tmpFile != null ) {
                                                                  widget.renderImageList[currentIndex].renderImageFirst!.imageBytes = await widget.renderImageList[currentIndex].renderImageFirst!.tmpFile!.readAsBytes();
                                                                }
                                                                
                                                                            
                                                                _setState((){});
                                                    
                                                              },
                                                              child: Container(
                                                                decoration: decoration,
                                                                height: widget.containerHeight,
                                                                width: widget.containerWidth,
                                                                child: widget.renderImageList[currentIndex].renderImageFirst!.tmpFile == null && widget.renderImageList[currentIndex].renderImageFirst!.isNetworkImage == false
                                                                ? Center(
                                                                  child: Transform.rotate(
                                                                    angle: pi/4,
                                                                    child: SvgPicture.asset(
                                                                      'assets/icons/close-popup.svg',
                                                                      width: 20,
                                                                      color: const Color(0xff999999),
                                                                    ),
                                                                  ),
                                                                )
                                                                : Container(),
                                                              
                                                              ),
                                                            ),
                                                          ),
                                                          SizedBox(height: 5,),
                                                          NarFormLabelWidget(
                                                            label: 'Stanza',
                                                            fontSize: 12,
                                                            textColor: Color(0xff717171),
                                                            fontWeight: '600',
                                                          ),
                                                          SizedBox(height: 4,),
                                                          Row(
                                                            mainAxisAlignment: MainAxisAlignment.start,
                                                            children: [
                                                              Expanded(
                                                                child: NarSelectBoxWidget(
                                                                  options: List.of(appConst.roomsList)..sort((a, b) => a.toLowerCase().compareTo(b.toLowerCase())),
                                                                  controller: widget.renderImageList[currentIndex].renderImageFirst!.contRoom,
                                                                  contentPadding: EdgeInsets.only(top: 10, bottom: 8, right: 2, left: 10),
                                                                  dropdownFontSize: 11,
                                                                ),
                                                              ),
                                                            ],
                                                          )
                                                          
                                                      
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              
                                              
                                              // Second Box
                                              Container(
                                                width: widget.containerWidth,
                                                margin: EdgeInsets.only(left:20),
                                                child: Column(
                                                  mainAxisAlignment: MainAxisAlignment.start,
                                                  children: [
                                                    Container(
                                                      height: 30,
                                                      child: Row(
                                                        crossAxisAlignment: CrossAxisAlignment.center,
                                                        mainAxisAlignment: MainAxisAlignment.start,
                                                        children: [
                                                          Transform.scale(
                                                            scale: 0.6,
                                                            child: SizedBox(
                                                              width: 50,
                                                              child: Switch(
                                                                activeThumbColor: Theme.of(context).primaryColor,
                                                                value: widget.renderImageList[currentIndex].renderImageFirst!.hasMatch!, 
                                                                onChanged: ( value ) {
                                                                  _setState((){
                                                                    widget.renderImageList[currentIndex].renderImageFirst!.hasMatch = value;
                                                                    if( !value ) {
                                                                      widget.renderImageList[currentIndex].renderImageSecond = RenderImage.empty();
                                                                    }
                                                                  });
                                                                }
                                                              ),
                                                            ),
                                                          ),
                                                          // SizedBox(width: 5,),
                                                          NarFormLabelWidget(
                                                            label: 'Abbina Render',
                                                            fontSize: 12,
                                                            textColor: Color(0xff6A6A6A),
                                                          )
                                                        ],
                                                      )
                                                    ),
                                                    if( widget.renderImageList[currentIndex].renderImageFirst!.hasMatch! ) Container(
                                                      width: widget.containerWidth,
                                                      child: Column(
                                                        crossAxisAlignment: CrossAxisAlignment.start,
                                                        children: [
                                                          
                                                          MouseRegion(
                                                            cursor: SystemMouseCursors.click,
                                                            child: GestureDetector(
                                                              onTap: () async{
                                                                showImageGallery(currentIndex, _setState);
                                                              },
                                                              child: Container(
                                                                decoration: decoration2,
                                                                height: widget.containerHeight,
                                                                width: widget.containerWidth,
                                                                child: widget.renderImageList[currentIndex].renderImageSecond!.isNetworkImage == false
                                                                ? Center(
                                                                  child: Transform.rotate(
                                                                    angle: pi/4,
                                                                    child: SvgPicture.asset(
                                                                      'assets/icons/close-popup.svg',
                                                                      width: 20,
                                                                      color: const Color(0xff999999),
                                                                    ),
                                                                  ),
                                                                )
                                                                : Container(),
                                                              
                                                              ),
                                                            ),
                                                          ),
                                                          
                                                      
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),

                                              
                                                      
                                            ],
                                          ),
                                
                                          MouseRegion(
                                            cursor: SystemMouseCursors.click,
                                            child: GestureDetector(
                                              onTap: (){
                                                removeRow(currentIndex, _setState );
                                              },
                                              child: Row(
                                                crossAxisAlignment: CrossAxisAlignment.center,
                                                mainAxisAlignment: MainAxisAlignment.end,
                                                mainAxisSize: MainAxisSize.max,
                                                children: [
                                                  NarFormLabelWidget(
                                                    label: 'Elimina',
                                                    textColor: Color(0xffB5B4B4),
                                                    fontSize: 13,
                                                    textDecoration: TextDecoration.underline,
                                                    fontWeight: '600',
                                                  ),
                                                  SizedBox(width:5),
                                                  SvgPicture.asset(
                                                    "assets/icons/trash.svg",
                                                    color: Color(0xffB5B4B4),
                                                    height: 20,
                                                  ),
                                                ],
                                              ),
                                            ),
                                          )
                                        ],
                                      ),
                                    );
                                  }).toList(),
                              
                                  // emptyImageAddBlockTypeNone(),
                                ],
                              ),
                              
                            ),
                            
                            SizedBox(height: 10,),
                        
                            BaseNewarcButton(
                              width: 190,
                              onPressed: (){
                                RenderImageList tmpList = RenderImageList.empty();
                                tmpList.index = widget.renderImageList.length;
                                widget.renderImageList.add( tmpList );
                                _setState((){});
                              },
                              buttonText: 'Aggiungi',
                              textColor: Color(0xff636363),
                              fontSize: 18,
                              fontWeight: 'bold',
                              notAccent: true,
                        
                            ),
                        
                            SizedBox(height: 25,), 
                        
                            if( formMessages[0] != '' ) Container(
                              margin: EdgeInsets.only(bottom: 5),
                              child: NarFormLabelWidget(label: formMessages[0])
                            ),
                        
                            BaseNewarcButton(
                              width: 200,
                              onPressed: () async {
                                print("==============> 1703 Block File Picker");

                                final renderListToUpload = widget.renderImageList
                                    .where((e) => e.renderImageFirst?.tmpFile != null)
                                    .toList();

                                formMessages.clear();
                                formMessages.add('Saving...');
                                _setState((){}); 

                                await Future.forEach(renderListToUpload, (RenderImageList _renderList) async {
                                  int currentIndex = widget.renderImageList.indexWhere((e) => _renderList.index == e.index );
                                  if( _renderList.renderImageFirst!.tmpFile != null ) {
                                    await uploadFileDelayed( widget.storageLocation, _renderList.renderImageFirst!.tmpFile!.name, _renderList.renderImageFirst!.tmpFile! );
                                    if (widget.isResizeImage ?? false) {
                                      if (widget.resizeImageSize?.isNotEmpty ?? false) {
                                        for (int i = 0; i < widget.resizeImageSize!.length; i++) {
                                          List size = widget.resizeImageSize![i];
                                          final resizedFile = await resizeXFile(
                                              _renderList.renderImageFirst!.tmpFile!,
                                              width: size[0],
                                              height: size[1],
                                              quality: 50,
                                              thumbnailTitle: size[2]
                                          );

                                          await uploadFileDelayed(
                                            widget.storageLocation,
                                            resizedFile.name,
                                            resizedFile,
                                          );
                                        }
                                      }
                                    }
                                    widget.renderImageList[currentIndex].renderImageFirst!.filename = _renderList.renderImageFirst!.tmpFile!.name;
                                    widget.renderImageList[currentIndex].renderImageFirst!.location = widget.storageLocation;
                                    widget.renderImageList[currentIndex].renderImageFirst!.tmpFile = null;
                                    widget.renderImageList[currentIndex].renderImageFirst!.imageBytes = null;
                                    widget.renderImageList[currentIndex].renderImageFirst!.isNetworkImage = true;
                                    widget.renderImageList[currentIndex].renderImageFirst!.room = widget.renderImageList[currentIndex].renderImageFirst!.contRoom!.text;
                                    
                                    widget.renderImageList[currentIndex].renderImageFirst!.filenameUrl = await printUrl( widget.storageLocation, '', widget.renderImageList[currentIndex].renderImageFirst!.filename );

                                  }

                                  if( widget.renderImageList[currentIndex].renderImageFirst!.filename == '' ) {
                                    widget.renderImageList.removeAt(currentIndex);
                                  }
                                });
                                formMessages.clear();
                                formMessages.add('Saved!');
                                _setState((){});
                                widget.onUploadCompleted();

                                for( int i = 0; i < widget.renderImageList.length; i++ ) {
                                  widget.renderImageList[i].index = i;
                                }

                                widget.onUploadCompleted();
                                if (mounted) {
                                  Navigator.pop(context); // Ensures widget is still in tree before popping
                                }
                              
                              },
                              buttonText: 'Conferma',
                              textColor: Colors.white,
                              fontSize: 18,
                              fontWeight: 'bold',
                              
                            )
                                          
                                          
                          ],
                        ),
                      ],
                    ),
                  
                  
                  ),
                ),
              );
            }
          );
      }
    );
  }
  
  
  @override
  Widget build(BuildContext context) {
    return widget.displayType == 'button' ? 
    Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        
        
        BaseNewarcButton(
          // notAccent: widget.buttonNotAccent,
          onPressed: () {
            if( widget.selectionType == 'none' ) {
              imageDialogTypeNone();
            } else if( widget.selectionType == 'beforeAfter' ) {
              imageDialogTypeBeforeAfter();
            } else if( widget.selectionType == 'dayNight' ) {
              imageDialogTypeDayNight();
            }
            
          },
          color: widget.buttonColor,
          buttonText: widget.invokerButtonText,
          borderColor: widget.buttonBorderColor,
          textColor: widget.buttonTextColor,
          height: widget.buttonHeight,
          width: widget.buttonWidth,
        ) 
      ],
    )
    : Column(
      children: [
        singleImageContainer()
      ],
    );
  }
}

