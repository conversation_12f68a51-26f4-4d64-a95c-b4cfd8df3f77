import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/image-select-box.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';

class ProjectAssignAgency extends StatefulWidget {
  final NewarcProject? project;
  final List<Agency>? agencies;
  final Function? updateProject;
  final List<bool>? isInputChangeDetected;

  const ProjectAssignAgency({
    Key? key, 
    this.project, 
    this.agencies, 
    this.updateProject, 
    this.isInputChangeDetected
  }) : super(key: key);

  @override
  State<ProjectAssignAgency> createState() => _ProjectAssignAgencyState();
}

class _ProjectAssignAgencyState extends State<ProjectAssignAgency> {
  bool loading = false;
  String progressMessage = '';

  TextEditingController? agencyId = new TextEditingController();
  TextEditingController? contCommissionIn = new TextEditingController();
  TextEditingController? contCommissionOut = new TextEditingController();
  TextEditingController? contBonusIns = new TextEditingController();
  TextEditingController? contBonusObi = new TextEditingController();
  TextEditingController? contAdPrice = new TextEditingController();
  TextEditingController? contSaleTarget = new TextEditingController();
  TextEditingController? contMinSale = new TextEditingController();
  TextEditingController? contSaleDate = new TextEditingController();
  TextEditingController? contMandateDuration = new TextEditingController();

  bool commissionIn = false;
  bool commissionOut = false;
  bool bonusIns = false;
  bool bonusObi = false;
  List<Map> _agencies = [];

  bool isCommissionInPaid = false;
  bool isCommissionOutPaid = false;
  bool isBonusInsPaid = false;
  bool isBonusObiPaid = false;
  
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    setInitialValues();

    _agencies = widget.agencies!.map((e) => {'value': e.id, 'label': e.name}).toList();

    agencyId!.addListener(_checkForChanges);
    contCommissionIn!.addListener(_checkForChanges);
    contCommissionOut!.addListener(_checkForChanges);
    contBonusIns!.addListener(_checkForChanges);
    contBonusObi!.addListener(_checkForChanges);
    contAdPrice!.addListener(_checkForChanges);
    contSaleTarget!.addListener(_checkForChanges);
    contMinSale!.addListener(_checkForChanges);
    contSaleDate!.addListener(_checkForChanges);
    contMandateDuration!.addListener(_checkForChanges);
  }

  void _checkForChanges() {

    setState(() {
      widget.isInputChangeDetected![0] = true;  
    });
  }

  @protected
  void didUpdateWidget(ProjectAssignAgency oldWidget) {
    super.didUpdateWidget(oldWidget);
    setInitialValues();

    
  }

  @override
  void dispose() {
    
    agencyId!.removeListener(_checkForChanges);
    contCommissionIn!.removeListener(_checkForChanges);
    contCommissionOut!.removeListener(_checkForChanges);
    contBonusIns!.removeListener(_checkForChanges);
    contBonusObi!.removeListener(_checkForChanges);
    contAdPrice!.removeListener(_checkForChanges);
    contSaleTarget!.removeListener(_checkForChanges);
    contMinSale!.removeListener(_checkForChanges);
    contSaleDate!.removeListener(_checkForChanges);
    contMandateDuration!.removeListener(_checkForChanges);

    super.dispose();
  }

  setInitialValues() {
    NewarcProject _proj = widget.project!;
    if (_proj.assignedAgency!.length > 0) {
      agencyId!.text = _proj.assignedAgency!['agencyId'] ?? '';
      contCommissionIn!.text = _proj.assignedAgency!['commissionIn'] ?? '';
      contCommissionOut!.text = _proj.assignedAgency!['commissionOut'] ?? '';
      contBonusIns!.text = _proj.assignedAgency!['bonusIns'] ?? '';
      contBonusObi!.text = _proj.assignedAgency!['bonusObi'] ?? '';
      contAdPrice!.text = _proj.assignedAgency!['adPrice'] ?? '';
      contSaleTarget!.text = _proj.assignedAgency!['saleTarget'] ?? '';
      contMinSale!.text = _proj.assignedAgency!['minSale'] ?? '';
      contSaleDate!.text = _proj.assignedAgency!['saleDate'] ?? '';
      contMandateDuration!.text =
          _proj.assignedAgency!['mandateDuration'] ?? '';

      commissionIn = _proj.assignedAgency!['commissionIn'] == null ? false : true;
      commissionOut = _proj.assignedAgency!['commissionOut'] == null ? false : true;
      bonusIns = _proj.assignedAgency!['bonusIns'] == null ? false : true;
      bonusObi = _proj.assignedAgency!['bonusObi'] == null ? false : true;

      isCommissionInPaid = _proj.assignedAgency!['commissionInPaid'];
      isCommissionOutPaid = _proj.assignedAgency!['commissionOutPaid'];
      isBonusInsPaid = _proj.assignedAgency!['bonusInsPaid'];
      isBonusObiPaid = _proj.assignedAgency!['bonusObiPaid'];

    }
  }

  formatDateForParsing(String dateString) {
    List splittedDate = dateString.split('/');
    // print(splittedDate);
    return splittedDate[2] + '-' + splittedDate[1] + '-' + splittedDate[0];
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: double.infinity,
      child: loading == true
          ? NarFormLabelWidget(label: 'Loading')
          : Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        child: ListView(
                          // crossAxisAlignment: CrossAxisAlignment.start,
                          // mainAxisAlignment: MainAxisAlignment.start,
                          // mainAxisSize: MainAxisSize.max,
                          children: [
                            SizedBox(height: 20),
                            NarFormLabelWidget(
                              label: 'Agenzia',
                              fontSize: 20,
                              fontWeight: 'bold',
                            ),
                            SizedBox(height: 30),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Expanded(
                                  flex: 1,
                                  child: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      NarFormLabelWidget(
                                        label: "Seleziona agenzia",
                                        textColor: Color(0xff696969),
                                        fontSize: 14,
                                        fontWeight: '600',
                                      ),
                                      SizedBox(height: 4),
                                      NarImageSelectBoxWidget(
                                          options: _agencies,
                                          controller: agencyId,
                                          validationType: 'required',
                                          parametersValidate: 'Required!'),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(
                              height: 15,
                            ),
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  flex: 1,
                                  child: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      NarFormLabelWidget(
                                        label: "Durata mandato",
                                        textColor: Color(0xff696969),
                                        fontSize: 14,
                                        fontWeight: '600',
                                      ),
                                      SizedBox(height: 4),
                                      NarSelectBoxWidget(
                                          options: ["3 mesi", "6 mesi"],
                                          controller: contMandateDuration,
                                          validationType: 'required',
                                          parametersValidate: 'Required!'),
                                    ],
                                  ),
                                ),
                                SizedBox(
                                  width: 15,
                                ),
                                CustomTextFormField(
                                  label: "Data messa in vendita",
                                  controller: contSaleDate,
                                  suffixIcon: Container(
                                    padding: const EdgeInsets.all(10),
                                    height: 20,
                                    width: 20,
                                    child: Image.asset('assets/icons/calendar.png'),
                                  ),
                                  // validationMessage: 'Required!',
                                  validator: (value) {
                                    if (value == '') {
                                      return 'Required!';
                                    }
    
                                    return null;
                                  },
                                  onTap: () async {
                                    DateTime? pickedDate = await showDatePicker(
                                        context: context,
                                        initialDate: contSaleDate!.text == ''
                                            ? DateTime.now()
                                            : DateTime.tryParse(
                                                formatDateForParsing(
                                                    contSaleDate!.text))!,
                                        firstDate: DateTime(1950),
                                        lastDate: DateTime(2300));
    
                                    if (pickedDate != null) {
                                      String formattedDate =
                                          DateFormat('dd/MM/yyyy')
                                              .format(pickedDate);
                                      setState(() {
                                        contSaleDate!.text =
                                            formattedDate; //set output date to TextField value.
                                      });
                                    } else {}
                                  },
                                ),
                              ],
                            ),
                            SizedBox(
                              height: 15,
                            ),
                            /*Container(
                              width: double.infinity,
                              height: 1,
                              decoration: BoxDecoration(
                                color: Color(0xFFDCDCDC),
                              ),
                              child: SizedBox(height: 0),
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            NarFormLabelWidget(
                              label: 'Provvigioni',
                              fontSize: 16,
                              fontWeight: 'bold',
                            ),
                            SizedBox(height: 20),*/
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Expanded(
                                  child: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      NarFormLabelWidget(
                                        label: "Commissione IN",
                                        textColor: Color(0xff696969),
                                        fontSize: 14,
                                        fontWeight: '600',
                                      ),
                                      SizedBox(height: 4),
                                      Padding(
                                        padding: const EdgeInsets.only(
                                            top: 6.0, bottom: 18, left: 0),
                                        child: Switch(
                                          // This bool value toggles the switch.
                                          value: commissionIn,
                                          activeThumbColor:
                                              Theme.of(context).primaryColor,
                                          onChanged: (bool value) async {
                                            // This is called when the user toggles the switch.
    
                                            commissionIn = value;
                                            _checkForChanges();
                                            setState(() {
                                              commissionIn = value;
    
                                              if(value == false ) {
                                                contCommissionIn!.text = '';
                                              }
                                              //activeColor = value == true ? colorBlack : colorGrey;
                                              //print({value, activeColor});
                                            });
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(
                                  width: 15,
                                ),
                                CustomTextFormField(
                                  label: "Costo",
                                  controller: contCommissionIn,
                                  enabled: commissionIn,
                                  suffixIcon: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          NarFormLabelWidget(
                                            label: '€',
                                            fontSize: 14,
                                            textColor: Color.fromRGBO(
                                                123, 123, 123, 1),
                                          ),
                                          SizedBox(
                                            width: 5,
                                          )
                                        ],
                                      ),
                                    ],
                                  ),
                                  // validationMessage: 'Required!',
                                  validator: (value) {
                                    if (value == '') {
                                      return 'Required!';
                                    }
    
                                    return null;
                                  },
                                  onTap: () async {},
                                ),
                              ],
                            ),
                            SizedBox(
                              height: 15,
                            ),
                            Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      NarFormLabelWidget(
                                        label: "Commissione OUT",
                                        textColor: Color(0xff696969),
                                        fontSize: 14,
                                        fontWeight: '600',
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.only(
                                            top: 6.0, bottom: 18, left: 0),
                                        child: Switch(
                                          // This bool value toggles the switch.
                                          value: commissionOut,
                                          activeThumbColor:
                                              Theme.of(context).primaryColor,
                                          onChanged: (bool value) async {
                                            // This is called when the user toggles the switch.
    
                                            commissionOut = value;
                                            _checkForChanges();
                                            setState(() {
                                              commissionOut = value;
                                              if(value == false ) {
                                                contCommissionOut!.text = '';
                                              }
                                              //activeColor = value == true ? colorBlack : colorGrey;
                                              //print({value, activeColor});
                                            });
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(
                                  width: 15,
                                ),
                                CustomTextFormField(
                                  label: "Costo",
                                  controller: contCommissionOut,
                                  enabled: commissionOut,
                                  suffixIcon: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          NarFormLabelWidget(
                                            label: '€',
                                            fontSize: 14,
                                            textColor: Color.fromRGBO(
                                                123, 123, 123, 1),
                                          ),
                                          SizedBox(
                                            width: 5,
                                          )
                                        ],
                                      ),
                                    ],
                                  ),
                                  // validationMessage: 'Required!',
                                  validator: (value) {
                                    if (value == '') {
                                      return 'Required!';
                                    }
    
                                    return null;
                                  },
                                  onTap: () async {},
                                ),
                              ],
                            ),
                            SizedBox(height: 15),
                            Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      NarFormLabelWidget(
                                        label: "Bonus obiettivo",
                                        textColor: Color(0xff696969),
                                        fontSize: 14,
                                        fontWeight: '600',
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.only(
                                            top: 6.0, bottom: 18, left: 0),
                                        child: Switch(
                                          value: bonusObi,
                                          activeThumbColor:
                                              Theme.of(context).primaryColor,
                                          onChanged: (bool value) async {
                                            bonusObi = value;
                                            setState(() {
                                              _checkForChanges();
                                              bonusObi = value;
                                              if(value == false ) {
                                                contBonusObi!.text = '';
                                              }
                                            });
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(
                                  width: 15,
                                ),
                                CustomTextFormField(
                                  label: "Costo",
                                  controller: contBonusObi,
                                  enabled: bonusObi,
                                  suffixIcon: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          NarFormLabelWidget(
                                            label: '€',
                                            fontSize: 14,
                                            textColor: Color.fromRGBO(
                                                123, 123, 123, 1),
                                          ),
                                          SizedBox(
                                            width: 5,
                                          )
                                        ],
                                      ),
                                    ],
                                  ),
                                  // validationMessage: 'Required!',
                                  validator: (value) {
                                    if (value == '') {
                                      return 'Required!';
                                    }
    
                                    return null;
                                  },
                                  onTap: () async {},
                                ),
                              ],
                            ),
                            SizedBox(height: 15),
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Expanded(
                                  child: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      NarFormLabelWidget(
                                        label: "Bonus insieme",
                                        textColor: Color(0xff696969),
                                        fontSize: 14,
                                        fontWeight: '600',
                                      ),
                                      SizedBox(height: 4),
                                      Padding(
                                        padding: const EdgeInsets.only(
                                            top: 6.0, bottom: 18, left: 0),
                                        child: Switch(
                                          value: bonusIns,
                                          activeThumbColor:
                                              Theme.of(context).primaryColor,
                                          onChanged: (bool value) async {
                                            bonusIns = value;
                                            _checkForChanges();
                                            setState(() {
                                              bonusIns = value;
                                              if(value == false ) {
                                                contBonusIns!.text = '';
                                              }
                                            });
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(
                                  width: 15,
                                ),
                                CustomTextFormField(
                                  label: "Costo",
                                  controller: contBonusIns,
                                  enabled: bonusIns,
                                  suffixIcon: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          NarFormLabelWidget(
                                            label: '€',
                                            fontSize: 14,
                                            textColor: Color.fromRGBO(
                                                123, 123, 123, 1),
                                          ),
                                          SizedBox(
                                            width: 5,
                                          )
                                        ],
                                      ),
                                    ],
                                  ),
                                  // validationMessage: 'Required!',
                                  validator: (value) {
                                    if (value == '') {
                                      return 'Required!';
                                    }
    
                                    return null;
                                  },
                                  onTap: () async {},
                                ),
                              ],
                            ),
                            /*SizedBox(
                              height: 10,
                            ),
                            Container(
                              width: double.infinity,
                              height: 1,
                              decoration: BoxDecoration(
                                color: Color(0xFFDCDCDC),
                              ),
                              child: SizedBox(height: 0),
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            NarFormLabelWidget(
                              label: 'Vendita Immobile',
                              fontSize: 16,
                              fontWeight: 'bold',
                            ),
                            SizedBox(height: 20),*/
                            /*Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CustomTextFormField(
                                  label: "Prezzo annuncio",
                                  controller: contAdPrice,
                                  suffixIcon: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          NarFormLabelWidget(
                                            label: '€',
                                            fontSize: 14,
                                            textColor:
                                                Color.fromRGBO(123, 123, 123, 1),
                                          ),
                                          SizedBox(
                                            width: 5,
                                          )
                                        ],
                                      ),
                                    ],
                                  ),
                                  // validationMessage: 'Required!',
                                  validator: (value) {
                                    if (value == '') {
                                      return 'Required!';
                                    }
                      
                                    return null;
                                  },
                                  onTap: () async {},
                                ),
                                SizedBox(
                                  width: 15,
                                ),
                                CustomTextFormField(
                                  label: "Obiettivo vendita",
                                  controller: contSaleTarget,
                                  suffixIcon: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          NarFormLabelWidget(
                                            label: '€',
                                            fontSize: 14,
                                            textColor:
                                                Color.fromRGBO(123, 123, 123, 1),
                                          ),
                                          SizedBox(
                                            width: 5,
                                          )
                                        ],
                                      ),
                                    ],
                                  ),
                                  // validationMessage: 'Required!',
                                  validator: (value) {
                                    if (value == '') {
                                      return 'Required!';
                                    }
                      
                                    return null;
                                  },
                                  onTap: () async {},
                                ),
                                SizedBox(
                                  width: 15,
                                ),
                                CustomTextFormField(
                                  label: "Vendita minima",
                                  controller: contMinSale,
                                  suffixIcon: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          NarFormLabelWidget(
                                            label: '€',
                                            fontSize: 14,
                                            textColor:
                                                Color.fromRGBO(123, 123, 123, 1),
                                          ),
                                          SizedBox(
                                            width: 5,
                                          )
                                        ],
                                      ),
                                    ],
                                  ),
                                  // validationMessage: 'Required!',
                                  validator: (value) {
                                    if (value == '') {
                                      return 'Required!';
                                    }
                      
                                    return null;
                                  },
                                  onTap: () async {},
                                ),
                                SizedBox(
                                  width: 15,
                                ),
                                Expanded(
                                    child: SizedBox(
                                  height: 0,
                                )),
                              ],
                            ),*/
    
                            SizedBox(height: 6),
                          ],
                        ),
                      ),
                      Expanded(child: SizedBox(height: 0))
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    NarFormLabelWidget(label: progressMessage),
                    Padding(
                      padding: const EdgeInsets.only(bottom: 15.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          BaseNewarcButton(
                            buttonText: "Salva",
                            onPressed: () async {
                              setState(() {
                                progressMessage = 'Salvataggio in corso...';
                              });
    
                              dynamic agencyData = {
                                'agencyName': widget.agencies!
                                    .where(
                                        (agency) => agency.id == agencyId!.text)
                                    .first
                                    .name,
                                'agencyId': agencyId!.text,
                                'commissionIn': contCommissionIn!.text == '' ? null : contCommissionIn!.text,
                                'commissionInPaid': contCommissionIn!.text == '' ? false : isCommissionInPaid,
                                'commissionOut': contCommissionOut!.text == '' ? null : contCommissionOut!.text,
                                'commissionOutPaid': contCommissionOut!.text == '' ? false : isCommissionOutPaid,
                                'bonusIns': contBonusIns!.text == '' ? null : contBonusIns!.text,
                                'bonusInsPaid': contBonusIns!.text == '' ? false : isBonusInsPaid,
                                'bonusObi': contBonusObi!.text == '' ? null : contBonusObi!.text,
                                'bonusObiPaid': contBonusObi!.text == '' ? false : isBonusObiPaid,
                                'adPrice': contAdPrice!.text,
                                'saleTarget': contSaleTarget!.text,
                                'minSale': contMinSale!.text,
                                'saleDate': contSaleDate!.text,
                                'mandateDuration': contMandateDuration!.text,
                              };
    
                              widget.project!.assignedAgency = agencyData;
    
                              final FirebaseFirestore _db = FirebaseFirestore.instance;
    
                              try {
                                await _db
                                    .collection(
                                        appConfig.COLLECT_NEWARC_PROJECTS)
                                    .doc(widget.project!.id)
                                    .update(widget.project!.toMap());
    
                                widget.updateProject!(widget.project!);
    
                                setState(() {
                                  widget.isInputChangeDetected![0] = false;
                                  progressMessage = 'Saved!';
                                });
                              } catch (e) {
                                setState(() {
                                  progressMessage = 'Error';
                                });
                              }
                            },
                          )
                        ],
                      ),
                    ),
                  ],
                )
              ],
            ),
    );
  }
}
