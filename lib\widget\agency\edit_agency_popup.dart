import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:newarc_platform/app_config.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/utils/firestore.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

class EditAgencyPopup extends StatefulWidget {
  final Agency? agency;

  const EditAgencyPopup({
    required this.agency,
    Key? key,
  }) : super(key: key);

  @override
  State<EditAgencyPopup> createState() => _EditAgencyPopupState();
}

class _EditAgencyPopupState extends State<EditAgencyPopup> {
  TextStyle sectionTitleStyle =
      TextStyle(fontSize: 16, fontWeight: FontWeight.bold);

  final _formKey = GlobalKey<FormState>();
  bool loading = false;
  bool clicked = false;

  final List<String> list = <String>['Sì', 'No'];

  bool showOptionMissing(String? option) {
    try {
      return (option == null || option.isEmpty) && clicked;
    } catch (e) {
      print(e);
      return false;
    }
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 850, //MediaQuery.of(context).size.width * 0.6,
      margin: EdgeInsets.symmetric(
        horizontal: 20,
      ),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        child: loading
            ? Center(
                child: CircularProgressIndicator(
                    color: Theme.of(context).primaryColor),
              )
            : Wrap(
                children: [
                  Form(
                    key: _formKey,
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 30.0, top: 20),
                      child: Column(
                        children: [
                          Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: 20,
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                SizedBox(width: 30),
                                NarFormLabelWidget(
                                  label: 'Modifica',
                                  textColor: Colors.black,
                                  fontSize: 22,
                                  fontWeight: '800',
                                ),
                                Container(
                                  height: 17,
                                  width: 17,
                                  child: MouseRegion(
                                    cursor: SystemMouseCursors.click,
                                    child: GestureDetector(
                                        child: SvgPicture.asset(
                                          'assets/icons/close-popup.svg',
                                          width: 17,
                                        ),
                                        onTap: () {
                                          Navigator.pop(context);
                                        }),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 40.0),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                SizedBox(height: 25),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    CustomTextFormField(
                                      initialValue: widget.agency!.name,
                                      hintText: "Inserisci un nome",
                                      label: "Nome agenzia",
                                      onChangedCallback: (String value) {
                                        widget.agency!.name = value;
                                      },
                                    ),
                                    SizedBox(width: 18),
                                    CustomTextFormField(
                                      initialValue: widget.agency!.email,
                                      hintText: "Inserisci una mail",
                                      label: "Email",
                                      onChangedCallback: (String value) {
                                        widget.agency!.email = value;
                                      },
                                    ),
                                  ],
                                ),
                                SizedBox(height: 18),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    CustomTextFormField(
                                      initialValue:
                                          widget.agency!.points.toString(),
                                      hintText: "Inserisci il punteggio",
                                      label: "Punti",
                                      onChangedCallback: (String value) {
                                        try {
                                          widget.agency!.points =
                                              int.tryParse(value);
                                        } catch (e) {}
                                      },
                                    ),
                                    SizedBox(width: 18),
                                    CustomTextFormField(
                                      initialValue: widget.agency!.address,
                                      hintText: "Inserisci l'indirizzo",
                                      label: "Indirizzo",
                                      onChangedCallback: (String value) {
                                        widget.agency!.address = value;
                                      },
                                    ),
                                  ],
                                ),
                                SizedBox(height: 15),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    CustomTextFormField(
                                      initialValue: widget.agency!.vat,
                                      hintText: "Inserisci la partita iva",
                                      label: "P.IVA",
                                      onChangedCallback: (String value) {
                                        widget.agency!.vat = value;
                                      },
                                    ),
                                    SizedBox(width: 18),
                                    CustomTextFormField(
                                      initialValue: widget.agency!.phone,
                                      hintText: "Inserisci il telefono",
                                      label: "Telefono",
                                      onChangedCallback: (String value) {
                                        widget.agency!.phone = value;
                                      },
                                    ),
                                  ],
                                ),
                                SizedBox(height: 15),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    CustomTextFormField(
                                      initialValue: widget.agency!.city,
                                      hintText: "Inserisci la città",
                                      label: "Città",
                                      onChangedCallback: (String value) {
                                        widget.agency!.city = value;
                                      },
                                    ),
                                  ],
                                ),
                                SizedBox(height: 25),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    MouseRegion(
                                      cursor: SystemMouseCursors.click,
                                      child: GestureDetector(
                                        onTap: () async {
                                          setState(() {
                                            clicked = true;
                                          });

                                          if (!_formKey.currentState!
                                              .validate()) {
                                            return;
                                          }

                                          setState(() {
                                            loading = true;
                                          });
                                          print(widget.agency!.toMap());
                                          await updateDocument(
                                            COLLECT_AGENCIES,
                                            widget.agency!.id!,
                                            widget.agency!.toMap(),
                                          );

                                          setState(() {
                                            loading = false;
                                          });
                                          Navigator.of(context).pop(true);
                                        },
                                        child: Container(
                                          height: 45,
                                          decoration: BoxDecoration(
                                            color:
                                                Theme.of(context).primaryColor,
                                            borderRadius:
                                                BorderRadius.circular(7),
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 30.0),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                NarFormLabelWidget(
                                                  label: 'Conferma',
                                                  textColor: Colors.white,
                                                  fontSize: 15,
                                                  fontWeight: '600',
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}
