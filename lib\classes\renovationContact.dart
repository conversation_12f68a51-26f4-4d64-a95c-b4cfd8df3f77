import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/basePersonInfo.dart';
import 'package:newarc_platform/classes/lead.dart';
import 'package:newarc_platform/classes/renovationContactAddress.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/utils/various.dart';

class RenovationContact {
  String? id;
  String? userId;
  @Deprecated('Use RenovationContact.personInfo.name instead, keep in mind retrocompatibility')
  String? name;
  @Deprecated('Use RenovationContact.personInfo.surname instead, keep in mind retrocompatibility')
  String? surname;
  @Deprecated('Use RenovationContact.personInfo.email instead, keep in mind retrocompatibility')
  String? email;
  @Deprecated('Use RenovationContact.personInfo.phone instead, keep in mind retrocompatibility')
  String? phone;
  BasePersonInfo? personInfo;
  int? created;
  String? contactStatus;
  String? renovationStatus;
  String? assignedRenovatorId;
  String? assignedRenovationProjectId;
  @Deprecated('Use RenovationContact.addressInfo.fullAddress instead, keep in mind retrocompatibility')
  String? streetAddress;
  @Deprecated('Use RenovationContact.addressInfo.city instead, keep in mind retrocompatibility')
  String? city;
  List<String>? addressInfo = [];
  List? files;
  String? assignedQuotation;
  late bool isFirebaseUser;
  int? firebaseUserCreated;
  // attributes of agency suggested renovation contacts
  late bool isSuggestedContact;
  String? agencyId;
  num? renovationPrice;
  num? agencyCommission;
  String? suggestionStatus; // "segnalato", "acquisito", "non-acquisito"
  late bool isArchived;
  late bool isRequestingQuotation; // signals whether to show suggested contact in "clienti ristrutturazione" as well as "ristrutturazioni segnalate"


  RenovationContact(Map<String, dynamic> renContactMap) {

    this.userId = renContactMap['userId'];

    this.name = renContactMap['name'];
    this.surname = renContactMap['surname'];
    this.email = renContactMap['email'];
    this.phone = renContactMap['phone'];
    this.personInfo = (renContactMap.containsKey('personInfo') && renContactMap['personInfo'] != null) ? BasePersonInfo.fromMap(renContactMap['personInfo']) : null;
    this.created = renContactMap['created'];
    this.contactStatus = renContactMap['contactStatus'];
    this.renovationStatus = renContactMap['renovationStatus'];
    this.streetAddress = renContactMap['streetAddress'];
    this.city = renContactMap['city'];
    
    this.addressInfo = renContactMap.containsKey('addressInfo') && renContactMap['addressInfo'] != null ? renContactMap['addressInfo'] : [];

    this.assignedRenovatorId = renContactMap['assignedRenovatorId'];
    this.assignedRenovationProjectId = renContactMap['assignedRenovationProjectId'];
    this.files = renContactMap['files'].length != null ? renContactMap['files'] : [];
    this.assignedQuotation = renContactMap['assignedQuotation'] != null ? renContactMap['assignedQuotation'] : null;
    this.isFirebaseUser = renContactMap['isFirebaseUser'] ?? false;
    this.firebaseUserCreated = renContactMap['firebaseUserCreated'];
    this.isSuggestedContact = renContactMap['isSuggestedContact'] ?? false;
    this.agencyId = renContactMap['agencyId'];
    this.renovationPrice = renContactMap['renovationPrice'];
    this.agencyCommission = renContactMap['agencyCommission'];
    this.suggestionStatus = renContactMap['suggestionStatus'];
    this.isArchived = renContactMap['isArchived'] ?? false;
    this.isRequestingQuotation = renContactMap['isRequestingQuotation'] ?? true;

  }

  RenovationContact.empty() {
    this.id = '';
    this.userId = '';
    this.name = '';
    this.surname = '';
    this.email = '';
    this.phone = '';
    this.personInfo = BasePersonInfo.empty();
    this.created = Timestamp.now().millisecondsSinceEpoch;
    this.contactStatus = 'primo-incontro';
    this.renovationStatus = '';
    this.assignedRenovatorId = '';
    this.assignedRenovationProjectId = '';
    this.streetAddress = '';
    this.city = '';
    this.addressInfo = [];
    this.assignedRenovatorId = '';
    this.files = [];
    this.assignedQuotation = null;
    this.isFirebaseUser = false;
    this.firebaseUserCreated = null;
    this.isSuggestedContact = false;
    this.agencyId = null;
    this.renovationPrice = null;
    this.agencyCommission = null;
    this.suggestionStatus = null;
    this.isArchived = false;
    this.isRequestingQuotation = true;

  }

  RenovationContact.fromDocument(Map<String, dynamic> data, String id) {
    
    
      try {
        
      
    

      this.id = id;
      this.userId = data['userId'];

      this.name = data['name'];
      this.surname = data['surname'];
      this.email = data['email'];
      this.phone = data['phone'];
      this.personInfo = (data.containsKey('personInfo') && data['personInfo'] != null) ? BasePersonInfo.fromMap(data['personInfo']) : null;
      this.created = data['created'];
      this.contactStatus = data['contactStatus']??'';
      this.renovationStatus = data['renovationStatus']??'';
      this.assignedRenovatorId = data['assignedRenovatorId']??'';
      this.assignedRenovationProjectId = data['assignedRenovationProjectId']??'';
      this.streetAddress = data['streetAddress']??'';
      this.city = data['city'] == null ? '' : data['city'];
      this.files = data['files'] == null ? [] : data['files'];
      this.assignedQuotation = data['assignedQuotation'] == null ? null : data['assignedQuotation'];
      this.isFirebaseUser = data['isFirebaseUser'] ?? false;
      this.firebaseUserCreated = data['firebaseUserCreated'];
      this.isSuggestedContact = data['isSuggestedContact'] ?? false;
      this.agencyId = data['agencyId'];
      this.renovationPrice = data['renovationPrice'];
      this.agencyCommission = data['agencyCommission'];
      this.suggestionStatus = data['suggestionStatus'];
      this.isArchived = data['isArchived'] ?? false;
      this.isRequestingQuotation = data['isRequestingQuotation'] ?? true;

      try {
        this.addressInfo = data.containsKey('addressInfo') && data['addressInfo'] != null is Map<String, dynamic>
        ? (data['addressInfo'] as List).map((item) => item.toString()).toList()
        : [];
      } catch (e){
        this.addressInfo = [];

        if( data['addressInfo'] is Map ) {
          
          BaseAddressInfo address = BaseAddressInfo.fromMap(data['addressInfo']);
          RenovationContactAddress renovationContactAddress = RenovationContactAddress({
            'renovationContactid': id,
            'created': DateTime.now().millisecondsSinceEpoch,
            'addressInfo': address,
            'isArchived': false
          });

          // fallbackAddAddress(id, renovationContactAddress);

          

        }
        
      }
      // fallbackUserId();
      // fallbackAddToLead();
      } catch (e) {
        debugPrint(e.toString());
        debugPrintStack();
      }
    
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': this.userId,
      'name': this.name,
      'surname': this.surname,
      'email': this.email,
      'phone': this.phone,
      'personInfo': this.personInfo?.toMap(),
      'created': this.created,
      'contactStatus': this.contactStatus,
      'renovationStatus': this.renovationStatus,
      'assignedRenovatorId': this.assignedRenovatorId,
      'assignedRenovationProjectId': this.assignedRenovationProjectId,
      'streetAddress': this.streetAddress,
      'city': this.city,
      'addressInfo': this.addressInfo,
      'files': this.files,
      'assignedQuotation': this.assignedQuotation,
      'isFirebaseUser': this.isFirebaseUser,
      'firebaseUserCreated': this.firebaseUserCreated,
      'isSuggestedContact': this.isSuggestedContact,
      'agencyId': this.agencyId,
      'renovationPrice': this.renovationPrice,
      'agencyCommission': this.agencyCommission,
      'suggestionStatus': this.suggestionStatus,
      'isArchived': this.isArchived,
      'isRequestingQuotation': this.isRequestingQuotation,
    };
  }

  fallbackAddAddress( String renovationContactId, RenovationContactAddress address ) async {

    DocumentReference<Map<String, dynamic>> addedAddress = await FirebaseFirestore.instance
    .collection(appConfig.COLLECT_RENOVATION_CONTACT_ADDRESS)
    .add(address.toMap());


    await FirebaseFirestore.instance
    .collection(appConfig.COLLECT_RENOVATION_CONTACTS)
    .doc(renovationContactId)
    .update({ 'addressInfo': [ addedAddress.id ] });
  }

  // fallbackUserId() async {
  //   ''' fallback to change user_id to userId ''';
  //   await FirebaseFirestore.instance
  //     .collection(appConfig.COLLECT_RENOVATION_CONTACTS)
  //     .doc(this.id)
  //     .update(this.toMap());
  // }

  fallbackAddToLead() async {

    /*BasePersonInfo personInfo = BasePersonInfo.empty();
    
    if( this.personInfo == null ) {
      personInfo = new BasePersonInfo({
        'name': this.name!,
        'surname': this.surname,
        'phone': this.phone,
        'email': this.email

      });

      await FirebaseFirestore.instance
      .collection(appConfig.COLLECT_RENOVATION_CONTACTS)
      .doc(this.id)
      .update({ 
        'personInfo': personInfo.toMap() 
      });

    } else {
      personInfo = this.personInfo!;
    }

    */

    /* If a Renovation Contact has a userId then we don't need to take an action */
    if( this.userId != '' ) return;

    BasePersonInfo personInfo = this.personInfo!;

    print({personInfo.toFullName()});

    /* Get users with the email address */
    QuerySnapshot<Map<String, dynamic>> checkUser = await FirebaseFirestore.instance
    .collection(appConfig.COLLECT_USERS)
    .where('email', isEqualTo: personInfo.email)
    .get();

    /* If a user exists with the email address then update the userId in the Renovation Contact */
    if( checkUser.docs.length > 0 ) {
      // NewarcUser nu = new NewarcUser.fromDocument(checkUser.docs.first.data(), checkUser.docs.first.id);
      // if( nu. )
      await FirebaseFirestore.instance
      .collection(appConfig.COLLECT_RENOVATION_CONTACTS)
      .doc(this.id)
      .update({ 
        'userId': checkUser.docs.first.id
      });

      return;

    } 

    /*DocumentReference renovationContactRef = FirebaseFirestore.instance.collection(appConfig.COLLECT_RENOVATION_CONTACTS).doc(this.id);

    Lead leadData = new Lead({
      'basePersonInfo': personInfo,
      'insertTimestamp': Timestamp.now().millisecondsSinceEpoch,
      'source': this.isSuggestedContact ? 'agency' : 'direct',
      'sourceReference': renovationContactRef
    });

    DocumentReference<Map<String, dynamic>> leadRef = await FirebaseFirestore.instance
    .collection(appConfig.COLLECT_LEADS)
    .add(leadData.toMap());*/

    // createLead(this, this.isSuggestedContact ? 'agency' : 'direct', uid)
    registerRenovationContact(this).then(( uid ) async {

      createLead(this, 'direct', uid);

    });




  }
}

Map<String, String> stringMap = {'primo-incontro': 'Primo incontro'};
