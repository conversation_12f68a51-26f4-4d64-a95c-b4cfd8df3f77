import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/services.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/classes/property.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:newarc_platform/app_config.dart' as appConfig;


agencyDetails( agencyId ) async {
    
    QuerySnapshot<Map<String, dynamic>> collectionSnapshot = await FirebaseFirestore.instance
    .collection(appConfig.COLLECT_USERS)
    .where('agencyId', isEqualTo: agencyId)
    .limit(1)
    .get();

    if( collectionSnapshot.docs.length > 0 ) {

      AgencyUser agency = AgencyUser.fromDocument(collectionSnapshot.docs[0].data() , collectionSnapshot.docs[0].id);

      
      var agencyImageUrl = await agencyProfileUrl(agency.agencyId, agency.profilePicture);

      print({ agency.toMap(), agencyImageUrl });


    }

}


pdfAdsBrochureCopy( Property adData, NewarcProject project ) async {
  
  try {
    print({'pdf download: pdfAdsBrochure'});

    final ByteData fontMediumData = await rootBundle.load('assets/fonts/Raleway-Medium.ttf');
    final ralewayMedium = pw.Font.ttf(fontMediumData.buffer.asByteData());

    final ByteData fontBoldData =await rootBundle.load('assets/fonts/Raleway-Bold.ttf');
    final ralewayBold = pw.Font.ttf(fontBoldData.buffer.asByteData());

    final ByteData fontItalicData =await rootBundle.load('assets/fonts/Raleway-BoldItalic.ttf');
    final ralewayItalic = pw.Font.ttf(fontItalicData.buffer.asByteData());

    

    if( adData.photoBrochureCoverPaths!.length > 0 ) {
      String coverImageUrl = await printUrl(adData.photoBrochureCoverPaths![0]['location'], '', adData.photoBrochureCoverPaths![0]['filename']);
    }
    
    // final coverImage = await getNetworkImage( coverImageUrl );

    final Uint8List coverLogoData = await loadImage('assets/logo_newarc_immagina.png');
    final coverLogo = pw.MemoryImage(coverLogoData);

    final pdf = pw.Document();

    // List<pw.Widget> pdfCover = [];

    String projectTitle = adData.propertyName!;

    // Fetch agency data
    var agencyLogo;
    String agencyImageUrl = '';
    Agency agencyData = Agency.empty();
    if( project.assignedAgency!['agencyId'] != '' ) {
      
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot = await FirebaseFirestore.instance
      .collection(appConfig.COLLECT_USERS)
      .where('agencyId', isEqualTo: project.assignedAgency!['agencyId'])
      .limit(1)
      .get();

      DocumentSnapshot<Map<String, dynamic>> collectionSnapshotAgency = await FirebaseFirestore.instance
        .collection(appConfig.COLLECT_AGENCIES)
        .doc(project.assignedAgency!['agencyId'])
        .get();

      if( collectionSnapshot.docs.length > 0 ) {

        AgencyUser agency = AgencyUser.fromDocument(collectionSnapshot.docs[0].data() , collectionSnapshot.docs[0].id);
        agencyImageUrl = await agencyProfileUrl(agency.agencyId, agency.profilePicture);
        agencyLogo = await getNetworkImage( agencyImageUrl );

      }

      if( collectionSnapshotAgency.exists ) {
        agencyData = Agency.fromDocument(collectionSnapshotAgency.data()!, collectionSnapshotAgency.id);
      }

    }

    pw.Widget pageFooter;

    pageFooter = pw.Padding(
      padding: pw.EdgeInsets.only(left: 45, right: 45, top: 10, bottom: 25 ),
      child: pw.Row(
        children: [
          pw.Expanded(
            flex: 4,
            child: pw.Container(
              padding: pw.EdgeInsets.symmetric( vertical: 10, horizontal: 15 ),
              decoration: pw.BoxDecoration(
                borderRadius: pw.BorderRadius.circular(12),
                color: PdfColor.fromHex('#F5F5F5')
              ),
              child: pw.Center(
                child: pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Text(
                      'UN PROGETTO',
                      style: pw.TextStyle(
                        fontSize: 11,
                        color: PdfColor.fromHex('#5A5A5A'),
                        font: ralewayMedium
                      )
                    ),

                    pw.Image(coverLogo, height: 20)
                  ]
                )
              )
            )
          ),
          pw.SizedBox(width: 20),
          pw.Expanded(
            flex: 7,
            child: pw.Container(
              padding: pw.EdgeInsets.symmetric( vertical: 10, horizontal: 15 ),
              decoration: pw.BoxDecoration(
                borderRadius: pw.BorderRadius.circular(12),
                color: PdfColor.fromHex('#F5F5F5')
              ),
              child: pw.Center(
                child: pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [

                    pw.Row(
                      children: [
                        pw.Text(
                          'PER',
                          style: pw.TextStyle(
                            fontSize: 11,
                            color: PdfColor.fromHex('#5A5A5A'),
                            font: ralewayMedium
                          )
                        ),
                        pw.SizedBox(width: 10),
                        pw.Image(agencyLogo, height: 20),
                        pw.SizedBox(width: 10),
                        pw.Text(
                          agencyData.name!,
                          style: pw.TextStyle(
                            fontSize: 14,
                            color: PdfColors.black,
                            font: ralewayBold
                          )
                        ),
                      ]
                    ),
                    pw.Text(
                      '${agencyData.address} • ${agencyData.phone}',
                      style: pw.TextStyle(
                        fontSize: 14,
                        color: PdfColors.black,
                        font: ralewayMedium
                      )
                    ),
                  ]
                )
              )
            )
          ),
        ]
      )
    );


    int counterAI = 0;

    
    

    final Uint8List backgroundImageData = await loadImage('assets/background_last_page.jpg');
    final backgroundImage = pw.MemoryImage(backgroundImageData);

    final Uint8List risLogoData = await loadImage('assets/rq-pdf-cover-logo.png');
    final risLogoImage = pw.MemoryImage(risLogoData);

    final Uint8List immaginaLogoData = await loadImage('assets/immagina_logo_white.png');
    final immaginaLogoImage = pw.MemoryImage(immaginaLogoData);

    final Uint8List locationIconData = await loadImage('assets/icons/pdf-location.png');
    final locationIcon = pw.MemoryImage(locationIconData);

    final Uint8List phoneIconData = await loadImage('assets/icons/pdf-phone.png');
    final phoneIcon = pw.MemoryImage(phoneIconData);

    


    // Last Page

    pw.Widget lastPage = pw.Container(
      height: 900,
      width: 1300,
      child: pw.Stack(
        children: [
          pw.Positioned(
            top: 0,
            left: 0,
            child: pw.Container(
              height: 900,
              width: 1300,
              decoration: pw.BoxDecoration(
                image: pw.DecorationImage(image: backgroundImage, fit: pw.BoxFit.cover)
              )
            )
          ),
          pw.Positioned(
            top: 0,
            left: 0,
            child: pw.Container(
              padding: pw.EdgeInsets.symmetric(vertical: 40, horizontal: 45),
              height: 900,
              width: 1300,
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.center,
                mainAxisAlignment: pw.MainAxisAlignment.center,
                mainAxisSize: pw.MainAxisSize.max,
                children: [
                  pw.Image( 
                    immaginaLogoImage,
                    height: 97,
                  ),
                  pw.SizedBox(height: 50),

                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Container(
                        decoration: pw.BoxDecoration(
                          borderRadius: pw.BorderRadius.circular(20),
                          color: PdfColors.white
                        ),
                        width: 565,
                        height: 550,
                        child: pw.Center(
                          child: pw.Column(
                            mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly,
                            children: [
                              pw.Image( agencyLogo , height: 70 ),
                              pw.Column(
                                children: [
                                  pw.Text(
                                    'Ti interessa questo immobile?',
                                    style: pw.TextStyle(
                                      fontSize: 30,
                                      color: PdfColors.black,
                                      font: ralewayBold
                                    )
                                  ),
                                  pw.SizedBox(height: 10),
                                  pw.Text(
                                    'Contatta l’agenzia',
                                    style: pw.TextStyle(
                                      fontSize: 28,
                                      color: PdfColors.black,
                                      font: ralewayMedium 
                                    )
                                  ),
                                ]
                              ),
                              pw.Row(
                                children: [
                                  pw.Expanded(
                                    child: pw.Column(
                                      crossAxisAlignment: pw.CrossAxisAlignment.center,
                                      children: [
                                        pw.Image( phoneIcon, height: 40 ),
                                        pw.SizedBox(height: 10),
                                        pw.Text(
                                          agencyData.phone!,
                                          style: pw.TextStyle(
                                            fontSize: 17,
                                            color: PdfColors.black,
                                            font: ralewayMedium 
                                          )
                                        ),
                                      ]
                                    )
                                  ),
                                  pw.Expanded(
                                    child: pw.Column(
                                      crossAxisAlignment: pw.CrossAxisAlignment.center,
                                      children: [
                                        pw.Image( locationIcon, height: 40 ),
                                        pw.SizedBox(height: 10),
                                        pw.Text(
                                          agencyData.address! +', '+ agencyData.city!,
                                          style: pw.TextStyle(
                                            fontSize: 17,
                                            color: PdfColors.black,
                                            font: ralewayMedium 
                                          )
                                        ),
                                      ]
                                    )
                                  ),
                                ]
                              )
                            ]
                          )
                        )
                      ),
                      pw.SizedBox(width: 30),
                      pw.Container(
                        decoration: pw.BoxDecoration(
                          borderRadius: pw.BorderRadius.circular(20),
                          color: PdfColors.white
                        ),
                        width: 565,
                        height: 550,
                        child: pw.Center(
                          child: pw.Column(
                            mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly,
                            children: [
                              pw.Image( risLogoImage , height: 70 ),
                              pw.Column(
                                children: [
                                  pw.Text(
                                    'Ti piace questa ristrutturazione?',
                                    style: pw.TextStyle(
                                      fontSize: 30,
                                      color: PdfColor.fromHex('#489B79'),
                                      font: ralewayBold
                                    )
                                  ),
                                  pw.SizedBox(height: 10),
                                  pw.Text(
                                    'Contatta Newarc',
                                    style: pw.TextStyle(
                                      fontSize: 28,
                                      color: PdfColor.fromHex('#489B79'),
                                      font: ralewayMedium 
                                    )
                                  ),
                                ]
                              ),
                              pw.Row(
                                children: [
                                  pw.Expanded(
                                    child: pw.Column(
                                      crossAxisAlignment: pw.CrossAxisAlignment.center,
                                      children: [
                                        pw.Image( phoneIcon, height: 40 ),
                                        pw.SizedBox(height: 10),
                                        pw.Text(
                                          '************',
                                          style: pw.TextStyle(
                                            fontSize: 17,
                                            color: PdfColors.black,
                                            font: ralewayMedium 
                                          )
                                        ),
                                      ]
                                    )
                                  ),
                                  pw.Expanded(
                                    child: pw.Column(
                                      crossAxisAlignment: pw.CrossAxisAlignment.center,
                                      children: [
                                        pw.Image( locationIcon, height: 40 ),
                                        pw.SizedBox(height: 10),
                                        pw.Text(
                                          'Corso Ferrucci 36, Torino',
                                          style: pw.TextStyle(
                                            fontSize: 17,
                                            color: PdfColors.black,
                                            font: ralewayMedium 
                                          )
                                        ),
                                      ]
                                    )
                                  ),
                                ]
                              )
                            ]
                          )
                        )
                      ),
                    ]
                  ),

                  pw.SizedBox(height: 50),
                  
                  pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.center,
                    children: [
                      pw.Text(
                        'Newarc Srl',
                        style: pw.TextStyle(
                          fontSize: 16,
                          color: PdfColor.fromHex('#ffffff'),
                          font: ralewayMedium
                        )
                      ),
                      pw.SizedBox(height: 15),

                      pw.Text(
                        'Sede Legale: Via Vittorio Emanuele II 29, Chieri • Sede Operativa: Corso Ferrucci 36, Torino • P.Iva 12533550013',
                        style: pw.TextStyle(
                          fontSize: 14,
                          color: PdfColor.fromHex('#ffffff'),
                          font: ralewayMedium
                        )
                      ),
                      pw.SizedBox(height: 3),
                      pw.Text(
                        '<EMAIL> • 011 02 63 850',
                        style: pw.TextStyle(
                          fontSize: 14,
                          color: PdfColor.fromHex('#ffffff'),
                          font: ralewayMedium
                        )
                      ),

                    ]
                  )




                ]

              )
            )
          )
          
        ]
      )
      
    );

    pdf.addPage(
        pw.Page(
          theme:
              pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
          pageFormat: PdfPageFormat(1300, 900),
          orientation: pw.PageOrientation.landscape,
          margin: const pw.EdgeInsets.all(0),
          build: (pw.Context context) => lastPage,
        )
    );
    
    List<pw.Widget> pdfPage1 = [];

    
    pdf.addPage(
        pw.MultiPage(
          theme:
              pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
          pageFormat: PdfPageFormat(1300, 900),
          // pageFormat: PdfPageFormat.a4,
          orientation: pw.PageOrientation.landscape,
          margin: const pw.EdgeInsets.all(0),
          footer: (context) => pageFooter,
          // build: (pw.Context context) => [beforeAfter[0]],
          build: (pw.Context context) => pdfPage1,
        )
    );

  
    final pdfBytes = await pdf.save();

    await downloadPdf(pdfBytes: pdfBytes,fileName: '${adData.code!}.pdf');

  } catch (e,s) {
    print({e,s});
  }





}