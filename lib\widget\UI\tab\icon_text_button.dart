import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:newarc_platform/utils/color_schema.dart';

class IconTextButtonWidget extends StatelessWidget {
  final String text;
  final String icon;
  final Color? textColor;
  final Color? iconColor;
  final Color? backgroundColor, borderColor;
  final bool? iconOnLeft;
  final double? iconHeight;
  final VoidCallback? onPressed;
  final TextStyle? textStyle;
  final double? height;

  const IconTextButtonWidget({
    Key? key,
    required this.text,
    required this.icon,
    this.textColor,
    required this.iconColor,
    this.backgroundColor,
    this.borderColor = AppColor.lightGreyColor,
    this.iconOnLeft = true,
    this.iconHeight = 18,
    this.height,
    required this.onPressed,
    this.textStyle =  const TextStyle(
      fontSize: 12,
      fontFamily: 'Raleway-600',
      color: Colors.black,
    )
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          height: height,
          padding: EdgeInsets.symmetric(horizontal: 14, vertical: 6),
          decoration: BoxDecoration(
            color: backgroundColor ?? AppColor.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              width: 1,
              color: borderColor!,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: iconOnLeft == true
                ? [
              SvgPicture.asset(
                icon,
                color: iconColor,
                height: iconHeight,
              ),
              SizedBox(width: 8),
              Flexible(
                // child: NarFormLabelWidget(
                //   label: text,
                //   overflow: TextOverflow.ellipsis,
                //   fontSize: 12,
                //   fontWeight: '600',
                //   textColor: textColor ?? AppColor.black,
                // ),
                child: Text(
                  text,
                  style: textStyle
                )
                // child: Text(
                //   text,
                //   overflow: TextOverflow.ellipsis,
                //   style: TextStyle().text12w600.textColor(textColor ?? AppColor.black),
                // ),
              ),
            ]
                : [
              Flexible(
                child: Text(
                  text,
                  style: textStyle
                )
                // child: Text(
                //   text,
                //   overflow: TextOverflow.ellipsis,
                //   style: TextStyle().text12w600.textColor(textColor ?? AppColor.black),
                // ),
              ),
              SizedBox(width: 8),
              SvgPicture.asset(
                icon,
                color: iconColor,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
