import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/property.dart';
import 'package:intl/intl.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:newarc_platform/app_config.dart' as appConfig;


downloadBrochureQuotationPagePDF(Property adData, Map projectDetails,{ValueNotifier<double>? progress}) async {
  progress?.value = 0.0;
  NumberFormat localCurrencyFormatMain =
  NumberFormat.decimalPattern('it_IT');

  try {
    print({'pdf download: pdfAdsBrochure'});

    final ByteData fontMediumData =
    await rootBundle.load('assets/fonts/Raleway-Medium.ttf');
    final ralewayMedium = pw.Font.ttf(fontMediumData.buffer.asByteData());
    progress?.value = 0.1;

    final ByteData fontBoldData =
    await rootBundle.load('assets/fonts/Raleway-Bold.ttf');
    final ralewayBold = pw.Font.ttf(fontBoldData.buffer.asByteData());
    progress?.value = 0.2;

    final ByteData fontItalicData =
    await rootBundle.load('assets/fonts/Raleway-BoldItalic.ttf');
    final ralewayItalic = pw.Font.ttf(fontItalicData.buffer.asByteData());
    progress?.value = 0.3;



    final Uint8List coverLogoData =
    await loadImage('assets/logo_newarc_immagina.png');
    final coverLogo = pw.MemoryImage(coverLogoData);


    final pdf = pw.Document();
    progress?.value = 0.4;

    String projectTitle = projectDetails['title'];


    final Uint8List hammerIconData =
    await loadImage('assets/icons/pdf-restructure-hammer.png');
    final hammerIcon = pw.MemoryImage(hammerIconData);

    final Uint8List arrowHouseData =
    await loadImage('assets/icons/pdf-house-arrow.png');
    final arrowHouseIcon = pw.MemoryImage(arrowHouseData);

    final Uint8List materialPremiumData =
    await loadImage('assets/icons/pdf-material-premium.png');
    final materialPremiumIcon = pw.MemoryImage(materialPremiumData);
    progress?.value = 0.5;

    final Uint8List materialStandardData =
    await loadImage('assets/icons/pdf-material-standard.png');
    final materialStandardIcon = pw.MemoryImage(materialStandardData);

    final Uint8List infissiData =
    await loadImage('assets/icons/pdf-infissi.png');
    final infissiDataIcon = pw.MemoryImage(infissiData);
    progress?.value = 0.6;

    final Uint8List arrowData = await loadImage('assets/icons/pdf-arrow.png');
    final arrowIcon = pw.MemoryImage(arrowData);

    final Uint8List workDurationData =
    await loadImage('assets/icons/work-duration.png');
    final workDurationIcon = pw.MemoryImage(workDurationData);
    progress?.value = 0.7;

    List standardFeatures = [
      'Allestimento cantiere',
      'Rifacimento completo bagni',
      'Controsoffitto bagni',
      'Ripristino pavimenti',
      'Posa porte interne',
      'Decorazioni e finiture'
    ];

    List premiumFeatures = [
      'Progettazione',
      'Pratiche',
      'Allestimento cantiere',
      'Demolizioni e costruzioni',
      'Massetti',
      'Controsoffitti',
      'Impianto elettrico',
      'Impianto idrico / gas',
      'Rifacimento completo bagni',
      'Posa pavimenti e rivestimenti',
      'Posa porte interne',
      'Predisposizione climatizzazione',
      'Rasature',
      'Decorazioni e finiture'
    ];

    // Fetch agency data
    var agencyLogo;
    String agencyImageUrl = '';
    Agency agencyData = Agency.empty();
    if (projectDetails['agencyId'] != '') {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot =
      await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_USERS)
          .where('agencyId', isEqualTo: projectDetails['agencyId'])
          .limit(1)
          .get();
      progress?.value = 0.8;
      DocumentSnapshot<Map<String, dynamic>> collectionSnapshotAgency =
      await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_AGENCIES)
          .doc(projectDetails['agencyId'])
          .get();

      if (collectionSnapshot.docs.length > 0) {
        AgencyUser agency = AgencyUser.fromDocument(
            collectionSnapshot.docs[0].data(), collectionSnapshot.docs[0].id);
        agencyImageUrl =
        await agencyProfileUrl(agency.agencyId, agency.profilePicture);
        agencyLogo = await getNetworkImage(agencyImageUrl);
      }
      progress?.value = 0.9;

      if (collectionSnapshotAgency.exists) {
        agencyData = Agency.fromDocument(
            collectionSnapshotAgency.data()!, collectionSnapshotAgency.id);
        agencyData.phone = agencyData.phone!.replaceFirst('+39 ', '');
      }
    }

    // Cover page starts

    List<pw.Widget> pdfPage1 = [];
    String addressLine = '';
    if (adData.zone != '') {
      addressLine += '${adData.zone}, ';
    }

    if (adData.city != '') {
      addressLine += '${adData.city}';
    }
    pw.Widget pageFooter;

    pageFooter = pw.Padding(
        padding: pw.EdgeInsets.only(left: 45, right: 45, top: 20, bottom: 10),
        child: pw.Row(children: [
          pw.Expanded(
              flex: 3,
              child: pw.Container(
                  padding:
                  pw.EdgeInsets.symmetric(vertical: 20, horizontal: 20),
                  decoration: pw.BoxDecoration(
                      borderRadius: pw.BorderRadius.circular(12),
                      color: PdfColor.fromHex('#F5F5F5')),
                  child: pw.Center(
                      child: pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                          children: [
                            pw.Text('UN PROGETTO',
                                style: pw.TextStyle(
                                    fontSize: 11,
                                    color: PdfColor.fromHex('#5A5A5A'),
                                    font: ralewayMedium)),
                            pw.Image(coverLogo, height: 35)
                          ])))),
          pw.SizedBox(width: 20),
          pw.Expanded(
              flex: 7,
              child: pw.Container(
                  padding:
                  pw.EdgeInsets.symmetric(vertical: 20, horizontal: 20),
                  decoration: pw.BoxDecoration(
                      borderRadius: pw.BorderRadius.circular(12),
                      color: PdfColor.fromHex('#F5F5F5')),
                  child: pw.Center(
                      child: pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                          children: [
                            pw.Row(children: [
                              pw.Text('PER',
                                  style: pw.TextStyle(
                                      fontSize: 11,
                                      color: PdfColor.fromHex('#5A5A5A'),
                                      font: ralewayMedium)),
                              pw.SizedBox(width: 15),
                              pw.Image(agencyLogo, height: 35),
                              pw.SizedBox(width: 15),
                              pw.Text(agencyData.name!,
                                  style: pw.TextStyle(
                                      fontSize: 14,
                                      color: PdfColors.black,
                                      font: ralewayBold)),
                            ]),
                            pw.Text(
                                '${agencyData.toShortAddress()} • ${agencyData.phone}',
                                style: pw.TextStyle(
                                    fontSize: 14,
                                    color: PdfColors.black,
                                    font: ralewayMedium)),
                          ])))),
        ]));
    progress?.value = 0.95;



    // Restoration Page
    pdfPage1.add(
      pw.Container(
        height: 820,
        padding: pw.EdgeInsets.symmetric(vertical: 20, horizontal: 45 ),
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          mainAxisSize: pw.MainAxisSize.max,
          children: [
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              mainAxisAlignment: pw.MainAxisAlignment.start,
              children: [


                pw.Container(
                  decoration: pw.BoxDecoration(
                    border: pw.Border(
                      bottom: pw.BorderSide( width: 1, color: PdfColors.black )
                    )
                  ),
                  padding: pw.EdgeInsets.only(bottom: 10),
                  child: pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: pw.CrossAxisAlignment.center,
                    children: [
                      pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                            'Ristrutturazione',
                            style: pw.TextStyle(
                              fontSize: 30,
                              color: PdfColors.black,
                              font: ralewayBold
                            )
                          ),
                        ]
                      ),

                      pw.Text(
                        projectTitle,
                        style: pw.TextStyle(
                          fontSize: 16,
                          color: PdfColors.black,
                          font: ralewayBold
                        )
                      )
                    ]
                  )
                ),
                pw.SizedBox(height: 30),
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Expanded(
                      flex: 5,
                      child: pw.Column(
                        mainAxisAlignment: pw.MainAxisAlignment.start,
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                            'Lavori',
                            style: pw.TextStyle(
                              fontSize: 18,
                              color: PdfColor.fromHex('#000000'),
                              font: ralewayMedium
                            )
                          ),
                          pw.SizedBox(height: 20),
                          pw.Row(
                            mainAxisAlignment: pw.MainAxisAlignment.start,
                            crossAxisAlignment: pw.CrossAxisAlignment.start,
                            children: [
                              pw.Expanded(
                                child: pw.Container(
                                  height: 550,
                                  decoration: pw.BoxDecoration(
                                    borderRadius: pw.BorderRadius.circular(10),
                                    color: PdfColor.fromHex('#66B393')
                                  ),
                                  child: pw.Column(
                                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                                    children: [
                                      pw.Padding(
                                        padding: pw.EdgeInsets.symmetric(vertical: 20, horizontal: 10),
                                        child: pw.Row(
                                          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                                          children: [
                                            pw.Row(
                                              crossAxisAlignment: pw.CrossAxisAlignment.start,
                                              children: [
                                                pw.Image(
                                                  hammerIcon,
                                                  height: 25,
                                                  width: 25
                                                ),
                                                pw.SizedBox(width: 10),
                                                pw.Container(
                                                  width: 345,
                                                  child: pw.Column(
                                                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                                                    children: [
                                                      pw.SizedBox(height: 3),
                                                      pw.Row(
                                                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                        mainAxisSize: pw.MainAxisSize.max,
                                                        children: [

                                                          pw.Text(
                                                            'Leggera',
                                                            style: pw.TextStyle(
                                                              fontSize: 16,
                                                              color: PdfColors.white,
                                                              font: ralewayMedium
                                                            )
                                                          ),

                                                          pw.Row(
                                                            children: [
                                                              pw.Text(
                                                                '${localCurrencyFormatMain.format(double.tryParse(adData.LighRenoInsMin.toString()))}-${localCurrencyFormatMain.format(double.tryParse(adData.LighRenoInsMax.toString()))}€',
                                                                style: pw.TextStyle(
                                                                  fontSize: 18,
                                                                  color: PdfColors.white,
                                                                  font: ralewayBold
                                                                )
                                                              ),
                                                              pw.Text(
                                                                '+iva',
                                                                style: pw.TextStyle(
                                                                  fontSize: 13,
                                                                  color: PdfColors.white,
                                                                  font: ralewayBold
                                                                )
                                                              ),
                                                            ]
                                                          )
                                                        ]
                                                      ),
                                                      pw.SizedBox(height: 30),
                                                      ...standardFeatures.map((e){
                                                        return pw.Column(
                                                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                                                          children: [
                                                            pw.Text(
                                                            e,
                                                            style: pw.TextStyle(
                                                              fontSize: 14,
                                                              color: PdfColors.white,
                                                              font: ralewayMedium
                                                            )
                                                          ),
                                                          pw.SizedBox(height: 8),
                                                          ]
                                                        );
                                                      }).toList(),

                                                    ]
                                                  ),

                                                )
                                                
                                              ]
                                            ),
                                            // pw.SizedBox(width: 10),
                                            /* pw.Column(
                                              children: [
                                                pw.SizedBox(height: 1),
                                                

                                              ]
                                            ) */


                                          ]
                                        )
                                      ),
                                      pw.Row(
                                        children: [
                                          pw.Expanded(
                                            child: pw.Container(
                                              padding: pw.EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                                              decoration: pw.BoxDecoration(
                                                border: pw.Border(
                                                  top: pw.BorderSide( width: 1, color: PdfColor.fromHex('#AFD8C7') ),
                                                  right: pw.BorderSide( width: 1, color: PdfColor.fromHex('#AFD8C7') ),
                                                )
                                              ),
                                              child: pw.Column(
                                                crossAxisAlignment: pw.CrossAxisAlignment.end,
                                                children: [
                                                  pw.Row(
                                                    crossAxisAlignment: pw.CrossAxisAlignment.center,
                                                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                    children: [
                                                      pw.Image(
                                                        materialStandardIcon,
                                                        height: 25,
                                                        width: 25
                                                      ),
                                                      pw.Text(
                                                        'Materiali Standard',
                                                        style: pw.TextStyle(
                                                          fontSize: 16,
                                                          color: PdfColors.white,
                                                          font: ralewayMedium
                                                        )
                                                      ),
                                                    ],

                                                  ),
                                                  pw.Row(
                                                    mainAxisAlignment: pw.MainAxisAlignment.end,
                                                    children: [
                                                      pw.Text(
                                                        '${localCurrencyFormatMain.format(double.tryParse(adData.materialStandardLightMin.toString()))}-${localCurrencyFormatMain.format(double.tryParse(adData.materialStandardLightMax.toString()))}€',
                                                        style: pw.TextStyle(
                                                          fontSize: 16,
                                                          color: PdfColors.white,
                                                          font: ralewayBold
                                                        )
                                                      ),
                                                      pw.Text(
                                                        '+iva',
                                                        style: pw.TextStyle(
                                                          fontSize: 13,
                                                          color: PdfColors.white,
                                                          font: ralewayBold
                                                        )
                                                      ),
                                                    ]
                                                  )
                                                ]
                                              )
                                            )

                                          ),
                                          pw.Expanded(
                                            child: pw.Container(
                                              padding: pw.EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                                              decoration: pw.BoxDecoration(
                                                border: pw.Border(
                                                  top: pw.BorderSide( width: 1, color: PdfColor.fromHex('#AFD8C7') ),
                                                )
                                              ),
                                              child: pw.Column(
                                                crossAxisAlignment: pw.CrossAxisAlignment.end,
                                                children: [
                                                  pw.Row(
                                                    crossAxisAlignment: pw.CrossAxisAlignment.center,
                                                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                    children: [
                                                      pw.Image(
                                                        materialPremiumIcon,
                                                        height: 25,
                                                        width: 31
                                                      ),
                                                      pw.Text(
                                                        'Materiali Premium',
                                                        style: pw.TextStyle(
                                                          fontSize: 16,
                                                          color: PdfColors.white,
                                                          font: ralewayMedium
                                                        )
                                                      ),
                                                    ],

                                                  ),
                                                  pw.Row(
                                                    mainAxisAlignment: pw.MainAxisAlignment.end,
                                                    children: [
                                                      pw.Text(
                                                        '${localCurrencyFormatMain.format(double.tryParse(adData.materialPremiumLightMin.toString()))}-${localCurrencyFormatMain.format(double.tryParse(adData.materialPremiumLightMax.toString()))}€',
                                                        style: pw.TextStyle(
                                                          fontSize: 16,
                                                          color: PdfColors.white,
                                                          font: ralewayBold
                                                        )
                                                      ),
                                                      pw.Text(
                                                        '+iva',
                                                        style: pw.TextStyle(
                                                          fontSize: 13,
                                                          color: PdfColors.white,
                                                          font: ralewayBold
                                                        )
                                                      ),
                                                    ]
                                                  )
                                                ]
                                              )
                                            )

                                          ),
                                        ]
                                      )
                                    ]
                                  )
                                )
                              ),

                              pw.SizedBox(width:30),

                              pw.Expanded(
                                child: pw.Container(
                                  height: 550,
                                  decoration: pw.BoxDecoration(
                                    borderRadius: pw.BorderRadius.circular(10),
                                    color: PdfColor.fromHex('#449272')
                                  ),
                                  child: pw.Column(
                                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                                    children: [
                                      pw.Padding(
                                        padding: pw.EdgeInsets.symmetric(vertical: 20, horizontal: 10),
                                        child: pw.Row(
                                          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                                          children: [
                                            pw.Row(
                                              crossAxisAlignment: pw.CrossAxisAlignment.start,
                                              children: [
                                                pw.Image(
                                                  hammerIcon,
                                                  height: 25,
                                                  width: 25
                                                ),
                                                pw.SizedBox(width: 10),
                                                pw.Container(
                                                  width: 345,
                                                  child: pw.Column(
                                                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                                                    children: [
                                                      pw.SizedBox(height: 3),
                                                      pw.Row(
                                                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                        mainAxisSize: pw.MainAxisSize.max,
                                                        children: [
                                                          pw.Text(
                                                            'Completa',
                                                            style: pw.TextStyle(
                                                              fontSize: 16,
                                                              color: PdfColors.white,
                                                              font: ralewayMedium
                                                            )
                                                          ),

                                                          pw.Row(
                                                            children: [
                                                              pw.Text(
                                                                '${localCurrencyFormatMain.format(double.tryParse(adData.FullRenoInsMin.toString()))}-${localCurrencyFormatMain.format(double.tryParse(adData.FullRenoInsMax.toString()))}€',
                                                                style: pw.TextStyle(
                                                                  fontSize: 18,
                                                                  color: PdfColors.white,
                                                                  font: ralewayBold
                                                                )
                                                              ),
                                                              pw.Text(
                                                                '+iva',
                                                                style: pw.TextStyle(
                                                                  fontSize: 13,
                                                                  color: PdfColors.white,
                                                                  font: ralewayBold
                                                                )
                                                              ),
                                                            ]
                                                          )
                                                        ]
                                                      ),
                                                      pw.SizedBox(height: 30),
                                                      ...premiumFeatures.map((e){
                                                        return pw.Column(
                                                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                                                          children: [
                                                            pw.Text(
                                                            e,
                                                            style: pw.TextStyle(
                                                              fontSize: 14,
                                                              color: PdfColors.white,
                                                              font: ralewayMedium
                                                            )
                                                          ),
                                                          pw.SizedBox(height: 8),
                                                          ]
                                                        );
                                                      }).toList(),

                                                    ]
                                                  ),

                                                )
                                                
                                              ]
                                            ),
                                            // pw.SizedBox(width: 10),
                                            

                                          ]
                                        )
                                      ),
                                      pw.Column(
                                        children: [
                                          pw.Container(
                                            padding: pw.EdgeInsets.all(10),
                                            decoration: pw.BoxDecoration(
                                              border: pw.Border(
                                                top: pw.BorderSide( width: 1, color: PdfColor.fromHex('#AFD8C7') ),
                                              )
                                            ),
                                            child: pw.Row(
                                              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                              crossAxisAlignment: pw.CrossAxisAlignment.center,
                                              children: [
                                                pw.Row(
                                                  children: [
                                                    pw.Image(
                                                      infissiDataIcon,
                                                      height: 25,
                                                      width: 25
                                                    ),
                                                    pw.SizedBox(width: 20),
                                                    pw.Text(
                                                      'Infissi',
                                                      style: pw.TextStyle(
                                                        fontSize: 16,
                                                        color: PdfColors.white,
                                                        font: ralewayMedium
                                                      )
                                                    ),
                                                  ],

                                                ),

                                                pw.Row(
                                                  mainAxisAlignment: pw.MainAxisAlignment.end,
                                                  children: [
                                                    pw.Text(
                                                      '${localCurrencyFormatMain.format(double.tryParse(adData.materialFixtureFullMin.toString()))}-${localCurrencyFormatMain.format(double.tryParse(adData.materialFixtureFullMax.toString()))}€',
                                                      style: pw.TextStyle(
                                                        fontSize: 16,
                                                        color: PdfColors.white,
                                                        font: ralewayBold
                                                      )
                                                    ),
                                                    pw.Text(
                                                      '+iva',
                                                      style: pw.TextStyle(
                                                        fontSize: 13,
                                                        color: PdfColors.white,
                                                        font: ralewayBold
                                                      )
                                                    ),
                                                  ]
                                                )
                                              ]
                                            )

                                          ),
                                          pw.Row(
                                            children: [
                                              pw.Expanded(
                                                child: pw.Container(
                                                  padding: pw.EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                                                  decoration: pw.BoxDecoration(
                                                    border: pw.Border(
                                                      top: pw.BorderSide( width: 1, color: PdfColor.fromHex('#AFD8C7') ),
                                                      right: pw.BorderSide( width: 1, color: PdfColor.fromHex('#AFD8C7') ),
                                                    )
                                                  ),
                                                  child: pw.Column(
                                                    crossAxisAlignment: pw.CrossAxisAlignment.end,
                                                    children: [
                                                      pw.Row(
                                                        crossAxisAlignment: pw.CrossAxisAlignment.center,
                                                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                        children: [
                                                          pw.Image(
                                                            materialStandardIcon,
                                                            height: 25,
                                                            width: 25
                                                          ),
                                                          pw.Text(
                                                            'Materiali Standard',
                                                            style: pw.TextStyle(
                                                              fontSize: 16,
                                                              color: PdfColors.white,
                                                              font: ralewayMedium
                                                            )
                                                          ),
                                                        ],

                                                      ),
                                                      pw.Row(
                                                        mainAxisAlignment: pw.MainAxisAlignment.end,
                                                        children: [
                                                          pw.Text(
                                                            '${localCurrencyFormatMain.format(double.tryParse(adData.materialStandardFullMin.toString()) )}-${localCurrencyFormatMain.format(double.tryParse(adData.materialStandardFullMax.toString()) )}€',
                                                            style: pw.TextStyle(
                                                              fontSize: 16,
                                                              color: PdfColors.white,
                                                              font: ralewayBold
                                                            )
                                                          ),
                                                          pw.Text(
                                                            '+iva',
                                                            style: pw.TextStyle(
                                                              fontSize: 13,
                                                              color: PdfColors.white,
                                                              font: ralewayBold
                                                            )
                                                          ),
                                                        ]
                                                      )
                                                    ]
                                                  )
                                                )

                                              ),
                                              pw.Expanded(
                                                child: pw.Container(
                                                  padding: pw.EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                                                  decoration: pw.BoxDecoration(
                                                    border: pw.Border(
                                                      top: pw.BorderSide( width: 1, color: PdfColor.fromHex('#AFD8C7') ),
                                                    )
                                                  ),
                                                  child: pw.Column(
                                                    crossAxisAlignment: pw.CrossAxisAlignment.end,
                                                    children: [
                                                      pw.Row(
                                                        crossAxisAlignment: pw.CrossAxisAlignment.center,
                                                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                        children: [
                                                          pw.Image(
                                                            materialPremiumIcon,
                                                            height: 25,
                                                            width: 31
                                                          ),
                                                          pw.Text(
                                                            'Materiali Premium',
                                                            style: pw.TextStyle(
                                                              fontSize: 16,
                                                              color: PdfColors.white,
                                                              font: ralewayMedium
                                                            )
                                                          ),
                                                        ],

                                                      ),
                                                      pw.Row(
                                                        mainAxisAlignment: pw.MainAxisAlignment.end,
                                                        children: [
                                                          pw.Text(
                                                            '${localCurrencyFormatMain.format( double.tryParse(adData.materialPremiumFullMin.toString()))}-${localCurrencyFormatMain.format( double.tryParse(adData.materialPremiumFullMax.toString()))}€',
                                                            style: pw.TextStyle(
                                                              fontSize: 16,
                                                              color: PdfColors.white,
                                                              font: ralewayBold
                                                            )
                                                          ),
                                                          pw.Text(
                                                            '+iva',
                                                            style: pw.TextStyle(
                                                              fontSize: 13,
                                                              color: PdfColors.white,
                                                              font: ralewayBold
                                                            )
                                                          ),
                                                        ]
                                                      )
                                                    ]
                                                  )
                                                )

                                              ),
                                            ]
                                          )
                                        ]
                                      ),

                                    ]
                                  )
                                )
                              )
                            ]
                          )
                        ]
                      )

                    ),

                    pw.SizedBox(width: 50),

                    pw.Expanded(
                      flex: 2,
                      child: pw.Column(
                        mainAxisAlignment: pw.MainAxisAlignment.start,
                        mainAxisSize: pw.MainAxisSize.max,
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                            'Classe energetica',
                            style: pw.TextStyle(
                              fontSize: 18,
                              color: PdfColor.fromHex('#000000'),
                              font: ralewayMedium
                            )
                          ),
                          pw.SizedBox(height: 3),
                          pw.Text(
                            'Miglioramento stimato sostituendo gli infissi.',
                            style: pw.TextStyle(
                              fontSize: 10,
                              color: PdfColor.fromHex('#8E8E8E'),
                              fontItalic: ralewayItalic
                            )
                          ),
                          pw.SizedBox(height: 5),
                          pw.Row(
                            children: [
                              pw.Expanded(
                                child: pw.Container(
                                  height: 550,
                                  width: 300,
                                  decoration: pw.BoxDecoration(
                                    borderRadius: pw.BorderRadius.circular(10),
                                    // color: PdfColor.fromHex('#66B393')
                                  ),
                                  child: pw.Column(
                                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                                    children: [

                                      pw.Row(
                                        crossAxisAlignment: pw.CrossAxisAlignment.center,
                                        children: [
                                          pw.Container(
                                            height: 78,
                                            width: 125,
                                            padding: pw.EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                                            decoration: pw.BoxDecoration(
                                              borderRadius: pw.BorderRadius.circular(10),
                                              border: pw.Border.all(
                                                width: 1,
                                                color: PdfColor.fromHex('#D3D3D3')
                                              )

                                            ),

                                            child: pw.Row(
                                              children: [
                                                pw.Image(
                                                  arrowHouseIcon,
                                                  width: 54,
                                                  height: 40,
                                                ),
                                                pw.Container(
                                                  height: 43,
                                                  width: 43,
                                                  decoration: pw.BoxDecoration(
                                                    color: getEnergyColorCode(adData.actualEnergyClass!),
                                                    borderRadius: pw.BorderRadius.circular(21.5)
                                                  ),
                                                  child: pw.Center(
                                                    child: pw.Text(
                                                      adData.actualEnergyClass!,
                                                      style: pw.TextStyle(
                                                        font: ralewayBold,
                                                        fontSize: 30,
                                                        color: PdfColors.white
                                                      )
                                                    )
                                                  )

                                                )
                                              ]
                                            )
                                          ),
                                          pw.Container(
                                            height: 97,
                                            width: 10,
                                            child: pw.Stack(
                                              overflow: pw.Overflow.visible,

                                              children: [
                                                pw.Positioned(
                                                  child: pw.Image(
                                                    arrowIcon,
                                                    width: 37,
                                                    height: 28,
                                                  ),
                                                  top: 33,
                                                  left: -7
                                                )
                                              ]
                                            )
                                          ),
                                          pw.Container(
                                            height: 97,
                                            width: 160,
                                            padding: pw.EdgeInsets.only(right: 15, left: 30, top: 10, bottom: 10),
                                            decoration: pw.BoxDecoration(
                                              borderRadius: pw.BorderRadius.circular(10),
                                              border: pw.Border.all(
                                                width: 1,
                                                color: PdfColor.fromHex('#D3D3D3')
                                              )

                                            ),

                                            child: pw.Row(
                                              children: [
                                                pw.Image(
                                                  arrowHouseIcon,
                                                  width: 64,
                                                  height: 48,
                                                ),
                                                pw.Container(
                                                  height: 43,
                                                  width: 43,
                                                  decoration: pw.BoxDecoration(
                                                    color: getEnergyColorCode(adData.projectEnergyClass!),
                                                    borderRadius: pw.BorderRadius.circular(21.5)
                                                  ),
                                                  child: pw.Center(
                                                    child: pw.Text(
                                                      adData.projectEnergyClass!,
                                                      style: pw.TextStyle(
                                                        font: ralewayBold,
                                                        fontSize: 30,
                                                        color: PdfColors.white
                                                      )
                                                    )
                                                  )

                                                )
                                              ]
                                            )
                                          )

                                        ]
                                      ),


                                      pw.Column(
                                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                                        children: [
                                          pw.Text(
                                            'Bonus ristrutturazioni 2025',
                                            style: pw.TextStyle(
                                              fontSize: 18,
                                              color: PdfColor.fromHex('#000000'),
                                              font: ralewayMedium
                                            )
                                          ),
                                          pw.SizedBox(height: 10),
                                          pw.Container(
                                            decoration: pw.BoxDecoration(
                                              border: pw.Border.all(
                                                width: 1,
                                                color: PdfColor.fromHex('#D3D3D3'),
                                              ),
                                              borderRadius: pw.BorderRadius.circular(10)
                                            ),
                                            padding: pw.EdgeInsets.symmetric(vertical: 15, horizontal: 10),
                                            child: pw.Column(
                                              crossAxisAlignment: pw.CrossAxisAlignment.start,
                                              children: [
                                                pw.Row(
                                                  mainAxisAlignment: pw.MainAxisAlignment.start,
                                                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                                                  children: [
                                                    pw.Container(
                                                      height: 41,
                                                      width: 41,
                                                      decoration: pw.BoxDecoration(
                                                        borderRadius: pw.BorderRadius.circular(20.5),
                                                        color: PdfColor.fromHex('#489B79')
                                                      ),
                                                      margin: pw.EdgeInsets.only(right:10),
                                                      child: pw.Center(
                                                        child: pw.Text(
                                                          '50%',
                                                          style: pw.TextStyle(
                                                            fontSize: 17,
                                                            color: PdfColor.fromHex('#ffffff'),
                                                            font: ralewayBold
                                                          )
                                                        ),
                                                      )

                                                    ),
                                                    pw.Text(
                                                      'Sulla prima casa',
                                                      style: pw.TextStyle(
                                                        fontSize: 16,
                                                        color: PdfColor.fromHex('#489B79'),
                                                        font: ralewayBold
                                                      )
                                                    ),


                                                  ]
                                                ),
                                                pw.Row(
                                                  mainAxisAlignment: pw.MainAxisAlignment.start,
                                                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                                                  children: [
                                                    pw.Container(
                                                      height: 36,
                                                      width: 36,
                                                      decoration: pw.BoxDecoration(
                                                        borderRadius: pw.BorderRadius.circular(18),
                                                        color: PdfColor.fromHex('#489B79')

                                                      ),
                                                      margin: pw.EdgeInsets.only(right:10, left: 40),
                                                      child: pw.Center(
                                                        child: pw.Text(
                                                          '36%',
                                                          style: pw.TextStyle(
                                                            fontSize: 14,
                                                            color: PdfColor.fromHex('#ffffff'),
                                                            font: ralewayBold
                                                          )
                                                        ),
                                                      )

                                                    ),
                                                    pw.Text(
                                                      'Sulla seconda casa',
                                                      style: pw.TextStyle(
                                                        fontSize: 14,
                                                        color: PdfColor.fromHex('#489B79'),
                                                        font: ralewayBold
                                                      )
                                                    ),


                                                  ]
                                                ),
                                                pw.SizedBox(height: 15),
                                                pw.Text(
                                                  'Il bonus fiscale consiste in una detrazione dall’Irpef, da ripartire in 10 quote annuali di pari importo, del 50% delle spese sostenute sulla prima casa fino ad un massimo di 96.000€ e 36% sulla seconda casa fino ad un massimo di 48.000€.',
                                                  overflow: pw.TextOverflow.visible,
                                                  textAlign: pw.TextAlign.justify,
                                                  style: pw.TextStyle(

                                                    fontSize: 11,
                                                    height: 16,
                                                    letterSpacing: 0.01,
                                                    color: PdfColor.fromHex('#737373'),
                                                    font: ralewayMedium
                                                  )
                                                ),
                                              ]

                                            )
                                          ),

                                        ]
                                      ),

                                      pw.Column(
                                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                                        children: [
                                          pw.Text(
                                            'Tempistiche',
                                            style: pw.TextStyle(
                                              fontSize: 18,
                                              color: PdfColor.fromHex('#000000'),
                                              font: ralewayMedium
                                            )
                                          ),
                                          pw.SizedBox(height: 10),
                                          pw.Container(
                                            decoration: pw.BoxDecoration(
                                              border: pw.Border.all(
                                                width: 1,
                                                color: PdfColor.fromHex('#D3D3D3'),
                                              ),
                                              borderRadius: pw.BorderRadius.circular(10)
                                            ),
                                            padding: pw.EdgeInsets.symmetric(vertical: 15, horizontal: 20),
                                            child: pw.Column(
                                              crossAxisAlignment: pw.CrossAxisAlignment.start,
                                              children: [
                                                pw.Row(
                                                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                                                  children: [
                                                    pw.Text(
                                                      'Durata presunta cantiere',
                                                      style: pw.TextStyle(
                                                        fontSize: 14,
                                                        color: PdfColor.fromHex('#737373'),
                                                        font: ralewayMedium
                                                      )
                                                    ),
                                                    pw.Container(
                                                      height: 35,
                                                      width: 35,
                                                      decoration: pw.BoxDecoration(
                                                        image: pw.DecorationImage(image: workDurationIcon, fit: pw.BoxFit.cover)
                                                      ),

                                                    ),
                                                  ]
                                                ),
                                                pw.SizedBox(height: 15),
                                                pw.Row(
                                                  mainAxisAlignment: pw.MainAxisAlignment.start,
                                                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                                                  children: [
                                                    pw.Text(
                                                      '75 giorni lavorativi',
                                                      style: pw.TextStyle(
                                                        fontSize: 22,
                                                        color: PdfColor.fromHex('#000000'),
                                                        font: ralewayBold
                                                      )
                                                    ),
                                                  ]
                                                ),
                                                pw.SizedBox(height: 5),
                                                pw.Text(
                                                  'da inizio lavori',
                                                  overflow: pw.TextOverflow.visible,
                                                  textAlign: pw.TextAlign.justify,
                                                  style: pw.TextStyle(

                                                    fontSize: 14,
                                                    color: PdfColor.fromHex('#000000'),
                                                    font: ralewayMedium
                                                  )
                                                ),
                                              ]

                                            )
                                          )
                                        ]
                                      )


                                    ]
                                  )
                                )
                              )
                            ]
                          )
                        ]
                      )


                    )

                  ]
                ),

                pw.SizedBox(height: 20),

              ]
            ),
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [

                pw.Text(
                  'La presente quotazione è una stima e potrebbe subire variazioni in sede di preventivazione definitiva. Si consiglia di venire in sede per avere un preventivo gratuito e personalizzato. ',
                  overflow: pw.TextOverflow.visible,
                  textAlign: pw.TextAlign.justify,
                  style: pw.TextStyle(

                    fontSize: 12,
                    height: 18,
                    letterSpacing: 0.01,
                    color: PdfColor.fromHex('#4B4B4B'),
                    font: ralewayBold
                  )
                ),
                pw.SizedBox(height: 5),

                pw.Text(
                'Nella ristrutturazione leggera i materiali inclusi sono: porte interne, pavimenti e rivestimenti bagno, rubinetterie, sanitari, kit doccia incasso, piatti doccia, box doccia, mobili bagno e termoarredi. Nella ristrutturazione completa oltre ai materiali precedenti sono inclusi anche i pavimenti di tutta casa, battiscopa, materiale elettrico e illuminotecnico. I materiali Standard sono i materiali da capitolato di buona finitura e dall’ottimo rapporto qualità/prezzo. I materiali Premium sono materiali di prima scelta e dal massimo livello qualitativo.',
                overflow: pw.TextOverflow.visible,
                textAlign: pw.TextAlign.justify,
                style: pw.TextStyle(

                  fontSize: 10,
                  height: 13,
                  letterSpacing: 0.02,
                  color: PdfColor.fromHex('#8E8E8E'),
                  font: ralewayMedium
                )
              ),
              ]
            )
          ]
        )
      )
    );

    pdf.addPage(pw.MultiPage(
      theme: pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
      pageFormat: PdfPageFormat(1300, 940),
      orientation: pw.PageOrientation.landscape,
      margin: const pw.EdgeInsets.all(0),
      footer: (context) => pageFooter,
      build: (pw.Context context) => pdfPage1,
    ));
    progress?.value = 0.98;

    final pdfBytes = await pdf.save();
    await downloadPdf(pdfBytes: pdfBytes,fileName: '${adData.code!}.pdf');
  } catch (e, s) {
    print({e, s});
  }
}

PdfColor getEnergyColorCode(String code) {
  code = code.toLowerCase();

  switch (code) {
    case 'g':
      return PdfColor.fromHex('#D30202');
    case 'f':
      return PdfColor.fromHex('#E8422E');
    case 'e':
      return PdfColor.fromHex('#F38F24');
    case 'd':
      return PdfColor.fromHex('#FDDF04');
    case 'c':
      return PdfColor.fromHex('#7DCB2D');
    case 'b':
      return PdfColor.fromHex('#25B106');
    case 'a1':
      return PdfColor.fromHex('#037603');
    case 'a2':
      return PdfColor.fromHex('#037603');
    case 'a3':
      return PdfColor.fromHex('#037603');
    case 'a4':
      return PdfColor.fromHex('#037603');
    default:
      return PdfColor.fromHex('#D30202');
  }
}