import 'dart:convert';
import 'dart:js_interop';
import 'dart:typed_data';
import 'package:go_router/go_router.dart';
import 'package:web/web.dart' as web;
import 'package:csv/csv.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/classes/projectEconomic.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:intl/intl.dart';
import 'package:flutter_svg/flutter_svg.dart';

class ProvisionalEconomicAccount extends StatefulWidget {
  final String? firebaseId;

  const ProvisionalEconomicAccount(
      {Key? key, this.firebaseId})
      : super(key: key);

  @override
  State<ProvisionalEconomicAccount> createState() =>
      _ProvisionalEconomicAccountState();
}

class _ProvisionalEconomicAccountState
    extends State<ProvisionalEconomicAccount> {
  NumberFormat localCurrencyFormat =
      NumberFormat.currency(locale: 'it_IT', symbol: '\€', decimalDigits: 2);
  NumberFormat localCurrencyFormatMain =
      NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 2);

  bool isAccount = true;

  String progressMessage = '';
  double cost = 0;
  double revenue = 0;
  bool loading = false;

  List<bool> isActive = [];
  List<TextEditingController> contProvisionalPrice = [];

  Map breakdownLabels = {
    "revenueExpected": 'venditaImmobile',
    "acquisitionCosts": 'costiDiAcquisizione',
    "agencyCost": 'costiDiAgenzia',
    "restorationAndMaterail": 'ristrutturazioneEMateriali',
    "variableCost": "costiVari"
  };

  Map<String, Map<String, List<List>>> costBreakdown = {
    "Entrate": {
      "revenueExpected": [
        ["vendita", 0, false]
      ]
    },
    "Uscite": {
      "acquisitionCosts": [
        ["acquisto", 0, false],
        ["ricavoGarantito", 0, false],
        ["costiNotarili", 0, false],
        ["impostaDiRegistro", 0, false],
      ],
      "agencyCost": [
        ["commissioneIn", 0, false],
        ["commissioneOut", 0, false],
        ["bonusObiettivo", 0, false],
        ["bonusInsieme", 0, false],
      ],
      "restorationAndMaterail": [
        ["materiali", 0, false],
        ["lavoriRistrutturazione", 0, false]
      ],
      "variableCost": [
        ["ipotesiCostiVai", 0, false]
      ]
    }
  };

  List<Map> totals = [];
  List<String> activities = [
    'Demolizioni/Costruzioni',
    'Elettricista',
    'Idraulico',
    'Palchettista',
    'Piastrellista',
    'Decoratore'
  ];

  Map<String, dynamic> existingPEA = {};
  ProjectEconomic? tempEconomic;

  @override
  void initState() {
    super.initState();

    fetchProvisional();
  }

  fetchProvisional() async {
    loading = true;

    DocumentSnapshot<Map<String, dynamic>> collectionSnapshot =
        await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_PROVISIONAL_ECONOMIC_ACCOUNT)
            .doc(widget.firebaseId)
            .get();

    tempEconomic = ProjectEconomic.empty();

    if (collectionSnapshot.exists) {
      tempEconomic = ProjectEconomic.fromDocument(
          collectionSnapshot.data()!, collectionSnapshot.id);
    }

    existingPEA = tempEconomic!.toMap();

    try {
      int counter = 0;
      costBreakdown.keys.forEach((key) {
        if (costBreakdown.containsKey(key)) {
          costBreakdown[key]!.forEach((category, items) {
            totals.add({category: 0});

            for (var i = 0; i < items.length; i++) {
              String title = costBreakdown[key]![category]![i][0];

              isActive.add(false);
              contProvisionalPrice.add(new TextEditingController());
              contProvisionalPrice[counter].text = '';

              if (existingPEA.length > 0 &&
                  existingPEA[category] != null &&
                  existingPEA[category].length > 0 &&
                  existingPEA[category][title].runtimeType.toString() !=
                      'Null') {
                if (existingPEA[category][title] != null ||
                    existingPEA[category][title] != '') {
                  
                  contProvisionalPrice[counter].text = localCurrencyFormatMain.format( double.tryParse(existingPEA[category][title]));

                  if (double.tryParse(existingPEA[category][title])! > 0) {
                    isActive[counter] = true;
                  }
                }
              }

              costBreakdown[key]![category]![i][1] =
                  contProvisionalPrice[counter];
              costBreakdown[key]![category]![i][2] = isActive[counter];
              costBreakdown[key]![category]![i].add(counter);
              costBreakdown[key]![category]![i].add(0);

              counter++;
            }
          });
        }
      });

      setInitialValues();
    } catch (e) {
      setState(() {
        loading = false;
      });
    }
  }

  @protected
  void didUpdateWidget(ProvisionalEconomicAccount oldWidget) {
    super.didUpdateWidget(oldWidget);

    setInitialValues();
  }

  setInitialValues() {
    cost = 0;

    costBreakdown.keys.forEach((key) {
      if (costBreakdown.containsKey(key)) {
        costBreakdown[key]!.forEach((category, items) {
          double amount = 0;
          for (var i = 0; i < items.length; i++) {
            int itemIndex = costBreakdown[key]![category]![i][3];
            
            amount += contProvisionalPrice[itemIndex].text == ''
                ? 0
                : double.tryParse(contProvisionalPrice[itemIndex]
                    .text
                    .replaceAll('.', '')
                    .replaceAll(',', '.'))!;

            
          }

          int totalIndex =
              totals.indexWhere((map) => map.containsKey(category));
          totals[totalIndex][category] = amount;
          cost += amount;
        });
      }
    });

    if (costBreakdown['Entrate']!['revenueExpected']!.length > 0) {
      revenue = double.tryParse(
          costBreakdown['Entrate']!['revenueExpected']![0][1].text == ''
              ? '0'
              : costBreakdown['Entrate']!['revenueExpected']![0][1]
                  .text
                  .replaceAll('.', '')
                  .replaceAll(',', '.'))!;

          
    } else {
      revenue = 0;
    }

    cost -= revenue;

    setState(() {
      loading = false;
    });
  }

  void setCostAndRevenue() {
    double _cost = 0;
    double _revenue = 0;
    costBreakdown.forEach((category, subcategories) {
      subcategories.forEach((subcategory, items) {
        for (var item in items) {
          // Summing amounts for "Uscite" and "Entrate"
          if (category == "Uscite") {
            _cost += (item[1] is num) ? item[1] : 0;
          } else if (category == "Entrate") {
            _revenue += (item[1] is num) ? item[1] : 0;
          }
        }
      });
    });

    setState(() {
      cost = _cost;
      revenue = _revenue;
    });
  }

  Color getCostBoxColor(double amount) {
    double spendPercent = (cost / revenue) * 100;
    double roi = ((revenue - cost) / cost) * 100;

    if( roi.isInfinite ) roi = 0;

    if (roi > 16) {
      return Color(0xff4E9A7A);
    } else if (roi > 10 && roi < 16) {
      return Color(0xffFFC702);
    } else {
      return Color(0xffE82525);
    }
  }

  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: loading ?
      Center(
        child: CircularProgressIndicator(
          color: Theme.of(context).primaryColor,
        ),
      )
          :
      LayoutBuilder(
          builder: (BuildContext context, BoxConstraints constraints) {
            String address = "";
            if (tempEconomic != null){
              address = tempEconomic!.address!;
              if (tempEconomic!.addressInfo != null) {
                address = "${tempEconomic!.addressInfo!.toShortAddress()}";
              }
            }
        return SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.max,
            children: [
              Row(
                children: [
                  IconButton(
                    hoverColor: Colors.transparent,
                    focusColor: Colors.transparent,
                    onPressed: () {
                      context.pop();
                    },
                    icon: SvgPicture.asset('assets/icons/arrow_left.svg',
                        height: 20, color: Colors.black),
                  ),
                  SizedBox(
                    width: 10,
                  ),
                  NarFormLabelWidget(
                    label: loading == true ? '' : address,
                    fontSize: 22,
                    fontWeight: 'bold',
                    textColor: Colors.black,
                  ),
                  SizedBox(
                    height: 10,
                  ),
                ],
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(
                        width: 1, color: Color.fromRGBO(219, 219, 219, 1))),
                child: ListView(
                  shrinkWrap: true,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        NarFormLabelWidget(
                          label: 'Conto economico previsionale',
                          fontSize: 20,
                          fontWeight: 'bold',
                        ),
                        BaseNewarcButton(
                          onPressed: () {
                            List<List<dynamic>> rows = [];

                            // Adding headers to the CSV
                            rows.add([
                              "Category",
                              "Subcategory",
                              "Description",
                              "Value"
                            ]);

                            Map tmpEconomic = tempEconomic!.toMap();

                            // Iterating through the map to generate rows
                            costBreakdown.forEach((category, subcategories) {
                              subcategories.forEach((subcategory, items) {
                                if (tmpEconomic[subcategory].length > 0) {
                                  for (var item in items) {
                                    List rowData = [
                                      convertFromCamelCase(item[0]),
                                      double.tryParse(item[1].text),
                                    ];

                                    rows.add([
                                      category,
                                      convertFromCamelCase(subcategory),
                                      ...rowData
                                    ]);
                                  }
                                }
                              });
                            });

                            // Converting rows to CSV string
                            String csv = const ListToCsvConverter().convert(rows);

                            final bytes = utf8.encode(csv);

                            final uint8 = Uint8List.fromList(bytes).toJS;

                            final blob = web.Blob(
                              [uint8].toJS,
                              web.BlobPropertyBag(type: 'text/csv'),
                            );

                            // Create Object URL
                            final url = web.URL.createObjectURL(blob);

                            // Create and click anchor
                            web.HTMLAnchorElement()
                              ..href = url
                              ..download = "cost_breakdown.csv"
                              ..click();

                            // Clean up
                            web.URL.revokeObjectURL(url);
                          },
                          buttonText: 'Scarica csv',
                          notAccent: true,
                        )
                      ],
                    ),
                    SizedBox(height: 30),
                    Container(
                      child: ListView(
                        //should be a column
                        shrinkWrap: true,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: EdgeInsets.symmetric(
                                    vertical: 7, horizontal: 15),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                    width: 1,
                                    color: Color.fromRGBO(214, 214, 214, 1),
                                  ),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    NarFormLabelWidget(
                                        label: 'Costi',
                                        fontSize: 18,
                                        fontWeight: '600',
                                        textColor: Colors.black),
                                    SizedBox(
                                      height: 15,
                                    ),
                                    Container(
                                      width: 170,
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        mainAxisSize: MainAxisSize.max,
                                        children: [
                                          NarFormLabelWidget(
                                              label: localCurrencyFormatMain
                                                  .format(cost),
                                              fontSize: 25,
                                              fontWeight: 'bold',
                                              textColor: Colors.black),
                                          SizedBox(
                                            width: 20,
                                          ),
                                          NarFormLabelWidget(
                                              label: '€',
                                              fontSize: 17,
                                              fontWeight: 'bold',
                                              textColor: Colors.black),
                                        ],
                                      ),
                                    )
                                  ],
                                ),
                              ),
                              SizedBox(
                                width: 25,
                              ),
                              Container(
                                padding: EdgeInsets.symmetric(
                                    vertical: 7, horizontal: 15),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                    width: 1,
                                    color: Color.fromRGBO(214, 214, 214, 1),
                                  ),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    NarFormLabelWidget(
                                      label: 'Ricavi',
                                      fontSize: 18,
                                      fontWeight: '600',
                                      textColor: Colors.black,
                                    ),
                                    SizedBox(
                                      height: 15,
                                    ),
                                    Container(
                                      width: 170,
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        mainAxisSize: MainAxisSize.max,
                                        children: [
                                          NarFormLabelWidget(
                                            label: localCurrencyFormatMain
                                                .format(revenue),
                                            fontSize: 30,
                                            fontWeight: 'bold',
                                            textColor: Colors.black,
                                          ),
                                          SizedBox(
                                            width: 20,
                                          ),
                                          NarFormLabelWidget(
                                            label: '€',
                                            fontSize: 17,
                                            fontWeight: 'bold',
                                            textColor: Colors.black,
                                          ),
                                        ],
                                      ),
                                    )
                                  ],
                                ),
                              ),
                              SizedBox(
                                width: 25,
                              ),
                              Container(
                                padding: EdgeInsets.symmetric(
                                    vertical: 7, horizontal: 15),
                                constraints: BoxConstraints(minWidth: 200),
                                decoration: BoxDecoration(
                                  color: getCostBoxColor(revenue - cost),
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                    width: 1,
                                    color: getCostBoxColor(revenue - cost),
                                  ),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    NarFormLabelWidget(
                                      label: 'Guadagno',
                                      fontSize: 18,
                                      fontWeight: '600',
                                      textColor: Colors.white,
                                    ),
                                    SizedBox(
                                      height: 15,
                                    ),
                                    Container(
                                      width: 170,
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        mainAxisSize: MainAxisSize.max,
                                        children: [
                                          NarFormLabelWidget(
                                            label: localCurrencyFormatMain
                                                .format(revenue - cost),
                                            fontSize: 30,
                                            fontWeight: 'bold',
                                            textColor: Colors.white,
                                          ),
                                          SizedBox(
                                            width: 20,
                                          ),
                                          NarFormLabelWidget(
                                            label: '€',
                                            fontSize: 17,
                                            fontWeight: 'bold',
                                            textColor: Colors.white,
                                          ),
                                        ],
                                      ),
                                    )
                                  ],
                                ),
                              ),
                              SizedBox(
                                width: 25,
                              ),
                              Container(
                                padding: EdgeInsets.symmetric(
                                    vertical: 7, horizontal: 15),
                                constraints: BoxConstraints(minWidth: 200),
                                decoration: BoxDecoration(
                                  color: getCostBoxColor(revenue - cost),
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                    width: 1,
                                    color: getCostBoxColor(revenue - cost),
                                  ),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    NarFormLabelWidget(
                                      label: 'ROI',
                                      fontSize: 18,
                                      fontWeight: '600',
                                      textColor: Colors.white,
                                    ),
                                    SizedBox(
                                      height: 15,
                                    ),
                                    Container(
                                      width: 170,
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        mainAxisSize: MainAxisSize.max,
                                        children: [
                                        
                                          Container(
                                            child: NarFormLabelWidget(
                                              label: loading == true
                                                  ? '0'
                                                  : (((revenue - cost) /
                                                              cost) *
                                                          100).isInfinite 
                                                          ? '0' 
                                                          : localCurrencyFormatMain
                                                      .format(((revenue - cost) /
                                                              cost) *
                                                          100),
                                              fontSize: 30,
                                              fontWeight: 'bold',
                                              textColor: Colors.white,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                          SizedBox(
                                            width: 20,
                                          ),
                                          NarFormLabelWidget(
                                            label: '%',
                                            fontSize: 17,
                                            fontWeight: 'bold',
                                            textColor: Colors.white,
                                          ),
                                        ],
                                      ),
                                    )
                                  ],
                                ),
                              )
                            ],
                          ),
                          Form(
                            key: _formKey,
                            child: Column(
                              children: costBreakdown.keys.map((category) {
                                return Column(
                                  children: [
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        SizedBox(height: 50),
                                        NarFormLabelWidget(
                                          label: category.toUpperCase(),
                                          fontSize: 18,
                                        ),
                                        SizedBox(height: 10),
                                      ],
                                    ),
                                    loading == false
                                        ? Column(
                                            children: costBreakdown[category]!
                                                .keys
                                                .map((subCategory) {
                                            return processWrapper(
                                                context,
                                                category,
                                                subCategory,
                                                costBreakdown[category]![
                                                    subCategory]!,
                                                isEntrata: true);
                                          }).toList())
                                        : Center(
                                            child: NarFormLabelWidget(
                                                label: 'Loading...')),
                                  ],
                                );
                              }).toList(),
                            ),
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              NarFormLabelWidget(label: progressMessage),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  BaseNewarcButton(
                                    buttonText: "Salva",
                                    onPressed: () async {
                                      if (!_formKey.currentState!.validate()) {
                                        progressMessage =
                                            'Please check all values';
                                        return;
                                      }
                                      setState(() {
                                        progressMessage =
                                            'Salvataggio in corso...';
                                      });

                                      Map<String, dynamic> valuesTotal = {};
                                      costBreakdown.forEach((key, value) {
                                        value.forEach((subKey, subValue) {
                                          Map<String, String> subMap = {};
                                          for (var item in subValue) {
                                            subMap[item[0]] = item[1].text == ''
                                                ? '0'
                                                : item[1]
                                                    .text
                                                    .replaceAll('.', '')
                                                    .replaceAll(',', '.');
                                          }
                                          valuesTotal[subKey] = subMap;
                                        });
                                      });

                                      ProjectEconomic _projectEconomic =
                                          ProjectEconomic(valuesTotal);

                                      _projectEconomic.address =
                                          tempEconomic!.address;
                                      _projectEconomic.city =
                                          tempEconomic!.city;
                                      _projectEconomic.addressInfo =
                                          tempEconomic!.addressInfo;
                                      _projectEconomic.isArchived =
                                          tempEconomic!.isArchived;
                                      _projectEconomic.isAssigned =
                                          tempEconomic!.isAssigned;
                                      _projectEconomic.created =
                                          tempEconomic!.created;
                                      _projectEconomic.creatorUserId =
                                          tempEconomic!.creatorUserId;

                                      final FirebaseFirestore _db =
                                          FirebaseFirestore.instance;

                                      await _db
                                          .collection(appConfig
                                              .COLLECT_PROVISIONAL_ECONOMIC_ACCOUNT)
                                          .doc(widget.firebaseId)
                                          .update(_projectEconomic.toMap());

                                      setState(() {
                                        progressMessage =
                                            'Salvataggio avvenuto con successo!';
                                      });
                                    },
                                  )
                                ],
                              ),
                            ],
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  Widget processWrapper(BuildContext context, String category,
      String subCategory, List<List> paymentList,
      {bool isEntrata = false}) {
    // contStatus.text =
    double initialValue = 0;

    int totalIndex = totals.indexWhere((map) => map.containsKey(subCategory));

    int _counter = -1;
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
              color: Color(0xfff9f9f9),
              borderRadius: BorderRadius.circular(14),
              border: Border.all(color: Color(0xffe7e7e7))),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.only(
                    top: 10, bottom: 20, right: 20, left: 20),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                            flex: 2,
                            child: NarFormLabelWidget(
                              label: convertFromCamelCase(
                                  breakdownLabels[subCategory]),
                              fontSize: 18,
                            )),
                        // Expanded(child: SizedBox(height: 0,)),
                        Row(
                          children: [
                            getTotalWidget(
                                'Tot. Previs.',
                                totalIndex > -1
                                    ? totals[totalIndex][subCategory]
                                    : 0)
                          ],
                        ),
                        // SizedBox(width:30)
                      ],
                    ),
                    SizedBox(height: 10),
                    Container(
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(color: Color(0xffe7e7e7))),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10.0, vertical: 20),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                    flex: 1,
                                    child: NarFormLabelWidget(label: "")),
                                Expanded(
                                    flex: 2,
                                    child: NarFormLabelWidget(
                                        label: isEntrata
                                            ? "Tipo di entrata"
                                            : "Tipo di costo")),
                                Expanded(child: SizedBox(height: 0)),
                                Expanded(
                                    child: NarFormLabelWidget(
                                        label: "Costo previsionale")),
                                SizedBox(width: 25)
                              ],
                            ),
                            Column(
                              children: paymentList.map((payment) {
                                _counter++;

                                int itemIndex = costBreakdown[category]![
                                    subCategory]![_counter][3];
                                return Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      flex: 1,
                                      child: Padding(
                                        // padding: const EdgeInsets.only( top: 6.0, bottom: 18, left: 0),
                                        padding: const EdgeInsets.all(0),
                                        child: Switch(
                                          // This bool value toggles the switch.
                                          value: isActive[itemIndex],
                                          activeThumbColor:
                                              Theme.of(context).primaryColor,
                                          onChanged: (bool value) async {
                                            setState(() {
                                              isActive[itemIndex] = value;

                                              if (value == false) {
                                                contProvisionalPrice[itemIndex]
                                                    .text = '';
                                                setInitialValues();
                                              }
                                            });
                                          },
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                        flex: 2,
                                        child: NarFormLabelWidget(
                                          label:
                                              convertFromCamelCase(payment[0]),
                                          textColor: Color(0xff454545),
                                          fontWeight: '500',
                                        )),
                                    Expanded(child: SizedBox(height: 0)),
                                    CustomTextFormField(
                                      // key: Key(payment[0]),
                                      isMoney: true,
                                      controller: contProvisionalPrice[itemIndex],
                                      label: "",
                                      hintText: "",
                                      // initialValue: payment[1].toString(),
                                      suffixIcon: Column(
                                        mainAxisSize: MainAxisSize.max,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              NarFormLabelWidget(
                                                label: '€',
                                                fontSize: 14,
                                                textColor: Colors.black,
                                              ),
                                              SizedBox(
                                                width: 5,
                                              )
                                            ],
                                          ),
                                        ],
                                      ),
                                      //controller: revenueController,
                                      enabled: isActive[itemIndex],
                                      onChangedCallback:
                                          (String newValue) async {
                                        if (newValue == '') {
                                          costBreakdown[category]![
                                              subCategory]![_counter][4] = 0;
                                        } else {
                                          costBreakdown[category]![
                                                  subCategory]![_counter][4] =
                                              double.tryParse(newValue);
                                        }
                                        setInitialValues();
                                        setState(() {});
                                      },
                                      validator: (value) {
                                        if (value == '') return null;
                                        value = value
                                            .replaceAll('.', '')
                                            .replaceAll(',', '.');
                                        if (!isNumber(value) ||
                                            double.tryParse(value)! < 0) {
                                          return 'Invalid value!';
                                        }

                                        return null;
                                      },
                                    ),
                                    SizedBox(width: 25)
                                  ],
                                );
                              }).toList(),
                            )
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 8)
      ],
    );
  }

  Widget getTotalWidget(String title, double amount, {bool? checkColor}) {
    Color _spendColor = Colors.red;
    return Container(
      margin: EdgeInsets.only(right: 30),
      width: 200,
      height: 50,
      padding: EdgeInsets.symmetric(vertical: 7, horizontal: 15),
      decoration: BoxDecoration(
        color: checkColor != null ? _spendColor : Color(0xffEAEAEA),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          NarFormLabelWidget(
            label: title.toUpperCase(),
            fontSize: 9,
            fontWeight: 'bold',
            textColor: checkColor != null ? Colors.white : Colors.black,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.max,
            children: [
              SizedBox(width: 8),
              NarFormLabelWidget(
                label: localCurrencyFormatMain.format(amount),
                fontSize: 15,
                fontWeight: 'bold',
                textColor: checkColor != null ? Colors.white : Colors.black,
              ),
              SizedBox(
                width: 10,
              ),
              NarFormLabelWidget(
                label: '€',
                fontSize: 15,
                fontWeight: 'bold',
                textColor: checkColor != null ? Colors.white : Colors.black,
              ),
            ],
          )
        ],
      ),
    );
  }
}
