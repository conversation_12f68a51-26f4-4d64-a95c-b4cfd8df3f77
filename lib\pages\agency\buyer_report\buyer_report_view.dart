import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/reportAcquirente.dart';
import 'package:newarc_platform/pages/agency/buyer_report/buyer_report_controller.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/UI/filter.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/newarc_data_table.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import '../../../routes/agency_routes.dart';
import '../../../utils/buyerReportPDF.dart';
import '../../../widget/UI/base_newarc_button.dart';
import '../../../widget/UI/base_newarc_popup.dart';
import '../../../widget/UI/buyer_report_data_insertion_popup.dart';
import 'buyer_report_request_source.dart';

class BuyerReportView extends StatefulWidget {
  BuyerReportView({
    super.key,
    required this.agencyUser,
  });
  final AgencyUser agencyUser;

  @override
  State<BuyerReportView> createState() => _BuyerReportViewState();
}

class _BuyerReportViewState extends State<BuyerReportView> {
  final controller = Get.put<BuyerReportController>(BuyerReportController());

  @override
  void initState() {
    super.initState();
    controller.clearFilter();
    initialFetch(force: true,reloadAll: true);
  }


  void reloadAfterPop({bool force = false}) {
    controller.reports = [];
    controller.documentList = [];
    controller.filters = [];
    controller.cacheFireStore = [];
    controller.totalRecords = 0;
    controller.currentlyShowing = '';
    controller.recordsPerPage = 20;
    controller.pageCounter = 1;
    controller.totalPages = 0;
    controller.disablePreviousButton = true;
    controller.disableNextButton = false;
    controller.statusFilterController.clear();
    controller.cacheFireStore.clear();
    initialFetch(force: force);
  }

  Widget dataTablePagination() {
    return Visibility(
      visible: controller.currentlyShowing.isNotEmpty,
      child: Padding(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: IconTheme.merge(
            data: const IconThemeData(opacity: 0.54),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "${controller.currentlyShowing} di  ${controller.totalRecords.toString()}",
                  style: TextStyle(
                    fontFamily: '',
                    fontSize: 12.0,
                    color: Colors.black.withOpacity(0.54),
                  ),
                ),
                SizedBox(width: 32.0),
                IconButton(
                  icon: const Icon(Icons.chevron_left),
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    if (controller.disablePreviousButton == true) return;
                    if (controller.loadingProperties == true) return;
                    fetchPrevProperties();
                  },
                ),
                SizedBox(width: 24.0),
                IconButton(
                  padding: EdgeInsets.zero,
                  icon: const Icon(Icons.chevron_right),
                  onPressed: () {
                    if (controller.disableNextButton == true) return;
                    if (controller.loadingProperties == true) return;

                    fetchNextProperties();
                  },
                ),
                SizedBox(width: 14.0),
              ],
            ),
          )),
    );
  }


  Future<void> initialFetch({bool force = false,bool reloadAll = false}) async {
    if (controller.reports.isNotEmpty && !force && !reloadAll) return;

    controller.pageCounter = 1;

    setState(() {
      controller.loadingProperties = true;
      controller.reports = [];
    });

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshotCounter;
      Query<Map<String, dynamic>> collectionSnapshotCounterQuery;

      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      List<ReportAcquirente> _allReports = [];

      collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_REPORT_ACQUIRENTE);
      collectionSnapshotCounterQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_REPORT_ACQUIRENTE);

      if (controller.filters.length > 0) {
        for (var i = 0; i < controller.filters.length; i++) {
          if (controller.filters[i]['search'] == 'equal') {
            collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
            collectionSnapshotCounterQuery = collectionSnapshotCounterQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }
      }

        if(reloadAll){
          collectionSnapshot = await collectionSnapshotQuery
              .where("agencyId",isEqualTo: widget.agencyUser.agencyId)
              .orderBy('insertionTimestamp', descending: true)
              .get();
          _allReports = await collectionSnapshot.docs.map((doc) =>
              ReportAcquirente.fromDocument(doc.data(), doc.id)).toList();
        }else{
          collectionSnapshot = await collectionSnapshotQuery
              .where("agencyId",isEqualTo: widget.agencyUser.agencyId)
              .orderBy('insertionTimestamp', descending: true)
              .limit(controller.recordsPerPage)
              .get();
        }

        collectionSnapshotCounter = await collectionSnapshotCounterQuery
            .where("agencyId",isEqualTo: widget.agencyUser.agencyId)
            .orderBy('insertionTimestamp', descending: true)
            .get();


      controller.totalRecords = collectionSnapshotCounter.docs.length;

      controller.totalPages = (controller.totalRecords / controller.recordsPerPage).ceil();

      if (controller.totalRecords > controller.recordsPerPage) {
        controller.currentlyShowing = '1-' + controller.recordsPerPage.toString();
      } else {
        controller.currentlyShowing = '1-' + controller.recordsPerPage.toString();
      }
      controller.documentList = collectionSnapshot.docs;

      await generateDataRows(collectionSnapshot);
      if (mounted)
        setState(() {
          controller.loadingProperties = false;
          if(reloadAll) controller.allReports = _allReports;
        });
    } catch (e,s) {
      print("initialFetch Buyer Report Error ==> ${e}");
      print("initialFetch Buyer Report Trace ==> ${s}");
      if (mounted)
        setState(() {
          controller.loadingProperties = false;
          print(e);
        });
    }
  }

  fetchNextProperties() async {
    setState(() {
      controller.loadingProperties = true;
    });

    controller.pageCounter++;

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;
      int indexOfSnapshot = isRecordExists(controller.pageCounter);

      if (indexOfSnapshot > -1) {
        collectionSnapshot = controller.cacheFireStore[indexOfSnapshot]['snapshot'];
      } else {
        collectionSnapshotQuery = await FirebaseFirestore.instance.collection(appConfig.COLLECT_REPORT_ACQUIRENTE);

        if (controller.filters.length > 0) {
          for (var i = 0; i < controller.filters.length; i++) {
            collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }
          collectionSnapshot = await collectionSnapshotQuery
              .where("agencyId",isEqualTo: widget.agencyUser.agencyId)
              .orderBy('insertionTimestamp', descending: true)
              .startAfterDocument(controller.documentList[controller.documentList.length - 1])
              .limit(controller.recordsPerPage)
              .get();

      }

      controller.documentList = collectionSnapshot.docs;
      generateDataRows(collectionSnapshot);
    } catch (e) {
      setState(() {
        print(e);
        controller.loadingProperties = false;
      });
    }
  }

  fetchPrevProperties() async {
    setState(() {
      controller.loadingProperties = true;
    });

    controller.pageCounter--;

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      int indexOfSnapshot = isRecordExists(controller.pageCounter);
      if (indexOfSnapshot > -1) {
        collectionSnapshot = controller.cacheFireStore[indexOfSnapshot]['snapshot'];
      } else {
        collectionSnapshotQuery = await FirebaseFirestore.instance.collection(appConfig.COLLECT_REPORT_ACQUIRENTE);
        if (controller.filters.length > 0) {
          for (var i = 0; i < controller.filters.length; i++) {
            collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }

          collectionSnapshot = await collectionSnapshotQuery
              .where("agencyId",isEqualTo: widget.agencyUser.agencyId)
              .orderBy('insertionTimestamp', descending: true)
              .endBeforeDocument(controller.documentList[controller.documentList.length - 1])
              .limit(controller.recordsPerPage)
              .get();

      }

      controller.documentList = collectionSnapshot.docs;

      generateDataRows(collectionSnapshot);
    } catch (e) {
      setState(() {
        controller.loadingProperties = false;
      });
    }
  }

  int isRecordExists(int pageCounter) {
    String filtersKey = controller.filters.map((f) => '${f['field']}:${f['value']}').join('|');
    return controller.cacheFireStore.indexWhere(
      (record) => record['key'] == pageCounter && record['filtersKey'] == filtersKey,
    );
  }

  generateDataRows(QuerySnapshot<Map<String, dynamic>> collectionSnapshot) {
    if (isRecordExists(controller.pageCounter) < 0) {
      String filtersKey = controller.filters.map((f) => '${f['field']}:${f['value']}').join('|');
      controller.cacheFireStore.add({
        'key': controller.pageCounter,
        'snapshot': collectionSnapshot,
        'filtersKey': filtersKey,
      });
    }
    if (controller.pageCounter == 1) {
      controller.disablePreviousButton = true;
    } else {
      controller.disablePreviousButton = false;
    }

    if (controller.pageCounter == controller.totalPages) {
      controller.disableNextButton = true;
    } else {
      controller.disableNextButton = false;
    }

    List<ReportAcquirente> _projects = [];


    for (var element in collectionSnapshot.docs) {
      ReportAcquirente _tmp = ReportAcquirente.fromDocument(element.data(), element.id);
      _projects.add(_tmp);
    }

    int lastRecordNumber = controller.pageCounter * controller.recordsPerPage;
    if (_projects.length == controller.recordsPerPage) {
      controller.currentlyShowing = (lastRecordNumber - (controller.recordsPerPage - 1)).toString() + '-' + lastRecordNumber.toString();
    } else if (_projects.length > 0 && _projects.length < controller.recordsPerPage) {
      int prevLastRecordNumber = (controller.pageCounter - 1) * controller.recordsPerPage;

      controller.currentlyShowing = (lastRecordNumber - (controller.recordsPerPage - 1)).toString() + '-' + (prevLastRecordNumber + _projects.length).toString();
    }
      if (mounted)
       setState(() {
         controller.reports = _projects;
         controller.loadingProperties = false;
       });
  }

  @override
  Widget build(BuildContext context) {
    double pageWidth = MediaQuery.of(context).size.width - 260;
    if (pageWidth < 1800) {
      pageWidth = 1800;
    }
    return LayoutBuilder(
      builder: (context, constraints) {
        return Column(
          children: [
            Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                _headerTitle(),
                Expanded(
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      MouseRegion(
                        cursor: SystemMouseCursors.click,
                        child: GestureDetector(
                          onTap: () async {
                            try{
                              DocumentSnapshot<Map<String, dynamic>> docSnap = await FirebaseFirestore.instance
                                  .collection(appConfig.COLLECT_AGENCIES)
                                  .doc(widget.agencyUser.agencyId).get();
                              if(docSnap.exists){
                                Agency agency = Agency.fromDocument(docSnap.data()!, docSnap.id);
                                if(agency.reportSubscriptionId?.isNotEmpty ?? false){
                                  await showDialog(
                                    context: context,
                                    barrierDismissible: false,
                                    builder: (BuildContext context) {
                                      return Center(
                                        child: BuyerReportDataInsertionPopup(
                                          isFromEdit: false,
                                          procedureStep: 'initial',
                                          agencyUser: widget.agencyUser,
                                          onClose: (String projectId) {
                                            context.go(AgencyRoutes.agencyReportAcquirenteId(projectId));
                                          },
                                        ),
                                      );
                                    },
                                  );
                                }else{
                                  _subscriptionExpiredDialog();
                                }
                              }
                            }catch(e){
                              log("Error while fetching Agency ===> ${e.toString()}");
                            }
                          },
                          child: Container(
                            height: 32,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 20.0),
                              child: NarFormLabelWidget(
                                label: 'Nuovo report',
                                fontSize: 13,
                                fontWeight: '600',
                                textColor: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ),
            _filter(),
            SizedBox(height: 10),
            Container(
              height: constraints.maxHeight / 1.2,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: AppColor.white,
                border: Border.all(width: 1.5, color: AppColor.borderColor),
              ),
              child: Column(
                children: [
                  //_tabBar(),
                  Expanded(
                    child: Stack(
                      children: [
                        Opacity(
                            opacity: 1,
                            child: NewarcDataTable(
                                    rowsPerPage: 20,
                                    isHasDecoration: false,
                                    hidePaginator: true,
                                    onPageChanged: (val) {
                                      print("page : $val");
                                    },
                                    source: BuyerReportRequestSource(
                                      projects: controller.reports,
                                      onOpenAzioniDialog: (project,type)async{
                                        if(type == "edit"){
                                          // widget.projectArguments!.clear();
                                          // widget.projectArguments!.addAll({
                                          //   'projectFirebaseId': project.id,
                                          //   'property': null,
                                          //   'agencyUser': widget.agencyUser,
                                          //   'updateViewCallback': widget.updateViewCallback,
                                          //   'initialFetchProperties': initialFetch(force: true),
                                          // });
                                          // widget.updateViewCallback!('report-acquirente-inside', projectArguments: widget.projectArguments);
                                          context.go(AgencyRoutes.agencyReportAcquirenteId(project.id!));
                                        }else if(type == "delete"){
                                          Future.delayed(Duration(milliseconds: 200),(){
                                            showDeleteConfirmDialog(context: context, id: project.id!);
                                          });
                                        }else if(type == "download_pdf"){
                                          final ValueNotifier<double> progress = ValueNotifier(0);
                                          _showDownloadingDialog(context,progress: progress);
                                          try{
                                            await downloadBuyerReportPDF(reportAcquirente: project, progress: progress);
                                          }catch(e){
                                            log("Error while generating seller report PDF ===> ${e.toString()}");
                                          }finally{
                                            Navigator.of(context, rootNavigator: true).pop();
                                          }
                                        }
                                      },
                                    ),
                                    columns: [
                                      DataColumn2(label: Text("Indirizzo")),
                                      DataColumn2(label: Text("Creazione")),
                                      DataColumn2(label: Text("Azioni"),fixedWidth: 150),
                                    ],
                                  )),
                        if (controller.loadingProperties)
                          Positioned.fill(
                            child: Center(
                              child: CircularProgressIndicator(
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                  Align(
                    alignment: Alignment.centerRight,
                    child: dataTablePagination(),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  void showDeleteConfirmDialog({required BuildContext context,required String id}) async {
    return showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context,setStateDialog){
            return Center(
              child: BaseNewarcPopup(
                  key: ValueKey("Attenzione"),
                  buttonColor: Theme.of(context).primaryColor,
                  title: "Attenzione!",
                  buttonText: "Elimina",
                  onPressed: () async {
                    try{
                      //*------delete report
                      await FirebaseFirestore.instance
                          .collection(appConfig.COLLECT_REPORT_ACQUIRENTE)
                          .doc(id)
                          .delete();
                      initialFetch(force: true,reloadAll: true);
                    }catch(e,s){
                      log("---------- ERROR While Deleting Buy Report ------> ${e.toString()}");
                      log("---------- STACKTRACE While Deleting Buy Report ------> ${s.toString()}");
                    }
                  },
                  column: Container(
                    width: 400,
                    padding: EdgeInsets.symmetric(vertical: 25),
                    child: Center(
                      child: NarFormLabelWidget(
                        label:  "Vuoi davvero eliminare questo rapporto?" ,
                        textColor: Color(0xff696969),
                        fontSize: 18,
                        fontWeight: '600',
                      ),
                    ),
                  )),
            );
          });
        });
  }

  _subscriptionExpiredDialog() {
    return showDialog(
      context: context,
      builder: (context) {
        return Center(
          child: BaseNewarcPopup(
            noButton: true,
            title: "",
            column: Container(
              width: 400,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(height: 20),
                  Image.asset(
                    height: 27,
                    width: 212,
                    "assets/newarc_report_logo.png",
                  ),
                  SizedBox(height: 35),
                  NarFormLabelWidget(
                    label:
                    'Non hai ancora attivato un\nabbonamento a Newarc Reports',
                    fontSize: 20,
                    textAlign: TextAlign.center,
                    fontWeight: '600',
                    textColor: AppColor.black,
                  ),
                  SizedBox(height: 35),
                  BaseNewarcButton(
                    width: 142,
                    textColor: AppColor.white,
                    color: Theme.of(context).primaryColor,
                    buttonText: "Attivalo ora",
                    onPressed: () async {
                      // widget.projectArguments!.clear();
                      // widget.projectArguments!.addAll({
                      //   'agencyUser': widget.agencyUser.agency,
                      // });
                      // widget.updateViewCallback!('abbonamento', projectArguments: widget.projectArguments);
                      context.go(AgencyRoutes.agencyAbbonamento);
                      Navigator.pop(context);
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }


  NarFilter _filter() {
    return NarFilter(
      showSearchInput: true,
      searchHintText: "Cerca per indirizzo o per codice...",
      searchTextEditingControllers: controller.searchTextController,
      onChanged: (String? searchQuery)async{
        if(searchQuery?.isNotEmpty ?? false){
          if (searchQuery != null && searchQuery.isNotEmpty) {
            List<ReportAcquirente> filtered = controller.reports.where((project) {
              final address = project.addressInfo;
              final city = address?.city?.toLowerCase() ?? "";
              final streetName = address?.streetName?.toLowerCase() ?? '';
              final fullAddress = address?.fullAddress?.toLowerCase() ?? "";
              return city.contains(searchQuery.toLowerCase()) ||
                  streetName.contains(searchQuery.toLowerCase()) ||
                  fullAddress.contains(searchQuery.toLowerCase());
            }).toList();

            setState(() {
              controller.reports = filtered;
            });
          }
        }else{
          await initialFetch(force: true);
        }
      },
      suffixIconOnTap: ()async{
        await initialFetch(force: true,reloadAll: true);
        if(controller.searchTextController.text.trim().isNotEmpty){
          List<ReportAcquirente> filtered = controller.reports.where((project) {
            final address = project.addressInfo;
            final city = address?.city?.toLowerCase()  ?? "";
            final streetName = address?.streetName?.toLowerCase() ?? '';
            final fullAddress = address?.fullAddress?.toLowerCase()  ?? "";
            return city.contains(controller.searchTextController.text.toLowerCase()) ||
                streetName.contains(controller.searchTextController.text.toLowerCase()) ||
                fullAddress.contains(controller.searchTextController.text.toLowerCase());
          }).toList();

          setState(() {
            controller.reports = filtered;
          });
        }else{
          await initialFetch(force: true);
        }
      },
      selectedFilters: [
        controller.statusSelectedFilter
      ],
      textEditingControllers: [controller.statusFilterController],
      filterFields: [
        {
          'Città': NarSelectBoxWidget(
            options: controller.allReports.map((e) => e.addressInfo?.city?.trim() ?? '')
                .where((city) => city.isNotEmpty)
                .toSet()
                .toList(),
            onChanged: (value) {
              controller.filters = [
                {
                  'field': 'addressInfo.city',
                  'value': controller.statusFilterController.text,
                  'search': 'equal',
                }
              ];
              setState(() {
                controller.statusSelectedFilter = controller.statusFilterController.text;
              });
            },
            controller: controller.statusFilterController,
          ),
        },
      ],
      onSubmit: () async {
        await initialFetch(force: true);
      },
      onReset: () async {
        controller.clearFilter();
        await initialFetch(force: true);
      },
    );
  }

  NarFormLabelWidget _headerTitle() {
    return NarFormLabelWidget(
      label:  "Report Acquirente",
      fontSize: 19,
      fontWeight: '700',
      textColor: Colors.black,
    );
  }

  void _showDownloadingDialog(BuildContext context,{ValueNotifier<double>? progress}) {
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black54,
      builder: (_) {
        return Scaffold(
          backgroundColor: Colors.transparent,
          body: Center(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              decoration: BoxDecoration(
                //color: Colors.black87,
                borderRadius: BorderRadius.circular(30),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  CircularProgressIndicator(color: AppColor.white,),
                  SizedBox(height: 10,),
                  NarFormLabelWidget(
                    textAlign: TextAlign.center,
                    label: "Generazione in corso…",
                    fontSize: 18,
                    fontWeight: '700',
                    textColor: AppColor.white,
                  ),
                  SizedBox(height: 10,),
                  ValueListenableBuilder<double>(
                    valueListenable: progress!,
                    builder: (context, value, child) {
                      return Text(
                        '${value*100}%',
                        style: TextStyle(
                          color: AppColor.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
