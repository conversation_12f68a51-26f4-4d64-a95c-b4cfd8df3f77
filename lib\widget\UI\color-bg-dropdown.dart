
import 'package:flutter/material.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:dropdown_button2/dropdown_button2.dart';

class NarColorBgDropdown extends StatefulWidget {
  final List<dynamic> options;
  final TextEditingController? controller;
  final Color? borderColor;
  final Color? iconColor;
  final String? hintText;
  final TextStyle? labelTextStyle;
  final Function? onChanged;
  final double? downArrowSize;

  // final Function? onFieldTap;
  // final String? hint;
  // final String? disabledHint;
  // final bool? autoFocus;

  // final String? validationType;
  // final String? parametersValidate;
  // final String? label;
  // final Color? labelColor;
  // final int? flex;
  final double? height;
  final EdgeInsetsGeometry? padding;

  const NarColorBgDropdown({
    Key? key,
    required this.options,
    this.controller,
    this.borderColor = Colors.transparent,
    this.iconColor = Colors.black,
    this.onChanged,
    this.hintText,
    this.labelTextStyle,
    this.downArrowSize,
    this.padding = const EdgeInsets.symmetric(horizontal: 10),
    this.height = 30,
    // this.flex = 1,
    // this.label = '',
    // this.labelColor = const Color(0xff696969),

    // this.hint,
    // this.disabledHint,

    // this.autoFocus,

    // this.onFieldTap,
    // this.validationType,
    // this.parametersValidate,
    // this.iconSize = 24,
    // this.buttonPadding =
    // const EdgeInsets.only(top: 17, bottom: 17, left: 21.0, right: 8.0)
  }) : super(key: key);

  @override
  State<NarColorBgDropdown> createState() => _NarColorBgDropdownState();
}

class _NarColorBgDropdownState extends State<NarColorBgDropdown> {
  double bottomPaddingToError = 12;
  Map? dropdownValue;
  String? selectedValue;

  @override
  void initState() {
    super.initState();
    
    widget.options.removeWhere((test) => test['value'] == '' && test['label'] == '');
    if (widget.controller!.text == '') {
      dropdownValue = widget.options.first;
    } else {
      try {
        dropdownValue = widget.options.where((element) => element['value'] == widget.controller!.text).toList().first;
      } catch (e) {
        dropdownValue = widget.options.first;
      }
    }
  }

  @override
  void didUpdateWidget(covariant NarColorBgDropdown oldWidget) {
    // TODO: implement didUpdateWidget
    super.didUpdateWidget(oldWidget);

    if (widget.controller!.text == '') {
      dropdownValue = widget.options.first;
    } else {
      try {
        dropdownValue = widget.options.where((element) => element['value'] == widget.controller!.text).toList().first;
      } catch (e) {
        dropdownValue = widget.options.first;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    try {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(
            width: 1,
            color: AppColor.borderColor,
          ),
          color: AppColor.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: DropdownButtonHideUnderline(
          child: DropdownButton2(
            isExpanded: true,
            style: TextStyle(
              fontSize: 11,
              fontFamily: 'Raleway-600',
              color: AppColor.black,
            ),

            // style: TextStyle().text11w600.textColor(AppColor.black),
            value: dropdownValue,
            enableFeedback: true,
            // hint: Text(
            //   widget.hintText ?? "",
            //   style: widget.textStyle ?? TextStyle().text11w600.textColor(AppColor.greyColor),
            // ),
            hint: NarFormLabelWidget(
              label: widget.hintText ?? "",
              fontSize: 11,
              fontWeight: '600',
              textColor: AppColor.greyColor,
            ),
            dropdownStyleData: DropdownStyleData(
              elevation: 0,
              padding: EdgeInsets.zero,
              decoration: BoxDecoration(
                color: AppColor.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  width: 1,
                  color: AppColor.borderColor,
                ),
                boxShadow: [
                  BoxShadow(
                    offset: Offset(0, 7),
                    color: AppColor.black.withOpacity(.08),
                    blurRadius: 17,
                  )
                ],
              ),
            ),
            buttonStyleData: ButtonStyleData(
              decoration: BoxDecoration(
                color: AppColor.white,
                borderRadius: BorderRadius.circular(8),
              ),
              elevation: 0,
              padding: widget.padding,
              height: widget.height,
              width: 148,
            ),
            menuItemStyleData: MenuItemStyleData(
              height: widget.height!,
              padding: widget.padding,

            ),
            iconStyleData: IconStyleData(
              iconSize: widget.downArrowSize ?? 15,
              icon: Icon(
                Icons.keyboard_arrow_down,
                color: AppColor.iconGreyColor,
              ),
            ),
            // customButton: Container(
            //   height: 32,
            //   width: 150,
            //   padding: const EdgeInsets.symmetric(horizontal: 8),
            //   decoration: BoxDecoration(
            //     color: dropdownValue!['bgColor'] != null ? dropdownValue!['bgColor'] : Colors.white,
            //     borderRadius: BorderRadius.circular(8),
            //     border: Border.all(
            //       color: dropdownValue!['borderColor'] != null ? dropdownValue!['borderColor'] : widget.borderColor!,
            //       width: dropdownValue!['borderColor'] != null ? 1 : 0,
            //     ),
            //   ),
            //   child: Container(
            //     decoration: BoxDecoration(
            //       color: dropdownValue!['bgColor'] != null ? dropdownValue!['bgColor'] : Colors.white,
            //     ),
            //     child: Row(
            //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //       children: [
            //         Expanded(
            //           child: Text(
            //             dropdownValue!['label'],
            //             style: TextStyle().text11w600.textColor(AppColor.black).textColor(
            //                   dropdownValue!['textColor'] != null ? dropdownValue!['textColor'] : AppColor.black,
            //                 ),
            //           ),
            //         ),
            //         Padding(
            //           padding: EdgeInsets.only(right: 8),
            //           child: Icon(
            //             Icons.keyboard_arrow_down,
            //             color: dropdownValue!['textColor'] != null ? dropdownValue!['textColor'] : AppColor.black,
            //             size: 15,
            //           ),
            //         )
            //       ],
            //     ),
            //   ),
            // ),
            items: widget.options
                .map(
                  (item) => DropdownMenuItem<Map>(
                    value: item,
                    child: Container(
                      child: Row(
                        children: [
                          if( item['bgColor'] != null ) Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Container(
                                height: 12,
                                width: 12,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: item['bgColor'] == null || item['label'] == '' ? Colors.white : item['bgColor'],
                                ),
                              ),
                              const SizedBox(
                                width: 10,
                              ),
                            ],
                          ),
                          Text(
                            item['label'],
                            style: widget.labelTextStyle,
                          ),
                        ],
                      ),
                    ),
                  ),
                )
                .toList(),
            onChanged: (Map? value) {
              // value as MenuItem;
              setState(() {
                widget.controller!.text = value!['value'];
                dropdownValue = value;
              });

              if (widget.onChanged != null) {
                widget.onChanged!();
              }
            },
            // itemHeight: 48,
            // itemPadding: const EdgeInsets.only(left: 16, right: 16),
            // dropdownWidth: 200,
            // dropdownPadding: const EdgeInsets.symmetric(vertical: 6),
            // dropdownDecoration: BoxDecoration(
            //   borderRadius: BorderRadius.circular(7),
            //   color: Colors.white,
            // ),
            // dropdownElevation: 8,
            // offset: const Offset(0, 8),
          ),
        ),
      );
    } catch (e) {
      return Text('');
    }
  }
}
