
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/classes/renovationContact.dart';
import 'package:newarc_platform/classes/renovationContactAddress.dart';
import 'package:newarc_platform/classes/renovationQuotation.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/pages/work/renovation_quotation/renovation_quotation_controller.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:intl/intl.dart';
import '../../../utils/common_utils.dart';
import '../../../widget/UI/image_dropdown.dart';
import '../../../widget/UI/tab/status_widget.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;

class RenovationQuotationDataSource extends DataTableSource {
  final List<List<RenovationQuotation>> groupedQuotations;
  final List<NewarcUser> renovators;
  final BuildContext context;
  final List<Map> status;
  final Function() initialFetchContacts;
  final Function(RenovationQuotation row, String type,List<RenovationQuotation> group) onOpenAzioniDialog;
  final controller = Get.put<RenovationQuotationController>(RenovationQuotationController());

  NumberFormat localCurrencyFormatMain = NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 0);

  RenovationQuotationDataSource({
    required this.context,
    required this.groupedQuotations,
    required this.renovators,
    required this.status,
    required this.initialFetchContacts,
    required this.onOpenAzioniDialog,
  });


  Future<RenovationContact> _getRenovationContact( String contactId ) async {
    
    DocumentSnapshot<Map<String, dynamic>> contact = await FirebaseFirestore.instance
      .collection(appConfig.COLLECT_RENOVATION_CONTACTS)
      .doc(contactId)
      .get();

    if( contact.exists ) {

      RenovationContact responseAddress = RenovationContact.fromDocument(contact.data()!, contact.id);
      return responseAddress;

    }

    return RenovationContact.empty();

  }

  @override
  DataRow? getRow(int index) {
    int currentIndex = 0;

    for (var group in groupedQuotations) {
      final mainQuotation = group.first;

      if (mainQuotation.isHidden == true) continue;
      if (currentIndex == index) {
        return _buildMainQuotationRow(group.first, group);
      }
      currentIndex++;



      final mainExpandKey = "${mainQuotation.code}_V${mainQuotation.version}_R${mainQuotation.revision}";
      final isMainExpanded = controller.expandedChildren.contains(mainExpandKey);

      // === If mainQuotation is the only one in the group (no children)
      if (isMainExpanded) {
        for (int j = 0; j < (mainQuotation.renovationCategory?.length ?? 0); j++) {
          final category = mainQuotation.renovationCategory![j];
          final isLastCategory = j == (mainQuotation.renovationCategory!.length - 1);
          if (currentIndex == index) {
            return _buildCategoryRow(category, mainQuotation, isLastCategory);
          }
          currentIndex++;
        }
      }

      if (controller.expandedGroups.contains(group.first.code)) {
        for (int i = 1; i < group.length; i++) {
          final child = group[i];
          if (child.isHidden == true) continue;
            bool isLastChild = (i == group.length - 1);
            if (currentIndex == index) {
              return _buildChildQuotationRow(group[i],isLastChild);
            }
            currentIndex++;

          // for renovationCategory rows
          if (controller.expandedChildren.contains("${child.code}_V${child.version}_R${child.revision}")) {
            for (int j = 0; j < (child.renovationCategory?.length ?? 0); j++) {
              final category = child.renovationCategory![j];
              bool isLastChildCategory = (j == (child.renovationCategory?.length ?? 0) - 1);
              if (currentIndex == index) {
                return _buildCategoryRow(category,child,isLastChildCategory);
              }
              currentIndex++;
            }
          }
        }
      }
    }
    return null;
  }

  associateAddressWithQuotation( RenovationQuotation mainQuotation) async {
    await FirebaseFirestore.instance
    .collection( appConfig.COLLECT_RENOVATION_QUOTATION)
    .doc(mainQuotation.id)
    .update(mainQuotation.toMap());
  }


  DataRow _buildMainQuotationRow(RenovationQuotation mainQuotation, List<RenovationQuotation> group) {
    
    final childExpandKey = "${mainQuotation.code}_V${mainQuotation.version}_R${mainQuotation.revision}";
    bool isExpanded = controller.expandedGroups.contains(mainQuotation.code);
    bool isChildExpanded = controller.expandedChildren.contains(childExpandKey);
    bool isSingleQuotation = group.length == 1;
    String status = getGroupStatusForNotExpanded(group);

    int quotationIndex = controller.quotations.indexWhere((q) => mainQuotation.id == q.id );

    /* Adding a fallback, so that, if there is any address is still not migrated to the new format then it will do that. This code snippet can be removed when everything is migrated to new format. */
    if( (mainQuotation.renovationContactAddressId == null || mainQuotation.renovationContactAddressId == '') && mainQuotation.renovationContact!.addressInfo!.length == 1  ) {
      mainQuotation.renovationContactAddressId = mainQuotation.renovationContact!.addressInfo![0];
      associateAddressWithQuotation(mainQuotation);
    }

    if( mainQuotation.isHidden! ) {
      return DataRow2(
        specificRowHeight: 0,
        cells: [
          DataCell( SizedBox(height: 0,)),
          DataCell( SizedBox(height: 0,)),
          DataCell( SizedBox(height: 0,)),
          DataCell( SizedBox(height: 0,)),
          DataCell( SizedBox(height: 0,)),
        ]
      );
    }
    

    return DataRow2(
      
      decoration: BoxDecoration(
        
        border: Border(
          bottom: BorderSide(
              color: (isExpanded && isChildExpanded) ? AppColor.white : (isExpanded && !isSingleQuotation) ? AppColor.white : AppColor.borderColor,
              width: 1,
          ),
        ),
      ),
      cells: [
        //---- Address + Client Name
        DataCell(
          GestureDetector(
            onTap: () {
              if (isExpanded) {
                controller.expandedGroups.remove(mainQuotation.code);
                controller.expandedChildren.clear();
              } else {
                controller.expandedGroups.add(mainQuotation.code ?? "");
              }
              notifyListeners();
            },
            child: Row(
              children: [
                Icon(isExpanded ? Icons.expand_less : Icons.expand_more, color: Colors.black),
                SizedBox(width: 5),
                Flexible(
                  child:
                  mainQuotation.renovationContactAddressId != null
                  ? Wrap(
                    children: [
                      controller.quotations[quotationIndex].renovationContactAddress!.id == ''
                      ? FutureBuilder<RenovationContactAddress>(
                        future: getRenovationContactAddress(mainQuotation.renovationContactAddressId!),
                        builder: (context, snapshot) {
                          if( snapshot.connectionState == ConnectionState.waiting || snapshot.hasError || !snapshot.hasData || snapshot.data!.id == '' ){
                            return NarFormLabelWidget(
                              label: "",
                              fontSize: 12,
                              fontWeight: '700',
                              overflow: TextOverflow.visible,
                              textAlign: TextAlign.start,
                              textColor: Colors.black,
                            );
                          }

                          controller.quotations[quotationIndex].renovationContactAddress = snapshot.data;
                          // notifyListeners();
                          return NarFormLabelWidget(
                            label: snapshot.data!.addressInfo!.toShortAddress() + '-' ,
                            fontSize: 12,
                            fontWeight: '700',
                            overflow: TextOverflow.visible,
                            textAlign: TextAlign.start,
                            textColor: Colors.black,
                          );
                        }
                      )
                      : NarFormLabelWidget(
                        label: mainQuotation.renovationContactAddress!.addressInfo!.toShortAddress() + '-' ,
                        fontSize: 12,
                        fontWeight: '700',
                        overflow: TextOverflow.visible,
                        textAlign: TextAlign.start,
                        textColor: Colors.black,
                      ),
            
                      controller.quotations[quotationIndex].renovationContact!.id == ''
                      ? FutureBuilder<RenovationContact>(
                        future: _getRenovationContact(mainQuotation.renovationContactId!),
                        builder: (context, snapshot) {
                          if( snapshot.connectionState == ConnectionState.waiting || snapshot.hasError || !snapshot.hasData || snapshot.data!.id == '' ){
                            return NarFormLabelWidget(
                              label: "",
                              fontSize: 12,
                              fontWeight: '700',
                              overflow: TextOverflow.visible,
                              textAlign: TextAlign.start,
                              textColor: Colors.black,
                            );
                          }
                          
                          controller.quotations[quotationIndex].renovationContact = snapshot.data;
                          
                          return NarFormLabelWidget(
                            label: "${snapshot.data!.personInfo?.name ?? ""} ${snapshot.data!.personInfo?.surname ?? ""}",
                            fontSize: 12,
                            fontWeight: '700',
                            overflow: TextOverflow.visible,
                            textAlign: TextAlign.start,
                            textColor: Colors.black,
                          );
                        }
                      )
                      : NarFormLabelWidget(
                        label: "${mainQuotation.renovationContact!.personInfo?.name ?? ""} ${mainQuotation.renovationContact!.personInfo?.surname ?? ""}",
                        fontSize: 12,
                        fontWeight: '700',
                        overflow: TextOverflow.visible,
                        textAlign: TextAlign.start,
                        textColor: Colors.black,
                      )
                        
                    ],
                  )
                  : NarFormLabelWidget(
                    label: " - ${controller.quotations[quotationIndex].renovationContact?.personInfo?.name ?? ""} ${mainQuotation.renovationContact?.personInfo?.surname ?? ""}",
                    fontSize: 12,
                    fontWeight: '700',
                    overflow: TextOverflow.visible,
                    textAlign: TextAlign.start,
                    textColor: Colors.black,
                  ),
                ),
                
              ],
            ),
          ),
        ),
        //---- Architetto Name
        DataCell(
          controller.quotations[quotationIndex].architect!.id == ''
          ? FutureBuilder<NewarcUser>(
            future: _getArchitectName(quotationIndex, controller.quotations[quotationIndex].renovationContact?.assignedRenovatorId ?? ""),
            builder: (context, snapshot) {
              if (snapshot.hasData) {
                
                controller.quotations[quotationIndex].architect = snapshot.data;
                return NarFormLabelWidget(
                  label: "${snapshot.data!.firstName} ${snapshot.data!.lastName}",
                  fontSize: 12,
                  fontWeight: '600',
                  textAlign: TextAlign.start,
                  textColor: Colors.black,
                );
              }
              return Container(
                width: 30,
                height: 30,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(100),
                  color: Colors.grey,
                ),
                child: Text('ll'),
              );
            },
          )
          : NarFormLabelWidget(
            label: "${controller.quotations[quotationIndex].architect!.firstName} ${controller.quotations[quotationIndex].architect!.lastName}",
            fontSize: 12,
            fontWeight: '600',
            textAlign: TextAlign.start,
            textColor: Colors.black,
          ),
        ),
        //---- Codice
        DataCell(
         GestureDetector(
           onTap: () {
             if(mainQuotation.status == CommonUtils.accettato && isExpanded){
               if (isChildExpanded) {
                 controller.expandedChildren.remove(childExpandKey);
               } else {
                 controller.expandedChildren.add(childExpandKey);
               }
               notifyListeners();
             }
           },
           child: Row(
            children: [
              StatusWidget(
                statusColor: CommonUtils.getRenovationQuotationStatusColor(isExpanded ?  mainQuotation.status! : status),
              ),
              SizedBox(width: 5),

              NarFormLabelWidget(
                label: isExpanded ? childExpandKey : mainQuotation.code ?? "",
                fontSize: 12,
                fontWeight: (status == CommonUtils.accettato) ? '700' : '600',
                textAlign: TextAlign.start,
                textColor: Colors.black,
                textDecoration: (status == CommonUtils.accettato) ? TextDecoration.underline : TextDecoration.none ,
              ),
              mainQuotation.status == CommonUtils.accettato && isExpanded?
              Padding(
                padding: const EdgeInsets.only(left: 5),
                child: Icon(isChildExpanded ? Icons.expand_less : Icons.expand_more, color: Colors.black),
              ):SizedBox(),
            ],
                   ),
         )),
        //----comments
        DataCell(
          NarFormLabelWidget(
            label: isExpanded ? mainQuotation.comment : "",
            fontSize: 12,
            fontWeight: '600',
            textAlign: TextAlign.start,
            textColor: Colors.black,
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          )
        ),
        //---- Action
        DataCell(_buildActionsDropdown(mainQuotation,group)),
      ],
    );
    
  }

  DataRow _buildChildQuotationRow(RenovationQuotation childQuotation,bool isLastChild) {
    String codeWithVersion = "${childQuotation.code}_V${childQuotation.version}_R${childQuotation.revision}";
    bool isExpanded = controller.expandedChildren.contains(codeWithVersion);

    return DataRow2(
      decoration: isLastChild && !isExpanded
          ? BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColor.borderColor,
            width: 1,
          ),
        ),
      )
          : null,
      cells: [
        DataCell(Container()),
        DataCell(Container()),
        DataCell(GestureDetector(
          onTap: () {
            if(childQuotation.status == CommonUtils.accettato){
              if (isExpanded) {
                controller.expandedChildren.remove(codeWithVersion);
              } else {
                controller.expandedChildren.add(codeWithVersion);
              }
              notifyListeners();
            }
          },
          child: Row(
            children: [
              StatusWidget(
                statusColor: CommonUtils.getRenovationQuotationStatusColor(childQuotation.status ?? ""),
              ),
              SizedBox(width: 5),
              NarFormLabelWidget(
                label: codeWithVersion,
                fontSize: 12,
                fontWeight: childQuotation.status == CommonUtils.accettato ? '700' : '600',
                textAlign: TextAlign.start,
                textColor: Colors.black,
                textDecoration:  childQuotation.status == CommonUtils.accettato ? TextDecoration.underline : TextDecoration.none,
              ),
              SizedBox(width: 5),
              childQuotation.status == CommonUtils.accettato ?
              Icon(isExpanded ? Icons.expand_less : Icons.expand_more, color: Colors.black) : SizedBox(),
            ],
          ),
        )),

        //----comments
        DataCell(
          NarFormLabelWidget(
            label: childQuotation.comment,
            fontSize: 12,
            fontWeight: '600',
            textAlign: TextAlign.start,
            textColor: Colors.black,
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          )
        ),
        DataCell(Container()),
      ],
    );
  }

  DataRow _buildCategoryRow(RenovationCategory category,RenovationQuotation row,bool isLastChild) {
    String? iconPath;
    Color color = Color(0xFFA2A2A2);
    String mailStatus = category.mailStatus ?? "";
    if(mailStatus == "sent" || mailStatus == "delivered"){
      color = Colors.black;
      iconPath = "assets/icons/new_mail.svg";
    }else if(mailStatus == "completed"){
      color = Color(0xFF39C14F);
      iconPath = "assets/icons/confirmed.svg";
    }else if(mailStatus == "voided" || mailStatus == "declined"){
      color = Color(0xFFCD5959);
      iconPath = "assets/icons/cross.svg";
    }else{
      color = Color(0xFFA2A2A2);
      iconPath = "";
    }
    return DataRow2(
      cells: [
        DataCell(Container()),
        DataCell(Container()),
        DataCell(
          Row(
            children: [
              SizedBox(width: 10),
              StatusWidget(
                statusColor: CommonUtils.getRenovationQuotationStatusColor(category.quotationStatus ?? ""),
              ),
              SizedBox(width: 10),
              iconPath.isNotEmpty ?
              Padding(
                padding: const EdgeInsets.only(right: 5),
                child: SvgPicture.asset(iconPath,height: 11,width: 14,color: color,),
              )
                  :
              SizedBox(width: 10,),
              NarFormLabelWidget(
                label: "${row.code ?? ""}_V${row.version}_R${row.revision}_${category.category}",
                fontSize: 12,
                fontWeight: '600',
                textAlign: TextAlign.start,
                textColor: Colors.black,
              ),
            ],
          ),
        ), // optional
        DataCell(Container()),
        DataCell(Container()),
      ],
    );
  }

  Widget _buildActionsDropdown(RenovationQuotation row,List<RenovationQuotation> group) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 5.0),
      child: NarImageDropdown(
        controller: TextEditingController(),
        customButton: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 10.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              NarFormLabelWidget(
                label: "Azioni",
                fontSize: 11,
                fontWeight: '600',
                textColor: AppColor.greyColor,
              ),
              Icon(
                Icons.keyboard_arrow_down,
                color: AppColor.iconGreyColor,
                size: 15,
              )
            ],
          ),
        ),
        options: [
          {
            'value': 'edit',
            'label': 'Modifica',
            'image': 'assets/icons/edit.svg',
            'iconColor': AppColor.greyColor
          },
          {
            'value': 'status_modification',
            'label': 'Modifica stato',
            'image': 'assets/icons/dot_icon.svg',
            'iconColor': AppColor.greyColor
          },
          {
            'value': 'new_version',
            'label': 'Nuova Versione',
            'image': 'assets/icons/copy.svg',
            'iconColor': AppColor.greyColor
          },
          {
            'value': 'new_revision',
            'label': 'Nuova Revisione',
            'image': 'assets/icons/copy.svg',
            'iconColor': AppColor.greyColor
          },
          {
            'value': 'download_pdf',
            'label': 'Scarica PDF',
            'image': 'assets/icons/download.png',
            'iconColor': AppColor.greyColor
          },
          {
            'value': 'calculator_pdf',
            'label': 'Gestisci CME',
            'image': 'assets/icons/calculator.svg',
            'iconColor': AppColor.greyColor
          },

          {
            'value': 'comment',
            'label': 'Commenta',
            'image': 'assets/icons/comment.svg',
            'iconColor': AppColor.greyColor
          },{
            'value': 'send_contract',
            'label': 'Gestisci firme',
            'image': 'assets/icons/complete.svg',
            'iconColor': AppColor.greyColor
          },
          if (row.status != 'accettato')
            {
              'value': 'delete',
              'label': 'Elimina',
              'image': 'assets/icons/trash-process.png',
              'iconColor': AppColor.redColor,
              'labelColor': AppColor.redColor
            },
        ],
        iconSize: 15,
        hintText: "Azioni",
        onChanged: (value) {
          if (value == null || value['value'] == null) return;
          onOpenAzioniDialog(row, value['value'].toString().trim(),group);
        },
      ),
    );
  }

  Future<NewarcUser> _getArchitectName(int quotationIndex, String userId) async {
    
    NewarcUser user = renovators.firstWhere(
      (element) => element.id == userId,
      orElse: () => NewarcUser({"id": ""}),
    );
    try {

      controller.quotations[quotationIndex].architect = user;
      
      // return "${user.firstName} ${user.lastName}";
      return user;
      
    } catch (error) {

      return NewarcUser.empty();
    }
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount {
    int total = 0;

    for (var group in groupedQuotations) {

      final mainQuotation = group.first;

      if(mainQuotation.isHidden!) continue;

        total += 1; // main quotation


        final mainKey = "${mainQuotation.code}_V${mainQuotation.version}_R${mainQuotation.revision}";
        final isMainChildExpanded = controller.expandedChildren.contains(mainKey);



        if (isMainChildExpanded) {
          total += mainQuotation.renovationCategory?.length ?? 0;
        }

        if (controller.expandedGroups.contains(mainQuotation.code)) {
          total += group.length - 1; // all children except main

          for (int i = 1; i < group.length; i++) {
            final child = group[i];
            if (child.isHidden == true) continue;
            final childKey = "${child.code}_V${child.version}_R${child.revision}";
            final isChildExpanded = controller.expandedChildren.contains(childKey);
            if (isChildExpanded) {
              total += child.renovationCategory?.length ?? 0;
            }
          }
        }
      // }


    }

    return total;
  }


  @override
  int get selectedRowCount => 0;
}



