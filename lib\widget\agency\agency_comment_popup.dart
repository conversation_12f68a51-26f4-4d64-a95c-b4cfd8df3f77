
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/acquiredContact.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/agencyValuatorSubmissionComment.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/widget/UI/alert.dart';
import 'package:newarc_platform/widget/UI/button.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/textarea.dart';

class AgencyCommentPopup extends StatefulWidget {
  const AgencyCommentPopup(
      {Key? key,
      required this.acquiredContact,
      this.agency,
      this.newarcUser
      // this.initialFetchContacts
      })
      : super(key: key);

  final AcquiredContact acquiredContact;
  final Agency? agency;
  final NewarcUser? newarcUser;
  // final Function? initialFetchContacts;

  @override
  State<AgencyCommentPopup> createState() => _AgencyCommentPopupState();
}

class _AgencyCommentPopupState extends State<AgencyCommentPopup> {
  TextStyle sectionTitleStyle =
      TextStyle(fontSize: 16, fontWeight: FontWeight.bold);

  bool loading = false;
  bool hasComments = true;
  QuerySnapshot<Map<String, dynamic>>? collectionSnapshot;
  List<Agency>? agencyUser = [];
  List<Agency>? tmpAgencyUser = [];
  TextEditingController contSearchAgency = new TextEditingController();
  Key? listviewKey;

  TextEditingController contComment = new TextEditingController();
  final ScrollController contCommentListScroll = new ScrollController();
  String? activeComment = '';
  bool? hasEmptyMessage = false;

  String? loadingMessage;

  List<AgencyValuatorSubmissionComment> comments = [];
  List<AgencyValuatorSubmissionComment> tmpComments = [];

  @override
  void initState() {
    super.initState();
    loading = true;
    initialDataLoading();
  }

  /*
  * Fetch comments when popup appears.
  */
  initialDataLoading() async {
    // print(widget.agencyUser.id);
    comments.clear();

    setState(() {
      hasComments = true;
      loading = true;
      loadingMessage = 'Loading...';
    });

    collectionSnapshot = await FirebaseFirestore.instance
        .collection(appConfig.COLLECT_VALUATOR_SUBMISSION_COMMENTS)
        .where('contactId', isEqualTo: widget.acquiredContact.firebaseId)
        .orderBy('date', descending: true)
        .get();
    // print(collectionSnapshot);
    if (collectionSnapshot!.docs.length > 0) {
      for (var element in collectionSnapshot!.docs) {
        comments.add(AgencyValuatorSubmissionComment.fromDocument(
            element.data(), element.id));
        tmpComments.add(AgencyValuatorSubmissionComment.fromDocument(
            element.data(), element.id));
      }

      setState(() {
        hasComments = true;
        loading = false;
        loadingMessage = '';
        // var position = contCommentListScroll.position.maxScrollExtent;
        // contCommentListScroll.jumpTo(position);
        // print(position);
      });

      // var position = contCommentListScroll.position.maxScrollExtent;
      // contCommentListScroll.jumpTo(0);
      // print({ 'postion', position});
    } else {
      setState(() {
        hasComments = false;
        loadingMessage = 'Inserisci il primo commento!';
        loading = false;
      });
    }
  }

  filterAgency(value) {
    tmpAgencyUser!.clear();

    for (var i = 0; i < agencyUser!.length; i++) {
      if (agencyUser![i].name!.toLowerCase().indexOf(value.toLowerCase()) >=
          0) {
        // print({value.toLowerCase(), agencyUser![i].name});
        tmpAgencyUser!.add(agencyUser![i]);
      }
    }
  }

  saveComment(AgencyValuatorSubmissionComment comment) async {
    setState(() {
      loadingMessage = 'Saving...';
      loading = true;
    });

    final FirebaseFirestore _db = FirebaseFirestore.instance;
    await _db
        .collection(appConfig.COLLECT_VALUATOR_SUBMISSION_COMMENTS)
        .add(comment.toMap())
        .then((value) {
      // Navigator.pop(context);
      comment.firebaseId = value.id;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: NarFormLabelWidget(
          label: 'Commento salvato!',
          fontWeight: '800',
          textColor: Colors.white,
        )),
      );

      setState(() {
        contComment.text = '';
        comments.add(comment);
        loading = false;
      });
    }).catchError((onError) {
      debugPrint(onError);
      NarAlertDialog(
          context, 'Error', 'Could not add comment. ' + onError.toString(), []);
    });

    // await Future.delayed(Duration(seconds: 2));

    // contCommentListScroll.position.

    var position = contCommentListScroll.position.maxScrollExtent;
    contCommentListScroll.jumpTo(position + 100);
    setState(() {
      loading = false;
    });
  }

  updateComment(
    AgencyValuatorSubmissionComment comment,
  ) async {
    // tmpComments = comments;
    setState(() {
      loadingMessage = 'Updating...';
      loading = true;
    });

    final FirebaseFirestore _db = FirebaseFirestore.instance;
    await _db
        .collection(appConfig.COLLECT_VALUATOR_SUBMISSION_COMMENTS)
        .doc(comment.firebaseId)
        .update(comment.toMap())
        .then((value) {
      // Navigator.pop(context);
      // comment.firebaseId = value.id;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: NarFormLabelWidget(
          label: 'Commento aggiornato!',
          fontWeight: '800',
          textColor: Colors.white,
        )),
      );

      var commentIndex =
          comments.indexWhere((comm) => comm.firebaseId == comment.firebaseId);
      print(contComment.text);
      comments[commentIndex].message = contComment.text;
      contComment.text = '';
      activeComment = '';

      // var position = contCommentListScroll.position.maxScrollExtent;
      // contCommentListScroll.jumpTo(position);
      // contCommentListScroll.animateTo(0, duration: Duration(seconds: 1), curve: Curves.bounceIn);
      // contCommentListScroll.animateTo( comments.length - 1 , duration: Duration(seconds: 1), curve: Curves.bounceIn);

      setState(() {
        loading = false;
      });
    }).catchError((onError) {
      loading = false;
      debugPrint(onError);
      NarAlertDialog(context, 'Error',
          'Could not update comment. ' + onError.toString(), []);
    });
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // setState(() {
        //   widget.initialFetchContacts!(widget.agency);
        // });
        return true;
      },
      child: Material(
        borderRadius: BorderRadius.circular(20),
        child: StatefulBuilder(builder: (context, setState) {
          return Container(
            height: 550,
            clipBehavior: Clip.antiAlias,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              border: Border(
                  bottom: BorderSide(
                      width: 1, color: Color.fromRGBO(226, 226, 226, 1)),
                  left: BorderSide(
                      width: 1, color: Color.fromRGBO(226, 226, 226, 1)),
                  top: BorderSide(
                      width: 1, color: Color.fromRGBO(226, 226, 226, 1)),
                  right: BorderSide(
                      width: 1, color: Color.fromRGBO(226, 226, 226, 1))),
              color: Colors.white,
            ),
            width: MediaQuery.of(context).size.width * 0.5,
            padding: EdgeInsets.symmetric(horizontal: 30, vertical: 20),
            child: Stack(
              alignment: Alignment.center,
              children: [
                Container(
                  width: 450,
                  child: Column(
                    children: [
                      NarFormLabelWidget(
                        label: 'Commenti',
                        textColor: Colors.black,
                        fontSize: 22,
                        fontWeight: '800',
                      ),
                      NarFormLabelWidget(
                          label: widget.acquiredContact.address,
                          textColor: Color.fromRGBO(113, 113, 113, 1),
                          fontSize: 15,
                          fontWeight: '600'),
                      SizedBox(height: 20),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          NarFormLabelWidget(label: 'Commenti'),
                          NarTextareaWidget(
                            maxLines: 2,
                            minLines: 2,
                            controller: contComment,
                          ),
                          hasEmptyMessage == true
                              ? NarFormLabelWidget(
                                  label: 'Message required!',
                                  fontSize: 9,
                                  textColor: Colors.red,
                                )
                              : SizedBox(
                                  height: 0,
                                ),
                          SizedBox(height: 10),
                          NarButtonWidget(
                            text: 'Aggiungi commento',
                            leadingIcon: Icon(
                              Icons.add,
                              color: Colors.white,
                            ),
                            onClick: () async {
                              if (contComment.text == '') {
                                hasEmptyMessage = true;
                                return;
                              }
                              hasEmptyMessage = false;

                              AgencyValuatorSubmissionComment comment =
                                  AgencyValuatorSubmissionComment(
                                      authorId: widget.newarcUser!.id,
                                      contactId:
                                          widget.acquiredContact.firebaseId,
                                      message: contComment.text,
                                      date: Timestamp.now()
                                          .millisecondsSinceEpoch,
                                      isAvailable: true);

                              if (activeComment == '') {
                                saveComment(comment);
                              } else {
                                setState(() {
                                  loading = true;
                                });
                                comment.firebaseId = activeComment;
                                updateComment(comment);
                                activeComment = '';
                              }

                              //Navigator.pop(context, widget.initialFetchContacts!(widget.agency));
                            },
                            color: Color.fromRGBO(78, 154, 122, 1),
                            textColor: Colors.white,
                            fontSize: 14,
                            fontWeight: '700',
                            borderRadius: 10,
                            borderSideColor: Color.fromRGBO(78, 154, 122, 1),
                            height: 20,
                          ),
                        ],
                      ),
                      SizedBox(height: 20),
                      Container(
                          height: 200,
                          key: ValueKey(Timestamp.now().millisecondsSinceEpoch),
                          clipBehavior: Clip.antiAlias,
                          decoration: BoxDecoration(),
                          child: loading == false && comments.length > 0
                              ? ListView.builder(
                                  controller: contCommentListScroll,
                                  shrinkWrap: true,
                                  itemCount: comments.length,
                                  reverse: false,
                                  key: listviewKey,
                                  itemBuilder: (context, index) {
                                    var commentDate =
                                        DateTime.fromMillisecondsSinceEpoch(
                                            comments[index].date!);
                                    var date = commentDate.day.toString() +
                                        '/' +
                                        commentDate.month.toString() +
                                        '/' +
                                        commentDate.year.toString();

                                    return Card(
                                      margin: const EdgeInsets.only(bottom: 15),
                                      shadowColor: Colors.grey[100],
                                      shape: RoundedRectangleBorder(
                                        //<-- SEE HERE
                                        borderRadius: BorderRadius.circular(15),
                                        side: BorderSide(
                                          color:
                                              Color.fromRGBO(231, 231, 231, 1),
                                        ),
                                      ),
                                      clipBehavior: Clip.antiAlias,
                                      borderOnForeground: true,
                                      child: Padding(
                                        padding: const EdgeInsets.all(12.0),
                                        child: Column(
                                          children: [
                                            Row(
                                              mainAxisSize: MainAxisSize.max,
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                NarFormLabelWidget(
                                                  label: date.toString(),
                                                  textColor: Color.fromRGBO(
                                                      163, 163, 163, 1),
                                                  fontSize: 10,
                                                  fontWeight: '600',
                                                ),
                                                /*comments[index].authorId ==
                                                        widget.agencyUser.id
                                                    ? GestureDetector(
                                                        child: Icon(
                                                          Icons.edit,
                                                          color: Color.fromRGBO(
                                                              193, 193, 193, 1),
                                                          size: 18,
                                                        ),
                                                        onTap: () {
                                                          activeComment =
                                                              comments[index]
                                                                  .firebaseId;
                                                          contComment.text =
                                                              comments[index]
                                                                  .message!;
                                                        },
                                                      )
                                                    : SizedBox(
                                                        height: 0,
                                                      )*/
                                              ],
                                            ),
                                            SizedBox(
                                              height: 10,
                                            ),
                                            Row(
                                              mainAxisSize: MainAxisSize.max,
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.end,
                                              children: [
                                                Expanded(
                                                  key: ValueKey(comments[index]
                                                      .firebaseId),
                                                  child: NarFormLabelWidget(
                                                    label: comments[index]
                                                            .message
                                                            .toString() +
                                                        "\n",
                                                    textColor: Color.fromRGBO(
                                                        0, 0, 0, 1),
                                                    fontSize: 14,
                                                    fontWeight: '700',
                                                    overflow: TextOverflow.fade,
                                                  ),
                                                ),
                                                comments[index].authorId ==
                                                        widget.newarcUser!.id
                                                    ? MouseRegion(
                                                        cursor:
                                                            SystemMouseCursors
                                                                .click,
                                                        child: GestureDetector(
                                                          child: Icon(
                                                            Icons.delete,
                                                            color:
                                                                Color.fromRGBO(
                                                                    193,
                                                                    193,
                                                                    193,
                                                                    1),
                                                            size: 18,
                                                          ),
                                                          onTap: () {
                                                            activeComment =
                                                                comments[index]
                                                                    .firebaseId;
                                                            NarAlertDialog(
                                                                context,
                                                                'Confirm!',
                                                                'Delete comment?',
                                                                <Widget>[
                                                                  NarButtonWidget(
                                                                    // color: Colors.grey[200],
                                                                    splashColor:
                                                                        Colors.grey[
                                                                            200],
                                                                    textColor:
                                                                        Colors
                                                                            .black,
                                                                    borderSideColor:
                                                                        Colors
                                                                            .black,
                                                                    text:
                                                                        'Cancel',
                                                                    onClick:
                                                                        () {
                                                                      activeComment =
                                                                          '';
                                                                      Navigator.pop(
                                                                          context);
                                                                    },
                                                                  ),
                                                                  NarButtonWidget(
                                                                    splashColor:
                                                                        Colors.red[
                                                                            800],
                                                                    textColor:
                                                                        Colors
                                                                            .white,
                                                                    borderSideColor:
                                                                        Colors.red[
                                                                            800],
                                                                    text:
                                                                        'Delete',
                                                                    onClick:
                                                                        () async {
                                                                      //Navigator.pop(context);

                                                                      final FirebaseFirestore
                                                                          _db =
                                                                          FirebaseFirestore
                                                                              .instance;

                                                                      await _db
                                                                          .collection(appConfig
                                                                              .COLLECT_VALUATOR_SUBMISSION_COMMENTS)
                                                                          .doc(
                                                                              activeComment)
                                                                          .delete()
                                                                          .then(
                                                                              (value) {
                                                                        ScaffoldMessenger.of(context)
                                                                            .showSnackBar(
                                                                          SnackBar(
                                                                              content: NarFormLabelWidget(
                                                                            label:
                                                                                'Commento cancellato!',
                                                                            fontWeight:
                                                                                '800',
                                                                            textColor:
                                                                                Colors.white,
                                                                          )),
                                                                        );

                                                                        // var deletedComment = comments.where((element) => element.firebaseId == toBeDeletedComment );
                                                                        comments.removeWhere((element) =>
                                                                            element.firebaseId ==
                                                                            activeComment);
                                                                        Navigator.pop(
                                                                            context);

                                                                        activeComment =
                                                                            '';
                                                                        setState(
                                                                            () {});
                                                                      }).onError((error,
                                                                              stackTrace) {
                                                                        NarAlertDialog(
                                                                            context,
                                                                            'Error',
                                                                            'Could not delete comment. ' +
                                                                                error.toString(),
                                                                            []);

                                                                        print(
                                                                            error);
                                                                        print(
                                                                            stackTrace);
                                                                      });
                                                                    },
                                                                  ),
                                                                ]);
                                                          },
                                                        ),
                                                      )
                                                    : SizedBox(height: 0)
                                              ],
                                            )
                                          ],
                                        ),
                                      ),
                                    );
                                  },
                                )
                              : NarFormLabelWidget(label: loadingMessage)),
                    ],
                  ),
                ),
                Positioned(
                  top: 0,
                  right: 0,
                  child: IconButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      icon: Icon(Icons.close, size: 30, color: Colors.black)),
                )
              ],
            ),
          );
        }),
      ),
    );
  }
}
