import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:newarc_platform/pages/work/gestione/persone/persone_view.dart';
import 'package:newarc_platform/pages/work/porte_interne/porte_interne_inside_view.dart';
import 'package:newarc_platform/pages/work/porte_interne/porte_interne_view.dart';
import 'package:newarc_platform/pages/work/rivestimenti/rivestimenti_inside_view.dart';
import 'package:newarc_platform/pages/work/rivestimenti/rivestimenti_view.dart';
import 'package:newarc_platform/pages/work/tinte/tinte_inside_view.dart';
import 'package:newarc_platform/pages/work/tinte/tinte_view.dart';
import 'package:newarc_platform/routes/user_provider.dart';
import 'package:provider/provider.dart';
import '../pages/common/web_leads/web_leads_view.dart';
import '../pages/work/acquired_contacts/acquired_contacts_view.dart';
import '../pages/work/cep_view/cep_view.dart';
import '../pages/work/gestione/agencies/agencies_inside_view.dart';
import '../pages/work/gestione/agencies/agencies_view.dart';
import '../pages/work/gestione/contractors/contractors_inside_view.dart';
import '../pages/work/gestione/contractors/contractors_view.dart';
import '../pages/work/gestione/persone/persone.dart';
import '../pages/work/gestione/professionals/professionals_inside_view.dart';
import '../pages/work/gestione/professionals/professionals_view.dart';
import '../pages/work/home_newarc.dart';
import '../pages/work/immagina_project_review/immagina_project_review.dart';
import '../pages/work/immagina_request_review/request_review_screen.dart';
import '../pages/work/leads/leads_single_view.dart';
import '../pages/work/leads/leads_view.dart';
import '../pages/work/newarc_active_ads.dart';
import '../pages/work/newarc_active_projects/newarc_active_projects_view.dart';
import '../pages/work/newarc_immagina/newarc_immagina_view.dart';
import '../pages/work/newarc_immagina_project_archive/newarc_immagina_project_archive_view.dart';
import '../pages/work/newarc_project_view.dart';
import '../pages/work/pavimenti/pavimenti_inside_view.dart';
import '../pages/work/pavimenti/pavimenti_view.dart';
import '../pages/work/renovation_contacts/renovation_contacts_view.dart';
import '../pages/work/renovation_quotation/cme_information.dart';
import '../pages/work/renovation_quotation/renovation_quotation_view.dart';
import '../pages/work/suggested_contacts/suggested_contacts_view.dart';
import '../pages/work/web-registrations/web_registrations_view.dart' show WebRegistrationsView;
import '../widget/work/active_ad_add.dart';
import '../widget/work/provisional_economic_account.dart';
import '../widget/work/renovation_quotation.dart';
import '../widget/work/settings.dart';

class WorkRoutes {
  static const String workSetting = '/work-setting';
  static const String workValutazioniOnline = '/contatti/valutazioni-online';
  static const String workLeadAcquisto = '/contatti/lead-acquisto';
  static const String workLeadConfiguratore = '/contatti/lead-configuratore';
  static const String workRegistrazioniSito = '/contatti/registrazioni-sito';

  static const String workGestisciContatti = '/gestisci-contatti/gestisci-contatti';
  static String workGestisciContattiInside(String id) => '/gestisci-contatti/gestisci-contatti/$id';

  static const String workAgenzie = '/gestione/agenzie';
  static String workAgenzieInside(String id) => '/gestione/agenzie/$id';

  static const String workDitte = '/gestione/ditte';
  static String workDitteInside(String id) => '/gestione/ditte/$id';

  static const String workPersone = '/gestione/persone';
  static String workPersoneInside(String id) => '/gestione/persone/$id';

  static const String workProfessionals = '/gestione/professionals';
  static String workProfessionalsInside(String id) => '/gestione/professionals/$id';

  static const String workPavimenti = '/forniture/pavimenti';
  static String workPavimentiInside(String id) => '/forniture/pavimenti/$id';

  static const String workPorteInterne = '/forniture/porte-interne';
  static String workPorteInterneInside(String id) => '/forniture/porte-interne/$id';

  static const String workTinte = '/forniture/tinte';
  static String workTinteInside(String id) => '/forniture/tinte/$id';

  static const String workRivestimenti = '/forniture/rivestimenti';
  static String workRivestimentiInside(String id) => '/forniture/rivestimenti/$id';

  static const String workProgettiInCorso = '/operazioni-newarc/progetti-in-corso';
  static String workProgettiInCorsoInside(String id) => '/operazioni-newarc/progetti-in-corso/$id';

  static const String workRenovationProject = '/ristrutturazioni/progetti';
  static String workRenovationProjectInside(String id) => '/ristrutturazioni/progetti/$id';

  static const String workStoricoProgetti = '/operazioni-newarc/storico-progetti';
  static String workStoricoProgettiInside(String id) => '/operazioni-newarc/storico-progetti/$id';

  static const String workCEprevisionali = '/operazioni-newarc/ce-previsionali';
  static String workCEprevisionaliInside(String id) => '/operazioni-newarc/ce-previsionali/$id';

  static const String workAnnunciAttivi = '/ads-manager/annunci-attivi';
  static String workAnnunciAttiviInside({required String type,required String projectId,required String propertyId}) => '/ads-manager/annunci-attivi/$type/$projectId/$propertyId';

  static const String workAnnunciArchiviati = '/ads-manager/annunci-archiviati';
  static String workAnnunciArchiviatiInside(String id) => '/ads-manager/annunci-archiviati/$id';

  static const String workSegnalazioni = '/ristrutturazioni/segnalazioni';

  static const String workClienti = '/ristrutturazioni/clienti';
  static String workClientiInside(String id) => '/ristrutturazioni/clienti/$id';

  static const String workPreventivi = '/ristrutturazioni/preventivi';
  static String workPreventiviInside(String id) => '/ristrutturazioni/preventivi/$id';
  static String workCMEInside(String id) => '/ristrutturazioni/preventivi/cme/$id';


  static String workImmagina(String mode) => '/immagina/$mode';
  static String workImmaginaInside({
    required String mode,
    required String id,
    required String childMode,
  }) => '/immagina/$mode/$childMode/$id';

  static final routes = [
    ShellRoute(
      pageBuilder: (context, state, child) {
        final user = context.read<UserProvider>().workUser;

        if (user == null) {
          return NoTransitionPage(
            child: Scaffold(
              body: Center(child: CircularProgressIndicator()),
            ),
          );
        }

        return NoTransitionPage(
          child: HomeNewarc(
            newarcUser: user,
            child: child,
          ),
        );
      },
      routes: [
        GoRoute(
          path: workSetting,
          name: 'work-setting',
          pageBuilder: (context, state) {
            final user = context.read<UserProvider>().workUser!;
            return NoTransitionPage(
              child: NewarcTeamSetting(
                newarcUser: user,
              ),
            );
          },
        ),

        GoRoute(
          path: workValutazioniOnline,
          name: 'contatti-valutazioni-online',
          pageBuilder: (context, state) {
            return NoTransitionPage(child: AcquiredContactsView());
          },
        ),

        GoRoute(
          path: workLeadAcquisto,
          name: 'contatti-lead-acquisto',
          pageBuilder: (context, state) {
            return NoTransitionPage(child: WebLeadsView( tag: 'agency',key: UniqueKey(),));
          },
        ),

        GoRoute(
          path: workLeadConfiguratore,
          name: 'contatti-newarc-lead',
          pageBuilder: (context, state) {
            return NoTransitionPage(child: WebLeadsView( tag: 'newarc',key: UniqueKey(),));
          },
        ),

        GoRoute(
          path: workRegistrazioniSito,
          name: 'contatti-web-client',
          pageBuilder: (context, state) {
            return NoTransitionPage(child: WebRegistrationsView());
          },
        ),

        GoRoute(
          path: '/immagina/:mode',
          name: 'immagina-view-work',
          pageBuilder: (context, state) {
            final user = context.read<UserProvider>().workUser!;
            final parentMode = state.pathParameters['mode']!;
            final isArchived = parentMode == 'progetti-archiviati';
            final isRequest = parentMode == 'richieste';

            if (isArchived) {
              return NoTransitionPage(
                child: NewarcImmaginaProjectArchiveView(
                  newarcUser: user,
                  projectArguments: {
                    'projectFirebaseId': '',
                    'updateViewCallback': '',
                    'initialFetchProperties': '',
                  },
                  key: Key('progetti-archiviati'),
                ),
              );
            } else {
              if(isRequest){
                return NoTransitionPage(
                  child: NewarcImmagina(
                    newarcUser: user,
                    projectArguments: {
                      'projectFirebaseId': '',
                      'updateViewCallback': '',
                      'initialFetchProperties': '',
                      'isFromRequest': true,
                    },
                    key: Key('richieste'),
                  ),
                );
              }else{
                return NoTransitionPage(
                  child: NewarcImmagina(
                    newarcUser: user,
                    projectArguments: {
                      'projectFirebaseId': '',
                      'updateViewCallback': '',
                      'initialFetchProperties': '',
                      'isFromRequest': false,
                    },
                    key: Key('progetti-attivi'),
                  ),
                );
              }

            }
          },
          routes: [
            GoRoute(
              path: ':childMode/:id',
              name: 'immagina-inside-work',
              pageBuilder: (context, state) {
                final user = context.read<UserProvider>().workUser!;
                final id = state.pathParameters['id']!;
                final mode = state.pathParameters['childMode']!;
                final parentMode = state.pathParameters['mode']!;
                final isArchived = parentMode == 'progetti-archiviati';
                if (mode == "request") {
                  return NoTransitionPage(
                    child: RequestReviewView(
                      projectFirebaseId: id,
                      isFromRequest: true,
                    ),
                  );
                } else {
                  return NoTransitionPage(
                    child: ImmaginaProjectReview(
                      projectFirebaseId: id,
                      isFromRequest: false,
                      isFromProjectArchive: isArchived,
                      newarcUser: user,
                    ),
                  );
                }
              },
            ),
          ],
        ),

        GoRoute(
          path: workPavimenti,
          name: 'pavimenti',
          pageBuilder: (context, state) {
            return NoTransitionPage(child: PavimentiView(
              key: UniqueKey(),
            ));
          },
          routes: [
            GoRoute(
                path: ":id",
                name: 'pavimenti-inside',
                pageBuilder: (context, state) {
                  final id = state.pathParameters['id']!;
                  return NoTransitionPage(child: PavimentiInsideView(
                    key: Key('pavimenti-inside-view'),
                    id: id,
                  ));
                },
            ),
          ]
        ),

        GoRoute(
          path: workPorteInterne,
          name: 'porte-interne',
          pageBuilder: (context, state) {
            return NoTransitionPage(child: PorteInterneView(
              key: UniqueKey(),
            ));
          },
          routes: [
            GoRoute(
                path: ":id",
                name: 'porte-interne-inside',
                pageBuilder: (context, state) {
                  final id = state.pathParameters['id']!;
                  return NoTransitionPage(child: PorteInterneInsideView(
                    key: Key('porte-interne-inside-view'),
                    id: id,
                  ));
                },
            ),
          ]
        ),

        GoRoute(
            path: workRivestimenti,
            name: 'rivestimenti-interne',
            pageBuilder: (context, state) {
              return NoTransitionPage(child: RivestimentiView(
                key: UniqueKey(),
              ));
            },
            routes: [
              GoRoute(
                path: ":id",
                name: 'rivestimenti-interne-inside',
                pageBuilder: (context, state) {
                  final id = state.pathParameters['id']!;
                  return NoTransitionPage(child: RivestimentiInsideView(
                    key: Key('rivestimenti-interne-inside-view'),
                    id: id,
                  ));
                },
              ),
            ]
        ),

        GoRoute(
            path: workTinte,
            name: 'tinte',
            pageBuilder: (context, state) {
              return NoTransitionPage(child: TinteView(
                key: UniqueKey(),
              ));
            },
            routes: [
              GoRoute(
                path: ":id",
                name: 'tinte-inside',
                pageBuilder: (context, state) {
                  final id = state.pathParameters['id']!;
                  return NoTransitionPage(child: TinteInsideView(
                    key: Key('tinte-inside-view'),
                    id: id,
                  ));
                },
              ),
            ]
        ),

        GoRoute(
            path: workAgenzie,
            name: 'agenzie',
            pageBuilder: (context, state) {
              return NoTransitionPage(child: AgenciesView(
                key: UniqueKey(),
              ));
            },
            routes: [
              GoRoute(
                path: ":id",
                name: 'agenzie-inside',
                pageBuilder: (context, state) {
                  final id = state.pathParameters['id']!;
                  return NoTransitionPage(child: AgenciesInsideView(
                    key: Key('agenzie-inside-view'),
                    id: id,
                  ));
                },
              ),
            ]
        ),

        GoRoute(
            path: workPersone,
            name: 'persone',
            pageBuilder: (context, state) {
              return NoTransitionPage(child: PersoneView(
                key: UniqueKey(),
                isArchived: false,
                projectArguments: {
                'current_menu': 'team',
                },
              ));
            },
            routes: [
              GoRoute(
                path: ":id",
                name: 'persone-inside',
                pageBuilder: (context, state) {
                  final id = state.pathParameters['id']!;
                  return NoTransitionPage(child: PersoneInsideView(
                    key: Key('persone-inside-view'),
                    userId: id,
                  ));
                },
              ),
            ]
        ),

        GoRoute(
            path: workDitte,
            name: 'ditte',
            pageBuilder: (context, state) {
              return NoTransitionPage(child: ContractorsView(key: UniqueKey(),));
            },
            routes: [
              GoRoute(
                path: ":id",
                name: 'ditte-inside',
                pageBuilder: (context, state) {
                  final id = state.pathParameters['id']!;
                  return NoTransitionPage(child: ContractorsInsideView(
                    key: Key('ditte-inside-view'),
                    id: id,
                  ));
                },
              ),
            ]
        ),

        GoRoute(
            path: workProfessionals,
            name: 'professionals',
            pageBuilder: (context, state) {
              return NoTransitionPage(child: ProfessionalsView(key: UniqueKey(),));
            },
            routes: [
              GoRoute(
                path: ":id",
                name: 'professionals-inside',
                pageBuilder: (context, state) {
                  final id = state.pathParameters['id']!;
                  return NoTransitionPage(child: ProfessionalsInsideView(
                    key: Key('professionals-inside-view'),
                    id: id,
                  ));
                },
              ),
            ]
        ),

        GoRoute(
            path: workProgettiInCorso,
            name: 'progetti-in-corso',
            pageBuilder: (context, state) {
              final user = context.read<UserProvider>().workUser;
              return NoTransitionPage(child: NewarcActiveProjectsView(
                  key: UniqueKey(),
                  isArchived: false,
                  newarcUser: user!,
                  projectArguments: {
                    'current_menu': "progetti-in-corso",
                    'firebaseId': '',
                    'property': '',
                    'updateViewCallback': '',
                    'initialFetchProperties': '',
                    'isRestoration': false,
                  },
                  ));
            },
            routes: [
              GoRoute(
                path: ":id",
                name: 'progetti-in-corso-inside',
                pageBuilder: (context, state) {
                  final user = context.read<UserProvider>().workUser;
                  final id = state.pathParameters['id']!;
                  return NoTransitionPage(child: NewarcProjectView(
                    key: Key('progetti-in-corso-inside-view'),
                    projectFirebaseId: id,
                    newarcUser: user!,
                  ));
                },
              ),
            ]
        ),


        GoRoute(
          path: workRenovationProject,
          name: 'renovation-project',
          pageBuilder: (context, state) {
            final user = context.read<UserProvider>().workUser;
            return NoTransitionPage(
              child: NewarcActiveProjectsView(
                key: UniqueKey(),
                isArchived: false,
                newarcUser: user!,
                projectArguments: {
                  'current_menu': "renovation-project",
                  'firebaseId': '',
                  'property': '',
                  'updateViewCallback': '',
                  'initialFetchProperties': '',
                  'isRestoration': true,
                },
              ),
            );
          },
          routes: [
            GoRoute(
              path: ":id",
              name: 'renovation-project-inside',
              pageBuilder: (context, state) {
                final user = context.read<UserProvider>().workUser;
                final id = state.pathParameters['id']!;
                return NoTransitionPage(
                  child: NewarcProjectView(
                    key: Key('renovation-project-inside-view'),
                    projectFirebaseId: id,
                    newarcUser: user!,
                  ),
                );
              },
            ),
          ],
        ),

        GoRoute(
          path: workStoricoProgetti,
          name: 'storico-progetti',
          pageBuilder: (context, state) {
            final user = context.read<UserProvider>().workUser;
            return NoTransitionPage(
              child: NewarcActiveProjectsView(
                key: UniqueKey(),
                isArchived: true,
                newarcUser: user!,
                projectArguments: {
                  'current_menu': "storico-progetti",
                  'firebaseId': '',
                  'property': '',
                  'updateViewCallback': '',
                  'initialFetchProperties': '',
                  'isRestoration': false,
                },
              ),
            );
          },
          routes: [
            GoRoute(
              path: ":id",
              name: 'storico-progetti-inside',
              pageBuilder: (context, state) {
                final user = context.read<UserProvider>().workUser;
                final id = state.pathParameters['id']!;
                return NoTransitionPage(
                  child: NewarcProjectView(
                    key: Key('storico-progetti-inside-view'),
                    projectFirebaseId: id,
                    newarcUser: user!,
                  ),
                );
              },
            ),
          ],
        ),

        GoRoute(
          path: workCEprevisionali,
          name: 'ce-previsionali',
          pageBuilder: (context, state) {
            final user = context.read<UserProvider>().workUser;
            return NoTransitionPage(
              child: NewarcCEPView(
                key: UniqueKey(),
                isArchived: false,
                newarcUser: user!,
              ),
            );
          },
          routes: [
            GoRoute(
              path: ":id",
              name: 'ce-previsionali-inside',
              pageBuilder: (context, state) {
                final id = state.pathParameters['id']!;
                return NoTransitionPage(
                  child: ProvisionalEconomicAccount(
                    key: Key('cep-single'),
                    firebaseId: id,
                  ),
                );
              },
            ),
          ],
        ),

        GoRoute(
          path: workGestisciContatti,
          name: 'gestisci-contatti',
          pageBuilder: (context, state) {
            final user = context.read<UserProvider>().workUser;
            return NoTransitionPage(
              key: UniqueKey(),
              child: LeadsView(newarcUser: user!),
            );
          },
          routes: [
            GoRoute(
              path: ":id",
              name: 'gestisci-contatti-inside',
              pageBuilder: (context, state) {
                final id = state.pathParameters['id']!;
                return NoTransitionPage(
                  child: LeadsSingleView(
                  id: id,
                  ),
                );
              },
            ),
          ],
        ),

        GoRoute(
          path: workSegnalazioni,
          name: 'segnalazioni',
          pageBuilder: (context, state) {
            return NoTransitionPage(
              key: UniqueKey(),
              child: SuggestedContactsWorkView(),
            );
          },
        ),

        GoRoute(
          path: workClienti,
          name: 'clienti',
          pageBuilder: (context, state) {
            final user = context.read<UserProvider>().workUser;
            return NoTransitionPage(
              child: RenovationContactsView(newarcUser: user!,key: UniqueKey(),),
            );
          },
        ),

        GoRoute(
          path: workPreventivi,
          name: 'preventivi',
          pageBuilder: (context, state) {
            final user = context.read<UserProvider>().workUser;
            return NoTransitionPage(
              child: RenovationQuotationView(
                  key: UniqueKey(),
                  newarcUser: user!,
                 ),
            );
          },
          routes: [
            GoRoute(
              path: ":id",
              name: 'preventivi-inside',
              pageBuilder: (context, state) {
                final id = state.pathParameters['id']!;
                return NoTransitionPage(
                  child: RenovationQuotationSingle(
                    key: Key('renovation-quotation-single'),
                    id: id,
                  ),
                );
              },
            ),

            GoRoute(
              path: "cme/:id",
              name: 'preventivi-cme-inside',
              pageBuilder: (context, state) {
                final id = state.pathParameters['id']!;
                return NoTransitionPage(
                  child: CMEInformationInsideView(
                    id: id,
                    key: Key('cme-information-inside-view'),
                  ),
                );
              },
            ),

          ],
        ),

        GoRoute(
          path: workAnnunciAttivi,
          name: 'annunci-attivi',
          pageBuilder: (context, state) {
            return NoTransitionPage(
              child: NewarcActiveAds(
                isArchived: false,
                projectArguments: {
                  'current_menu': "active-ads",
                  'firebaseId': '',
                  'property': '',
                  'updateViewCallback': '',
                  'initialFetchProperties': '',
                  'forceDataFetch': true
                },
                key: UniqueKey(),
              ),
            );
          },
          routes: [
            GoRoute(
              path: "/:type/:projectId/:propertyId",
              name: 'annunci-attivi-inside',
              pageBuilder: (context, state) {
                final projectId = state.pathParameters['projectId']!;
                final propertyId = state.pathParameters['propertyId']!;
                final type = state.pathParameters['type']!;
                return NoTransitionPage(
                  child: ActiveAdAdd(
                    key: Key('annunci-attivi-inside'),
                    isInputChangeDetected: [false],
                    wasArchived: false,
                    projectId: projectId,
                    propertyId: propertyId,
                    type: type,
                  ),
                );
              },
            ),
          ],
        ),

        GoRoute(
          path: workAnnunciArchiviati,
          name: 'annunci-archiviati',
          pageBuilder: (context, state) {
            return NoTransitionPage(
              child: NewarcActiveAds(
                isArchived: true,
                projectArguments: {
                  'current_menu': "active-ads",
                  'firebaseId': '',
                  'property': '',
                  'updateViewCallback': '',
                  'initialFetchProperties': '',
                  'forceDataFetch': true
                },
                key: UniqueKey(),
              ),
            );
          },
        ),

      ],
    ),
  ];
}