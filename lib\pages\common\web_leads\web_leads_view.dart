import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:data_table_2/data_table_2.dart';
// import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// import 'package:newarc_platform/classes/acquiredContact.dart';
// import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/property.dart';
// import 'package:newarc_platform/classes/user.dart';
// import 'package:newarc_platform/classes/webLeads.dart';
import 'package:newarc_platform/classes/wpformSubmission.dart';
import 'package:newarc_platform/pages/common/web_leads/web_leads_controller.dart';
import 'package:newarc_platform/pages/common/web_leads/web_leads_data_source.dart';
import 'package:newarc_platform/utils/color_schema.dart';
// import 'package:newarc_platform/utils/common_utils.dart';
import 'package:newarc_platform/widget/UI/filter.dart';
// import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/widget/UI/newarc_data_table.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;

class WebLeadsView extends StatefulWidget {
  //solo robe master si devono vedere
  final String? agencyId;
  final String? tag;

  const WebLeadsView({
    Key? key,
    this.agencyId = '',
    this.tag = 'agency'
  }) : super(key: key);

  @override
  State<WebLeadsView> createState() => _WebLeadsViewState();
}

class _WebLeadsViewState extends State<WebLeadsView> {
  final controller = Get.put<WebLeadsController>(WebLeadsController());
  Key? paddingKey;

  @override
  void initState() {
    controller.clearFilter();
    controller.selectedStatus = controller.testList.first;
    initialFetchContacts();
    
    // getAgencies();
    // getNewarcUser();

    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @protected
  void didUpdateWidget(WebLeadsView oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if( oldWidget.tag != widget.tag ) {
      controller.clearFilter();
      controller.selectedStatus = controller.testList.first;
      initialFetchContacts();
    }

  }

  Widget dataTablePagination() {
    return Visibility(
      visible: controller.currentlyShowing.isNotEmpty,
      child: Padding(
        key: paddingKey,
        padding: EdgeInsets.symmetric(vertical: 10),
        child: IconTheme.merge(
          data: const IconThemeData(opacity: 0.54),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                "${controller.currentlyShowing} di  ${controller.totalRecords.toString()}",
                style: TextStyle(
                  fontFamily: '',
                  fontSize: 12.0,
                  color: Colors.black.withOpacity(0.54),
                ),
              ),
              SizedBox(width: 32.0),
              IconButton(
                icon: const Icon(Icons.chevron_left),
                onPressed: () {
                  if (controller.disablePreviousButton == true) return;
                  if (controller.loadingContacts.value == true) return;
                  fetchPrevContacts();
                },
                padding: EdgeInsets.zero,
              ),
              SizedBox(width: 24.0),
              IconButton(
                icon: const Icon(Icons.chevron_right),
                padding: EdgeInsets.zero,
                onPressed: () {
                  if (controller.disableNextButton == true) return;
                  if (controller.loadingContacts.value == true) return;

                  fetchNextContacts();
                },
              ),
              SizedBox(width: 14.0),

              // TextButton(
              //   child: Icon(Icons.refresh, size: 20, color: disableNextButton ? Colors.grey : Colors.black),
              //   onPressed: () {
              //     cacheFirestore.clear();
              //     initialFetchContacts();
              //   },
              //   style: ButtonStyle(overlayColor: MaterialStateProperty.all(Colors.transparent)),
              // )
            ],
          ),
        ),
      ),
    );
  }

  Future<void> initialFetchContacts({bool force = false, bool reloadAll = false}) async {
    
    // if (controller.contacts.isNotEmpty && !force && !reloadAll) return;

    //TODO Rimuovere la collectionSnapshotCounterQuery, ha duplicato tutto555
    controller.pageCounter = 1;

    setState(() {
      controller.contacts = [];
      controller.loadingContacts.value = true;
    });

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshotCounter;
      Query<Map<String, dynamic>> collectionSnapshotCounterQuery;

      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_WP_FORM_SUBMISSION);
      collectionSnapshotCounterQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_WP_FORM_SUBMISSION);

      collectionSnapshotQuery = collectionSnapshotQuery.where('tag', isEqualTo: widget.tag );
      collectionSnapshotCounterQuery = collectionSnapshotCounterQuery.where('tag', isEqualTo: widget.tag );

      if( widget.agencyId != '' ) {
        collectionSnapshotQuery = collectionSnapshotQuery.where('agencyId', isEqualTo: widget.agencyId );
        collectionSnapshotCounterQuery = collectionSnapshotCounterQuery.where('agencyId', isEqualTo: widget.agencyId );
      }

      //print({'master filter', filters.length, filters});
      if (controller.filters.length > 0) {
        for (var i = 0; i < controller.filters.length; i++) {
          // collectionSnapshotQuery = collectionSnapshotQuery
          //     .where(filters[i]['field'], isEqualTo: filters[i]['value']);
          //collectionSnapshotCounterQuery = collectionSnapshotCounterQuery
          //    .where(filters[i]['field'], isEqualTo: filters[i]['value']);
          if (controller.filters[i]['search'] == 'equal') {
            collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
            collectionSnapshotCounterQuery = collectionSnapshotCounterQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          } else if (controller.filters[i]['search'] == 'like') {
            collectionSnapshotQuery = collectionSnapshotQuery
                .where(controller.filters[i]['field'], isGreaterThanOrEqualTo: controller.filters[i]['value'])
                .where(controller.filters[i]['field'], isLessThanOrEqualTo: controller.filters[i]['value'] + "\uf7ff");
            collectionSnapshotCounterQuery
                .where(controller.filters[i]['field'], isGreaterThanOrEqualTo: controller.filters[i]['value'])
                .where(controller.filters[i]['field'], isLessThanOrEqualTo: controller.filters[i]['value'] + "\uf7ff");
          }
        }
      }


      if (reloadAll) {
        collectionSnapshot = await collectionSnapshotQuery.get();
      } else {
        collectionSnapshot = await collectionSnapshotQuery.orderBy('insertTimestamp', descending: true).limit(controller.recordsPerPage).get();
      }
      //await collectionSnapshotQuery.limit(recordsPerPage).get();

      collectionSnapshotCounter = await collectionSnapshotCounterQuery.get();

      //totalRecords = collectionSnapshot.docs.length;
      controller.totalRecords = collectionSnapshotCounter.docs.length;

      controller.totalPages = (controller.totalRecords / controller.recordsPerPage).ceil();

      if (controller.totalRecords > controller.recordsPerPage) {
        controller.currentlyShowing = '1-' + controller.recordsPerPage.toString();
      } else {
        controller.currentlyShowing = '1-' + controller.recordsPerPage.toString();
      }

      controller.documentList = collectionSnapshot.docs;

      await generateContacts(collectionSnapshot);

      setState(() {
        controller.loadingContacts.value = false;
      });
    } catch (e,s) {
      setState(() {
        controller.loadingContacts.value = false;
      });
      print({e,s });
    }
  }

  fetchNextContacts() async {
    setState(() {
      controller.loadingContacts.value = true;
    });

    controller.pageCounter++;

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;
      int indexOfSnapshot = isRecordExists(controller.pageCounter);

      if (indexOfSnapshot > -1) {
        collectionSnapshot = controller.cacheFirestore[indexOfSnapshot]['snapshot'];
      } else {
        collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_WP_FORM_SUBMISSION);
        collectionSnapshotQuery = collectionSnapshotQuery.where('tag', isEqualTo: widget.tag );
      

        if( widget.agencyId != '' ) {
          collectionSnapshotQuery = collectionSnapshotQuery.where('agencyId', isEqualTo: widget.agencyId);
        }

        if (controller.filters.length > 0) {
          for (var i = 0; i < controller.filters.length; i++) {
            collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }

        collectionSnapshot = await collectionSnapshotQuery
            .orderBy('insertTimestamp', descending: true)
            .limit(controller.recordsPerPage)
            .startAfterDocument(controller.documentList[controller.documentList.length - 1])
            .get();
        // collectionSnapshot = await collectionSnapshotQuery.limit(recordsPerPage).startAfterDocument(documentList[documentList.length - 1]).get();
      }

      // List<DocumentSnapshot> newDocumentList = collectionSnapshot.docs;
      controller.documentList = collectionSnapshot.docs;

      // documentList.addAll(newDocumentList);

      generateContacts(collectionSnapshot);
    } catch (e) {
      setState(() {
        controller.loadingContacts.value = false;
      });
      // print(e.toString());
      // movieController.sink.addError(e);
    }
  }

  fetchPrevContacts() async {
    setState(() {
      controller.loadingContacts.value = true;
    });

    controller.pageCounter--;

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      int indexOfSnapshot = isRecordExists(controller.pageCounter);
      if (indexOfSnapshot > -1) {
        collectionSnapshot = controller.cacheFirestore[indexOfSnapshot]['snapshot'];
      } else {
        collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_WP_FORM_SUBMISSION);
        collectionSnapshotQuery = collectionSnapshotQuery.where('tag', isEqualTo: widget.tag );

        if( widget.agencyId != '' ) {
          collectionSnapshotQuery = collectionSnapshotQuery.where('agencyId', isEqualTo: widget.agencyId);
        }

        if (controller.filters.length > 0) {
          for (var i = 0; i < controller.filters.length; i++) {
            collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }

        collectionSnapshot = await collectionSnapshotQuery
            .orderBy('insertTimestamp', descending: true)
            .limit(controller.recordsPerPage)
            .endBeforeDocument(controller.documentList[controller.documentList.length - 1])
            .get();
        // collectionSnapshot = await collectionSnapshotQuery.limit(recordsPerPage).endBeforeDocument(documentList[documentList.length - 1]).get();

        // List<DocumentSnapshot> newDocumentList = collectionSnapshot.docs;
      }

      // updateIndicator(true);

      // documentList.addAll(newDocumentList);
      controller.documentList = collectionSnapshot.docs;
      generateContacts(collectionSnapshot);
    } catch (e) {
      setState(() {
        controller.loadingContacts.value = false;
      });
    }
  }

  int isRecordExists(int pageCounter) {
    String filtersKey = controller.filters.map((f) => '${f['field']}:${f['value']}').join('|');
    return controller.cacheFirestore.indexWhere(
      (record) => record['key'] == pageCounter && record['filtersKey'] == filtersKey,
    );
  }

  generateContacts(collectionSnapshot) async {
    // If a record already doesn't exists then store
    // if (isRecordExists(pageCounter) < 0) {
    //   cacheFirestore.add({'key': pageCounter, 'snapshot': collectionSnapshot});
    // }
    if (isRecordExists(controller.pageCounter) < 0) {
      String filtersKey = controller.filters.map((f) => '${f['field']}:${f['value']}').join('|');
      controller.cacheFirestore.add({
        'key': controller.pageCounter,
        'snapshot': collectionSnapshot,
        'filtersKey': filtersKey,
      });
    }
    if (controller.pageCounter == 1) {
      controller.disablePreviousButton = true;
    } else {
      controller.disablePreviousButton = false;
    }

    if (controller.pageCounter == controller.totalPages) {
      controller.disableNextButton = true;
    } else {
      controller.disableNextButton = false;
    }

    List<WpFormsSubmission> _contacts = [];
    for (var element in collectionSnapshot.docs) {
      try {

        var _tmp = WpFormsSubmission.fromDocument(element.data(), element.id);

        if( _tmp.projectId!.length > 0 ) {
          
          for (var i = 0; i < _tmp.newarcHomesId!.length; i++) {
            var _tmp2 = WpFormsSubmission.fromDocument(element.data(), element.id);
            if ( _tmp.newarcHomesId![i] != null && _tmp.newarcHomesId![i] != '') {
              DocumentSnapshot<Map<String, dynamic>> collectionSnapshot;
              collectionSnapshot = await FirebaseFirestore.instance.collection(appConfig.COLLECT_NEWARC_HOME).doc(_tmp.newarcHomesId![i]).get();

              _tmp2.adData = Property.fromDocument(collectionSnapshot);
              
              _contacts.add(_tmp2);

            }
          }
        }

        /*if (_tmp.adId != null && _tmp.adId != '') {
          DocumentSnapshot<Map<String, dynamic>> collectionSnapshot;
          collectionSnapshot = await FirebaseFirestore.instance.collection(appConfig.COLLECT_NEWARC_HOME).doc(_tmp.adId).get();

          _tmp.adData = Property.fromDocument(collectionSnapshot);
          // contacts[contacts.indexOf(contact)].agencyUser = Agency.fromDocument(collectionSnapshot.data()!
          // , collectionSnapshot.id);

          // print(contact.agencyUser);
        }*/

        
      } catch (e) {
        // print("error in document ${element.id}");
        // print(e);
      }
    }

    // _contacts.sort((a, b) => b.insertTimestamp!.compareTo(a.insertTimestamp!));

    int lastRecordNumber = controller.pageCounter * controller.recordsPerPage;
    if (_contacts.length == controller.recordsPerPage) {
      controller.currentlyShowing = (lastRecordNumber - (controller.recordsPerPage - 1)).toString() + '-' + lastRecordNumber.toString();
    } else if (_contacts.length > 0 && _contacts.length < controller.recordsPerPage) {
      int prevLastRecordNumber = (controller.pageCounter - 1) * controller.recordsPerPage;

      controller.currentlyShowing = (lastRecordNumber - (controller.recordsPerPage - 1)).toString() + '-' + (prevLastRecordNumber + _contacts.length).toString();
    }

    setState(() {
      controller.contacts = _contacts;
      controller.displayContacts = _contacts;
      controller.loadingContacts.value = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    double pageWidth = MediaQuery.of(context).size.width - 260;
    if (pageWidth < 1800) {
      pageWidth = 1800;
    }

    return LayoutBuilder(builder: (context, constraints) {
      return Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              NarFormLabelWidget(
                label: widget.tag == 'agency' ?  'Lead acquisto' : 'Lead configuratore',
                fontSize: 19,

                fontWeight: '700',
                textColor: Colors.black,
              ),
            ],
          ),
          _filter(),
          SizedBox(height: 10),
          Container(
            height: constraints.maxHeight / 1.2,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: AppColor.white,
              border: Border.all(width: 1.5, color: AppColor.borderColor),
            ),
            child: Column(
              children: [
                Expanded(
                  child: Stack(
                    children: [
                      Opacity(
                        opacity: controller.loadingContacts.value ? 0.5 : 1,
                        child: NewarcDataTable(
                          rowsPerPage: 20,
                          hidePaginator: true,
                          onPageChanged: (val) {
                            // print("page : $val"); 
                          },
                          columns: [
                            DataColumn2(
                              label: Text(
                                'Indirizzo',
                              ),
                              size: ColumnSize.L,
                            ),
                            DataColumn2(
                              size: ColumnSize.M,
                              label: Text(
                                'Nome e Cognome',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.M,
                              label: Text(
                                'Telefono',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.L,
                              label: Text(
                                'Email',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.S,
                              label: Text(
                                'Data',
                              ),
                            ),
                            if( widget.tag == 'agency' ) DataColumn2(
                              size: ColumnSize.S,
                              label: Text(
                                'Stato',
                              ),
                            ),
                          ],
                          isHasDecoration: false,
                          source: WebLeadsDataSource(
                            agencyId: widget.agencyId,
                            query: controller.query,
                            displayContacts: controller.displayContacts,
                            context: context,
                            initialFetchContacts: initialFetchContacts,
                            selectedStatus: controller.selectedStatus,
                            newarcUser: controller.newarcUser,
                            tag: widget.tag
                          ),
                        ),
                      ),
                      if (controller.loadingContacts.value)
                        Positioned.fill(
                          child: Center(
                            child: CircularProgressIndicator(
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                Align(
                  alignment: Alignment.centerRight,
                  child: dataTablePagination(),
                ),
              ],
            ),
          ),
        ],
      );
    });
  }

  NarFilter _filter() {
    return NarFilter(
      showSearchInput: true,
      searchTextEditingControllers: controller.searchTextController,
      onChanged: (String? searchQuery)async{
        if(searchQuery?.isNotEmpty ?? false){
          if (searchQuery != null && searchQuery.isNotEmpty) {
            List<WpFormsSubmission> filtered = controller.contacts.where((contact) {
              final name = contact.basePersonInfo!.name?.toLowerCase() ?? "";
              final lastname = contact.basePersonInfo!.surname?.toLowerCase() ?? "";
              final address = contact.adData?.addressInfo?.toFullAddress().toString().toLowerCase() ?? "";
              
              return name.contains(searchQuery.toLowerCase()) ||
              lastname.contains(searchQuery.toLowerCase()) ||
                  address.contains(searchQuery.toLowerCase());
            }).toList();

            setState(() {
              controller.displayContacts = filtered;
            });
          }
        }else{
          await initialFetchContacts(force: true);
          setState(() {
            controller.displayContacts = controller.contacts;
          });
        }
      },
      suffixIconOnTap: ()async{
        await initialFetchContacts(force: true,reloadAll: true);
        if(controller.searchTextController.text.trim().isNotEmpty){
          List<WpFormsSubmission> filtered = controller.contacts.where((contact) {
            final name = contact.basePersonInfo!.name?.toLowerCase() ?? "";
            final surname = contact.basePersonInfo!.name?.toLowerCase() ?? "";
            final address = contact.adData?.addressInfo?.toFullAddress().toString().toLowerCase() ?? "";
            // final address = '';
            return name.contains(controller.searchTextController.text.toLowerCase()) ||
            surname.contains(controller.searchTextController.text.toLowerCase()) ||
                address.contains(controller.searchTextController.text.toLowerCase());
          }).toList();

          setState(() {
            controller.displayContacts = filtered;
          });
        }else{
          await initialFetchContacts(force: true);
          setState(() {
            controller.displayContacts = controller.contacts;
          });
        }
      },
      searchHintText: "Cerca per indirizzo o per nome...",
      selectedFilters: [controller.agencyFilter, controller.cityFilter, controller.newarcTypeFilter],
      textEditingControllers: [controller.agencyFilterController, controller.cityFilterController, controller.newarcTypeFilterController],
      isFilterHide: widget.tag == 'newarc' ? true : false,
      filterFields: [
        {
          'Status': NarSelectBoxWidget(
            options: ["Contattato","Interessato","Non interessato","Da contattare","Acquirente"],
            controller: controller.newarcTypeFilterController,
            onChanged: (value) {
              if (controller.newarcTypeFilterController.text == 'Status') {
                controller.filters.removeWhere((element) {
                  return element['field'] == 'status';
                });
              } else {
                String status = controller.newarcTypeFilterController.text;

                controller.filters.removeWhere((element) {
                  return element['field'] == 'status';
                });

                controller.filters.add({'field': 'status', 'value': status, 'search': 'equal'});
              }
              controller.newarcTypeFilter = controller.newarcTypeFilterController.text;

              setState(() {
                controller.cacheFirestore.clear();
                initialFetchContacts(force: true);
              });
            },
          ),
        }
      ],
      onSubmit: () async {
        await initialFetchContacts(force: true);
      },
      onReset: () async {
        controller.clearFilter();
        controller.filters.clear();
        await initialFetchContacts(force: true);
      },
    );
  }

  //Why not a stream?
  Future getCommentCount(String firebaseId) async {
    QuerySnapshot<Map<String, dynamic>>? collectionSnapshot;
    collectionSnapshot = await FirebaseFirestore.instance.collection(appConfig.COLLECT_VALUATOR_SUBMISSION_COMMENTS).where('contactId', isEqualTo: firebaseId).orderBy('date', descending: true).get();

    return collectionSnapshot.docs.length;
  }

}