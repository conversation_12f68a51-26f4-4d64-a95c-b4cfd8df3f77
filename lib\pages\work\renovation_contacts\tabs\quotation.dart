import 'dart:async';

import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/classes/renovationContact.dart';
import 'package:newarc_platform/classes/renovationContactAddress.dart';
import 'package:newarc_platform/classes/renovationQuotation.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/tab/dropdown_popup_menu.dart';
import 'package:newarc_platform/widget/UI/tab/status_widget.dart';


class RQQuotation extends StatefulWidget {
  final RenovationContact? renovationConatct;
  final Function? updateContact;
  
  const RQQuotation({Key? key, this.renovationConatct, this.updateContact})
      : super(key: key);

  @override
  State<RQQuotation> createState() => _RQQuotationState();
}

class _RQQuotationState extends State<RQQuotation> {
  bool loading = false;
  List<RenovationQuotation> quotations = [];
  bool loadingQuotations = false;
  String progressMessage = '';

  List<Map> status = [
    {
      'value': 'in-attesa',
      'label': 'In Attesa',
      'bgColor': Color(0xffD4D4D4),
      'textColor': Colors.black
    },
    {
      'value': 'accettato',
      'label': 'Accettato',
      'bgColor': Color(0xff39C14F),
      'textColor': Colors.white
    },
    {
      'value': 'rifiutato',
      'label': 'Rifiutato',
      'bgColor': Color(0xffDD0000),
      'textColor': Colors.white
    },
    {
      'value': 'da-modificare',
      'label': 'Da modificare',
      'bgColor': Color(0xffF2CB00),
      'textColor': Colors.white
    }
  ];



  @override
  void initState() {
    super.initState();
    initialFetchContacts();
    
  }

  @protected
  void didUpdateWidget(RQQuotation oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    super.dispose();
  }

  setInitialValues() {

  }

  getStatusTag(status ) {
    
    switch (status) {
      case 'in-attesa':
        return {
          'value': 'in-attesa',
          'label': 'In Attesa',
          'bgColor': Color(0xffD4D4D4),
          'textColor': Colors.black
        };

      case 'accettato':
        return {
          'value': 'accettato',
          'label': 'Accettato',
          'bgColor': Color(0xff39C14F),
          'textColor': Colors.white
        };
      
      case 'rifiutato':
        return {
          'value': 'rifiutato',
          'label': 'Rifiutato',
          'bgColor': Color(0xffDD0000),
          'textColor': Colors.white
        };
      
      case 'rifiutato':
        return {
          'value': 'da-modificare',
          'label': 'Da modificare',
          'bgColor': Color(0xffF2CB00),
          'textColor': Colors.white
        };
        
    }
    
  }

  Future<void> initialFetchContacts() async {
    
    
    setState(() {
      quotations.clear();
      loadingQuotations = true;
    });

    try {

      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_RENOVATION_QUOTATION)
      .where('isArchived', isEqualTo: false )
      .where('renovationContactId', isEqualTo: widget.renovationConatct!.id )
      .orderBy('created', descending: true);
      collectionSnapshot = await collectionSnapshotQuery.get();
      
      await generateQuotationRow(collectionSnapshot);
        
      setState(() {
        loadingQuotations = false;
      });

    } catch (e, s) {
      setState(() {
        loadingQuotations = false;
      });
      print({'Following error',e, s});
    }
  }

  generateQuotationRow(collectionSnapshot) async {
    List<RenovationQuotation> _quotation = [];
    
    for (var element in collectionSnapshot.docs) {
      try {
        var _tmp = RenovationQuotation.fromDocument(element.data(), element.id);

        _tmp.renovationContact = RenovationContact.empty();
        _tmp.renovationContactAddress = RenovationContactAddress.empty();
        _tmp.architect = NewarcUser.empty();
        
        quotations.add(_tmp);

      } catch (e, s) {
        print({e, s});
      }
    }

    
    
  }

  Future<NewarcUser> _getArchitectName(int quotationIndex) async {
    

    RenovationContact renovContact =  await getRenovationContact( quotations[quotationIndex].renovationContactId! );
    try {

      String userId = renovContact.assignedRenovatorId!;

      DocumentSnapshot<Map<String, dynamic>> collectionSnapshot = 
      await FirebaseFirestore.instance.collection(appConfig.COLLECT_USERS)
      .doc(userId).get();

      NewarcUser user = NewarcUser.fromDocument(collectionSnapshot.data()!, collectionSnapshot.id );

      

      // return "${user.firstName} ${user.lastName}";
      String profileUrl = await printUrl('users/', user.id, user.profilePicture);
      user.profilePicture = profileUrl;
      
      quotations[quotationIndex].architect = user;

      return user;
      
    } catch (error) {

      return NewarcUser.empty();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: double.infinity,
      child: loading == true
          ? NarFormLabelWidget(label: 'Loading')
          : Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: [
                Expanded(
                  child: ListView(
                    children: [
                      SizedBox(height: 20),
                      NarFormLabelWidget(
                        label: 'Preventivi',
                        fontSize: 20,
                        fontWeight: 'bold',
                      ),
                      SizedBox(height: 30),
                      
                      ...quotations.map((quote) {
                              
                            int quotationIndex = quotations.indexWhere((q) => quote.id == q.id ); 
                            
                            return Container(
                              decoration: BoxDecoration(
                                border: Border(
                                  bottom: BorderSide(
                                    color: Color(0xffEAEAEA),
                                    width: 1
                                  )
                                )
                              ),
                              child: DataTable(
                                
                                border: TableBorder(
                                  horizontalInside: BorderSide.none, // remove between rows
                                  bottom: BorderSide.none,           // remove bottom border
                                ),
                                dividerThickness: 0.00000001,
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.transparent)
                                ),
                                horizontalMargin: 0,
                                columns: [
                              
                                  DataColumn(
                                    label: NarFormLabelWidget(
                                      label: "Indirizzo",
                                      fontSize: 12,
                                      letterSpacing: 0.2,
                                      textColor: Color(0xff838383),
                                      fontWeight: "600",
                                    ),
                                  ),
                                  DataColumn(
                                    label: NarFormLabelWidget(
                                      label: "Architetto",
                                      fontSize: 12,
                                      letterSpacing: 0.2,
                                      textColor: Color(0xff838383),
                                      fontWeight: "600",
                                    ),
                                  ),
                                  DataColumn(
                                    label: NarFormLabelWidget(
                                      label: "Stato",
                                      fontSize: 12,
                                      letterSpacing: 0.2,
                                      textColor: Color(0xff838383),
                                      fontWeight: "600",
                                    ),
                                  ),
                                  DataColumn(
                                    headingRowAlignment: MainAxisAlignment.end,
                                    label: Container(
                                      clipBehavior: Clip.none,
                                      child: NarFormLabelWidget(
                                        label: "Azioni",
                                        fontSize: 12,
                                        letterSpacing: 0.2,
                                        textColor: Color(0xff838383),
                                        fontWeight: "600",
                                      ),
                                    ),
                                  ),
                                ], 
                                rows: [
                                  DataRow(
                                    
                                    cells: [
                                    
                                      DataCell(

                                        quotations[quotationIndex].renovationContactAddress!.id == ''
                                        ? FutureBuilder<RenovationContactAddress>(
                                          future: getRenovationContactAddress(quote.renovationContactAddressId!),
                                          builder: (context, snapshot) {
                                            if( snapshot.connectionState == ConnectionState.waiting || snapshot.hasError || !snapshot.hasData || snapshot.data!.id == '' ){
                                              return NarFormLabelWidget(
                                                label: "",
                                                fontSize: 12,
                                                letterSpacing: 0.2,
                                                textColor: Colors.black,
                                              );
                                            }
                                            quotations[quotationIndex].renovationContactAddress = snapshot.data;
                                            // notifyListeners();
                                            return NarFormLabelWidget(
                                              label: snapshot.data!.addressInfo!.toShortAddress(),
                                              fontSize: 12,
                                              letterSpacing: 0.2,
                                              textColor: Colors.black,
                                            );
                                          }
                                        )
                                        : NarFormLabelWidget(
                                          label: quote.renovationContactAddress!.addressInfo!.toShortAddress() ,
                                          fontSize: 12,
                                          letterSpacing: 0.2,
                                          textColor: Colors.black,
                                        ),
                                      ),
                                      DataCell(
                                        quotations[quotationIndex].architect!.id == ''
                                        ? FutureBuilder<NewarcUser>(
                                          future: _getArchitectName(quotationIndex),
                                          builder: (context, snapshot) {
                                            if (snapshot.hasData) {
                                              
                                              quotations[quotationIndex].architect = snapshot.data;
                                              return Row(
                                                children: [
                                                  Container(
                                                    width: 40,
                                                    height: 40,
                                                    decoration: BoxDecoration(
                                                      shape: BoxShape.circle,
                                                    ),
                                                    child: ClipOval(
                                                      child: Image(
                                                        image: NetworkImage(snapshot.data!.profilePicture!),
                                                        fit: BoxFit.cover,
                                                      ),
                                                    ),
                                                  ),
                                                  SizedBox(width: 5,),
                                                  NarFormLabelWidget(
                                                    label: "${snapshot.data!.firstName} ${snapshot.data!.lastName}",
                                                    fontSize: 12,
                                                    fontWeight: '600',
                                                    textAlign: TextAlign.start,
                                                    textColor: Colors.black,
                                                  ),
                                                ],
                                              );
                                            }
                                            return Container(
                                              width: 30,
                                              height: 30,
                                              decoration: BoxDecoration(
                                                borderRadius: BorderRadius.circular(100),
                                                color: Colors.grey,
                                              ),
                                              child: Text(''),
                                            );
                                          },
                                        )
                                        : Row(
                                          children: [
                                            Container(
                                              width: 40,
                                              height: 40,
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                              ),
                                              child: ClipOval(
                                                child: Image(
                                                  image: NetworkImage(quotations[quotationIndex].architect!.profilePicture!),
                                                  fit: BoxFit.cover,
                                                ),
                                              ),
                                            ),
                                            SizedBox(width: 5,),
                                            NarFormLabelWidget(
                                              label: "${quotations[quotationIndex].architect!.firstName} ${quotations[quotationIndex].architect!.lastName}",
                                              fontSize: 12,
                                              fontWeight: '600',
                                              textAlign: TextAlign.start,
                                              textColor: Colors.black,
                                            ),
                                          ],
                                        ),
                                      ),
                                      DataCell(
                                        Row(
                                          children: [
                                            StatusWidget(
                                              statusColor: getStatusTag( quote.status! )['bgColor'],
                                            ),
                                            SizedBox(width: 5,),
                                            NarFormLabelWidget(
                                              label: getStatusTag( quote.status! )['label'],
                                              fontSize: 12,
                                              fontWeight: '600',
                                              textAlign: TextAlign.start,
                                              textColor: Colors.black,
                                            ),
                                          ],
                                        ),
                                      ),
                                      DataCell(

                                        Row(
                                          mainAxisAlignment: MainAxisAlignment.end,
                                          mainAxisSize: MainAxisSize.max,
                                          children: [
                                            MouseRegion(
                                              cursor: SystemMouseCursors.click,
                                              child: GestureDetector(
                                                onTap: (){
                                                },
                                                child: DropdownPopupMenu(
                                                  children: [
                                                    {
                                                      'value': {
                                                        'value 1': 'value 1'
                                                      },
                                                      'widget': Row(
                                                        children: [NarFormLabelWidget(label: 'hello World!')],
                                                      )
                                                    },
                                                    {
                                                      'value': {
                                                        'value 1': 'value 1'
                                                      },
                                                      'widget': Row(
                                                        children: [NarFormLabelWidget(label: 'How are you doing?')],
                                                      )
                                                    }
                                                  ],
                                                )
                                                
                                                // Container(
                                                //   height: 27,
                                                //   width: 27,
                                                //   decoration: BoxDecoration(
                                                //     color: Color(0xffEAEAEA),
                                                //     borderRadius: BorderRadius.circular(7)
                                                //   ),
                                                //   child: Center(
                                                //     child: Transform.rotate(
                                                //       angle: 90 * 3.1415926535 / 180,
                                                //       child: SvgPicture.asset(
                                                //         "assets/icons/three_dot.svg",
                                                //         color: Color(0xffB4B4B4),
                                                //       ),
                                                //     ),
                                                //   ),
                                                // ),
                                              ),
                                            ),
                                          ],
                                        )
                                      ),
                                    ]
                                  )
                                ]
                                
                                
                              ),
                            );
                          }).toList(),

                      loadingQuotations 
                      ? Column(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Center(
                            child: CircularProgressIndicator( color: Theme.of(context).primaryColor ),
                          ),
                        ],
                      )
                      : SizedBox(height: 0,)
                      
                    ],
                  ),
                ),
                
              ],
            ),
    );
  }
}
