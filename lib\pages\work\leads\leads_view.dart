import 'dart:convert';
import 'dart:math';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:crypto/crypto.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/lead.dart';
import 'package:newarc_platform/classes/renovationContact.dart';
import 'package:newarc_platform/classes/renovationContactAddress.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/pages/work/leads/leads_controller.dart';
import 'package:newarc_platform/routes/work_routes.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/firestore.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/dropdown_search.dart';
import 'package:newarc_platform/widget/UI/filter.dart';
import 'package:newarc_platform/widget/UI/image-select-box.dart';
import 'package:newarc_platform/widget/UI/newarc_data_table.dart';
import 'package:newarc_platform/widget/UI/tab/tag_widget.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:firebase_storage/firebase_storage.dart';


class LeadsView extends StatefulWidget {
  //solo robe master si devono vedere
  final NewarcUser newarcUser;


  const LeadsView({
    Key? key,
    required this.newarcUser,
  }) : super(key: key);

  @override
  State<LeadsView> createState() => _LeadsViewState();
}

class _LeadsViewState extends State<LeadsView> {
  final controller = Get.put<LeadsController>(LeadsController());
  Key? paddingKey;

  @override
  void initState() {
  
    controller.filters.clear();
    controller.referenceFilterController.clear();
    controller.referenceFilter = '';
    initialFetchContacts();
    fetchRenovators();
    super.initState();
  }

  Widget dataTablePagination() {
    return Visibility(
      visible: controller.currentlyShowing.isNotEmpty,
      child: Padding(
        key: paddingKey,
        padding: EdgeInsets.symmetric(vertical: 10),
        child: IconTheme.merge(
          data: const IconThemeData(opacity: 0.54),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                "${controller.currentlyShowing} di  ${controller.totalRecords.toString()}",
                style: TextStyle(
                  fontFamily: '',
                  fontSize: 12.0,
                  color: Colors.black.withOpacity(0.54),
                ),
              ),
              SizedBox(width: 32.0),
              IconButton(
                icon: const Icon(Icons.chevron_left),
                onPressed: () {
                  if (controller.disablePreviousButton == true) return;
                  if (controller.loadingContacts == true) return;

                  fetchPrevContacts();
                },
                padding: EdgeInsets.zero,
              ),
              SizedBox(width: 24.0),
              IconButton(
                icon: const Icon(Icons.chevron_right),
                padding: EdgeInsets.zero,
                onPressed: () {
                  if (controller.disableNextButton == true) return;
                  if (controller.loadingContacts == true) return;
                  fetchNextContacts();
                },
              ),
              SizedBox(width: 14.0),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> initialFetchContacts({bool force = false,bool reloadAll = false}) async {
    
    controller.pageCounter = 1;

    if( force ) {
      controller.cacheFirestore.clear();
    }

    setState(() {
      controller.leads = [];
      controller.loadingContacts = true;
    });

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshotCounter;
      Query<Map<String, dynamic>> collectionSnapshotCounterQuery;

      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance
      .collection(appConfig.COLLECT_USERS)
      .where('source', whereIn: ['web', 'valuation', 'wordpress', 'direct'] )
      .orderBy('insertTimestamp', descending: true);
      

      collectionSnapshotCounterQuery = FirebaseFirestore.instance
      .collection(appConfig.COLLECT_USERS)
      .where('source', whereIn: ['web', 'valuation', 'wordpress', 'direct'] )
      .orderBy('insertTimestamp', descending: true);

      if( !reloadAll ) {
        collectionSnapshotQuery = collectionSnapshotQuery.limit(controller.recordsPerPage);
      }

      
      if (controller.filters.length > 0) {
        
        for (var i = 0; i < controller.filters.length; i++) {
          if( controller.filters[i]['value'] != '' ) {
            if( controller.filters[i]['value'] == 'web' ) {
              collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'],  whereIn: ['web', 'wordpress', 'valuation']);
              collectionSnapshotCounterQuery = collectionSnapshotCounterQuery.where(controller.filters[i]['field'], whereIn: ['web', 'wordpress', 'valuation']);
            } else {
              collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
              collectionSnapshotCounterQuery = collectionSnapshotCounterQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
            }
          }
          
        }
      }

      collectionSnapshot = await collectionSnapshotQuery.get();
      collectionSnapshotCounter = await collectionSnapshotCounterQuery.get();

      controller.totalRecords = collectionSnapshotCounter.docs.length;
      controller.totalPages = (controller.totalRecords / controller.recordsPerPage).ceil();

      if (controller.totalRecords > controller.recordsPerPage) {
        controller.currentlyShowing = '1-' + controller.recordsPerPage.toString();
      } else {
        controller.currentlyShowing = '1-' + controller.recordsPerPage.toString();
      }

      controller.documentList = collectionSnapshot.docs;

      print({ 'collectionSnapshot.docs', collectionSnapshot.docs.length});
      
      await generateContacts(collectionSnapshot);

      setState(() {
        controller.loadingContacts = false;
      });
    } catch (e,s) {
      print({e.toString(), s});
      setState(() {
        controller.loadingContacts = false;
      });
      
    }
  }

  fetchNextContacts() async {
    setState(() {
      controller.loadingContacts = true;
    });

    controller.pageCounter++;

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;
      int indexOfSnapshot = isRecordExists(controller.pageCounter);

      if (indexOfSnapshot > -1) {
        collectionSnapshot = controller.cacheFirestore[indexOfSnapshot]['snapshot'];
      } else {
        collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_USERS)
        .where('source', whereIn: ['web', 'valuation', 'wordpress', 'direct'] )
        .orderBy('insertTimestamp', descending: true)
        .limit(controller.recordsPerPage);

        if (controller.filters.length > 0) {
        
          for (var i = 0; i < controller.filters.length; i++) {

            if( controller.filters[i]['value'] != '' ) {
              if( controller.filters[i]['value'] == 'web' ) {
                collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'],  whereIn: ['web', 'wordpress', 'valuation']);
                
              } else {
                collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
                
              }
            }
            
            
          }
        }

        collectionSnapshot = await collectionSnapshotQuery.limit(controller.recordsPerPage).startAfterDocument(controller.documentList[controller.documentList.length - 1]).get();
      }

      controller.documentList = collectionSnapshot.docs;

      generateContacts(collectionSnapshot);
    } catch (e) {
      setState(() {
        controller.loadingContacts = false;
      });
      print(e.toString());
    }
  }

  
  fetchPrevContacts() async {
    setState(() {
      controller.loadingContacts = true;
    });

    controller.pageCounter--;

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      int indexOfSnapshot = isRecordExists(controller.pageCounter);
      if (indexOfSnapshot > -1) {
        collectionSnapshot = controller.cacheFirestore[indexOfSnapshot]['snapshot'];
      } else {
        collectionSnapshotQuery = FirebaseFirestore.instance
        .collection(appConfig.COLLECT_USERS)
        .where('source', whereIn: ['web', 'valuation', 'wordpress', 'direct'] )
        .orderBy('insertTimestamp', descending: true)
        .limit(controller.recordsPerPage);
        
        if (controller.filters.length > 0) {
        
          for (var i = 0; i < controller.filters.length; i++) {
            if( controller.filters[i]['value'] != '' ) {
              
              if( controller.filters[i]['value'] == 'web' ) {
                collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'],  whereIn: ['web', 'wordpress', 'valuation']);
                
              } else {
                collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
                
              }
            }
            
          }
        }

        collectionSnapshot = await collectionSnapshotQuery.limit(controller.recordsPerPage).endBeforeDocument(controller.documentList[controller.documentList.length - 1]).get();
      }

      controller.documentList = collectionSnapshot.docs;
      generateContacts(collectionSnapshot);
    } catch (e) {
      print(e);
      setState(() {
        controller.loadingContacts = false;
      });
    }
  }

  fetchRenovators() async {
    if (controller.renovators.length > 0) {
      controller.loadingRenovators = false;
      return;
    }
    List<NewarcUser> _renovators = [];
    setState(() {
      controller.loadingRenovators = true;
    });

    Query<Map<String, dynamic>> collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_USERS);

    QuerySnapshot<Map<String, dynamic>> collectionSnapshot = await collectionSnapshotQuery.where('type', isEqualTo: 'newarc').where('role', isEqualTo: 'renovator').get();

    _renovators.add(NewarcUser.empty());

    if (collectionSnapshot.docs.length > 0) {
      for (var element in collectionSnapshot.docs) {
        try {
          NewarcUser _tmp = NewarcUser.fromDocument(element.data(), element.id);
          _renovators.add(_tmp);
          controller.searchRef.add({'value': _tmp.id, 'label': _tmp.firstName! + ' ' + _tmp.lastName!});
        } catch (e) {}
      }
    }

    setState(() {
      controller.renovators = _renovators;
      controller.loadingRenovators = false;
    });
  }

  Future<void> showAddContactPopup(RenovationContact? acquiredContact) async {
    if (acquiredContact!.id != '') {
      controller.contactNameController.text = acquiredContact.personInfo!.name!;
      controller.contactSurnameController.text = acquiredContact.personInfo!.surname!;
      controller.contactEmailController.text = acquiredContact.personInfo!.email!;
      controller.contactPhoneController.text = acquiredContact.personInfo!.phone!;
      controller.renovatorController.text = acquiredContact.assignedRenovatorId ?? '';
      
      controller.renoContactAddressInfo = BaseAddressInfo.empty();
      
    } else {
      controller.contactNameController.clear();
      controller.contactSurnameController.clear();
      controller.contactEmailController.clear();
      controller.contactPhoneController.clear();
      controller.renoContactAddressInfo = BaseAddressInfo.empty();
    }

    setState(() {
      controller.formMessages.clear();
      controller.formProgressMessage = '';
    });

    await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context, state) {
            return Center(
              child: BaseNewarcPopup(
                  title: acquiredContact.id == '' ? "Aggiungi contatto" : "Modifica contatto",
                  buttonText: acquiredContact.id == '' ? "Aggiungi contatto" : "Modifica contatto",
                  formErrorMessage: controller.formMessages,
                  onPressed: () async {
                    setState(() {
                      controller.formMessages.clear();
                      controller.formMessages.add('Salvataggio...');
                      controller.formProgressMessage = 'Salvataggio';
                    });

                    try {
                      Map<String, dynamic> contactData = {
                        'name': controller.contactNameController.text,
                        'surname': controller.contactSurnameController.text,
                        'email': controller.contactEmailController.text,
                        'phone': controller.contactPhoneController.text,
                      };
                      Map<String, dynamic> data = {
                        // 'name': controller.contactNameController.text,
                        // 'surname': controller.contactSurnameController.text,
                        // 'email': controller.contactEmailController.text,
                        // 'phone': controller.contactPhoneController.text,
                        'created': acquiredContact.id == "" ? DateTime.now().millisecondsSinceEpoch : acquiredContact.created!,
                        // 'streetAddress': "${controller.renoContactAddressInfo.toShortAddress()}",
                        // 'city': controller.renoContactAddressInfo.city,
                        // 'addressInfo': controller.renoContactAddressInfo.toMap(),
                        'personInfo': contactData,
                        'assignedRenovatorId': controller.renovatorController.text,
                        'files': acquiredContact.files,
                        'assignedQuotation': acquiredContact.assignedQuotation,
                      };

                      
                      /*if (!controller.renoContactAddressInfo.isValidAddress()){
                        setState(() {
                          controller.formMessages.clear();
                          controller.formMessages.add('Indirizzo non valido');
                          controller.formProgressMessage = 'Error';
                        });
                        return false;
                      }*/

                      RenovationContact renovationContact = RenovationContact(data);

                      if (acquiredContact.id == '') {

                        DocumentReference<Map<String, dynamic>> renovationContactRef =  await FirebaseFirestore.instance
                            .collection(
                            appConfig.COLLECT_RENOVATION_CONTACTS)
                            .add(renovationContact.toMap());

                        renovationContact.id = renovationContactRef.id;
                        
                        await registerRenovationContact(renovationContact).then(( uid ) async {

                          createLead(renovationContact, 'direct', uid);

                          final result = await FirebaseFunctions.instance
                          .httpsCallable('handlePasswordReset_piattaforma')
                          .call({
                            'origin': 'website',
                            'templateId': 7280614,
                            'email': renovationContact.personInfo!.email,
                            'subject': 'Crea la tua password Newarc',

                          });

                          
                        });
                      
                      } else {
                        await updateDocument(appConfig.COLLECT_RENOVATION_CONTACTS, acquiredContact.id!, data);
                      }

                      setState(() {
                        controller.formProgressMessage = 'Salvataggio';
                        controller.formMessages.clear();
                      });

                      initialFetchContacts(force: true);
                      // return false;
                      return true;
                    } catch (e, s) {
                      setState(() {
                        controller.formMessages.clear();
                        controller.formMessages.add("L'email inserita non è valida");
                        controller.formProgressMessage = 'Error';
                      });
                      print({e, s});
                      return false;
                    }
                  },
                  // To make custom popup using the bluprint of  RenovationContactPopup
                  column: Container(
                    width: 400,
                    height: 400,
                    child: ListView(
                      children: [
                        
                        SizedBox(height: 10),
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            CustomTextFormField(
                              label: "Nome",
                              validator: (value) {
                                if (value == '') {
                                  return 'Obbligatorio!';
                                }
                                return null;
                              },
                              controller: controller.contactNameController,
                            ),
                          ],
                        ),
                        SizedBox(height: 10),
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            CustomTextFormField(
                              label: "Cognome",
                              controller: controller.contactSurnameController,
                            ),
                          ],
                        ),
                        SizedBox(height: 10),
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            CustomTextFormField(
                              label: "E-Mail",
                              validator: (value) {
                                if (value == '') {
                                  return 'Obbligatorio!';
                                }
                                return null;
                              },
                              controller: controller.contactEmailController,
                            ),
                          ],
                        ),
                        SizedBox(height: 10),
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            CustomTextFormField(
                              label: "Telefono",
                              controller: controller.contactPhoneController,
                            ),
                          ],
                        ),
                        SizedBox(height: 10),
                        controller.loadingRenovators
                            ? Center(
                              child: SizedBox(
                                width: 50,
                                child: CircularProgressIndicator(
                                    color: Theme.of(context).primaryColor,
                                  ),
                              ),
                            )
                            : Column(
                                mainAxisSize: MainAxisSize.max,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  NarFormLabelWidget(
                                    label: "Architetto",
                                    textColor: Color(0xff696969),
                                    fontSize: 13,
                                    fontWeight: '500',
                                  ),
                                  SizedBox(height: 4),
                                  NarImageSelectBoxWidget(
                                    options: controller.renovators.where((e) => e.isActive == true && e.isArchived == false).map((e) {
                                      return {'value': e.id, 'label': e.firstName! + " " + e.lastName!};
                                    }).toList(),
                                    controller: controller.renovatorController,
                                    validationType: 'required',
                                    parametersValidate: 'Obbligatorio!',
                                    onChanged: (val) {
                                      controller.renovators.where((element) => element.id == controller.renovatorController.text).first;

                                      setState(() {});
                                    },
                                  ),
                                ],
                              ),
                        SizedBox(height: 10),
                        Row(
                          children: [NarFormLabelWidget(label: controller.formProgressMessage)],
                        )
                      ],
                    ),
                  )),
            );
          });
        });
  }
  

  

  showCreateSuggestedContactPopup() {
    controller.formMessages.clear();
    controller.suggestedContactController.clear();
    controller.suggestedContactIDController.clear();
    controller.suggestedContactsSearchMap.clear();
    controller.suggestedContactsFetchedOnce = false;
    return showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter dialogSetState) {
            if (!controller.suggestedContactsFetchedOnce){
              fetchSuggestedContactsAndAgencies(dialogSetState);
            }
            return Center(
              child: BaseNewarcPopup(
                title: "Crea da segnalazione",
                noButton: controller.loadingSuggestedContactsAndAgencies ? true : false,
                buttonText: "Crea",
                buttonColor: Theme.of(context).primaryColor,
                formErrorMessage: controller.formMessages,
                onPressed: () async {
                  controller.formMessages.clear();
                  if (controller.suggestedContactIDController.text.isEmpty) {
                    controller.formMessages.add('Seleziona una segnalazione!');
                    return false;
                  }

                  try {
                    await FirebaseFirestore.instance
                      .collection(appConfig.COLLECT_RENOVATION_CONTACTS)
                      .doc(controller.suggestedContactIDController.text)
                      .update({
                        'isRequestingQuotation': true
                      });
                    initialFetchContacts(force: true);
                    return true;
                  } catch (e) {
                    controller.formMessages.add('Errore durante l\'aggiornamento della segnalazione');
                    return false;
                  }
                },
                column: Container(
                  width: 350,
                  height: 100,
                  // padding: EdgeInsets.symmetric(vertical: 15),
                  child: controller.loadingSuggestedContactsAndAgencies
                  ? Center(child: CircularProgressIndicator(color: Theme.of(context).primaryColor,))
                  : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      NarFormLabelWidget(
                        label: 'Cerca segnalazione',
                        textColor: Color(0xff696969),
                        fontSize: 13,
                        fontWeight: '600',
                        textAlign: TextAlign.left,
                      ),
                      SizedBox(height: 5,),
                      SearchSelectBox(
                        controller: controller.suggestedContactController,
                        options: controller.suggestedContactsSearchMap,
                        onSelection: (value){
                          controller.formMessages.clear();
                          controller.suggestedContactIDController.text = value;
                          dialogSetState(() {});
                        },
                      ),
                    ],
                  )
                )
              )
            );
          }
        );
      }
    );
  }

  Future<void> fetchSuggestedContactsAndAgencies(setState) async {
    if (controller.suggestedContactsSearchMap.length > 0 ) {
      controller.loadingSuggestedContactsAndAgencies = false;
      return;
    }
    List<RenovationContact> _suggestedContacts = [];
    List<Agency> _agencyList = [];
    List<Map<String, String>> _suggestedContactsSearchMap = [];

    setState(() {
      controller.loadingSuggestedContactsAndAgencies = true;
    });

    QuerySnapshot<Map<String, dynamic>> contactsSnapshot = await FirebaseFirestore.instance.collection(appConfig.COLLECT_RENOVATION_CONTACTS)
    .where('isRequestingQuotation', isEqualTo: false)
    .orderBy('created', descending: true)
    .get();
    QuerySnapshot<Map<String, dynamic>> agencySnapshot = await FirebaseFirestore.instance.collection(appConfig.COLLECT_AGENCIES)
                                                          .get();

    if (agencySnapshot.docs.length > 0) {
      agencySnapshot.docs.forEach((value){
        _agencyList.add(Agency.fromDocument(value.data(), value.id));
      });
      if (contactsSnapshot.docs.length > 0) {
        for (var element in contactsSnapshot.docs) {
          try {
            RenovationContact _tmp = RenovationContact.fromDocument(element.data(), element.id);
            Agency _tmpAgency = _agencyList.firstWhere((agency) => agency.id == _tmp.agencyId);
            _suggestedContacts.add(_tmp);

            RenovationContactAddress _renovationContactAddress = await getRenovationContactAddress(_tmp.addressInfo!.first );
            String searchSelectBoxName = "${_renovationContactAddress.addressInfo!.toShortAddress()} - ${_tmp.personInfo!.name} ${_tmp.personInfo!.surname} - ${_tmpAgency.name}";
            
            _suggestedContactsSearchMap.add({'id': _tmp.id!, 'name': searchSelectBoxName});
          } catch (e) {}
        }
      }
    }

    setState(() {
      controller.suggestedContactsSearchMap = _suggestedContactsSearchMap;
      controller.loadingSuggestedContactsAndAgencies = false;
      controller.suggestedContactsFetchedOnce = true;
    });
  }
  
  int isRecordExists(int pageCounter) {
    String filtersKey = controller.filters.map((f) => '${f['field']}:${f['value']}').join('|');
    return controller.cacheFirestore.indexWhere(
      (record) => record['key'] == pageCounter && record['filtersKey'] == filtersKey,
    );
  }

  generateContacts(QuerySnapshot<Map<String, dynamic>> collectionSnapshot) async {
    if (isRecordExists(controller.pageCounter) < 0) {
      String filtersKey = controller.filters.map((f) => '${f['field']}:${f['value']}').join('|');
      controller.cacheFirestore.add({
        'key': controller.pageCounter,
        'snapshot': collectionSnapshot,
        'filtersKey': filtersKey,
      });
    }

    if (controller.pageCounter == 1) {
      controller.disablePreviousButton = true;
    } else {
      controller.disablePreviousButton = false;
    }

    if (controller.pageCounter == controller.totalPages) {
      controller.disableNextButton = true;
    } else {
      controller.disableNextButton = false;
    }

    List<NewarcUser> _leads = [];
    for (var element in collectionSnapshot.docs) {
      try {
        var _tmp = NewarcUser.fromDocument(element.data(), element.id);

        _leads.add(_tmp);
      } catch (e) {}
    }

    _leads.sort((a, b) => b.insertTimestamp!.compareTo(a.insertTimestamp!));

    int lastRecordNumber = controller.pageCounter * controller.recordsPerPage;
    if (_leads.length == controller.recordsPerPage) {
      controller.currentlyShowing = (lastRecordNumber - (controller.recordsPerPage - 1)).toString() + '-' + lastRecordNumber.toString();
    } else if (_leads.length > 0 && _leads.length < controller.recordsPerPage) {
      int prevLastRecordNumber = (controller.pageCounter - 1) * controller.recordsPerPage;

      controller.currentlyShowing = (lastRecordNumber - (controller.recordsPerPage - 1)).toString() + '-' + (prevLastRecordNumber + _leads.length).toString();
    }

    setState(() {
      controller.leads = _leads;
      controller.displayLeads = _leads;
      controller.loadingContacts = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    double pageWidth = MediaQuery.of(context).size.width - 260;
    if (pageWidth < 1800) {
      pageWidth = 1800;
    }

    return LayoutBuilder(builder: (context, constraints) {
      return Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              NarFormLabelWidget(
                // label: 'Clienti ristrutturazione',
                label: 'Gestisci Contatti',
                fontSize: 19,
                fontWeight: '700',
                textColor: Colors.black,
              ),
              Expanded(
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    MouseRegion(
                      cursor: SystemMouseCursors.click,
                      child: GestureDetector(
                        onTap: () {
                          showCreateSuggestedContactPopup();
                        },
                        child: Container(
                          height: 32,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: AppColor.borderColor,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 20.0),
                            child: Text(
                              "Crea da segnalazione",
                              style: TextStyle(
                                color: AppColor.drawerButtonColor,
                                fontSize: 13,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 10,),
                    MouseRegion(
                      cursor: SystemMouseCursors.click,
                      child: GestureDetector(
                        onTap: () async {
                          showAddContactPopup(RenovationContact.empty());
                        },
                        child: Container(
                          height: 32,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 20.0),
                            child: Text(
                              "Crea cliente",
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 13,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
          FutureBuilder<NarFilter>(
            future: _filter(), 
            builder: (context, snapshot) {

              /* We don't need to show any special operation status */
              if( snapshot.connectionState == ConnectionState.waiting || snapshot.hasError || !snapshot.hasData ){
                return SizedBox(height: 0,);
              } 

              return snapshot.data!;
              
              
            }
          ),
          SizedBox(height: 10),
          Container(
            height: constraints.maxHeight / 1.2,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: AppColor.white,
              border: Border.all(width: 1.5, color: AppColor.borderColor),
            ),
            child: Column(
              children: [
                Expanded(
                  child: Stack(
                    children: [
                      Opacity(
                        opacity: controller.loadingContacts ? 0.5 : 1,
                        child: NewarcDataTable(
                          rowsPerPage: 20,
                          hidePaginator: true,
                          onPageChanged: (val) {
                            
                          },
                          columns: [
                            DataColumn2(
                              label: Text(
                                'Nome e cognome',
                              ),
                              size: ColumnSize.M
                            ),
                            DataColumn2(
                              label: Text(
                                'Origine',
                              ),
                              size: ColumnSize.L
                            ),
                            DataColumn2(
                              label: Text(
                                'Data',
                              ),
                              size: ColumnSize.L
                            ),
                            DataColumn2(
                              label: Text(
                                'Email',
                              ),
                              size: ColumnSize.M
                            ),
                            DataColumn2(
                              label: Text(
                                'Telefono',
                              ),
                              size: ColumnSize.M
                            ),
                            
                          ],
                          isHasDecoration: false,
                          source: LeadsDataSource(
                            onEditTap: (contact) {
                              // showAddContactPopup(lead);
                            },
                            // listAddressesPopup: (contact) async {
                            //   listAddressesPopup(contact);
                            // },
                            progressMessage: controller.progressMessage,
                            leadsContacts: controller.displayLeads,
                            context: context,
                          ),
                        ),
                      ),
                      if (controller.loadingContacts)
                        Positioned.fill(
                          child: Center(
                            child: CircularProgressIndicator(
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),

                // Expanded(
                //   child: DataTable2(
                //     dataRowHeight: loadingContacts ? 300 : 70,
                //     isHorizontalScrollBarVisible: true,
                //     minWidth: 1500,
                //     columnSpacing: 5,
                //     columns: getColumns(pageWidth),
                //     empty: Text('Nessun record trovato!'),
                //     rows: List.generate(displayContacts.where(filterFunction).length, (int index) {
                //       return DataRow(
                //         color: MaterialStateProperty.resolveWith((states) {
                //           return Colors.white;
                //         }),
                //         cells: getDataRow(
                //           displayContacts.where(filterFunction).elementAt(index),
                //         ),
                //       );
                //     }),
                //   ),
                // ),
                Align(
                  alignment: Alignment.centerRight,
                  child: dataTablePagination(),
                ),
              ],
            ),
          ),
        ],
      );
    });
  }

  Future<NarFilter> _filter() async {
    return NarFilter(
      showSearchInput: true,
      searchTextEditingControllers: controller.searchTextController,
      onChanged: (String? searchQuery) async {

        print({'searchQuery', searchQuery});
        /*if (searchQuery?.isNotEmpty ?? false) {
          List<Lead> filtered = [];

          for (var lead in controller.leads) {
            bool foundAddressFlag = false;

            final name = "${lead.basePersonInfo!.name!.toLowerCase()} "
                "${lead.basePersonInfo!.surname!.toLowerCase()}";

            if (name.contains(searchQuery!.toLowerCase()) || foundAddressFlag) {
              filtered.add(lead);
            }
          }

          setState(() {
            controller.displayLeads = filtered;
          });
        } else {
          // await initialFetchContacts(force: true);
          setState(() {
            controller.displayLeads = controller.leads;
          });
        }*/
      },
      suffixIconOnTap: ()async{
        await initialFetchContacts(force: true,reloadAll: true);
        if(controller.searchTextController.text.trim().isNotEmpty){
          
          List<NewarcUser> filtered = [];
          
          for (var lead in controller.leads) {
            
            if ( lead.toFullName().toString().toLowerCase().contains(controller.searchTextController.text.toLowerCase()) ) {
              filtered.add(lead);
            }
          }

          setState(() {
            controller.displayLeads = filtered;
          });
        }else{
          await initialFetchContacts(force: true);
          setState(() {
            controller.displayLeads = controller.leads;
          });
        }
      },
      textEditingControllers: [
        controller.referenceFilterController,
      ],
      selectedFilters: [
        controller.referenceFilter,
      ],
      filterFields: [
        {
          'Origine': NarImageSelectBoxWidget(
            options: [{ 'value': '', 'label': 'Tutti' }, { 'value': 'web', 'label': 'Web' }, { 'value': 'direct', 'label': 'Diretto' }, { 'value': 'agency', 'label': 'Agency' }],
            onChanged: (dynamic val) {

              controller.referenceFilter = val['label'];
              controller.filters = [
                {
                  'field': 'source',
                  'value': controller.referenceFilterController.text,
                }
              ];
              setState(() {});
              
            },
            controller: controller.referenceFilterController,
          ),
        },
      ],
      onSubmit: () async {
        await initialFetchContacts(force: true);
      },
      onReset: () async {
        controller.filters.clear();
        controller.referenceFilter = '';

        await initialFetchContacts(force: true);
      },
    );
  }

  // bool filterFunction(RenovationContact contact) {
  //   // To be implemented
  //   return true;
  // }

  // Function to generate a random salt
  String generateSalt([int length = 32]) {
    final random = Random.secure();
    var values = List<int>.generate(length, (i) => random.nextInt(256));
    return base64Url.encode(values);
  }

  // Function to hash password with salt
  String hashPassword(String password, String salt) {
    var bytes = utf8.encode(password + salt);
    var digest = sha256.convert(bytes);
    return digest.toString();
  }
}

class LeadsDataSource extends DataTableSource {
  List<NewarcUser> leadsContacts;
  List<String> progressMessage;
  BuildContext context;
  Function(NewarcUser contact) onEditTap;
  final controller = Get.put<LeadsController>(LeadsController());
  
  LeadsDataSource({
    required this.leadsContacts,
    required this.progressMessage,
    required this.context,
    required this.onEditTap,
  });

  @override
  DataRow? getRow(int index) {

    

    if (index < leadsContacts.length) {
      NewarcUser lead = leadsContacts[index];
      
      int millisecondsSinceEpoch = lead.insertTimestamp!;

      
      var date = DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch).day.toString() +
          '/' +
          DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch).month.toString() +
          '/' +
          DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch).year.toString();

      String origin = 'diretto';
      Color tagColor = Colors.black;

      if( lead.source == 'agency' ) {
        origin = 'agenzia';
        tagColor = Color(0xff5DB1E3);

      } else if( lead.source == 'wordpress' || lead.source == 'valuation' || lead.source == 'web' ) {
        origin = 'web';
        tagColor = Color(0xff499B79);
      } 

      return DataRow2(
        // specificRowHeight: rowHeight,
        cells: [
          DataCell(

            MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTap: () {
                  context.go(WorkRoutes.workGestisciContattiInside(lead.id!));
                },
                child: NarFormLabelWidget(
                  label: lead.toFullName()??'test',
                  fontSize: 12,
                  fontWeight: '600',
                  overflow: TextOverflow.ellipsis,
                  textColor: Colors.black,
                  textDecoration: TextDecoration.underline,
                ),
              ),
            ),
          ),
          DataCell(

            TagWidget( 
              text: origin, 
              borderRadius: 10, 
              statusColor: tagColor, 
              textColor: Colors.white, 
            )
            
          ),
          DataCell(

            NarFormLabelWidget(
              label: date,
              fontSize: 12,
              fontWeight: '600',
              overflow: TextOverflow.ellipsis,
              textColor: Colors.black,
            ),
            
          ),
          DataCell(
            NarFormLabelWidget(
              label: lead.email??'no email',
              fontSize: 12,
              fontWeight: '600',
              overflow: TextOverflow.ellipsis,
              textColor: Colors.black,
            ),
          ),
          DataCell(
            NarFormLabelWidget(
              label: lead.phone??'No phone',
              fontSize: 12,
              fontWeight: '600',
              overflow: TextOverflow.ellipsis,
              textColor: Colors.black,
            ),
          ),
          
        ],
      );
    }

    return null;
  }

  Future<ImageProvider> getImage(String userId) async {
    final extensions = ['.jpeg', '.png', '.jpg'];
    for (final extension in extensions) {
      final ref = FirebaseStorage.instance.ref().child('users/$userId/profile$extension');
      try {
        final url = await ref.getDownloadURL();
        return NetworkImage(url);
      } catch (error) {
        continue;
      }
    }
    throw Exception('Profile image not found for user $userId');
  }


  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => leadsContacts.length;

  @override
  int get selectedRowCount => 0;
}
