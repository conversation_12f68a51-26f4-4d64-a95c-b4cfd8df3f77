import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/basePersonInfo.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/widget/UI/image-picker.dart';
import 'package:path/path.dart' as p;

import '../../utils/resizeXFile.dart';

class AgencySettingWidget extends StatefulWidget {
  final Agency agency;
  final AgencyUser agencyUser;
  final Function? getProfilePicture;

  const AgencySettingWidget(
      {Key? key,
      required this.agency,
      required this.agencyUser,
      this.getProfilePicture})
      : super(key: key);

  @override
  State<AgencySettingWidget> createState() => AgencySettingWidgetState();
}

class AgencySettingWidgetState extends State<AgencySettingWidget> {
  AgencyUser? agencyUser;
  Agency? agency;

  TextEditingController agencyName = new TextEditingController();
  TextEditingController agencyCity = new TextEditingController();
  TextEditingController agencyAddress = new TextEditingController();
  TextEditingController agencyStreetNumber = new TextEditingController();
  TextEditingController agencyPhone = new TextEditingController();
  TextEditingController agencyAccountEmail = new TextEditingController();

  TextEditingController agencyUserName = new TextEditingController();
  TextEditingController agencyUserSurname = new TextEditingController();
  TextEditingController agencyUserPhone = new TextEditingController();
  TextEditingController agencyUserEmail = new TextEditingController();

  TextEditingController agencylegalEntity = new TextEditingController();
  TextEditingController agencyVat = new TextEditingController();
  TextEditingController agencySedeLegal = new TextEditingController();
  TextEditingController agencySdi = new TextEditingController();
  TextEditingController agencyIban = new TextEditingController();
  TextEditingController confirmPassword = new TextEditingController();
  TextEditingController newPassword = new TextEditingController();

  bool isNewPasswordVisible = false;
  bool isConfirmPasswordVisible = false;

  final FirebaseFirestore _db = FirebaseFirestore.instance;
  final profilePicture = [];
  // String? agencyProfilePicture;
  String? profilePictureFilename;
  String? validationMessage;

  String? progressMessage;

  double containerWidth = 0;
  @override
  void initState() {
    setInitValues();

    super.initState();
  }

  @protected
  void didUpdateWidget(AgencySettingWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    setInitValues();
  }

  setInitValues() {
    profilePicture.clear();
    agencyUser = widget.agencyUser;
    agency = widget.agency;

    getProfileImageUrl();
    profilePictureFilename = agencyUser!.profilePicture;

    agencyName.text = agency!.name!;
    agencyCity.text = agency!.city!;
    agencyAddress.text = agency!.address!;
    agencyStreetNumber.text = agency!.streetNumber!;
    agencyPhone.text = agency!.phone!;
    agencyAccountEmail.text = agencyUser!.email!;
    // agencyProfilePicture = profilePictureFilename;

    agencyUserName.text = agency?.referencePerson.name?? agencyUser!.name!;
    agencyUserSurname.text = agency?.referencePerson.surname??  agencyUser!.surname!;
    agencyUserPhone.text =  agency?.referencePerson.phone?? agencyUser!.phone!;
    agencyUserEmail.text = agency?.referencePerson.email?? agencyUser!.email!;

    agencylegalEntity.text = agency!.legalEntity!;
    agencyVat.text = agency!.vat!;
    agencySedeLegal.text = agency!.sedeLegaleFull?.fullAddress ?? agency!.sedeLegale??'';
    agencySdi.text = agency!.sdi!;
    agencyIban.text = agency!.iban!;
    confirmPassword.text = '';
    newPassword.text = '';
  }

  getProfileImageUrl() async {
    setState(() {
      profilePictureFilename = agencyUser!.profilePicture;
    });
  }

  Future<bool> updateData() async {
    setState(() {
      validationMessage = null;
    });
    // if (confirmPassword.text != '' && newPassword.text != '') {
    //   if (newPassword.text == confirmPassword.text) {
    //     setState(() {
    //       validationMessage = null;
    //     });
    //     updatePassword();
    //   } else {
    //     setState(() {
    //       validationMessage = 'Password mismatch!';
    //     });

    //     return false;
    //   }
    // }

    agency!.name = agencyName.text;
    agency!.city = agencyCity.text;
    agency!.address = agencyAddress.text;
    agency!.streetNumber = agencyStreetNumber.text;
    agency!.phone = agencyPhone.text;
    agency!.legalEntity = agencylegalEntity.text;
    agency!.vat = agencyVat.text;
    agency!.sedeLegale= agencySedeLegal.text;
    agency!.sedeLegaleFull = BaseAddressInfo.fromMap({'fullAddress': agencySedeLegal.text});
    agency!.sdi = agencySdi.text;
    agency!.iban = agencyIban.text;

    agencyUser!.name = agencyUserName.text;
    agencyUser!.surname = agencyUserSurname.text;
    agencyUser!.phone = agencyUserPhone.text;
    agencyUser!.email = agencyUserEmail.text;

    agency!.referencePerson = BasePersonInfo({
      'name': agencyUserName.text,
      'surname': agencyUserSurname.text,
      'phone': agencyUserPhone.text,
      'email': agencyUserEmail.text
    });

    if (profilePicture.length > 0) {
      String __profilePictureFilename =
          'agency-profile' + p.extension(profilePicture[0].name);

      await uploadProfilePicture('agencies/', agency!.id!, __profilePictureFilename, profilePicture[0]);

      final resizedFile = await resizeXFile(profilePicture[0],width: 240,height: 240,quality: 60,customFileName: "agency-profile_thumbnail");
      await uploadProfilePicture('${appConfig.COLLECT_AGENCIES}/',widget.agency.id!, resizedFile.name, resizedFile);

      agencyUser!.profilePicture = __profilePictureFilename;
      profilePictureFilename = __profilePictureFilename;
      profilePicture.clear();
    } else {
      profilePictureFilename = agencyUser!.profilePicture;
    }

    await _db.collection('agencies').doc(agency!.id).update(agency!.toMap());

    await _db
        .collection('users')
        .doc(agencyUser!.id)
        .update(agencyUser!.toMap());

    return true;
  }

  // updatePassword() async {
  //   User? user = await FirebaseAuth.instance.currentUser;

  //   user!.updatePassword(newPassword.text).then((_) {
  //     print('password changed');
  //   }).catchError((error) {
  //     print({'password no changed', error});
  //     //Error, show something
  //   });
  // }

  Future<bool> updateAgencyActiveStatus(Agency agency) async {
    final FirebaseFirestore _db = FirebaseFirestore.instance;
    return await _db
        .collection(appConfig.COLLECT_AGENCIES)
        .doc(agency.id)
        .update(agency.toMap())
        .then((value) {
      return true;
    }).onError((error, stackTrace) {
      // print({error, stackTrace});
      return false;
    });
  }

  setControllers(Agency agency, AgencyUser agencyUser) {}

  @override
  Widget build(BuildContext context) {
    containerWidth = MediaQuery.of(context).size.width * .75;

    return ListView(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Container(
              width: 500,
              child: NarFormLabelWidget(
                label: widget.agency.name! + ', ' + widget.agency.address!,
                fontSize: 20,
                fontWeight: 'bold',
              ),
            ),
            Row(
              children: [
                NarFormLabelWidget(
                  label: 'Attiva/Disattiva',
                  fontSize: 15,
                  fontWeight: '500',
                ),
                Switch(
                  // This bool value toggles the switch.
                  value: widget.agency.isActive!,
                  activeThumbColor: Theme.of(context).primaryColor,
                  onChanged: (bool value) async {
                    // This is called when the user toggles the switch.

                    widget.agency.isActive = value;
                    setState(() {
                      widget.agency.isActive = value;
                      //activeColor = value == true ? colorBlack : colorGrey;
                      //print({value, activeColor});
                    });

                    bool status = await updateAgencyActiveStatus(widget.agency);

                    if (status == false) {
                      await showAlertDialog(context, "Errore",
                          "Si è verificato un errore nell\'aggiornare lo stato.");

                      setState(() {
                        widget.agency.isActive = !value;
                        //activeColor = value == true ? colorGrey : colorBlack;
                      });
                    }
                  },
                ),
              ],
            )
          ],
        ),
        SizedBox(height: 35),
        NarFormLabelWidget(
          label: 'Dati Agenzia',
          fontSize: 16,
          fontWeight: 'bold',
        ),
        SizedBox(height: 15),
        Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            CustomTextFormField(
              label: 'Nome Agenzia',
              hintText: '',
              // initialValue: widget.agency.name,
              controller: agencyName,
            ),
            SizedBox(
              width: 15,
            ),
            CustomTextFormField(
              label: 'Città',
              hintText: '',
              // initialValue: widget.agency.city,
              controller: agencyCity,
            ),
          ],
        ),
        SizedBox(
          height: 15,
        ),
        Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            CustomTextFormField(
              label: 'Via/Corso/Piazza/Altro',
              hintText: '',
              // initialValue: widget.agency.address,
              controller: agencyAddress,
            ),
            SizedBox(
              width: 15,
            ),
            CustomTextFormField(
              label: 'Numero civico',
              hintText: '',
              // initialValue: widget.agency.streetNumber,
              controller: agencyStreetNumber,
            ),
            SizedBox(
              height: 0,
            )
          ],
        ),
        SizedBox(
          height: 15,
        ),
        Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            CustomTextFormField(
              label: 'Telefono sede',
              hintText: '',
              // initialValue: widget.agency.phone,
              controller: agencyPhone,
            ),
            SizedBox(
              width: 15,
            ),
            CustomTextFormField(
              label: 'E-mail sede',
              hintText: '',
              // initialValue: widget.agencyUser.email,
              controller: agencyAccountEmail,
            ),
          ],
        ),
        SizedBox(
          height: 15,
        ),

        Container(
          width: containerWidth * .80,
          height: 1,
          decoration: BoxDecoration(
            color: Color(0xFFDCDCDC),
          ),
          child: SizedBox(height: 0),
        ),
        SizedBox(
          height: 15,
        ),

        // Logo
        NarFormLabelWidget(
          label: 'Logo Agenzia',
          fontSize: 16,
          fontWeight: 'bold',
        ),
        SizedBox(
          height: 15,
        ),
        Row(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            NarImagePickerWidget(
                allowMultiple: false,
                imagesToDisplayInList: 0,
                removeButton: false,
                removeButtonText: 'rimuovi',
                uploadButtonPosition: 'back',
                showMoreButtonText: '+ espandi',
                removeButtonPosition: 'bottom',
                displayFormat: 'row',
                imageDimension: 100,
                imageBorderRadius: 50,
                borderRadius: 7,
                fontSize: 14,
                fontWeight: '600',
                text: 'Carica logo',

                borderSideColor: Theme.of(context).primaryColor,
                hoverColor: Color.fromRGBO(133, 133, 133, 1),
                images: profilePicture,
                pageContext: context,
                storageDirectory: 'agencies/',
                preloadedImages: profilePictureFilename == null ||
                        profilePictureFilename == ''
                    ? []
                    : [profilePictureFilename],
                firebaseId: widget.agency.id,
                removeExistingOnChange: true)
          ],
        ),

        SizedBox(
          height: 15,
        ),

        Container(
          width: containerWidth * .80,
          height: 1,
          decoration: BoxDecoration(
            color: Color(0xFFDCDCDC),
          ),
          child: SizedBox(height: 0),
        ),

        SizedBox(
          height: 15,
        ),

        // Persona di riferimento
        NarFormLabelWidget(
          label: 'Persona di riferimento',
          fontSize: 16,
          fontWeight: 'bold',
        ),
        SizedBox(
          height: 15,
        ),
        Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            CustomTextFormField(
              label: 'Nome persona riferimento',
              hintText: '',
              // initialValue: widget.agencyUser.name,
              controller: agencyUserName,
            ),
            SizedBox(
              width: 15,
            ),
            CustomTextFormField(
              label: 'Cognome persona riferimento',
              hintText: '',
              // initialValue: widget.agencyUser.surname,
              controller: agencyUserSurname,
            ),
          ],
        ),
        SizedBox(
          height: 15,
        ),
        Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            CustomTextFormField(
              label: 'Telefono persona riferimento',
              hintText: '',
              // initialValue: widget.agencyUser.phone,
              controller: agencyUserPhone,
            ),
            SizedBox(
              width: 15,
            ),
            CustomTextFormField(
              label: 'E-mail persona riferimento',
              hintText: '',
              // initialValue: widget.agencyUser.email,
              controller: agencyUserEmail,
            ),
          ],
        ),

        SizedBox(
          height: 15,
        ),

        Container(
          width: containerWidth * .80,
          height: 1,
          decoration: BoxDecoration(
            color: Color(0xFFDCDCDC),
          ),
          child: SizedBox(height: 0),
        ),
        SizedBox(
          height: 15,
        ),

        //Fatturazione e pagamenti

        NarFormLabelWidget(
          label: 'Fatturazione e pagamenti',
          fontSize: 16,
          fontWeight: 'bold',
        ),
        SizedBox(
          height: 15,
        ),
        Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            CustomTextFormField(
              label: 'Denominazione completa',
              hintText: '',
              // initialValue: widget.agency.legalEntity,
              controller: agencylegalEntity,
            ),
            Expanded(
                flex: 1,
                child: SizedBox(
                  height: 0,
                ))
          ],
        ),
        SizedBox(
          height: 15,
        ),
        Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            CustomTextFormField(
                label: 'P.Iva',
                hintText: '',
                // initialValue: widget.agency.vat,
                controller: agencyVat),
            SizedBox(
              width: 15,
            ),
            CustomTextFormField(
                label: 'Sede Legale',
                hintText: '',
                // initialValue: widget.agency.sedeLegale,
                controller: agencySedeLegal),
          ],
        ),

        SizedBox(
          height: 15,
        ),
        Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            CustomTextFormField(
              label: 'Codice Fatturazione Elettronico',
              hintText: '',
              // initialValue: widget.agency.sdi,
              controller: agencySdi,
            ),
            SizedBox(
              width: 15,
            ),
            CustomTextFormField(
              label: 'Iban',
              hintText: '',
              inputFormatters: [UpperCaseTextFormatter()],
              textCapitalization: TextCapitalization.words,
              //initialValue: widget.agency.iban,
              controller: agencyIban,
            ),
          ],
        ),

        SizedBox(
          height: 15,
        ),

        NarFormLabelWidget(
          label: validationMessage != '' ? validationMessage : '',
          fontSize: 12,
          textColor: Colors.red,
        ),

        SizedBox(
          height: 50,
        ),

        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          mainAxisSize: MainAxisSize.max,
          children: [
            NarFormLabelWidget(
              label: progressMessage != '' ? progressMessage : '',
              fontSize: 12,
            )
          ],
        ),
        SizedBox(height: 10),

        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          mainAxisSize: MainAxisSize.max,
          children: [
            BaseNewarcButton(
                buttonText: "Salva",
                onPressed: () async {
                  setState(() {
                    profilePictureFilename = '';
                    progressMessage = 'Salvataggio in corso...';
                  });
                  bool response = await updateData();

                  if (response == true) {
                    setState(() {
                      progressMessage = '';
                      widget.getProfilePicture!();
                    });
                    await showAlertDialog(context, "Salvataggio",
                        "Informazioni agenzia salvate con successo");
                  } else {
                    setState(() {
                      progressMessage =
                          'Si è verificato un errore. Contatta l\'assistenza.';
                    });
                  }
                })
          ],
        )
      ],
    );
  }
}
