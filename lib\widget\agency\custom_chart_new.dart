import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/acquiredContact.dart';

extension DateTimeExtension on DateTime {
  DateTime toMidnight() {
    return DateTime(this.year, this.month, this.day);
  }
}

class LineChartSample2 extends StatefulWidget {
  final List<AcquiredContact> contacts;
  const LineChartSample2({super.key, required this.contacts});

  @override
  State<LineChartSample2> createState() => _LineChartSample2State();
}

class _LineChartSample2State extends State<LineChartSample2> {
  List<Color> gradientColors = [Color(0xff489B79), Color(0xff489B79)];

  bool showAvg = false;
  List<FlSpot> datapoints = [];
  Map<int, List<AcquiredContact>> groupedDailyContacts = {};
  int maxValue = 0;
  int currentDay = 1;
  int currentMonth = 1;

  @override
  void initState() {
    int now = DateTime.now().millisecondsSinceEpoch;
    int oneDay = 86400000;
    int oneWeek = 604800000;

    for (AcquiredContact contact in widget.contacts) {
      DateTime date =
          DateTime.fromMillisecondsSinceEpoch(contact.insertionTimestamp!)
              .toUtc()
              .toLocal()
              .toMidnight();
      int day = date.weekday;

      if (!groupedDailyContacts.containsKey(day)) {
        groupedDailyContacts[day] = [];
      }

      groupedDailyContacts[day]!.add(contact);
    }
    print(groupedDailyContacts);
    /*groupedDailyContacts = groupBy(widget.contacts,
        //.where((element) => element.insertionTimestamp != null)
        //.where((element) => now - element.insertionTimestamp! <= oneWeek),
        (AcquiredContact contact) {
      return ((now - contact.insertionTimestamp!) / oneDay).ceil();
    });*/
    int _maxValue = 0;
    for (List list in groupedDailyContacts.values) {
      if (list.length > _maxValue) {
        _maxValue = list.length;
      }
    }

    final data = List.generate(7, (index) {
      if (groupedDailyContacts.keys.contains(index + 1)) {
        return FlSpot(index.ceilToDouble(),
            groupedDailyContacts[index + 1]!.length.ceilToDouble());
      } else {
        return FlSpot(index.ceilToDouble(), 0);
      }
    });
    //data.removeAt(0);
    print(data);
    setState(() {
      maxValue = _maxValue;
      datapoints = data;
      currentDay = DateTime.now().day;
      currentMonth = DateTime.now().month;
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        AspectRatio(
          aspectRatio: 1.70,
          child: Padding(
            padding: const EdgeInsets.only(
              right: 18,
              left: 12,
              top: 24,
              bottom: 12,
            ),
            child: LineChart(
              showAvg ? avgData() : mainData(),
            ),
          ),
        ),
        SizedBox(
          width: 60,
          height: 34,
          child: TextButton(
            onPressed: () {
              setState(() {
                showAvg = !showAvg;
              });
            },
            child: Text(
              'Contatti',
              style: TextStyle(
                fontSize: 12,
                color: showAvg ? Colors.white.withOpacity(0.5) : Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget bottomTitleWidgets(double value, TitleMeta meta) {
    const style = TextStyle(
      fontWeight: FontWeight.bold,
      fontSize: 16,
    );

    List<String> dates = calculatePreviousDates(currentDay, currentMonth);

    Widget text;
    switch (value.toInt()) {
      case 0:
        text = Text(dates[6], style: style);
        break;
      case 1:
        text = Text(dates[5], style: style);
        break;
      case 2:
        text = Text(dates[4], style: style);
        break;
      case 3:
        text = Text(dates[3], style: style);
        break;
      case 4:
        text = Text(dates[2], style: style);
        break;
      case 5:
        text = Text(dates[1], style: style);
        break;
      case 6:
        text = Text('${currentDay}/${currentMonth}', style: style);
        break;
      //case 7:
      //text = Text('${currentDay}/${currentMonth}', style: style);
      //break;
      default:
        text = const Text('', style: style);
        break;
    }

    return SideTitleWidget(
      meta: meta,
      child: text,
    );
  }

  Widget leftTitleWidgets(double value, TitleMeta meta) {
    const style = TextStyle(
      fontWeight: FontWeight.bold,
      fontSize: 15,
    );
    if (value == maxValue) {
      return Text(value.toString(), style: style, textAlign: TextAlign.left);
    } else if (value == 0) {
      return Text('0', style: style, textAlign: TextAlign.left);
    } else {
      return Container();
    }
  }

  LineChartData mainData() {
    return LineChartData(
      lineTouchData: LineTouchData(
        enabled: true,
        touchTooltipData: LineTouchTooltipData(
          getTooltipColor: (touchedSpot) {
            return Colors.white;
          },
          // getTooltipColor: Colors.white,
          // tooltipBgColor: Colors.white,

          tooltipBorder: BorderSide(color: Theme.of(context).primaryColor),
        ),
      ),
      clipData: FlClipData.horizontal(),
      gridData: FlGridData(
        show: true,
        drawVerticalLine: true,
        horizontalInterval: 1,
        verticalInterval: 1,
        getDrawingHorizontalLine: (value) {
          return FlLine(
            color: Colors.white,
            strokeWidth: 1,
          );
        },
        getDrawingVerticalLine: (value) {
          return FlLine(
            color: Colors.white,
            strokeWidth: 1,
          );
        },
      ),
      titlesData: FlTitlesData(
        show: true,
        rightTitles: AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        topTitles: AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 30,
            interval: 1,
            getTitlesWidget: bottomTitleWidgets,
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            interval: 1,
            getTitlesWidget: leftTitleWidgets,
            reservedSize: 42,
          ),
        ),
      ),
      borderData: FlBorderData(show: false),
      minX: 0,
      maxX: 7,
      minY: 0,
      maxY: maxValue.toDouble(),
      lineBarsData: [
        LineChartBarData(
          spots: datapoints,
          isCurved: true,
          color: Theme.of(context).primaryColor,
          barWidth: 4,
          isStrokeCapRound: false,
          dotData: FlDotData(
            show: false,
          ),
          belowBarData: BarAreaData(
            show: true,
            color: Theme.of(context).primaryColor.withOpacity(0.3),
          ),
        ),
      ],
    );
  }

  LineChartData avgData() {
    return LineChartData(
      lineTouchData: LineTouchData(
          enabled: false,
          touchTooltipData: LineTouchTooltipData(
            getTooltipColor: (touchedSpot) {
              return Colors.white;
            }
            // tooltipBgColor: Colors.white
          )
        ),
      gridData: FlGridData(
        show: true,
        drawHorizontalLine: true,
        verticalInterval: 1,
        horizontalInterval: 1,
        getDrawingVerticalLine: (value) {
          return FlLine(
            color: Color(0xff37434d),
            strokeWidth: 1,
          );
        },
        getDrawingHorizontalLine: (value) {
          return FlLine(
            color: Color(0xff37434d),
            strokeWidth: 1,
          );
        },
      ),
      titlesData: FlTitlesData(
        show: true,
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 30,
            getTitlesWidget: bottomTitleWidgets,
            interval: 1,
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            getTitlesWidget: leftTitleWidgets,
            reservedSize: 42,
            interval: 1,
          ),
        ),
        topTitles: AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        rightTitles: AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
      ),
      borderData: FlBorderData(
        show: false,
        border: Border.all(color: const Color(0xff37434d)),
      ),
      minX: 0,
      maxX: 11,
      minY: 0,
      maxY: 6,
      lineBarsData: [
        LineChartBarData(
          spots: datapoints,
          isCurved: true,
          gradient: LinearGradient(
            colors: [
              ColorTween(begin: gradientColors[0], end: gradientColors[1])
                  .lerp(0.2)!,
              ColorTween(begin: gradientColors[0], end: gradientColors[1])
                  .lerp(0.2)!,
            ],
          ),
          barWidth: 5,
          isStrokeCapRound: true,
          dotData: FlDotData(
            show: true,
          ),
          belowBarData: BarAreaData(
            show: true,
            gradient: LinearGradient(
              colors: [
                ColorTween(begin: gradientColors[0], end: gradientColors[1])
                    .lerp(0.2)!
                    .withOpacity(0.1),
                ColorTween(begin: gradientColors[0], end: gradientColors[1])
                    .lerp(0.2)!
                    .withOpacity(0.1),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

void main() {
  int currentDay = 31; // Example: Last day of data
  int currentMonth = 12; // Example: Last month of data

  List<String> previousDates = calculatePreviousDates(currentDay, currentMonth);

  print(previousDates);
}

List<String> calculatePreviousDates(int currentDay, int currentMonth) {
  List<String> previousDates = [];

  for (int i = 0; i < 7; i++) {
    if (currentDay > 7) {
      currentDay--;
    } else {
      currentMonth--;
      if (currentMonth == 0) {
        // January
        currentMonth = 12; // December of the previous year
      }
      int previousMonthDays = getDaysInMonth(currentMonth);
      currentDay = previousMonthDays - (6 - currentDay);
    }

    String date = formatDate(currentDay, currentMonth);
    previousDates.add(date);
  }

  return previousDates;
}

int getDaysInMonth(int month) {
  // Define the number of days in each month
  Map<int, int> daysInMonth = {
    1: 31,
    2: 28,
    3: 31,
    4: 30,
    5: 31,
    6: 30,
    7: 31,
    8: 31,
    9: 30,
    10: 31,
    11: 30,
    12: 31,
  };

  return daysInMonth[month]!;
}

String formatDate(int day, int month) {
  String dayString = day.toString().padLeft(2, '0');
  String monthString = month.toString().padLeft(2, '0');
  return '$dayString/$monthString';
}
