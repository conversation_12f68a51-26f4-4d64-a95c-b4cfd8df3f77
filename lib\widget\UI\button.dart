import 'package:flutter/material.dart';

/// *  textInputType - The type of information for which to optimize the text input control.
/// *  hintText - Text that suggests what sort of input the field accepts.
/// *  prefixIcon - Pass Icon if required
/// *  defaultText - If there is predefined value is there for a text field
/// *  focusNode - Currently focus node
/// *  obscureText - Is Password field?
/// *  controller - Text controller
/// *  functionValidate - Validation function(currently I have used to check empty field)
/// *  parametersValidate - Value to validate
/// *  actionKeyboard - Keyboard action eg. next, done, search, etc
/// *  onSubmitField - Done click action
/// *  onFieldTap - On focus on TextField
class NarButtonWidget extends StatefulWidget {
  final VoidCallback? onClick;
  final String? text;
  final Color? textColor;
  final Color? color;
  final Color? splashColor;
  final Color? hoverColor;
  final double? borderRadius;
  final double? minWidth;
  final double? textHeight,height;
  final Color? borderSideColor;
  final TextStyle? style;
  final Widget? leadingIcon;
  final Widget? trailingIcon;
  double? fontSize = 14;
  String? fontWeight = '800';
  EdgeInsets? buttonPadding;
  double elevation;

  NarButtonWidget(
      {this.onClick,
      this.text,
      this.textColor,
      this.color,
      this.splashColor,
      this.borderRadius,
      this.minWidth,
      this.textHeight,this.height,
      this.borderSideColor,
      this.style,
      this.leadingIcon,
      this.trailingIcon,
      this.hoverColor,
      this.fontSize,
      this.buttonPadding,
      this.fontWeight,
        this.elevation = 5,
      });

  @override
  _NarButtonWidgetState createState() => _NarButtonWidgetState();
}

class _NarButtonWidgetState extends State<NarButtonWidget> {
  double bottomPaddingToError = 12;
  double? fontSize;
  String? fontWeight;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    fontSize = widget.fontSize != null ? widget.fontSize : 14;
    fontWeight = widget.fontWeight != null ? widget.fontWeight : '800';
  }

  @override
  Widget build(BuildContext context) {
    return ButtonTheme(
      // minWidth: widget.minWidth??100,
      // padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 20),
      child: ElevatedButton(
          style: ButtonStyle(
            padding: WidgetStateProperty.all(widget.buttonPadding ?? EdgeInsets.symmetric(vertical: 15, horizontal: 35)),
            textStyle: WidgetStateProperty.all(
              TextStyle(
                // color: widget.textColor??Color.fromRGBO(105, 105, 105, 1),
                fontSize: fontSize!,
                letterSpacing: 0.5,
                fontFamily: 'Raleway-' + fontWeight!,
                height: widget.textHeight ?? 2,
              ),
            ),
            shape: WidgetStateProperty.all<OutlinedBorder>(RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(widget.borderRadius != null ? widget.borderRadius! : 7),
                side: BorderSide(color: widget.borderSideColor != null ? widget.borderSideColor! : Theme.of(context).primaryColor))),
            foregroundColor: WidgetStateProperty.all<Color>(widget.textColor != null ? widget.textColor! : Colors.white),
            backgroundColor: WidgetStateProperty.all<Color>(widget.splashColor != null ? widget.splashColor! : Theme.of(context).primaryColor),
            overlayColor: WidgetStateProperty.resolveWith<Color>(
              (Set<WidgetState> states) {
                if (states.contains(WidgetState.hovered)) return widget.hoverColor != null ? widget.hoverColor! : Theme.of(context).primaryColor;
                if (states.contains(WidgetState.focused) || states.contains(WidgetState.pressed)) return Theme.of(context).primaryColor;
                return Theme.of(context).primaryColor; // Defer to the widget's default.
              },
            ),
            elevation: WidgetStateProperty.all<double>(widget.elevation),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            // This is must when you are using Row widget inside Raised Button
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              // _buildLeadingIcon(widget.leadingIcon!),
              widget.leadingIcon == null
                  ? SizedBox(
                      height: 0,
                    )
                  : Row(
                      children: <Widget>[widget.leadingIcon!, SizedBox(width: 5)],
                    ),
              Text(
                widget.text ?? '',
              ),
              widget.trailingIcon == null
                  ? SizedBox(
                      height: 0,
                    )
                  : Row(
                      children: <Widget>[widget.trailingIcon!, SizedBox(width: 10)],
                    ),
            ],
          ),
          onPressed: () {
            return widget.onClick!();
          }),
    );
  }
}
