import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/renovationContact.dart';
import 'package:newarc_platform/classes/renovationQuotation.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:intl/intl.dart';

import '../../../classes/renovationContactAddress.dart';



class RenovationQuotationController extends GetxController {
  bool loadingQuotations = true;
  bool loadingRenovators = true;
  List<RenovationQuotation> quotations = [];
  List<RenovationQuotation> allQuotations = [];
  // List<RenovationQuotation> displayQuotations = [];

  RxSet<String> expandedGroups = <String>{}.obs;
  RxSet<String> expandedChildren = <String>{}.obs;

  void toggleGroup(String code) {
    if (expandedGroups.contains(code)) {
      expandedGroups.remove(code);
    } else {
      expandedGroups.add(code);
    }
    update(); // Notify listeners
  }

  List<List<RenovationQuotation>> displayQuotations = [];
  List<NewarcUser> renovators = [];
  List<RenovationContact> renovationContacts = [];
  List<DocumentSnapshot> documentList = [];

  TextEditingController referenceFilterController =  TextEditingController();
  TextEditingController preventiveStateFilterController =  TextEditingController();
  String referenceSelectedFilter = '';
  String preventiveStateSelectedFilter = '';


  final TextEditingController tipologiaController = new TextEditingController();
  TextEditingController assignmentController = new TextEditingController();
  TextEditingController agencyController = new TextEditingController();
  TextEditingController searchTextController = new TextEditingController();
  TextEditingController filterCity = new TextEditingController();
  TextEditingController filterNewarcType = new TextEditingController();
  TextEditingController contactNameController = new TextEditingController();
  TextEditingController contactSurnameController = new TextEditingController();
  TextEditingController contactEmailController = new TextEditingController();
  TextEditingController contactPhoneController = new TextEditingController();
  BaseAddressInfo contactAddressInfo = BaseAddressInfo.empty();
  final TextEditingController renovatorController = new TextEditingController();
  final TextEditingController renovationContactController = new TextEditingController();
  // final TextEditingController renovationQuotationController =  TextEditingController();
  final TextEditingController groupRenovationQuotationController =  TextEditingController();

  final TextEditingController renovationContactAddressController =  TextEditingController();

  List<RenovationContactAddress> selectedRenovationContactAddressList = [];

  String? selectedQuotationCode;
  String? selectedVersion;
  String? selectedRevision;

  List<Agency> agencyList = [];
  String formProgressMessage = '';

  int? maxCode = 0;
  int rowsPerPage = 20;
  bool? maxCodeEvaluated = false;

  NumberFormat localCurrencyFormatMain =
  NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 0);

  final RxString selectedStatus = "".obs;
  final TextEditingController contStatus = TextEditingController();

  RxMap<String, bool>? selectedRenovationMap = RxMap({});

  void clearFilter(){
    referenceFilterController.clear();
    preventiveStateFilterController.clear();
    referenceSelectedFilter = '';
    preventiveStateSelectedFilter = '';
  }

  void updateStatus(String newStatus) {
    selectedStatus.value = newStatus;
    contStatus.text = newStatus;
    update(); //
  }

  void updateSelectedRenovationMap(Map<String, bool> map) {
    selectedRenovationMap?.value = map;
    update();
  }



}
