import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:newarc_platform/classes/NAMaterial.dart';
import 'package:newarc_platform/classes/renovationQuotation.dart';
import 'package:newarc_platform/pages/work/rivestimenti/rivestimenti_data_source.dart';
import 'package:newarc_platform/pages/work/rivestimenti/rivestimenti_controller.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/filter.dart';
import 'package:newarc_platform/widget/UI/image-select-box.dart';
import 'package:newarc_platform/widget/UI/newarc_data_table.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/widget/UI/form-label.dart';

import '../../../classes/NewarcMaterialManufacturer.dart';
import '../../../classes/NewarcMaterialManufacturerCollection.dart';
import '../../../routes/work_routes.dart';



class RivestimentiView extends StatefulWidget {

  const RivestimentiView(
      {Key? key})
      : super(key: key);

  @override
  State<RivestimentiView> createState() =>
      _RivestimentiViewState();
}

class _RivestimentiViewState extends State<RivestimentiView> {
  Key? paddingKey;
  final controller = Get.put<RivestimentiController>(RivestimentiController());

  @override
  void initState() {
    controller.clearFilter();
    WidgetsBinding.instance.addPostFrameCallback((_){
      Future.wait([
        initialFetchNAMaterial(),
        fetchManufacturer(),
        fetchCollection(),
      ]);
    });

    super.initState();
  }

  List<Map> searchRef = [];
  bool isCopyFromExisting = false;

  @override
  void dispose() {
    controller.naMaterial = [];
    super.dispose();
  }
  

  Future<void> initialFetchNAMaterial({bool force = false}) async {

    // if (controller.naMaterial.isNotEmpty && !force) return;

    setState(() {
      controller.naMaterial = [];
      controller.loadingNAMaterial = true;
    });

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance
          .collection(appConfig.COLLECT_NEWARCHMATERIAL);

      if (controller.filters.length > 0) {
        for (var i = 0; i < controller.filters.length; i++) {
          if (controller.filters[i]['search'] == 'equal') {
            collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }
      }

      collectionSnapshotQuery = collectionSnapshotQuery.where('isArchive', isEqualTo: false );
      collectionSnapshotQuery = collectionSnapshotQuery.where('materialType', isEqualTo: 'rivestimenti' );
      collectionSnapshotQuery = collectionSnapshotQuery.orderBy('insertTimestamp', descending: true);

      collectionSnapshot = await collectionSnapshotQuery.get();

      controller.documentList = collectionSnapshot.docs;

      await generateNAMaterialRow(collectionSnapshot);

      setState(() {
        controller.loadingNAMaterial = false;
      });
    } catch (e, s) {
      setState(() {
        controller.loadingNAMaterial = false;
      });
      log("Error While fetching NAMaterial Rivestimenti ${e.toString()}");
      print({'Following error',e, s});
    }
  }

  Future<void> fetchManufacturer()async{
    QuerySnapshot<Map<String, dynamic>> manufacturerSnapshot = await FirebaseFirestore.instance
        .collection(appConfig.COLLECT_NEWARCMATERIALMANUFACTURER)
        .orderBy('insertTimestamp', descending: true)
        .get();

    List<NewarcMaterialManufacturer> _manufacturerList = [];
    List _manufacturerDropdownFilterList = [{"label" : "","value" : ""}];
    for(var element in manufacturerSnapshot.docs){
      NewarcMaterialManufacturer _manufacturer = NewarcMaterialManufacturer.fromDocument(element.data(), element.id);
      var dropdownVal = {"label" : _manufacturer.name,"value" : _manufacturer.firebaseId};
      _manufacturerList.add(_manufacturer);
      _manufacturerDropdownFilterList.add(dropdownVal);
    }



    setState(() {
      controller.manufacturerList = _manufacturerList;
      controller.manufacturerDropdownFilterList = _manufacturerDropdownFilterList;
    });
  }

  Future<void> fetchCollection()async{
    QuerySnapshot<Map<String, dynamic>> collectionSnapshot = await FirebaseFirestore.instance
        .collection(appConfig.COLLECT_NEWARCMATERIALMANUFACTURERCOLLECTION)
        .orderBy('insertTimestamp', descending: true)
        .get();

    List<NewarcMaterialManufacturerCollection> _collectionList = [];
    List _collectionDropdownFilterList = [{"label" : "","value" : ""}];
    for(var element in collectionSnapshot.docs){
      NewarcMaterialManufacturerCollection _collection = NewarcMaterialManufacturerCollection.fromDocument(element.data(), element.id);
      var dropdownVal = {"label" : _collection.name,"value" : _collection.firebaseId};
      _collectionDropdownFilterList.add(dropdownVal);
      _collectionList.add(_collection);
    }

    setState(() {
      controller.collectionList = _collectionList;
      controller.collectionDropdownFilterList = _collectionDropdownFilterList;
    });
  }

  Future<void> generateNAMaterialRow(QuerySnapshot collectionSnapshot) async {
    List<NAMaterial> _naMaterial = [];

    // Fetch manufacturers
    Map<String, String> manufacturerNames = {};
    QuerySnapshot manufacturerSnapshot = await FirebaseFirestore.instance
        .collection(appConfig.COLLECT_NEWARCMATERIALMANUFACTURER)
        .get();
    for (var doc in manufacturerSnapshot.docs) {
      manufacturerNames[doc.id] = (doc.data() as Map<String, dynamic>)['name'] ?? "";
    }

    // Fetch collections
    Map<String, String> collectionNames = {};
    QuerySnapshot collectionSnapshotQuery = await FirebaseFirestore.instance
        .collection(appConfig.COLLECT_NEWARCMATERIALMANUFACTURERCOLLECTION)
        .get();
    for (var doc in collectionSnapshotQuery.docs) {
      collectionNames[doc.id] = (doc.data() as Map<String, dynamic>)['name'] ?? "";
    }

    for (var element in collectionSnapshot.docs) {
      try {
        var data = element.data() as Map<String, dynamic>;
        var _tmp = NAMaterial.fromDocument(data, element.id);

        // Get names based on Firebase IDs
        _tmp.manufacturerName = manufacturerNames[_tmp.newarcManufacturerID] ?? "";
        _tmp.collectionName = collectionNames[_tmp.newarcProductCollectionID] ?? "";

        _naMaterial.add(_tmp);
      } catch (e, s) {
        print("Error processing document: $e\n$s");
      }
    }

    controller.naMaterial = _naMaterial;
    controller.loadingNAMaterial = false;

    if (mounted) {
      setState(() {});
    }
  }


  @override
  Widget build(BuildContext context) {
    double pageWidth = MediaQuery.of(context).size.width - 260;
    if (pageWidth < 1800) {
      pageWidth = 1800;
    }

    return LayoutBuilder(builder: (context, constraints) {
      return Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              NarFormLabelWidget(
                label: 'Rivestimenti',
                fontSize: 19,
                fontWeight: '700',
              ),
              Expanded(
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    MouseRegion(
                      cursor: SystemMouseCursors.click,
                      child: GestureDetector(
                        onTap: () {
                          controller.clearController();
                          context.go(WorkRoutes.workRivestimentiInside("new"));
                        },
                        child: Container(
                          height: 32,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Padding(
                            padding:
                            const EdgeInsets.symmetric(horizontal: 20.0),
                            child: Text(
                              "Aggiungi",
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 13,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
          Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
               _filter()
            ],
          ),
          SizedBox(height: 10),
          Container(
            height: constraints.maxHeight / 1.2,
            child: Column(
              children: [
                Expanded(
                  child: Stack(
                    children: [
                      Opacity(
                        opacity: controller.loadingNAMaterial ? 0.5 : 1,
                        child: NewarcDataTable(
                          rowsPerPage: 20,
                          dividerThickness: 1,
                          empty: Text(""),
                          columns: [
                            DataColumn2(
                              label: Text(
                                '',
                              ),
                              fixedWidth: 40
                            ),
                            DataColumn2(
                              label: Text(
                                '',
                              ),
                                fixedWidth: 100
                            ),
                            DataColumn2(
                              label: Text(
                                'Codice',
                              ),
                            ),
                            DataColumn2(
                              label: Text(
                                'Nome',
                              ),
                            ),
                            DataColumn2(
                              label: Text(
                                'Collezione',
                              ),
                            ),
                            DataColumn2(
                              label: Text(
                                'Produttore',
                              ),
                            ),
                            DataColumn2(
                              label: Text(
                                'Azioni',
                              ),
                            ),
                          ],
                          source: RivestimentiDataSource(
                            initialFetchNAMaterial: initialFetchNAMaterial,
                            onOpenAzioniDialog: (String type,NAMaterial naMaterialResponseModel)async{
                              if(type == "edit"){
                                controller.clearController();
                                context.go(WorkRoutes.workRivestimentiInside(naMaterialResponseModel.firebaseId!));
                              }else if(type == "archivia"){
                                showArchiveConfirmDialog(context: context,id: naMaterialResponseModel.firebaseId ?? "");
                              }
                            },
                            context: context,
                            naMaterial: controller.naMaterial,
                          ),
                        ),
                      ),
                      if (controller.loadingNAMaterial)
                        Positioned.fill(
                          child: Center(
                            child: CircularProgressIndicator(
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ),
                      // dataTablePagination(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    });
  }

  NarFilter _filter() {
    return NarFilter(
      showSearchInput: true,
      searchHintText: "Cerca per nome o per codice...",
      searchTextEditingControllers: controller.searchTextController,
      onChanged: (String? searchQuery)async{
        if(searchQuery?.isNotEmpty ?? false){
          if (searchQuery != null && searchQuery.isNotEmpty) {
            List<NAMaterial> filtered = controller.naMaterial.where((material) {
              final code = material.code?.toLowerCase() ?? "";
              final name = material.name?.toLowerCase() ?? "";
              return code.contains(searchQuery.toLowerCase()) ||
                  name.contains(searchQuery.toLowerCase());
            }).toList();

            setState(() {
              controller.naMaterial = filtered;
            });
          }
        }else{
          await initialFetchNAMaterial(force: true);
        }
      },
      suffixIconOnTap: ()async{
        if(controller.searchTextController.text.trim().isNotEmpty){
          List<NAMaterial> filtered = controller.naMaterial.where((material) {
            final code = material.code?.toLowerCase() ?? "";
            final name = material.name?.toLowerCase() ?? "";
            return code.contains(controller.searchTextController.text.toLowerCase()) ||
                name.contains(controller.searchTextController.text.toLowerCase());
          }).toList();

          setState(() {
            controller.naMaterial = filtered;
          });
        }else{
          await initialFetchNAMaterial(force: true);
        }
      },
      selectedFilters: [controller.manufacturerSelectedFilter,controller.collectionSelectedFilter],
      textEditingControllers: [controller.manufacturerFilterController,controller.collectionFilterController],
      filterFields: [
        {
          'Produttore': NarImageSelectBoxWidget(
            options: controller.manufacturerDropdownFilterList,
            controller: controller.manufacturerFilterController,
            onChanged: (value) {
              controller.filters.removeWhere((element) {
                return element['field'] == 'newarcManufacturerID';
              });
              controller.filters.add({'field': 'newarcManufacturerID', 'value': value["value"], 'search': 'equal'});
              controller.manufacturerSelectedFilter = value["label"];
            },
          ),
        },
        {
          'Collezione': NarImageSelectBoxWidget(
            options: controller.collectionDropdownFilterList,
            controller: controller.collectionFilterController,
            onChanged: (value) {
              controller.filters.removeWhere((element) {
                return element['field'] == 'newarcProductCollectionID';
              });
              controller.filters.add({'field': 'newarcProductCollectionID', 'value': value["value"], 'search': 'equal'});
              controller.collectionSelectedFilter = value["label"];
            },
          ),
        },
      ],
      onSubmit: () async {
        await initialFetchNAMaterial(force: true);
      },
      onReset: () async {
        controller.clearFilter();
        await initialFetchNAMaterial(force: true);
      },
    );
  }

  void showArchiveConfirmDialog({required BuildContext context,required String id}) async {
    return showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context,setStateDialog){
            return Center(
              child: BaseNewarcPopup(
                buttonColor: Theme.of(context).primaryColor,
                title: "Conferma archiviazione",
                buttonText: "Archivia",
                onPressed: () async {
                  try{
                    await FirebaseFirestore
                        .instance
                        .collection(appConfig.COLLECT_NEWARCHMATERIAL)
                        .doc(id)
                        .update({
                      "isArchive": true,
                    });
                    initialFetchNAMaterial(force: true);
                  }catch(e){
                    log("---------- ERROR While Archive Product Rivestimenti ------> ${e.toString()}");
                  }
                },
                column: Container(
                  width: 400,
                  padding: EdgeInsets.symmetric(vertical: 25),
                  child: Center(
                    child: NarFormLabelWidget(
                      label:  "Vuoi veramente archiviare il prodotto?" ,
                      textColor: Color(0xff696969),
                      fontSize: 18,
                      fontWeight: '600',
                    ),
                  ),
                )),
            );
          });
        });
  }

}
