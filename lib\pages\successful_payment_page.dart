import 'package:flutter/material.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

class SuccessfulPaymentPage extends StatefulWidget {
  const SuccessfulPaymentPage({Key? key}) : super(key: key);

  static const String route = '/successfulPayment';

  @override
  State<SuccessfulPaymentPage> createState() => _SuccessfulPaymentPageState();
}

class _SuccessfulPaymentPageState extends State<SuccessfulPaymentPage> {
  // int secondsCounter = 10;
  // Timer? timer;

  // @override
  // void initState() {
  //   super.initState();
  //   timer = Timer.periodic(Duration(seconds: 1), (timer) {
  //     setState(() {
  //       secondsCounter--;
  //     });
  //     if (secondsCounter == 0) {
  //       timer.cancel();
  //       Navigator.of(context).pushReplacementNamed(
  //         LoginPage.route,
  //       );
  //     }
  //   });
  // }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Container(
          constraints: BoxConstraints(maxWidth: 600),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                height: 100,
                "assets/newarc_classic.png",
              ),
              Container(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: Color(0xff499B79),
                        size: 80,
                      ),
                      SizedBox(height: 12),
                      NarFormLabelWidget(
                        label: 'Pagamento completato con successo!',
                        fontSize: 24,
                        fontWeight: '700',
                        textColor: Colors.black,
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 16),
                      NarFormLabelWidget(
                        label: 'Il tuo pagamento è stato ricevuto correttamente.',
                        fontSize: 16,
                        fontWeight: '500',
                        textColor: Color(0xff616161),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 8),
                      NarFormLabelWidget(
                        label: 'Riceverai a breve una email di conferma con tutti i dettagli.',
                        fontSize: 16,
                        fontWeight: '500',
                        textColor: Color(0xff616161),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 32),
                      NarFormLabelWidget(
                        label: 'Puoi chiudere questa scheda.',
                        fontSize: 16,
                        fontWeight: '500',
                        textColor: Color(0xff616161),
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.clip,
                      ),
                      // BaseNewarcButton(
                      //   width: 200,
                      //   textColor: AppColor.white,
                      //   color: Theme.of(context).primaryColor,
                      //   buttonText: "Torna alla piattaforma",
                      //   onPressed: () {
                      //     Navigator.of(context).pushReplacementNamed(
                      //       LoginPage.route,
                      //     );
                      //   },
                      // ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
