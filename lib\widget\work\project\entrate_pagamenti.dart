import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/app_const.dart' as appConst;
import 'package:newarc_platform/utils/downloadQuotationPDF.dart';
import '../../../classes/NewarcProjectFixedAssetsPropertyCategory.dart';
import '../../../classes/fixedProperty.dart';
import '../../../classes/newarcProject.dart';
import '../../../classes/newarcProjectFixedAssetsPropertyPagamento.dart';
import '../../../classes/newarcProjectFixedAssetsPropertyPercentage.dart';
import '../../../utils/color_schema.dart';
import '../../../utils/various.dart';
import '../../UI/base_newarc_button.dart';
import '../../UI/base_newarc_popup.dart';
import '../../UI/custom_textformfield.dart';
import '../../UI/form-label.dart';
import '../../UI/link.dart';
import '../../UI/tab/common_icon_button.dart';

class EntratePagamenti extends StatefulWidget {
  final NewarcProject? project;
  const EntratePagamenti({super.key,this.project});

  @override
  State<EntratePagamenti> createState() => _EntratePagamentiState();
}

class _EntratePagamentiState extends State<EntratePagamenti> {

  NumberFormat localCurrencyFormatMainWithDecimal = NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 2);

  bool isLoading = false;
  String progressMessage = '';
  @override
  void initState() {
    super.initState();
    setInitialValues();
  }

  @protected
  void didUpdateWidget(EntratePagamenti oldWidget) {
    super.didUpdateWidget(oldWidget);
    setInitialValues();
  }

  setInitialValues() async{
    if(!mounted) return;
    setState(() {
      isLoading = true;
    });
    FixedProperty fixedProperty = widget.project!.fixedProperty!;

    try {

      for (NewarcProjectPagamento pagamento in fixedProperty.newarcProjectFixedAssetsPropertyPagamento ?? []) {
        if(!(pagamento.isManualCategory ?? false)){
          DocumentSnapshot<Map<String, dynamic>> collectionQuery = await FirebaseFirestore.instance
              .collection(appConfig.COLLECT_NEWARCPROJECTFIXEDASSETSPROPERTYCATEGORY)
              .doc(pagamento.newarcProjectFixedAssetsPropertyCategoryId)
              .get();
          if (collectionQuery.exists) {
            pagamento.categoryName = collectionQuery.data()?["name"];
          }
        }

        for (var rate in pagamento.rate ?? []) {
          DocumentSnapshot<Map<String, dynamic>> perQuery = await FirebaseFirestore.instance
              .collection(appConfig.COLLECT_NEWARCPROJECTFIXEDASSETSPROPERTYPERCENTAGE)
              .doc(rate.newarcProjectFixedAssetsPercentageId)
              .get();
          if (perQuery.exists) {
            rate.percentage = perQuery.data()?["percentage"];
          }
        }
      }

      widget.project?.fixedProperty?.newarcProjectFixedAssetsPropertyPagamento = fixedProperty.newarcProjectFixedAssetsPropertyPagamento;

      if(!mounted) return;
      setState(() {
        isLoading = false;
      });
    } catch (e, s) {
      if(!mounted) return;
      setState(() {
        isLoading = false;
      });
      print({e, s});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      child: isLoading
          ? Center(child: NarFormLabelWidget(label: 'Loading'))
          : SingleChildScrollView(
            child: Column(
            children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    NarFormLabelWidget(
                      label: 'Entrate',
                      fontSize: 17,
                      fontWeight: '700',
                      textColor: AppColor.black,
                    ),
                    MouseRegion(
                      cursor: SystemMouseCursors.click,
                      child: GestureDetector(
                        onTap: () async {
                          showAddPagamentiPopup();
                        },
                        child: Container(
                            height: 35,
                            width: 175,
                            padding: const EdgeInsets.symmetric(horizontal: 15.0),
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child:NarFormLabelWidget(
                              label: "Aggiungi entrate",
                              fontSize: 14,
                              fontWeight: '600',
                              textColor: AppColor.white,
                            )
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 25),
                buildPaymentSummary(widget.project?.fixedProperty?.newarcProjectFixedAssetsPropertyPagamento ?? []),
                SizedBox(height: 10),
              ],
            ),
            SizedBox(height: 25),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                NarFormLabelWidget(label: progressMessage),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    BaseNewarcButton(
                      buttonText: "Salva",
                      onPressed: () async {
                        setState(() {
                          progressMessage =
                          'Salvataggio in corso...';
                        });

                        FixedProperty? _fixedProperty;
                        try {
                          _fixedProperty = FixedProperty({
                            "newarcProjectFixedAssetsPropertyPagamento": widget.project?.fixedProperty?.newarcProjectFixedAssetsPropertyPagamento
                                ?.map((pagamento) => pagamento.toMap())
                                .toList(),
                          });
                        } catch (e, s) {
                          print({e, s});
                        }

                        widget.project!.fixedProperty = _fixedProperty;


                        final FirebaseFirestore _db =
                            FirebaseFirestore.instance;

                        try {
                          await _db
                              .collection(
                              appConfig.COLLECT_NEWARC_PROJECTS)
                              .doc(widget.project!.id)
                              .update({
                            'fixedProperty.newarcProjectFixedAssetsPropertyPagamento':
                            widget.project!.fixedProperty!.newarcProjectFixedAssetsPropertyPagamento
                                ?.map((e) => e.toMap())
                                .toList(),
                          });

                          for (NewarcProjectPagamento pagamento in widget.project?.fixedProperty?.newarcProjectFixedAssetsPropertyPagamento ?? []) {

                            // category not added from manually then  fetch from DB
                            if(!(pagamento.isManualCategory ?? false)){
                              DocumentSnapshot<Map<String, dynamic>> collectionQuery = await FirebaseFirestore.instance
                                  .collection(appConfig.COLLECT_NEWARCPROJECTFIXEDASSETSPROPERTYCATEGORY)
                                  .doc(pagamento.newarcProjectFixedAssetsPropertyCategoryId)
                                  .get();
                              if (collectionQuery.exists) {
                                pagamento.categoryName = collectionQuery.data()?["name"];
                              }
                            }

                            for (var rate in pagamento.rate ?? []) {
                              DocumentSnapshot<Map<String, dynamic>> perQuery = await FirebaseFirestore.instance
                                  .collection(appConfig.COLLECT_NEWARCPROJECTFIXEDASSETSPROPERTYPERCENTAGE)
                                  .doc(rate.newarcProjectFixedAssetsPercentageId)
                                  .get();
                              if (perQuery.exists) {
                                rate.percentage = perQuery.data()?["percentage"];
                              }
                            }
                          }

                          setState(() {
                            widget.project?.fixedProperty?.newarcProjectFixedAssetsPropertyPagamento = widget.project?.fixedProperty?.newarcProjectFixedAssetsPropertyPagamento;
                            progressMessage = 'Saved!';
                          });
                        } catch (e) {
                          log("ERROR WHILE SAVING PROJECT =====> ${e.toString()}");
                          setState(() {
                            progressMessage = 'Error';
                          });
                        }
                      },
                    )
                  ],
                ),
              ],
            ),
                    ],
                  ),
          ),
    );
  }

  Widget buildPaymentSummary(List<NewarcProjectPagamento> pagamentiList) {
    double grandTotal = 0;
    double grandTotalIVA = 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // GRAND TOTAL HEADER
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 15),
          decoration: BoxDecoration(
              color: Color(0xFF343434),
              borderRadius: BorderRadius.circular(8)
          ),
          child: Row(
            // mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Expanded(
                child: NarFormLabelWidget(
                  label: "Totale",
                  fontSize: 14,
                  fontWeight: '600',
                  textColor: AppColor.white,
                ),
              ),
              Expanded(
                flex: 2,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        NarFormLabelWidget(
                          label: "Totale",
                          fontSize: 9,
                          fontWeight: '600',
                          textColor: AppColor.white,
                        ),
                        SizedBox(height: 3,),
                        NarFormLabelWidget(
                          label: "${localCurrencyFormatMain.format(_calculateTotal(pagamentiList))}€",
                          fontSize: 14,
                          fontWeight: '600',
                          textColor: AppColor.white,
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        NarFormLabelWidget(
                          label: "Iva",
                          fontSize: 9,
                          fontWeight: '600',
                          textColor: AppColor.white,
                        ),
                        SizedBox(height: 3,),
                        NarFormLabelWidget(
                          label: "+ ${localCurrencyFormatMain.format(_calculateTotalIVA(pagamentiList))}€",
                          fontSize: 14,
                          fontWeight: '600',
                          textColor: AppColor.white,
                        ),
                      ],
                    ),
                    SizedBox(width: 40,)
                  ],
                ),
              ),

            ],
          ),
        ),

        const SizedBox(height: 20),

        // CATEGORY LEVEL
        ...pagamentiList.asMap().entries.map((entry) {
          final index = entry.key;
          final pagamento = entry.value;
          final rateList = pagamento.rate ?? [];
          final total = pagamento.total ?? 0;
          final totalIVA = pagamento.totalIVA ?? 0;

          grandTotal += total;
          grandTotalIVA += totalIVA;

          return Container(
            width: double.infinity,
            margin: const EdgeInsets.only(bottom: 10),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Color(0xFFE0E0E0))
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Category Header
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 15),

                  decoration: BoxDecoration(
                      color: Color(0xFFE0E0E0),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Color(0xFFE0E0E0))
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Expanded(
                        child: NarFormLabelWidget(
                          label: pagamento.categoryName ?? "",
                          fontSize: 14,
                          fontWeight: '700',
                          textColor: AppColor.black,
                        ),
                      ),

                      Expanded(
                        flex: 2,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                NarFormLabelWidget(
                                  label: "Totale",
                                  fontSize: 9,
                                  fontWeight: '600',
                                  textColor: AppColor.black,
                                ),
                                SizedBox(height: 3,),
                                NarFormLabelWidget(
                                  label: "${localCurrencyFormatMain.format(total)}€",
                                  fontSize: 14,
                                  fontWeight: '700',
                                  textColor: AppColor.black,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                NarFormLabelWidget(
                                  label: "Iva",
                                  fontSize: 9,
                                  fontWeight: '600',
                                  textColor: AppColor.black,
                                ),
                                SizedBox(height: 3,),
                                NarFormLabelWidget(
                                  label: "+ ${localCurrencyFormatMain.format(totalIVA)}€",
                                  fontSize: 14,
                                  fontWeight: '600',
                                  textColor: AppColor.black,
                                ),
                              ],
                            ),
                            SizedBox(width: 10,),
                            IconButtonWidget(
                              onTap: () {
                                showAddPagamentiPopup(newNewarcProjectFixedAssetsPropertyPagamento: pagamento,index: index);
                              },
                              isSvgIcon: true,
                              icon: 'assets/icons/edit.svg',
                              iconColor: AppColor.greyColor,
                            ),
                          ],
                        ),
                      ),

                    ],
                  ),
                ),

                // Rata Rows
                ...rateList.map((rate) {
                  final rataLabel = "Rata ${rate.index} - ${rate.percentage ?? 0}%";
                  final amount = rate.rate ?? 0;
                  final iva = rate.ivaAmount ?? 0;
                  final ivaPerc = rate.ivaPercentage ?? 0;

                  return Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Row(
                      // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: NarFormLabelWidget(
                            label: rataLabel,
                            fontSize: 13,
                            fontWeight: '500',
                            textColor: AppColor.black,
                          ),
                        ),
                        Expanded(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              NarFormLabelWidget(
                                label: "${localCurrencyFormatMain.format(amount)}€",
                                fontSize: 13,
                                fontWeight: '500',
                                textColor: AppColor.black,
                              ),
                            ],
                          ),
                          flex: 2,
                        ),
                        Expanded(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              NarFormLabelWidget(
                                label: "+ ${localCurrencyFormatMain.format(iva)}€ iva $ivaPerc%",
                                fontSize: 13,
                                fontWeight: '500',
                                textColor: AppColor.black,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),

                const SizedBox(height: 10),
              ],
            ),
          );
        }).toList()
      ],
    );
  }

  double _calculateTotal(List<NewarcProjectPagamento> list) {
    return list.fold(0, (sum, p) => sum + (p.total ?? 0));
  }

  double _calculateTotalIVA(List<NewarcProjectPagamento> list) {
    return list.fold(0, (sum, p) => sum + (p.totalIVA ?? 0));
  }

  Future<void> showAddPagamentiPopup({NewarcProjectPagamento? newNewarcProjectFixedAssetsPropertyPagamento,int? index}) async {
    List<String> formErrorMessage = [];

    var _newarcProjectFixedAssetsPropertyPagamento = newNewarcProjectFixedAssetsPropertyPagamento != null
        ? NewarcProjectPagamento.fromDocument(newNewarcProjectFixedAssetsPropertyPagamento.toMap(),"")
        : NewarcProjectPagamento.empty();
    if(newNewarcProjectFixedAssetsPropertyPagamento?.categoryName != null){
      _newarcProjectFixedAssetsPropertyPagamento.categoryName = newNewarcProjectFixedAssetsPropertyPagamento?.categoryName;
    }

    TextEditingController manualCategoryController = TextEditingController(text: _newarcProjectFixedAssetsPropertyPagamento.isManualCategory ?? false ? _newarcProjectFixedAssetsPropertyPagamento.categoryName ?? "" : "");

    List<Rate> rateList = (newNewarcProjectFixedAssetsPropertyPagamento?.rate ?? [])
        .map((rate){
      Rate newRate = Rate.fromDocument(rate.toMap(),"");
      newRate.percentage = rate.percentage;
      return newRate;
    }).toList();

    bool isSaveButtonEnableFlag = false;

    bool isSaveButtonEnabled() {
      int totalPercentage = 0;
      for (var rate in rateList) {
        totalPercentage += rate.percentage ?? 0;
      }
      return totalPercentage == 100;
    }

    Future<List>? _fetchCategoryFuture;
    Future<List>? _fetchPercentageFuture;

    await showDialog(
        context: context,
        builder: (BuildContext _context) {

          return StatefulBuilder(builder: (__context, _setState) {
            _fetchCategoryFuture ??= fetchCategory();
            void refreshFetchCategory() {
              _setState(() {
                _fetchCategoryFuture = fetchCategory();
              });
            }

            _fetchPercentageFuture ??= fetchPercentage();

            void refreshFetchPercentage() {
              _setState(() {
                _fetchPercentageFuture = fetchPercentage();
              });
            }
            return Center(
                child: BaseNewarcPopup(
                  title: "Aggiungi pagamento",
                  buttonText: "Aggiungi",
                  formErrorMessage: formErrorMessage,
                  onPressed: (isSaveButtonEnableFlag) ? () async {
                    _setState((){
                      formErrorMessage.clear();
                      formErrorMessage.add("Salvataggio in corso...");
                    });
                    try {

                      double totalIVA = 0.0;
                      for (var rate in rateList) {
                        totalIVA += rate.ivaAmount ?? 0.0;
                        if((rate.isMerged ?? false) && (rate.mergedIntoRateId?.isNotEmpty ?? false)){
                          rate.isMerged = true;

                          
                          final targetRate = widget.project!.fixedProperty!.newarcProjectFixedAssetsPropertyPagamento!
                              .expand((element) => element.rate ?? [])
                              .firstWhere(
                                (r) {
                              return r.uniqueId == rate.mergedIntoRateId;
                            },
                            orElse: () => Rate.empty(),
                          );

                          if (targetRate.uniqueId?.isNotEmpty ?? false) {
                            targetRate.margeRateUniqueIDS ??= [];

                            final index = targetRate.margeRateUniqueIDS!.indexWhere((id) => id == rate.uniqueId);

                            if (index != -1) {
                              // Already exists: update the value
                              targetRate.margeRateUniqueIDS![index] = rate.uniqueId;
                            } else {
                              // Doesn't exist: add it
                              targetRate.margeRateUniqueIDS!.add(rate.uniqueId);
                            }
                          }
                        }else{
                          rate.isMerged = false;
                          rate.mergedIntoRateId = "";
                        }
                      }
                      _newarcProjectFixedAssetsPropertyPagamento.totalIVA = totalIVA;
                      _newarcProjectFixedAssetsPropertyPagamento.rate = rateList;

                      if(_newarcProjectFixedAssetsPropertyPagamento.isManualCategory ?? false){
                        _newarcProjectFixedAssetsPropertyPagamento.categoryName = manualCategoryController.text.trim();
                      }

                      setState(() {
                        if (index != null && index >= 0 && widget.project?.fixedProperty?.newarcProjectFixedAssetsPropertyPagamento != null && index < (widget.project?.fixedProperty?.newarcProjectFixedAssetsPropertyPagamento?.length ?? 0)) {
                          widget.project?.fixedProperty!.newarcProjectFixedAssetsPropertyPagamento?[index] = _newarcProjectFixedAssetsPropertyPagamento;
                        } else {
                          widget.project?.fixedProperty?.newarcProjectFixedAssetsPropertyPagamento?.add(_newarcProjectFixedAssetsPropertyPagamento);
                        }
                      });

                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Salvato");
                      });
                    } catch (e) {
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Si è verificato un errore.");
                      });
                      log("Error While Adding PAGAMENTO -----> ${e.toString()}");
                    }
                  } : (){return false;},
                  onPressedSecondButton: () {
                    WidgetsBinding.instance.addPostFrameCallback((_){
                      showDialog(
                        context: context,
                        barrierDismissible: true,
                        builder: (BuildContext context) {
                          return StatefulBuilder(builder: (context,setStateDialog){
                            return Center(
                              child: BaseNewarcPopup(
                                  buttonColor: Color(0xFFE82525),
                                  title: "Elimina Entrata",
                                  buttonText: "Rimuovi",
                                  onPressed: () async {
                                    try{
                                      if (index != null && index >= 0) {
                                        final sectionToRemove = widget.project!.fixedProperty!.newarcProjectFixedAssetsPropertyPagamento![index];

                                        for (final rate in sectionToRemove.rate ?? []) {
                                          // Step 1: If this rate is merged into another, remove its reference from the parent
                                          if (rate.mergedIntoRateId?.isNotEmpty ?? false) {
                                            final parentRate = widget.project!.fixedProperty!.newarcProjectFixedAssetsPropertyPagamento!
                                                .expand((e) => e.rate ?? [])
                                                .firstWhere(
                                                  (r) => r.uniqueId == rate.mergedIntoRateId,
                                              orElse: () => Rate.empty(),
                                            );

                                            if ((parentRate.uniqueId?.isNotEmpty ?? false) &&
                                                (parentRate.margeRateUniqueIDS?.contains(rate.uniqueId) ?? false)) {
                                              parentRate.margeRateUniqueIDS?.remove(rate.uniqueId);
                                              if (parentRate.margeRateUniqueIDS?.isEmpty ?? true) {
                                                parentRate.isMerged = false;
                                              }
                                            }
                                          }

                                          // Step 2: If this rate is a parent, unmerge all child rates
                                          final childRates = widget.project!.fixedProperty!.newarcProjectFixedAssetsPropertyPagamento!
                                              .expand((e) => e.rate ?? [])
                                              .where((r) => r.mergedIntoRateId == rate.uniqueId)
                                              .toList();

                                          for (final child in childRates) {
                                            child.mergedIntoRateId = "";
                                            child.isMerged = false;
                                          }
                                        }

                                        // Step 3: Remove the section
                                        widget.project!.fixedProperty!.newarcProjectFixedAssetsPropertyPagamento?.removeAt(index);
                                        _newarcProjectFixedAssetsPropertyPagamento = NewarcProjectPagamento.empty();
                                        rateList.clear();
                                        _setState(() {});
                                        setState(() {});
                                      } else {
                                        _newarcProjectFixedAssetsPropertyPagamento = NewarcProjectPagamento.empty();
                                        rateList.clear();
                                        _setState(() {});
                                        return false;
                                      }
                                    }catch(e){
                                      print("---------- ERROR While Elimina Entrata ------> ${e.toString()}");
                                    }
                                  },
                                  column: Container(
                                    width: 400,
                                    padding: EdgeInsets.symmetric(vertical: 25),
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      crossAxisAlignment: CrossAxisAlignment.center,
                                      children: [
                                        Center(
                                          child: NarFormLabelWidget(
                                            label:  "Vuoi davvero eliminare questa entrata?" ,
                                            fontSize: 18,
                                            fontWeight: '700',
                                          ),
                                        ),
                                        SizedBox(height: 10,),
                                        Center(
                                          child: NarFormLabelWidget(
                                            label:  "Assicurati prima che non sia già stata pagata!" ,
                                            fontSize: 18,
                                            fontWeight: '500',
                                          ),
                                        ),
                                      ],
                                    ),
                                  )),
                            );
                          });
                        },
                      );
                    });
                    return false;
                  },
                  disableButton: (!isSaveButtonEnableFlag),
                  buttonColor: Theme.of(context).primaryColor.withOpacity(isSaveButtonEnableFlag ? 1.0 : 0.5),
                  isSecondButtonVisible: true,
                  secondButtonColor: Color(0xFFEA3132),
                  secondButtonText: "Elimina",
                  column: Container(
                    height: MediaQuery.of(context).size.height * 0.80,
                    width: 800,
                    child: ListView(
                      shrinkWrap: true,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            //----- Category
                            _newarcProjectFixedAssetsPropertyPagamento.isManualCategory ?? false ?
                            SizedBox(
                              width: 350,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    height:38,
                                    width: 350,
                                    child: Row(
                                      crossAxisAlignment: CrossAxisAlignment.center,
                                      children: [
                                        NarFormLabelWidget(
                                          label: 'Inserisci categoria manuale',
                                          fontSize: 14,
                                          fontWeight: '600',
                                          textColor: Color(0xFF454545),
                                        ),
                                      ],
                                    ),
                                  ),
                                  // SizedBox(height: 10,),
                                  SizedBox(
                                    // height: 75,
                                    width: 350,
                                    child: CustomTextFormField(
                                      label: "",
                                      isExpanded: false,
                                      validator: (value) {
                                        if (value == '') {
                                          return 'Required!';
                                        }
                                        return null;
                                      },
                                      controller: manualCategoryController,
                                    ),
                                  ),
                                ],
                              ),
                            ) :
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [

                                SizedBox(
                                  width: 350,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      NarFormLabelWidget(
                                        label: "Seleziona categoria",
                                        fontSize: 14,
                                        fontWeight: '600',
                                        textColor: AppColor.greyColor,
                                      ),
                                      NarLinkWidget(
                                        fontSize: 12,
                                        fontWeight: '600',
                                        textColor: Theme.of(context).primaryColor,
                                        text: 'Nuova categoria',
                                        textDecoration: TextDecoration.underline,
                                        onClick: () {
                                          showAddCategoryPopup(onCategoryAdded: refreshFetchCategory);
                                        },
                                      )
                                    ],
                                  ),
                                ),
                                SizedBox(height: 5),
                                SizedBox(
                                  width: 350,
                                  height: 51,
                                  child: FutureBuilder<List>(
                                      future: _fetchCategoryFuture,
                                      builder: (context, snapshot) {
                                        if (snapshot.hasData) {
                                          return DropdownButtonFormField<String>(
                                            isExpanded: true,
                                            initialValue: _newarcProjectFixedAssetsPropertyPagamento.newarcProjectFixedAssetsPropertyCategoryId?.isNotEmpty ?? false
                                                ? "${_newarcProjectFixedAssetsPropertyPagamento.newarcProjectFixedAssetsPropertyCategoryId}|${_newarcProjectFixedAssetsPropertyPagamento.categoryName}"
                                                : null,
                                            icon: Padding(
                                              padding: EdgeInsets.only(right: 8),
                                              child: SvgPicture.asset(
                                                'assets/icons/arrow_down.svg',
                                                width: 12,
                                                color: Color(0xff7e7e7e),
                                              ),
                                            ),
                                            style: TextStyle(
                                              overflow: TextOverflow.ellipsis,
                                              color: Colors.black,
                                              fontSize: 12.0,
                                              fontWeight: FontWeight.bold,
                                              fontStyle: FontStyle.normal,
                                            ),
                                            borderRadius: BorderRadius.circular(8),
                                            decoration: InputDecoration(
                                              border: OutlineInputBorder(
                                                borderRadius:
                                                BorderRadius.all(Radius.circular(8)),
                                                borderSide: BorderSide(
                                                  color: Color.fromRGBO(227, 227, 227, 1),
                                                  width: 1,
                                                ),
                                              ),
                                              hintStyle: TextStyle(
                                                color: Colors.grey,
                                                fontSize: 15.0,
                                                fontWeight: FontWeight.w800,
                                                fontStyle: FontStyle.normal,
                                                letterSpacing: 0,
                                              ),
                                              focusedBorder: OutlineInputBorder(
                                                borderRadius:
                                                BorderRadius.all(Radius.circular(8)),
                                                borderSide: BorderSide(
                                                  color: Color.fromRGBO(227, 227, 227, 1),
                                                  width: 1,
                                                ),
                                              ),
                                              contentPadding: EdgeInsets.symmetric(
                                                  horizontal: 12, vertical: 8),
                                              fillColor: Colors.white,
                                            ),
                                            onChanged: (String? value) {
                                              if (value?.isNotEmpty ?? false) {
                                                final parts = value!.split('|');
                                                final firebaseId = parts[0];
                                                final name = parts.length > 1 ? parts[1] : "";
                                                _setState(() {
                                                  _newarcProjectFixedAssetsPropertyPagamento.newarcProjectFixedAssetsPropertyCategoryId = firebaseId;
                                                  _newarcProjectFixedAssetsPropertyPagamento.categoryName = name;
                                                });
                                              }
                                            },
                                            dropdownColor: Colors.white,
                                            items: snapshot.data
                                                ?.map<DropdownMenuItem<String>>((item) {
                                              return DropdownMenuItem<String>(
                                                value: item['value'],
                                                child: NarFormLabelWidget(
                                                  label: item['label']!,
                                                  textColor: Colors.black,
                                                  fontSize: 14,
                                                  fontWeight: '600',
                                                ),
                                              );
                                            }).toList(),
                                          );
                                        } else if (snapshot.hasError) {
                                          return Container(
                                            width: 30,
                                            height: 30,
                                            alignment: Alignment.center,
                                          );
                                        }
                                        return Center(
                                          child: CircularProgressIndicator(
                                            color: Theme.of(context).primaryColor,
                                          ),
                                        );
                                      }),
                                ),
                              ],
                            ),

                            //----- Total
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                  height: 38,
                                  width: 350,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      NarFormLabelWidget(
                                        label: "Cifra totale senza iva",
                                        fontSize: 14,
                                        fontWeight: '600',
                                        textColor: AppColor.greyColor,
                                      ),
                                      Row(
                                        children: [
                                          NarFormLabelWidget(
                                            label: 'Con Agevolazioni',
                                            fontSize: 12,
                                            fontWeight: '600',
                                            textColor: Color(0xFF454545),
                                          ),
                                          SizedBox(
                                            width: 10,
                                          ),
                                          SizedBox(
                                            height: 20,
                                            child: Switch(
                                              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                              value: _newarcProjectFixedAssetsPropertyPagamento.hasConcessions!,
                                              activeThumbColor: AppColor.white,
                                              activeTrackColor: Theme.of(context).primaryColor,
                                              onChanged: (bool value) async {
                                                _setState(() {
                                                  _newarcProjectFixedAssetsPropertyPagamento.hasConcessions = !_newarcProjectFixedAssetsPropertyPagamento.hasConcessions!;
                                                  isSaveButtonEnableFlag = isSaveButtonEnabled();
                                                });

                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  alignment: Alignment.center,
                                ),
                                SizedBox(
                                  height: 51,
                                  width: 350,
                                  child: CustomTextFormField(
                                    isExpanded: false,
                                    label: "",
                                    isMoney: true,
                                    isShowPrefillMoneyIcon: false,
                                    controller: TextEditingController(text: _newarcProjectFixedAssetsPropertyPagamento.total != null ? localCurrencyFormatMainWithDecimal.format(_newarcProjectFixedAssetsPropertyPagamento.total) .toString() : ""),
                                    suffixIcon: Container(
                                      width: 50,
                                      padding: const EdgeInsets.only(right: 5),
                                      child: Align(
                                          alignment: Alignment.centerRight,
                                          child: NarFormLabelWidget(
                                            label: "€",
                                            textColor: AppColor.greyColor,
                                            fontWeight: "500",
                                          )),
                                    ),
                                    minLines: 1,
                                    onChangedCallback: (String value){
                                      if(value.isNotEmpty){
                                        _newarcProjectFixedAssetsPropertyPagamento.total = double.tryParse(value.trim().replaceAll(".", "").replaceAll(",", "."));
                                      }
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        SizedBox(height: 10,),
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Container(
                            width: 350,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                NarFormLabelWidget(
                                  label: 'Categoria manuale',
                                  fontSize: 12,
                                  fontWeight: '600',
                                  textColor: Color(0xFF454545),
                                ),
                                SizedBox(
                                  width: 10,
                                ),
                                SizedBox(
                                  height: 20,
                                  child: Switch(
                                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                    value: _newarcProjectFixedAssetsPropertyPagamento.isManualCategory!,
                                    activeThumbColor: AppColor.white,
                                    activeTrackColor: Theme.of(context).primaryColor,
                                    onChanged: (bool value) async {
                                      _setState(() {
                                        _newarcProjectFixedAssetsPropertyPagamento.isManualCategory = !_newarcProjectFixedAssetsPropertyPagamento.isManualCategory!;
                                        isSaveButtonEnableFlag = isSaveButtonEnabled();
                                      });

                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(height: 15,),
                        ...rateList.map((rate){
                          return Container(
                            decoration: BoxDecoration(
                              border: Border.all(color: Color(0xFFDBDBDB),width: 1),
                              borderRadius: BorderRadius.circular(7)
                            ),
                            padding: EdgeInsets.all(10),
                            margin: EdgeInsets.only(bottom: 15),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                NarFormLabelWidget(
                                  label: "Rate ${rate.index}",
                                  fontSize: 14,
                                  fontWeight: '700',
                                  textColor: AppColor.black,
                                ),
                                SizedBox(height: 10,),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    //----Per
                                    Column(
                                      children: [
                                        SizedBox(
                                          width: 300,
                                          child: Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              NarFormLabelWidget(
                                                label: "Percentuale",
                                                fontSize: 14,
                                                fontWeight: '600',
                                                textColor: AppColor.greyColor,
                                              ),
                                              NarLinkWidget(
                                                fontSize: 12,
                                                fontWeight: '600',
                                                textColor: Theme.of(context).primaryColor,
                                                text: 'Nuova percentuale',
                                                textDecoration: TextDecoration.underline,
                                                onClick: () {
                                                  showAddPercentagePopup(onPercentageAdded: refreshFetchPercentage);
                                                },
                                              )
                                            ],
                                          ),
                                        ),
                                        SizedBox(height: 10),
                                        SizedBox(
                                          width: 300,
                                          child: FutureBuilder<List>(
                                              future: _fetchPercentageFuture,
                                              builder: (context, snapshot) {
                                                if (snapshot.hasData) {
                                                  return DropdownButtonFormField<String>(
                                                    isExpanded: true,
                                                    initialValue: rate.newarcProjectFixedAssetsPercentageId?.isNotEmpty ?? false ? "${rate.newarcProjectFixedAssetsPercentageId}|${rate.percentage}" : null,
                                                    icon: Padding(
                                                      padding: EdgeInsets.only(right: 8),
                                                      child: SvgPicture.asset(
                                                        'assets/icons/arrow_down.svg',
                                                        width: 12,
                                                        color: Color(0xff7e7e7e),
                                                      ),
                                                    ),
                                                    style: TextStyle(
                                                      overflow: TextOverflow.ellipsis,
                                                      color: Colors.black,
                                                      fontSize: 12.0,
                                                      fontWeight: FontWeight.bold,
                                                      fontStyle: FontStyle.normal,
                                                    ),
                                                    borderRadius: BorderRadius.circular(8),
                                                    decoration: InputDecoration(
                                                      border: OutlineInputBorder(
                                                        borderRadius:
                                                        BorderRadius.all(Radius.circular(8)),
                                                        borderSide: BorderSide(
                                                          color: Color.fromRGBO(227, 227, 227, 1),
                                                          width: 1,
                                                        ),
                                                      ),
                                                      hintStyle: TextStyle(
                                                        color: Colors.grey,
                                                        fontSize: 15.0,
                                                        fontWeight: FontWeight.w800,
                                                        fontStyle: FontStyle.normal,
                                                        letterSpacing: 0,
                                                      ),
                                                      focusedBorder: OutlineInputBorder(
                                                        borderRadius:
                                                        BorderRadius.all(Radius.circular(8)),
                                                        borderSide: BorderSide(
                                                          color: Color.fromRGBO(227, 227, 227, 1),
                                                          width: 1,
                                                        ),
                                                      ),
                                                      contentPadding: EdgeInsets.symmetric(
                                                          horizontal: 12, vertical: 8),
                                                      fillColor: Colors.white,
                                                    ),
                                                    onChanged: (String? value) {
                                                      if (value != null && value.contains('|')) {
                                                        final parts = value.split('|');
                                                        final percentageId = parts[0];
                                                        final per = int.tryParse(parts[1]) ?? 0;
                            
                                                        int rateIndex = rate.index! - 1;
                            
                                                        double baseRate = (per * (_newarcProjectFixedAssetsPropertyPagamento.total ?? 0)) / 100;
                                                        double newRate = baseRate;
                            
                                                        int iva = rate.ivaPercentage ?? 0;
                                                        double ivaAmount = (baseRate * iva) / 100;
                            
                                                        //rate.ivaAmount = ivaAmount;
                            
                            
                                                        _setState(() {
                                                          rateList[rateIndex].percentage = per;
                                                          rateList[rateIndex].newarcProjectFixedAssetsPercentageId = percentageId;
                                                          rateList[rateIndex].rate = newRate;
                                                          rateList[rateIndex].uniqueId = generateRandomString(20);
                                                          rateList[rateIndex].ivaAmount = ivaAmount;
                            
                                                          isSaveButtonEnableFlag = isSaveButtonEnabled();
                                                        });
                                                      }
                                                    },
                                                    dropdownColor: Colors.white,
                                                    items: snapshot.data?.map<DropdownMenuItem<String>>((item) {
                                                      return DropdownMenuItem<String>(
                                                        value: item['value'],
                                                        child: NarFormLabelWidget(
                                                          label: item['label']!,
                                                          textColor: Colors.black,
                                                          fontSize: 14,
                                                          fontWeight: '600',
                                                        ),
                                                      );
                                                    }).toList(),
                                                  );
                                                } else if (snapshot.hasError) {
                                                  return Container(
                                                    width: 30,
                                                    height: 30,
                                                    alignment: Alignment.center,
                                                  );
                                                }
                                                return Center(
                                                  child: CircularProgressIndicator(
                                                    color: Theme.of(context).primaryColor,
                                                  ),
                                                );
                                              }),
                                        ),
                                      ],
                                    ),
                                    // SizedBox(width: 20,),
                            
                                    //----Rate
                                    Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Container(
                                          height: 38,
                                          child: NarFormLabelWidget(
                                            label: "Rata",
                                            fontSize: 14,
                                            fontWeight: '600',
                                            textColor: AppColor.greyColor,
                                          ),
                                          alignment: Alignment.center,
                                        ),
                                        SizedBox(
                                          width: 136,
                                          child: CustomTextFormField(
                                            label: "",
                                            enabled: false,
                                            isShowPrefillMoneyIcon: false,
                                            fillColor: AppColor.disabledGreyColor,
                                            isExpanded: false,
                                            isMoney: true,
                                            validator: (value) {
                                              if (value == '') {
                                                return 'Required!';
                                              }
                                              return null;
                                            },
                                            controller: TextEditingController(text: localCurrencyFormatMainWithDecimal.format(rate.rate)),
                                          ),
                                        ),
                                      ],
                                    ),
                                    // SizedBox(width: 10,),
                            
                                    //---IVA
                                    Row(
                                      children: [
                                        Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Container(
                                              height: 38,
                                              child: NarFormLabelWidget(
                                                label: "Iva",
                                                fontSize: 14,
                                                fontWeight: '600',
                                                textColor: AppColor.greyColor,
                                              ),
                                              alignment: Alignment.center,
                                            ),
                                            SizedBox(
                                              width:136,
                                              child: DropdownButtonFormField<int>(
                                                isExpanded: true,
                                                initialValue: rate.ivaPercentage ?? 0,
                                                icon: Padding(
                                                  padding: EdgeInsets.only(right: 8),
                                                  child: SvgPicture.asset(
                                                    'assets/icons/arrow_down.svg',
                                                    width: 12,
                                                    color: Color(0xff7e7e7e),
                                                  ),
                                                ),
                                                style: TextStyle(
                                                  overflow: TextOverflow.ellipsis,
                                                  color: Colors.black,
                                                  fontSize: 12.0,
                                                  fontWeight: FontWeight.bold,
                                                  fontStyle: FontStyle.normal,
                                                ),
                                                borderRadius: BorderRadius.circular(8),
                                                decoration: InputDecoration(
                                                  border: OutlineInputBorder(
                                                    borderRadius:
                                                    BorderRadius.all(Radius.circular(8)),
                                                    borderSide: BorderSide(
                                                      color: Color.fromRGBO(227, 227, 227, 1),
                                                      width: 1,
                                                    ),
                                                  ),
                                                  hintStyle: TextStyle(
                                                    color: Colors.grey,
                                                    fontSize: 15.0,
                                                    fontWeight: FontWeight.w800,
                                                    fontStyle: FontStyle.normal,
                                                    letterSpacing: 0,
                                                  ),
                                                  focusedBorder: OutlineInputBorder(
                                                    borderRadius:
                                                    BorderRadius.all(Radius.circular(8)),
                                                    borderSide: BorderSide(
                                                      color: Color.fromRGBO(227, 227, 227, 1),
                                                      width: 1,
                                                    ),
                                                  ),
                                                  contentPadding: EdgeInsets.symmetric(
                                                      horizontal: 12, vertical: 8),
                                                  fillColor: Colors.white,
                                                ),
                                                onChanged: (int? value) {
                                                  _setState(() {
                                                    rate.ivaPercentage = value ?? 0;
                            
                                                    int per = rate.percentage ?? 0;
                                                    double baseRate = (per * (_newarcProjectFixedAssetsPropertyPagamento.total ?? 0)) / 100;
                            
                                                    int iva = rate.ivaPercentage ?? 0;
                                                    double ivaAmount = (baseRate * iva) / 100;
                            
                                                    rate.ivaAmount = ivaAmount;
                                                  });
                                                },
                                                validator: (value) {
                                                  if (value == null) {
                                                    return "Required!";
                                                  }
                                                  return null;
                                                },
                                                dropdownColor: Colors.white,
                                                items: <int>[0,10,22].map<DropdownMenuItem<int>>((item) {
                                                  return DropdownMenuItem<int>(
                                                    value: item,
                                                    child: NarFormLabelWidget(
                                                      label: item.toString(),
                                                      textColor: Colors.black,
                                                      fontSize: 14,
                                                      fontWeight: '600',
                                                    ),
                                                  );
                                                }).toList(),
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(width: 10,),
                                        IconButtonWidget(
                                          onTap: () {
                                            showDialog(
                                                context: context,
                                                barrierDismissible: true,
                                                builder: (BuildContext context) {
                                                  return StatefulBuilder(builder: (context,setStateDialog){
                                                    return Center(
                                                      child: BaseNewarcPopup(
                                                          buttonColor: Color(0xFFE82525),
                                                          title: "Elimina Entrata",
                                                          buttonText: "Rimuovi",
                                                          onPressed: () async {
                                                            try{
                            
                                                              // 1. If this rate is merged into another, remove it from that parent
                                                              if (rate.mergedIntoRateId?.isNotEmpty ?? false) {
                                                                final parentRate = widget.project!.fixedProperty!.newarcProjectFixedAssetsPropertyPagamento!
                                                                    .expand((element) => element.rate ?? [])
                                                                    .firstWhere(
                                                                      (r) => r.uniqueId == rate.mergedIntoRateId,
                                                                  orElse: () => Rate.empty(),
                                                                );
                            
                                                                if ((parentRate.uniqueId?.isNotEmpty ?? false) &&
                                                                    (parentRate.margeRateUniqueIDS?.contains(rate.uniqueId) ?? false)) {
                                                                  parentRate.margeRateUniqueIDS?.remove(rate.uniqueId);
                            
                                                                  if (parentRate.margeRateUniqueIDS?.isEmpty ?? true) {
                                                                    parentRate.isMerged = false;
                                                                  }
                                                                }
                                                              }
                            
                                                              // 2. If this rate is a parent (others are merged into it), unmerge them
                                                              final childRates = widget.project!.fixedProperty!.newarcProjectFixedAssetsPropertyPagamento!
                                                                  .expand((element) => element.rate ?? [])
                                                                  .where((r){
                                                                return r.mergedIntoRateId == rate.uniqueId;
                                                              }).toList();
                            
                                                              for (final child in childRates) {
                                                                child.mergedIntoRateId = "";
                                                                child.isMerged = false;
                                                              }
                            
                                                              // 3. Remove the rate itself
                                                              rateList.remove(rate);
                            
                                                              // 4. Reassign indexes
                                                              for (int i = 0; i < rateList.length; i++) {
                                                                rateList[i].index = i + 1;
                                                              }
                            
                                                              // 5. Refresh UI
                                                              _setState(() {});
                                                            }catch(e){
                                                              print("---------- ERROR While Elimina Entrata------> ${e.toString()}");
                                                            }
                                                          },
                                                          column: Container(
                                                            width: 400,
                                                            padding: EdgeInsets.symmetric(vertical: 25),
                                                            child: Column(
                                                              mainAxisAlignment: MainAxisAlignment.center,
                                                              crossAxisAlignment: CrossAxisAlignment.center,
                                                              children: [
                                                                Center(
                                                                  child: NarFormLabelWidget(
                                                                    label:  "Vuoi davvero eliminare questa entrata?" ,
                                                                    fontSize: 18,
                                                                    fontWeight: '700',
                                                                  ),
                                                                ),
                                                                SizedBox(height: 10,),
                                                                Center(
                                                                  child: NarFormLabelWidget(
                                                                    label:  "Assicurati prima che non sia già stata pagata!" ,
                                                                    fontSize: 18,
                                                                    fontWeight: '500',
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          )),
                                                    );
                                                  });
                                                },
                                            );
                                          },
                                          isSvgIcon: true,
                                          backgroundColor: Colors.transparent,
                                          icon: 'assets/icons/delete.svg',
                                          iconColor: AppColor.greyColor,
                                        ),
                                      ],
                                    )
                                  ],
                                ),
                                SizedBox(height: 15,),
                                CustomTextFormField(
                                  isExpanded: false,
                                  minLines: 1,
                                  label: "Descrizione",
                                  controller: rate.descriptionController,
                                  hintText: "Es. Posa impianti completata",
                                  onChangedCallback: (value){
                                    if(value.toString().trim().isNotEmpty){
                                      _setState(() {
                                        rate.description = value;
                                        isSaveButtonEnableFlag = isSaveButtonEnabled();
                                      });
                                    }
                                  },
                                ),
                                SizedBox(height: 15,),
                                Row(
                                  children: [
                                    SizedBox(
                                      width: 300,
                                      child: Row(
                                        children: [
                                          Switch(
                                            value: rate.isMerged!,
                                            activeThumbColor: AppColor.white,
                                            activeTrackColor: Theme.of(context).primaryColor,
                                            onChanged: (bool value) async {
                                              _setState(() {
                                                rate.isMerged = !rate.isMerged!;
                                                isSaveButtonEnableFlag = isSaveButtonEnabled();
                                              });
                                            },
                                          ),
                                          SizedBox(
                                            width: 10,
                                          ),
                                          NarFormLabelWidget(
                                            label: 'Accorpa a esistente',
                                            fontSize: 12,
                                            fontWeight: '600',
                                            textColor: AppColor.black,
                                          ),
                                        ],
                                      ),
                                    ),
                                    //----- RATA DROPDOWN
                                    rate.isMerged! ?
                                    Column(
                                      children: [
                                        SizedBox(
                                          width: 300,
                                          child: Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              NarFormLabelWidget(
                                                label: "Seleziona esistente",
                                                fontSize: 14,
                                                fontWeight: '600',
                                                textColor: AppColor.greyColor,
                                              ),
                                            ],
                                          ),
                                        ),
                                        SizedBox(height: 5),
                                        SizedBox(
                                          width: 300,
                                          height: 51,
                                          child: FutureBuilder<List>(
                                              future: fetchRate(currentUID: rate.uniqueId ?? ""),
                                              builder: (context, snapshot) {
                                                if (snapshot.hasData) {
                                                  return DropdownButtonFormField<String>(
                                                    key: ValueKey(rate.index),
                                                    isExpanded: true,
                                                    initialValue: rate.mergedIntoRateId?.isNotEmpty ?? false
                                                        ? rate.mergedIntoRateId
                                                        : null,
                                                    icon: Padding(
                                                      padding: EdgeInsets.only(right: 8),
                                                      child: SvgPicture.asset(
                                                        'assets/icons/arrow_down.svg',
                                                        width: 12,
                                                        color: Color(0xff7e7e7e),
                                                      ),
                                                    ),
                                                    style: TextStyle(
                                                      overflow: TextOverflow.ellipsis,
                                                      color: Colors.black,
                                                      fontSize: 12.0,
                                                      fontWeight: FontWeight.bold,
                                                      fontStyle: FontStyle.normal,
                                                    ),
                                                    borderRadius: BorderRadius.circular(8),
                                                    decoration: InputDecoration(
                                                      border: OutlineInputBorder(
                                                        borderRadius:
                                                        BorderRadius.all(Radius.circular(8)),
                                                        borderSide: BorderSide(
                                                          color: Color.fromRGBO(227, 227, 227, 1),
                                                          width: 1,
                                                        ),
                                                      ),
                                                      hintStyle: TextStyle(
                                                        color: Colors.grey,
                                                        fontSize: 15.0,
                                                        fontWeight: FontWeight.w800,
                                                        fontStyle: FontStyle.normal,
                                                        letterSpacing: 0,
                                                      ),
                                                      focusedBorder: OutlineInputBorder(
                                                        borderRadius:
                                                        BorderRadius.all(Radius.circular(8)),
                                                        borderSide: BorderSide(
                                                          color: Color.fromRGBO(227, 227, 227, 1),
                                                          width: 1,
                                                        ),
                                                      ),
                                                      contentPadding: EdgeInsets.symmetric(
                                                          horizontal: 12, vertical: 8),
                                                      fillColor: Colors.white,
                                                    ),
                                                    onChanged: (String? value) {
                                                      if (value?.isNotEmpty ?? false) {
                                                        _setState(() {
                                                          rate.mergedIntoRateId = value;
                                                          isSaveButtonEnableFlag = isSaveButtonEnabled();
                                                        });
                                                      }
                                                    },
                                                    dropdownColor: Colors.white,
                                                    items: snapshot.data
                                                        ?.map<DropdownMenuItem<String>>((item) {
                                                      return DropdownMenuItem<String>(
                                                        value: item['value'],
                                                        child: NarFormLabelWidget(
                                                          label: item['label']!,
                                                          textColor: Colors.black,
                                                          fontSize: 14,
                                                          fontWeight: '600',
                                                        ),
                                                      );
                                                    }).toList(),
                                                  );
                                                } else if (snapshot.hasError) {
                                                  return Container(
                                                    width: 30,
                                                    height: 30,
                                                    alignment: Alignment.center,
                                                  );
                                                }
                                                return Center(
                                                  child: CircularProgressIndicator(
                                                    color: Theme.of(context).primaryColor,
                                                  ),
                                                );
                                              }),
                                        ),
                                      ],
                                    ) : SizedBox.shrink(),
                                  ],
                                ),
                              ],),
                          );
                        }),
                        Align(
                          alignment: Alignment.bottomCenter,
                          child: NarLinkWidget(
                            fontSize: 14,
                            fontWeight: '600',
                            textColor: Theme.of(context).primaryColor,
                            text: '+ Aggiungi rata',
                            textDecoration: TextDecoration.underline,
                            onClick: () {
                              int newIndex = rateList.length + 1;
                              Rate newRate = Rate.empty();
                              newRate.index = newIndex;
                              newRate.isPaid = false;
                              newRate.paidDate = null;
                              rateList.add(newRate);
                              _setState(() {});
                            },
                          ),
                        )
                      ],
                    ),
                  ),
                ));
          });
        });
  }

  Future<void> showAddPercentagePopup({required VoidCallback onPercentageAdded}) async {
    TextEditingController perController = TextEditingController();
    List<String> formErrorMessage = [];
    await showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (__context, _setState) {
            return Center(
                child: BaseNewarcPopup(
                  title: "Aggiungi sottocategoria",
                  buttonText: "Aggiungi",
                  formErrorMessage: formErrorMessage,
                  onPressed: () async {
                    _setState((){
                      formErrorMessage.clear();
                      formErrorMessage.add("Salvataggio in corso...");
                    });
                    try {
                      User? user = FirebaseAuth.instance.currentUser;
                      if (user == null) return;

                      final String uid = user.uid;
                      final int per = int.tryParse(perController.text.trim()) ?? 0;
                      final collectionRef = FirebaseFirestore.instance
                          .collection(appConfig.COLLECT_NEWARCPROJECTFIXEDASSETSPROPERTYPERCENTAGE);

                      await collectionRef.add({
                        "percentage": per,
                        "insertTimestamp": DateTime.now().millisecondsSinceEpoch,
                        "uid": uid,
                      });

                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Salvato");
                      });
                      onPercentageAdded();
                    } catch (e) {
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Si è verificato un errore.");
                      });
                      log("Error While Adding percentage -----> ${e.toString()}");
                    }
                  },
                  buttonColor: Theme.of(context).primaryColor,
                  column: Container(
                    width: 400,
                    child: CustomTextFormField(
                      isExpanded: false,
                      isNumber: true,
                      isPercentage: true,
                      label: "Digita il nuovo percentuale",
                      validator: (value) {
                        if (value == '') {
                          return 'Required!';
                        }
                        return null;
                      },
                      controller: perController,
                    ),
                  ),
                ));
          });
        });
  }

  Future<void> showAddManualCategoryPopup({required VoidCallback onCategoryAdded,required NewarcProjectPagamento? pagamento}) async {
    TextEditingController manualCategoryController = TextEditingController();
    List<String> formErrorMessage = [];
    await showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (__context, _setState) {
            return Center(
                child: BaseNewarcPopup(
                  title: "Categoria manuale",
                  buttonText: "Aggiungi",
                  formErrorMessage: formErrorMessage,
                  onPressed: () async {
                    _setState((){
                      formErrorMessage.clear();
                      formErrorMessage.add("Salvataggio in corso...");
                    });
                    try {

                      pagamento?.isManualCategory = true;
                      pagamento?.categoryName = manualCategoryController.text.trim();

                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Salvato");
                      });
                      onCategoryAdded();
                    } catch (e) {
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Si è verificato un errore.");
                      });
                      log("Error While Adding manual category -----> ${e.toString()}");
                    }
                  },
                  buttonColor: Theme.of(context).primaryColor,
                  column: Container(
                    width: 400,
                    child: CustomTextFormField(
                      isExpanded: false,
                      label: "Categoria manuale",
                      validator: (value) {
                        if (value == '') {
                          return 'Required!';
                        }
                        return null;
                      },
                      controller: manualCategoryController,
                    ),
                  ),
                ));
          });
        });
  }

  Future<void> showAddCategoryPopup({required VoidCallback onCategoryAdded}) async {
    TextEditingController categoryController = TextEditingController();
    List<String> formErrorMessage = [];
    await showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (__context, _setState) {
            return Center(
                child: BaseNewarcPopup(
                  title: "Aggiungi sottocategoria",
                  buttonText: "Aggiungi",
                  formErrorMessage: formErrorMessage,
                  onPressed: () async {
                    _setState((){
                      formErrorMessage.clear();
                      formErrorMessage.add("Salvataggio in corso...");
                    });
                    try {
                      User? user = FirebaseAuth.instance.currentUser;
                      if (user == null) return;

                      final String uid = user.uid;
                      final collectionRef = FirebaseFirestore.instance
                          .collection(appConfig.COLLECT_NEWARCPROJECTFIXEDASSETSPROPERTYCATEGORY);

                      await collectionRef.add({
                        "name": categoryController.text.trim(),
                        "insertTimestamp": DateTime.now().millisecondsSinceEpoch,
                        "uid": uid,
                      });
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Salvato");
                      });
                      // setState(() {
                      //   fetchCategoryFuture = fetchCategory();
                      // });
                      onCategoryAdded();

                    } catch (e) {
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Si è verificato un errore.");
                      });
                      log("Error While Adding category -----> ${e.toString()}");
                    }
                  },
                  buttonColor: Theme.of(context).primaryColor,
                  column: Container(
                    width: 400,
                    child: CustomTextFormField(
                      isExpanded: false,
                      label: "Digita il nuovo categoria",
                      validator: (value) {
                        if (value == '') {
                          return 'Required!';
                        }
                        return null;
                      },
                      controller: categoryController,
                    ),
                  ),
                ));
          });
        });
  }

  Future<List> fetchPercentage() async {
    try {
      List<NewarcProjectFixedAssetsPropertyPercentage> _newarcProjectFixedAssetsPropertyPercentage = [];
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance
          .collection(appConfig.COLLECT_NEWARCPROJECTFIXEDASSETSPROPERTYPERCENTAGE);

      collectionSnapshot = await collectionSnapshotQuery
          .orderBy('insertTimestamp', descending: true)
          .get();

      if (collectionSnapshot.docs.length > 0) {
        for (var element in collectionSnapshot.docs) {
          try {
            NewarcProjectFixedAssetsPropertyPercentage _tmp =
            NewarcProjectFixedAssetsPropertyPercentage.fromDocument(
                element.data(), element.id);
            _newarcProjectFixedAssetsPropertyPercentage.add(_tmp);
          } catch (e) {
            print("ERROR Percentage ---> $e");
          }
        }
      }


      if (_newarcProjectFixedAssetsPropertyPercentage.length > 0) {
        return _newarcProjectFixedAssetsPropertyPercentage.map((e) {
          return {'value': "${e.firebaseId}|${e.percentage}", 'label': "${e.percentage}%"};
        }).toList();
      } else {
        return [
          {'value': '', 'label': ''}
        ];
      }
    } catch (e, s) {
      print('error: $e');
      print(s);
      return [
        {'value': '', 'label': ''}
      ];
    }
  }

  Future<List> fetchCategory() async {
    try {
      List<NewarcProjectFixedAssetsPropertyCategory> _newarcProjectFixedAssetsPropertyCategory = [];
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance
          .collection(appConfig.COLLECT_NEWARCPROJECTFIXEDASSETSPROPERTYCATEGORY);

      collectionSnapshot = await collectionSnapshotQuery
          .orderBy('insertTimestamp', descending: true)
          .get();

      if (collectionSnapshot.docs.length > 0) {
        for (var element in collectionSnapshot.docs) {
          try {
            NewarcProjectFixedAssetsPropertyCategory _tmp =
            NewarcProjectFixedAssetsPropertyCategory.fromDocument(
                element.data(), element.id);
            _newarcProjectFixedAssetsPropertyCategory.add(_tmp);
          } catch (e) {
            print("ERROR fetchCategory ---> $e");
          }
        }
      }


      if (_newarcProjectFixedAssetsPropertyCategory.length > 0) {
        return _newarcProjectFixedAssetsPropertyCategory.map((e) {
          return {'value': "${e.firebaseId}|${e.name}", 'label': e.name};
        }).toList();
      } else {
        return [
          {'value': '', 'label': ''}
        ];
      }
    } catch (e, s) {
      print('error: $e');
      print(s);
      return [
        {'value': '', 'label': ''}
      ];
    }
  }

  Future<List> fetchRate({required String currentUID}) async {
    try {
      List<Rate> _rates = [];

      if ((widget.project?.fixedProperty!.newarcProjectFixedAssetsPropertyPagamento?.length ?? 0) > 0) {
        for (var element in widget.project!.fixedProperty!.newarcProjectFixedAssetsPropertyPagamento!) {
          for (var rate in element.rate!) {
            if(!(rate.isMerged ?? false) && rate.uniqueId != currentUID){
              rate.category = element.categoryName;
              _rates.add(rate);
            }
          }
        }
      }


      if (_rates.isNotEmpty) {
        return _rates.map((e) {
          return {
            'value': e.uniqueId,
            'label': "${e.category} - RATA ${e.index} - ${e.percentage ?? 0}%"
          };
        }).toList();
      } else {
        return [{'value': '', 'label': ''}];
      }
    } catch (e, s) {
      print('error: $e');
      print(s);
      return [{'value': '', 'label': ''}];
    }
  }
}
