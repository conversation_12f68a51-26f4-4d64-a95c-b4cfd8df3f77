import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';


class SiNoToggleButton extends StatefulWidget {
  final bool? startingState;
  final Function(bool)? onStateChanged;
  final double? checkBoxHeight;
  final double? checkBoxWidth;
  final double? intraButtonSpacing;
  final double? interButtonsSpacing;
  const SiNoToggleButton ({
    Key? key, 
    this.startingState, 
    this.onStateChanged,
    this.checkBoxHeight = 15,
    this.checkBoxWidth = 15,
    this.intraButtonSpacing = 10,
    this.interButtonsSpacing = 30,
    }): super(key: key);
  @override
  _SiNoToggleButtonState createState() => _SiNoToggleButtonState();
}

class _SiNoToggleButtonState extends State<SiNoToggleButton> {
  late bool _isSelected;
  @override
  void initState() {
    super.initState();
    setState(() {
      _isSelected = widget.startingState ?? false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return 
    Row(
      mainAxisAlignment: MainAxisAlignment.start,
      // mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const SizedBox(width: 5,),
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () {
              setState(() {
              _isSelected = true;
              widget.onStateChanged?.call(_isSelected);
              });
            },
            child: 
              Container(
                width: widget.checkBoxWidth,
                height: widget.checkBoxHeight,
                decoration: 
                  BoxDecoration(
                    shape: BoxShape.circle, 
                    color: _isSelected ? Theme.of(context).primaryColor: Theme.of(context).unselectedWidgetColor,
                    border: Border.all(color: Theme.of(context).primaryColorLight)
                  ),
                  child: 
                    _isSelected 
                    ? Padding(
                      padding: EdgeInsets.all(1),
                      child: SvgPicture.asset(
                        'assets/icons/check.svg', 
                        color: Theme.of(context).unselectedWidgetColor
                      )
                    ) 
                    : null,
              )
          ),
        ),
        SizedBox(width: widget.intraButtonSpacing,),
        Text('Si',
          style: TextStyle(
            fontFamily: 'Raleway-500',
            fontSize: 15,
            color: Theme.of(context).primaryColorLight,),
        ),
        SizedBox(width: widget.interButtonsSpacing,),
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () {
              setState(() {
              _isSelected = false;
              widget.onStateChanged?.call(_isSelected);
              });
            },
            child: 
              Container(
                width: widget.checkBoxWidth,
                height: widget.checkBoxHeight,
                decoration: 
                  BoxDecoration(
                    shape: BoxShape.circle, 
                    color: _isSelected ? Theme.of(context).unselectedWidgetColor: Theme.of(context).primaryColor,
                    border: Border.all(color: Theme.of(context).primaryColorLight)
                  ),
                child: 
                  !_isSelected 
                  ? Padding(
                    padding: EdgeInsets.all(1),
                    child: SvgPicture.asset(
                      'assets/icons/check.svg', 
                      color: Theme.of(context).unselectedWidgetColor
                    )
                  ) 
                  : null,
              )
          ),
        ),
        SizedBox(width: widget.intraButtonSpacing,),
        Text('No',
          style: TextStyle(
            fontFamily: 'Raleway-500',
            fontSize: 15,
            color: Theme.of(context).primaryColorLight,),
        ),
      ],
    );
  }
}