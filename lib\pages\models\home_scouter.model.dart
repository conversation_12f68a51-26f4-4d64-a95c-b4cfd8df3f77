import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/comparabile.dart';

class HomeScouterModel extends ChangeNotifier {
  final GlobalKey<ScaffoldState> scaffoldKey;
  HomeScouterModel(this.scaffoldKey);

  bool drawerOpen = false;
  bool displaySearch = false;
  bool displayZoneFilter = false;
  Comparabile? selectedComparabile;

  String selectedZone = "None";
  List<Comparabile> comparabili = [];
  List<String> zones = [];
  bool fetchingDone = false;

  void drawerStateChange() {
    this.scaffoldKey.currentState!.openDrawer();
    notifyListeners();
  }

  void searchPopupShow() {
    this.displaySearch = !this.displaySearch;
    notifyListeners();
  }

  void showZoneFilter() {
    this.displayZoneFilter = !this.displayZoneFilter;
    notifyListeners();
  }

  /*void setZonesList() {
    Map<String, List<Comparabile>> groupedZoneMap =
        groupBy(comparabili, (c) => c.zonaOmi!);
    this.zones = groupedZoneMap.keys.toList();
    notifyListeners();
  }*/

  List<Comparabile> getFilteredComparabili() {
    var _list = this.comparabili;
    if (this.selectedZone != "None") {
      _list = _list.where((c) => c.zonaOmi == this.selectedZone).toList();
    }
    return _list;
  }

  void updateSelectedZone(String zone) {
    this.selectedZone = zone;
    notifyListeners();
  }

  void showComparabile(Comparabile? comparabile) {
    this.selectedComparabile = comparabile;
    notifyListeners();
  }
}

class HomeScouterArguments {
  HomeScouterArguments();
}
