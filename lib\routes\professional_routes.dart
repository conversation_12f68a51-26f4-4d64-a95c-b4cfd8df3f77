
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:newarc_platform/routes/user_provider.dart';
import 'package:provider/provider.dart';

import '../pages/agency/inside_project_view.dart';
import '../pages/agency/inside_request_view.dart';
import '../pages/professionals/dashboard_view.dart';
import '../pages/professionals/home_professionals.dart';
import '../pages/professionals/immagina/immagina_professionals_view.dart';
import '../pages/professionals/settings.dart';

class ProfessionalRoutes {
  static const String professionalSetting = '/professionals-setting';
  static const String professionalDashboard = '/professionals-dashboard';
  static String professionalImmagina(String mode) => '/immagina/$mode';
  static String professionalImmaginaInside({
    required String mode,
    required String id,
    required String childMode,
  }) => '/immagina/$mode/$childMode/$id';

  static final routes = [
    ShellRoute(
      pageBuilder: (context, state, child) {
        final user = context.read<UserProvider>().professionalsUser;

        if (user == null) {
          return NoTransitionPage(
            child: Scaffold(
              body: Center(child: CircularProgressIndicator()),
            ),
          );
        }

        return NoTransitionPage(
          child: HomeProfessionals(
            professionalsUser: user,
            child: child,
          ),
        );
      },
      routes: [
        GoRoute(
          path: professionalSetting,
          name: 'professionals-setting',
          pageBuilder: (context, state) {
            final user = context.read<UserProvider>().professionalsUser!;

            return NoTransitionPage(
              child: ProfessionalsSetting(
                professionalsUser: user,
              ),
            );
          },
        ),
        // GoRoute(
        //   path: professionalDashboard,
        //   name: 'professionals-dashboard',
        //   pageBuilder: (context, state) {
        //     final user = context.read<UserProvider>().professionalsUser!;
        //
        //     return NoTransitionPage(
        //       child: Dashboard(
        //           professional: user.professional!,
        //           professionalsUser: user,
        //           ),
        //     );
        //   },
        // ),
        GoRoute(
          path: '/immagina/:mode',
          name: 'immagina-view-professionals',
          pageBuilder: (context, state) {
            final user = context.read<UserProvider>().professionalsUser!;
            final parentMode = state.pathParameters['mode']!;
            final isArchived = parentMode == 'progetti-archiviati';

            if (isArchived) {
              return NoTransitionPage(
                child: ImmaginaProfessionalsView(
                  key: ValueKey("immagina-progetti-archiviati"),
                  professionalsUser: user,
                  isArchived: true,
                ),
              );
            } else {
              return NoTransitionPage(
                child: ImmaginaProfessionalsView(
                  key: ValueKey("immagina-progetti-attivi"),
                  professionalsUser: user,
                  isArchived: false,
                ),
              );
            }
          },
          routes: [
            GoRoute(
              path: ':childMode/:id',
              name: 'immagina-inside-professionals',
              pageBuilder: (context, state) {
                final user = context.read<UserProvider>().professionalsUser!;
                final id = state.pathParameters['id']!;
                final mode = state.pathParameters['childMode']!;
                if (mode == "request") {
                  return NoTransitionPage(
                    child: InsideRequestView(
                      projectFirebaseId: id,
                      professionalsUser: user,
                      isFromRequest: true,
                      isForProfessionals: true,
                    ),
                  );
                } else {
                  return NoTransitionPage(
                    child: InsideProjectView(
                      projectFirebaseId: id,
                      professionalsUser: user,
                      isFromRequest: false,
                      isFromProjectArchive: false,
                    ),
                  );
                }
              },
            ),
          ],
        ),

      ],
    ),
  ];
}