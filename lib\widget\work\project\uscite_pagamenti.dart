import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/utils/downloadQuotationPDF.dart';
import '../../../classes/newarcProject.dart';
import '../../../classes/newarcProjectFixedAssetsPropertyPagamento.dart';
import '../../../classes/newarcProjectFixedAssetsPropertyPercentage.dart';
import '../../../classes/supplier.dart';
import '../../../utils/color_schema.dart';
import '../../../utils/various.dart';
import '../../UI/base_newarc_button.dart';
import '../../UI/base_newarc_popup.dart';
import '../../UI/custom_textformfield.dart';
import '../../UI/form-label.dart';
import '../../UI/link.dart';
import '../../UI/tab/common_icon_button.dart';

class UscitePagamenti extends StatefulWidget {
  NewarcProject? project;
  final List<Supplier>? suppliers;
  final Function? updateProject;
  UscitePagamenti({super.key,this.project,this.suppliers,this.updateProject});

  @override
  State<UscitePagamenti> createState() => _UscitePagamentiState();
}

class _UscitePagamentiState extends State<UscitePagamenti> {

  NumberFormat localCurrencyFormatMainWithDecimal = NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 2);

  bool isLoading = false;
  String progressMessage = '';
  List<Map> _suppliers = [];
  List<Map> _initSuppliers = [];
  double budget = 1;
  Color? spendColor = Colors.white;
  double budgetMaterial = 1;
  Color? spendColorMaterial = Colors.white;
  TextEditingController contJobStart = new TextEditingController();
  TextEditingController contJobEnd = new TextEditingController();
  TextEditingController contPenalty = new TextEditingController();
  int? jobStart;
  int? jobEnd;

  @override
  void initState() {
    super.initState();
    _initSuppliers = widget.suppliers!.map((e) {
      return {'value': e.id, 'label': e.name! + ' ' + e.formationType!};
    }).toList();
    _suppliers.add({'value': '', 'label': ''});
    setInitialValues();
  }

  @protected
  void didUpdateWidget(UscitePagamenti oldWidget) {
    super.didUpdateWidget(oldWidget);
    setInitialValues();
  }

  setInitialValues() async{
    setState(() {
      isLoading = true;
    });
    try {
      DocumentSnapshot<Map<String, dynamic>> collectionSnapshot;

      collectionSnapshot = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_NEWARC_PROJECTS)
          .doc(widget.project!.id!)
          .get();

      if (collectionSnapshot.data() != null) {
        widget.project = NewarcProject.fromDocument(collectionSnapshot.data()!, widget.project!.id!);
      }

      budget = widget.project?.budgetDitteProfRistrutturazione ?? 1.0;
      budgetMaterial = widget.project?.budgetGastisciMaterialiRistrutturazione ?? 1.0;

      for (NewarcProjectPagamento pagamento in widget.project?.vendorAndProfessionalsUscite ?? []) {
        pagamento.categoryName = _initSuppliers
            .where((element) => element['value'] == pagamento.vendorUserId)
            .first['label'];
        for (Rate rate in pagamento.rate ?? []) {
          if(rate.newarcProjectFixedAssetsPercentageId?.isNotEmpty ?? false){
            DocumentSnapshot<Map<String, dynamic>> perQuery = await FirebaseFirestore.instance
                .collection(appConfig.COLLECT_NEWARCPROJECTFIXEDASSETSPROPERTYPERCENTAGE)
                .doc(rate.newarcProjectFixedAssetsPercentageId)
                .get();

            if (perQuery.exists) {
              rate.percentage = perQuery.data()?["percentage"];
            }
          }
        }
      }

      widget.project?.vendorAndProfessionalsUscite = widget.project?.vendorAndProfessionalsUscite;

      double spendPercent = (_calculateTotal(widget.project?.vendorAndProfessionalsUscite ?? []) / budget) * 100;
      if (spendPercent <= 25) {
        spendColor = Color(0xff4E9A7A);
      } else if (spendPercent > 25 && spendPercent <= 100) {
        spendColor = Color(0xffFFC702);
      } else if (spendPercent > 100) {
        spendColor = Color(0xffE82525);
      }

      //----------Material

      for (NewarcProjectPagamento pagamento in widget.project?.materialUscite ?? []) {
        for (Rate rate in pagamento.rate ?? []) {
          if(rate.newarcProjectFixedAssetsPercentageId?.isNotEmpty ?? false){
            DocumentSnapshot<Map<String, dynamic>> perQuery = await FirebaseFirestore.instance
                .collection(appConfig.COLLECT_NEWARCPROJECTFIXEDASSETSPROPERTYPERCENTAGE)
                .doc(rate.newarcProjectFixedAssetsPercentageId)
                .get();

            if (perQuery.exists) {
              rate.percentage = perQuery.data()?["percentage"];
            }
          }
        }
      }

      widget.project?.materialUscite = widget.project?.materialUscite;

      double spendPercentMaterial = (_calculateTotal(widget.project?.materialUscite ?? []) / budgetMaterial) * 100;
      if (spendPercentMaterial <= 25) {
        spendColorMaterial = Color(0xff4E9A7A);
      } else if (spendPercentMaterial > 25 && spendPercentMaterial <= 100) {
        spendColorMaterial = Color(0xffFFC702);
      } else if (spendPercentMaterial > 100) {
        spendColorMaterial = Color(0xffE82525);
      }

      setState(() {
        isLoading = false;
      });
    } catch (e, s) {
      setState(() {
        isLoading = false;
      });
      print({e, s});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      child: isLoading
          ? Center(child: NarFormLabelWidget(label: 'Loading'))
          : SingleChildScrollView(
        child: Column(
          children: [
            //------ Ditte e Professionisti
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 25,),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    NarFormLabelWidget(
                      label: 'Ditte e Professionisti',
                      fontSize: 20,
                      fontWeight: 'bold',
                    ),
                    Row(
                      children: [
                        Container(
                          width:200,
                          constraints: BoxConstraints(minWidth: 160, ),
                          padding: EdgeInsets.symmetric(vertical: 7, horizontal: 15),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(
                              width: 1,
                              color: Color.fromRGBO(214, 214, 214, 1),
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  NarFormLabelWidget(
                                      label: 'Budget',
                                      fontSize: 12,
                                      fontWeight: '600',
                                      textColor: Colors.black),

                                  widget.project?.type == "Ristrutturazione" ?
                                  IconButtonWidget(
                                    onTap: () {
                                      showEditBudgetPopup(isMaterial: false);
                                    },
                                    iconPadding: EdgeInsets.zero,
                                    height: 15,
                                    width: 15,
                                    isSvgIcon: false,
                                    backgroundColor: AppColor.white,
                                    icon:
                                    'assets/icons/edit.png',
                                    iconColor: AppColor.greyColor,
                                  ) : SizedBox.fromSize(),
                                ],
                              ),
                              SizedBox(
                                height: 10,
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  NarFormLabelWidget(
                                      label: localCurrencyFormatMain.format(budget),
                                      fontSize: 25,
                                      fontWeight: 'bold',
                                      textColor: Colors.black),
                                  SizedBox(
                                    width: 20,
                                  ),
                                  NarFormLabelWidget(
                                      label: '€',
                                      fontSize: 17,
                                      fontWeight: 'bold',
                                      textColor: Colors.black),
                                ],
                              )
                            ],
                          ),
                        ),
                        SizedBox(
                          width: 25,
                        ),
                        Container(
                          // width: 160,
                          constraints: BoxConstraints(
                              minWidth: 160
                          ),
                          padding: EdgeInsets.symmetric(
                              vertical: 7, horizontal: 15),
                          decoration: BoxDecoration(
                            color: spendColor!,
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(
                              width: 1,
                              color: spendColor!,
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              NarFormLabelWidget(
                                label: 'Costo raggiunto',
                                fontSize: 12,
                                fontWeight: '600',
                                textColor: Colors.white,
                              ),
                              SizedBox(
                                height: 10,
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  NarFormLabelWidget(
                                    label: localCurrencyFormatMain.format(_calculateTotal(widget.project?.vendorAndProfessionalsUscite ?? [])),
                                    fontSize: 25,
                                    fontWeight: 'bold',
                                    textColor: Colors.white,
                                  ),
                                  SizedBox(
                                    width: 20,
                                  ),
                                  NarFormLabelWidget(
                                    label: '€',
                                    fontSize: 17,
                                    fontWeight: 'bold',
                                    textColor: Colors.white,
                                  ),
                                ],
                              )
                            ],
                          ),
                        )
                      ],
                    ),
                  ],
                ),
                SizedBox(height: 25),
                buildPaymentSummary(widget.project?.vendorAndProfessionalsUscite ?? []),
                SizedBox(height: 10),
              ],
            ),
            SizedBox(height: 25),

            //------ Forniture
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 25,),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    NarFormLabelWidget(
                      label: 'Forniture',
                      fontSize: 20,
                      fontWeight: 'bold',
                    ),
                    Row(
                      children: [
                        Container(
                          width:200,
                          constraints: BoxConstraints(minWidth: 160, ),
                          padding: EdgeInsets.symmetric(vertical: 7, horizontal: 15),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(
                              width: 1,
                              color: Color.fromRGBO(214, 214, 214, 1),
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  NarFormLabelWidget(
                                      label: 'Budget',
                                      fontSize: 12,
                                      fontWeight: '600',
                                      textColor: Colors.black),

                                  widget.project?.type == "Ristrutturazione" ?
                                  IconButtonWidget(
                                    onTap: () {
                                      showEditBudgetPopup(isMaterial: true);
                                    },
                                    iconPadding: EdgeInsets.zero,
                                    height: 15,
                                    width: 15,
                                    isSvgIcon: false,
                                    backgroundColor: AppColor.white,
                                    icon:
                                    'assets/icons/edit.png',
                                    iconColor: AppColor.greyColor,
                                  ) : SizedBox.fromSize(),
                                ],
                              ),
                              SizedBox(
                                height: 10,
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  NarFormLabelWidget(
                                      label: localCurrencyFormatMain.format(budgetMaterial),
                                      fontSize: 25,
                                      fontWeight: 'bold',
                                      textColor: Colors.black),
                                  SizedBox(
                                    width: 20,
                                  ),
                                  NarFormLabelWidget(
                                      label: '€',
                                      fontSize: 17,
                                      fontWeight: 'bold',
                                      textColor: Colors.black),
                                ],
                              )
                            ],
                          ),
                        ),
                        SizedBox(
                          width: 25,
                        ),
                        Container(
                          constraints: BoxConstraints(
                              minWidth: 160
                          ),
                          padding: EdgeInsets.symmetric(
                              vertical: 7, horizontal: 15),
                          decoration: BoxDecoration(
                            color: spendColorMaterial!,
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(
                              width: 1,
                              color: spendColorMaterial!,
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              NarFormLabelWidget(
                                label: 'Costo raggiunto',
                                fontSize: 12,
                                fontWeight: '600',
                                textColor: Colors.white,
                              ),
                              SizedBox(
                                height: 10,
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  NarFormLabelWidget(
                                    label: localCurrencyFormatMain.format(_calculateTotal(widget.project?.materialUscite ?? [])),
                                    fontSize: 25,
                                    fontWeight: 'bold',
                                    textColor: Colors.white,
                                  ),
                                  SizedBox(
                                    width: 20,
                                  ),
                                  NarFormLabelWidget(
                                    label: '€',
                                    fontSize: 17,
                                    fontWeight: 'bold',
                                    textColor: Colors.white,
                                  ),
                                ],
                              )
                            ],
                          ),
                        )
                      ],
                    ),
                  ],
                ),
                SizedBox(height: 25),
                buildPaymentSummary(widget.project?.materialUscite ?? [],isMaterial: true),
                SizedBox(height: 10),
              ],
            ),
            SizedBox(height: 25),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                NarFormLabelWidget(label: progressMessage),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    BaseNewarcButton(
                      buttonText: "Salva",
                      onPressed: () async {
                        setState(() {
                          progressMessage =
                          'Salvataggio in corso...';
                        });

                        final FirebaseFirestore _db = FirebaseFirestore.instance;

                        try {
                          await _db
                              .collection(
                              appConfig.COLLECT_NEWARC_PROJECTS)
                              .doc(widget.project!.id)
                              .set(widget.project!.toMap());

                          for (NewarcProjectPagamento pagamento in widget.project?.vendorAndProfessionalsUscite ?? []) {
                            pagamento.categoryName = pagamento.categoryName = _initSuppliers
                                .where((element) => element['value'] == pagamento.vendorUserId)
                                .first['label'];
                            for (var rate in pagamento.rate ?? []) {
                              DocumentSnapshot<Map<String, dynamic>> perQuery = await FirebaseFirestore.instance
                                  .collection(appConfig.COLLECT_NEWARCPROJECTFIXEDASSETSPROPERTYPERCENTAGE)
                                  .doc(rate.newarcProjectFixedAssetsPercentageId)
                                  .get();
                              if (perQuery.exists) {
                                rate.percentage = perQuery.data()?["percentage"];
                              }
                            }
                          }

                          for (NewarcProjectPagamento pagamento in widget.project?.materialUscite ?? []) {
                            for (var rate in pagamento.rate ?? []) {
                              DocumentSnapshot<Map<String, dynamic>> perQuery = await FirebaseFirestore.instance
                                  .collection(appConfig.COLLECT_NEWARCPROJECTFIXEDASSETSPROPERTYPERCENTAGE)
                                  .doc(rate.newarcProjectFixedAssetsPercentageId)
                                  .get();
                              if (perQuery.exists) {
                                rate.percentage = perQuery.data()?["percentage"];
                              }
                            }
                          }

                          setState(() {
                            widget.project?.vendorAndProfessionalsUscite = widget.project?.vendorAndProfessionalsUscite;
                            progressMessage = 'Saved!';
                          });
                        } catch (e) {
                          log("ERROR WHILE SAVING PROJECT =====> ${e.toString()}");
                          setState(() {
                            progressMessage = 'Error';
                          });
                        }
                      },
                    )
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget buildPaymentSummary(List<NewarcProjectPagamento> pagamentiList,{bool isMaterial = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // GRAND TOTAL HEADER
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 15),
          decoration: BoxDecoration(
              color: Color(0xFF343434),
              borderRadius: BorderRadius.circular(8)
          ),
          child: Row(
            // mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Expanded(
                child: NarFormLabelWidget(
                  label: "Totale",
                  fontSize: 14,
                  fontWeight: '600',
                  textColor: AppColor.white,
                ),
              ),
              Expanded(
                flex: 2,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        NarFormLabelWidget(
                          label: "Totale",
                          fontSize: 9,
                          fontWeight: '600',
                          textColor: AppColor.white,
                        ),
                        SizedBox(height: 3,),
                        NarFormLabelWidget(
                          label: "${localCurrencyFormatMain.format(_calculateTotal(pagamentiList))}€",
                          fontSize: 14,
                          fontWeight: '600',
                          textColor: AppColor.white,
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        NarFormLabelWidget(
                          label: "Iva",
                          fontSize: 9,
                          fontWeight: '600',
                          textColor: AppColor.white,
                        ),
                        SizedBox(height: 3,),
                        NarFormLabelWidget(
                          label: "+ ${localCurrencyFormatMain.format(_calculateTotalIVA(pagamentiList))}€",
                          fontSize: 14,
                          fontWeight: '600',
                          textColor: AppColor.white,
                        ),
                      ],
                    ),
                    SizedBox(width: 40,)
                  ],
                ),
              ),

            ],
          ),
        ),
        SizedBox(height: 25,),
        // CATEGORY LEVEL
        ...pagamentiList.asMap().entries.map((entry) {
          final index = entry.key;
          final pagamento = entry.value;
          final rateList = pagamento.rate ?? [];
          final total = pagamento.total ?? 0;
          final totalIVA = pagamento.totalIVA ?? 0;
          return Container(
            width: double.infinity,
            margin: const EdgeInsets.only(bottom: 10),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Color(0xFFE0E0E0))
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 15),
                  decoration: BoxDecoration(
                      color: Color(0xFFE0E0E0),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Color(0xFFE0E0E0))
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Expanded(
                        child: NarFormLabelWidget(
                          label: "${pagamento.categoryName ?? ""}${pagamento.productName?.isNotEmpty ?? false ? " - ${pagamento.productName}" : ""}",
                          fontSize: 14,
                          fontWeight: '700',
                          textColor: AppColor.black,
                        ),
                      ),

                      Expanded(
                        flex: 2,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                NarFormLabelWidget(
                                  label: "Totale",
                                  fontSize: 9,
                                  fontWeight: '600',
                                  textColor: AppColor.black,
                                ),
                                SizedBox(height: 3,),
                                NarFormLabelWidget(
                                  label: "${localCurrencyFormatMain.format(total)}€",
                                  fontSize: 14,
                                  fontWeight: '700',
                                  textColor: AppColor.black,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                NarFormLabelWidget(
                                  label: "Iva",
                                  fontSize: 9,
                                  fontWeight: '600',
                                  textColor: AppColor.black,
                                ),
                                SizedBox(height: 3,),
                                NarFormLabelWidget(
                                  label: "+ ${localCurrencyFormatMain.format(totalIVA)}€",
                                  fontSize: 14,
                                  fontWeight: '600',
                                  textColor: AppColor.black,
                                ),
                              ],
                            ),
                            SizedBox(width: 10,),
                            IconButtonWidget(
                              onTap: () {
                                showAddPagamentiPopup(newNewarcProjectUscite: pagamento,index: index,isMaterial: isMaterial);
                              },
                              isSvgIcon: true,
                              icon: 'assets/icons/edit.svg',
                              iconColor: AppColor.greyColor,
                            ),
                          ],
                        ),
                      ),

                    ],
                  ),
                ),
                // Rata Rows
                ...rateList.map((rate) {
                  final rataLabel = "Rata ${rate.index} - ${rate.percentage ?? 0}%";
                  final amount = rate.rate ?? 0;
                  final iva = rate.ivaAmount ?? 0;
                  final ivaPerc = rate.ivaPercentage ?? 0;

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        child: Row(
                          children: [
                            Expanded(
                              child: NarFormLabelWidget(
                                label: rataLabel,
                                fontSize: 13,
                                fontWeight: '500',
                                textColor: AppColor.black,
                              ),
                            ),
                            Expanded(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  NarFormLabelWidget(
                                    label: "${localCurrencyFormatMain.format(amount)}€",
                                    fontSize: 13,
                                    fontWeight: '500',
                                    textColor: AppColor.black,
                                  ),
                                ],
                              ),
                              flex: 2,
                            ),
                            Expanded(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  NarFormLabelWidget(
                                    label: "+ ${localCurrencyFormatMain.format(iva)}€ iva $ivaPerc%",
                                    fontSize: 13,
                                    fontWeight: '500',
                                    textColor: AppColor.black,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 10),
                    ],
                  );
                }).toList(),


              ],
            ),
          );
        }).toList()
      ],
    );
  }

  double _calculateTotal(List<NewarcProjectPagamento> list) {
    return list.fold(0, (sum, p) => sum + (p.total ?? 0));
  }

  double _calculateTotalIVA(List<NewarcProjectPagamento> list) {
    return list.fold(0, (sum, p) => sum + (p.totalIVA ?? 0));
  }

  Future<void> showAddPagamentiPopup({NewarcProjectPagamento? newNewarcProjectUscite,int? index,bool isMaterial = false}) async {
    List<String> formErrorMessage = [];

    var _newNewarcProjectUscite = newNewarcProjectUscite != null
        ? NewarcProjectPagamento.fromDocument(newNewarcProjectUscite.toMap(),"")
        : NewarcProjectPagamento.empty();

    List<Rate> rateList = (newNewarcProjectUscite?.rate ?? [])
        .map((rate){
      Rate newRate = Rate.fromDocument(rate.toMap(),"");
      newRate.percentage = rate.percentage;
      return newRate;
    }).toList();

    if(!isMaterial){
      if( _newNewarcProjectUscite.jobStartTimestamp! > 0 ) {
        contJobStart.text = getFormattedDate(_newNewarcProjectUscite.jobStartTimestamp!);
      } else {
        contJobStart.text = '';
      }

      if( _newNewarcProjectUscite.jobEndTimestamp! > 0 ) {
        contJobEnd.text = getFormattedDate(_newNewarcProjectUscite.jobEndTimestamp!);
      } else {
        contJobEnd.text = '';
      }
      jobStart = _newNewarcProjectUscite.jobStartTimestamp;
      jobEnd = _newNewarcProjectUscite.jobEndTimestamp;
      contPenalty.text = _newNewarcProjectUscite.agreedPenalty!.toString();
    }



    bool isSaveButtonEnableFlag = false;

    bool isSaveButtonEnabled() {
      int totalPercentage = 0;
      for (var rate in rateList) {
        totalPercentage += rate.percentage ?? 0;
      }
      return totalPercentage == 100;
    }
    Future<List>? _fetchPercentageFuture;

    await showDialog(
        context: context,
        builder: (BuildContext _context) {

          return StatefulBuilder(builder: (__context, _setState) {
            _fetchPercentageFuture ??= fetchPercentage();

            void refreshFetchPercentage() {
              _setState(() {
                _fetchPercentageFuture = fetchPercentage();
              });
            }
            return Center(
                child: BaseNewarcPopup(
                  title: "Definisci pagamento",
                  buttonText: "Aggiungi",
                  formErrorMessage: formErrorMessage,
                  onPressed: (isSaveButtonEnableFlag) ? () async {
                    _setState((){
                      formErrorMessage.clear();
                      formErrorMessage.add("Salvataggio in corso...");
                    });
                    try {

                      double totalIVA = 0.0;
                      for (var rate in rateList) {
                        totalIVA += rate.ivaAmount ?? 0.0;
                        if((rate.isMerged ?? false) && (rate.mergedIntoRateId?.isNotEmpty ?? false)){
                          rate.isMerged = true;

                          List<NewarcProjectPagamento>? tagetList = [];

                          if(isMaterial){
                            tagetList = widget.project!.materialUscite;
                          }else{
                            tagetList = widget.project!.vendorAndProfessionalsUscite;
                          }

                          final targetRate = tagetList!.expand((element) => element.rate ?? [])
                              .firstWhere(
                                (r) {
                              return r.uniqueId == rate.mergedIntoRateId;
                            },
                            orElse: () => Rate.empty(),
                          );

                          if (targetRate.uniqueId?.isNotEmpty ?? false) {
                            targetRate.margeRateUniqueIDS ??= [];

                            final index = targetRate.margeRateUniqueIDS!.indexWhere((id) => id == rate.uniqueId);

                            if (index != -1) {
                              // Already exists: update the value
                              targetRate.margeRateUniqueIDS![index] = rate.uniqueId;
                            } else {
                              // Doesn't exist: add it
                              targetRate.margeRateUniqueIDS!.add(rate.uniqueId);
                            }
                          }
                        }else{
                          rate.isMerged = false;
                          rate.mergedIntoRateId = "";
                        }
                      }
                      _newNewarcProjectUscite.totalIVA = totalIVA;
                      _newNewarcProjectUscite.rate = rateList;

                      if(!isMaterial){
                        _newNewarcProjectUscite.agreedPenalty = double.tryParse(contPenalty.text.trim()) ?? 0.0;
                        _newNewarcProjectUscite.jobStartTimestamp = jobStart;
                        _newNewarcProjectUscite.jobEndTimestamp = jobEnd;
                        _newNewarcProjectUscite.categoryName = _initSuppliers
                            .where((element) => element['value'] == _newNewarcProjectUscite.vendorUserId)
                            .first['label'];
                      }



                      setState(() {
                        if(isMaterial){
                          if (index != null && index >= 0 && widget.project?.materialUscite != null && index < (widget.project?.materialUscite?.length ?? 0)) {
                            widget.project?.materialUscite?[index] = _newNewarcProjectUscite;
                          } else {
                            widget.project?.materialUscite?.add(_newNewarcProjectUscite);
                          }
                        }else{
                          if (index != null && index >= 0 && widget.project?.vendorAndProfessionalsUscite != null && index < (widget.project?.vendorAndProfessionalsUscite?.length ?? 0)) {
                            widget.project?.vendorAndProfessionalsUscite?[index] = _newNewarcProjectUscite;
                          } else {
                            widget.project?.vendorAndProfessionalsUscite?.add(_newNewarcProjectUscite);
                          }
                        }

                      });

                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Salvato");
                      });
                    } catch (e) {
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Si è verificato un errore.");
                      });
                      log("Error While Adding PAGAMENTO -----> ${e.toString()}");
                    }
                  } : (){return false;},
                  onPressedSecondButton: () {
                    WidgetsBinding.instance.addPostFrameCallback((_){
                      showDialog(
                        context: context,
                        barrierDismissible: true,
                        builder: (BuildContext context) {
                          return StatefulBuilder(builder: (context,setStateDialog){
                            return Center(
                              child: BaseNewarcPopup(
                                  buttonColor: Color(0xFFE82525),
                                  title: "Elimina Entrata",
                                  buttonText: "Rimuovi",
                                  onPressed: () async {
                                    try{
                                      if (index != null && index >= 0) {
                                        final sectionToRemove = widget.project!.vendorAndProfessionalsUscite![index];

                                        for (final rate in sectionToRemove.rate ?? []) {
                                          // Step 1: If this rate is merged into another, remove its reference from the parent
                                          if (rate.mergedIntoRateId?.isNotEmpty ?? false) {
                                            final parentRate = widget.project!.vendorAndProfessionalsUscite!
                                                .expand((e) => e.rate ?? [])
                                                .firstWhere(
                                                  (r) => r.uniqueId == rate.mergedIntoRateId,
                                              orElse: () => Rate.empty(),
                                            );

                                            if ((parentRate.uniqueId?.isNotEmpty ?? false) &&
                                                (parentRate.margeRateUniqueIDS?.contains(rate.uniqueId) ?? false)) {
                                              parentRate.margeRateUniqueIDS?.remove(rate.uniqueId);
                                              if (parentRate.margeRateUniqueIDS?.isEmpty ?? true) {
                                                parentRate.isMerged = false;
                                              }
                                            }
                                          }

                                          // Step 2: If this rate is a parent, unmerge all child rates
                                          final childRates = widget.project!.vendorAndProfessionalsUscite!
                                              .expand((e) => e.rate ?? [])
                                              .where((r) => r.mergedIntoRateId == rate.uniqueId)
                                              .toList();

                                          for (final child in childRates) {
                                            child.mergedIntoRateId = "";
                                            child.isMerged = false;
                                          }
                                        }

                                        // Step 3: Remove the section
                                        widget.project!.vendorAndProfessionalsUscite?.removeAt(index);
                                        rateList.clear();
                                        _setState(() {});
                                        setState(() {});
                                      } else {
                                        rateList.clear();
                                        _setState(() {});
                                        return false;
                                      }
                                    }catch(e){
                                      print("---------- ERROR While Elimina Entrata ------> ${e.toString()}");
                                    }
                                  },
                                  column: Container(
                                    width: 400,
                                    padding: EdgeInsets.symmetric(vertical: 25),
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      crossAxisAlignment: CrossAxisAlignment.center,
                                      children: [
                                        Center(
                                          child: NarFormLabelWidget(
                                            label:  "Vuoi davvero eliminare questa entrata?" ,
                                            fontSize: 18,
                                            fontWeight: '700',
                                          ),
                                        ),
                                        SizedBox(height: 10,),
                                        Center(
                                          child: NarFormLabelWidget(
                                            label:  "Assicurati prima che non sia già stata pagata!" ,
                                            fontSize: 18,
                                            fontWeight: '500',
                                          ),
                                        ),
                                      ],
                                    ),
                                  )),
                            );
                          });
                        },
                      );
                    });
                    return false;
                  },
                  disableButton: (!isSaveButtonEnableFlag),
                  buttonColor: Theme.of(context).primaryColor.withOpacity(isSaveButtonEnableFlag ? 1.0 : 0.5),
                  isSecondButtonVisible: true,
                  secondButtonColor: Color(0xFFEA3132),
                  secondButtonText: "Elimina",
                  column: Container(
                    height: MediaQuery.of(context).size.height * 0.80,
                    width: 800,
                    child: ListView(
                      shrinkWrap: true,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            NarFormLabelWidget(
                              label: "${newNewarcProjectUscite?.categoryName ?? ""}${newNewarcProjectUscite?.productName?.isNotEmpty ?? false ? " - ${newNewarcProjectUscite?.productName}" : ""}",
                              fontSize: 20,
                              fontWeight: '700',
                              textColor: AppColor.black,
                            ),
                            SizedBox(
                              width: 350,
                              child: CustomTextFormField(
                                isExpanded: false,
                                label: "Inserisci cifra totale",
                                isMoney: true,
                                isShowPrefillMoneyIcon: false,
                                controller: TextEditingController(text: _newNewarcProjectUscite.total != null ? localCurrencyFormatMainWithDecimal.format(_newNewarcProjectUscite.total) .toString() : ""),
                                suffixIcon: Container(
                                  width: 50,
                                  padding: const EdgeInsets.only(right: 5),
                                  child: Align(
                                      alignment: Alignment.centerRight,
                                      child: NarFormLabelWidget(
                                        label: "€",
                                        textColor: AppColor.greyColor,
                                        fontWeight: "500",
                                      )),
                                ),
                                minLines: 1,
                                onChangedCallback: (String value){
                                  if(value.isNotEmpty){
                                    _newNewarcProjectUscite.total = double.tryParse(value.trim().replaceAll(".", "").replaceAll(",", "."));
                                  }
                                },
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 10,),
                        SizedBox(height: 15,),
                        ...rateList.map((rate){
                          return Container(
                            decoration: BoxDecoration(
                                border: Border.all(color: Color(0xFFDBDBDB),width: 1),
                                borderRadius: BorderRadius.circular(7)
                            ),
                            padding: EdgeInsets.all(10),
                            margin: EdgeInsets.only(bottom: 15),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                NarFormLabelWidget(
                                  label: "Rate ${rate.index}",
                                  fontSize: 14,
                                  fontWeight: '700',
                                  textColor: AppColor.black,
                                ),
                                SizedBox(height: 10,),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    //----Per
                                    Column(
                                      children: [
                                        SizedBox(
                                          width: 300,
                                          child: Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              NarFormLabelWidget(
                                                label: "Percentuale",
                                                fontSize: 14,
                                                fontWeight: '600',
                                                textColor: AppColor.greyColor,
                                              ),
                                              NarLinkWidget(
                                                fontSize: 12,
                                                fontWeight: '600',
                                                textColor: Theme.of(context).primaryColor,
                                                text: 'Nuova percentuale',
                                                textDecoration: TextDecoration.underline,
                                                onClick: () {
                                                  showAddPercentagePopup(onPercentageAdded: refreshFetchPercentage);
                                                },
                                              )
                                            ],
                                          ),
                                        ),
                                        SizedBox(height: 10),
                                        SizedBox(
                                          width: 300,
                                          child: FutureBuilder<List>(
                                              future: _fetchPercentageFuture,
                                              builder: (context, snapshot) {
                                                if (snapshot.hasData) {
                                                  return DropdownButtonFormField<String>(
                                                    isExpanded: true,
                                                    initialValue: rate.newarcProjectFixedAssetsPercentageId?.isNotEmpty ?? false ? "${rate.newarcProjectFixedAssetsPercentageId}|${rate.percentage}" : null,
                                                    icon: Padding(
                                                      padding: EdgeInsets.only(right: 8),
                                                      child: SvgPicture.asset(
                                                        'assets/icons/arrow_down.svg',
                                                        width: 12,
                                                        color: Color(0xff7e7e7e),
                                                      ),
                                                    ),
                                                    style: TextStyle(
                                                      overflow: TextOverflow.ellipsis,
                                                      color: Colors.black,
                                                      fontSize: 12.0,
                                                      fontWeight: FontWeight.bold,
                                                      fontStyle: FontStyle.normal,
                                                    ),
                                                    borderRadius: BorderRadius.circular(8),
                                                    decoration: InputDecoration(
                                                      border: OutlineInputBorder(
                                                        borderRadius:
                                                        BorderRadius.all(Radius.circular(8)),
                                                        borderSide: BorderSide(
                                                          color: Color.fromRGBO(227, 227, 227, 1),
                                                          width: 1,
                                                        ),
                                                      ),
                                                      hintStyle: TextStyle(
                                                        color: Colors.grey,
                                                        fontSize: 15.0,
                                                        fontWeight: FontWeight.w800,
                                                        fontStyle: FontStyle.normal,
                                                        letterSpacing: 0,
                                                      ),
                                                      focusedBorder: OutlineInputBorder(
                                                        borderRadius:
                                                        BorderRadius.all(Radius.circular(8)),
                                                        borderSide: BorderSide(
                                                          color: Color.fromRGBO(227, 227, 227, 1),
                                                          width: 1,
                                                        ),
                                                      ),
                                                      contentPadding: EdgeInsets.symmetric(
                                                          horizontal: 12, vertical: 8),
                                                      fillColor: Colors.white,
                                                    ),
                                                    onChanged: (String? value) {
                                                      if (value != null && value.contains('|')) {
                                                        final parts = value.split('|');
                                                        final percentageId = parts[0];
                                                        final per = int.tryParse(parts[1]) ?? 0;

                                                        int rateIndex = rate.index! - 1;

                                                        double baseRate = (per * (_newNewarcProjectUscite.total ?? 0)) / 100;
                                                        double newRate = baseRate;

                                                        int iva = rate.ivaPercentage ?? 0;
                                                        double ivaAmount = (baseRate * iva) / 100;

                                                        _setState(() {
                                                          rateList[rateIndex].percentage = per;
                                                          rateList[rateIndex].newarcProjectFixedAssetsPercentageId = percentageId;
                                                          rateList[rateIndex].rate = newRate;
                                                          rateList[rateIndex].uniqueId = generateRandomString(20);
                                                          rateList[rateIndex].ivaAmount = ivaAmount;

                                                          isSaveButtonEnableFlag = isSaveButtonEnabled();
                                                        });
                                                      }
                                                    },
                                                    dropdownColor: Colors.white,
                                                    items: snapshot.data?.map<DropdownMenuItem<String>>((item) {
                                                      return DropdownMenuItem<String>(
                                                        value: item['value'],
                                                        child: NarFormLabelWidget(
                                                          label: item['label']!,
                                                          textColor: Colors.black,
                                                          fontSize: 14,
                                                          fontWeight: '600',
                                                        ),
                                                      );
                                                    }).toList(),
                                                  );
                                                } else if (snapshot.hasError) {
                                                  return Container(
                                                    width: 30,
                                                    height: 30,
                                                    alignment: Alignment.center,
                                                  );
                                                }
                                                return Center(
                                                  child: CircularProgressIndicator(
                                                    color: Theme.of(context).primaryColor,
                                                  ),
                                                );
                                              }),
                                        ),
                                      ],
                                    ),
                                    // SizedBox(width: 20,),

                                    //----Rate
                                    Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Container(
                                          height: 38,
                                          child: NarFormLabelWidget(
                                            label: "Rata",
                                            fontSize: 14,
                                            fontWeight: '600',
                                            textColor: AppColor.greyColor,
                                          ),
                                          alignment: Alignment.center,
                                        ),
                                        SizedBox(
                                          width: 136,
                                          child: CustomTextFormField(
                                            label: "",
                                            enabled: false,
                                            isShowPrefillMoneyIcon: false,
                                            fillColor: AppColor.disabledGreyColor,
                                            isExpanded: false,
                                            isMoney: true,
                                            validator: (value) {
                                              if (value == '') {
                                                return 'Required!';
                                              }
                                              return null;
                                            },
                                            controller: TextEditingController(text: localCurrencyFormatMainWithDecimal.format(rate.rate)),
                                          ),
                                        ),
                                      ],
                                    ),
                                    // SizedBox(width: 10,),

                                    //---IVA
                                    Row(
                                      children: [
                                        Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Container(
                                              height: 38,
                                              child: NarFormLabelWidget(
                                                label: "Iva",
                                                fontSize: 14,
                                                fontWeight: '600',
                                                textColor: AppColor.greyColor,
                                              ),
                                              alignment: Alignment.center,
                                            ),
                                            SizedBox(
                                              width:136,
                                              child: DropdownButtonFormField<int>(
                                                isExpanded: true,
                                                initialValue: rate.ivaPercentage ?? 0,
                                                icon: Padding(
                                                  padding: EdgeInsets.only(right: 8),
                                                  child: SvgPicture.asset(
                                                    'assets/icons/arrow_down.svg',
                                                    width: 12,
                                                    color: Color(0xff7e7e7e),
                                                  ),
                                                ),
                                                style: TextStyle(
                                                  overflow: TextOverflow.ellipsis,
                                                  color: Colors.black,
                                                  fontSize: 12.0,
                                                  fontWeight: FontWeight.bold,
                                                  fontStyle: FontStyle.normal,
                                                ),
                                                borderRadius: BorderRadius.circular(8),
                                                decoration: InputDecoration(
                                                  border: OutlineInputBorder(
                                                    borderRadius:
                                                    BorderRadius.all(Radius.circular(8)),
                                                    borderSide: BorderSide(
                                                      color: Color.fromRGBO(227, 227, 227, 1),
                                                      width: 1,
                                                    ),
                                                  ),
                                                  hintStyle: TextStyle(
                                                    color: Colors.grey,
                                                    fontSize: 15.0,
                                                    fontWeight: FontWeight.w800,
                                                    fontStyle: FontStyle.normal,
                                                    letterSpacing: 0,
                                                  ),
                                                  focusedBorder: OutlineInputBorder(
                                                    borderRadius:
                                                    BorderRadius.all(Radius.circular(8)),
                                                    borderSide: BorderSide(
                                                      color: Color.fromRGBO(227, 227, 227, 1),
                                                      width: 1,
                                                    ),
                                                  ),
                                                  contentPadding: EdgeInsets.symmetric(
                                                      horizontal: 12, vertical: 8),
                                                  fillColor: Colors.white,
                                                ),
                                                onChanged: (int? value) {
                                                  _setState(() {
                                                    rate.ivaPercentage = value ?? 0;

                                                    int per = rate.percentage ?? 0;
                                                    double baseRate = (per * (_newNewarcProjectUscite.total ?? 0)) / 100;

                                                    int iva = rate.ivaPercentage ?? 0;
                                                    double ivaAmount = (baseRate * iva) / 100;

                                                    rate.ivaAmount = ivaAmount;
                                                  });
                                                },
                                                validator: (value) {
                                                  if (value == null) {
                                                    return "Required!";
                                                  }
                                                  return null;
                                                },
                                                dropdownColor: Colors.white,
                                                items: <int>[0,10,22].map<DropdownMenuItem<int>>((item) {
                                                  return DropdownMenuItem<int>(
                                                    value: item,
                                                    child: NarFormLabelWidget(
                                                      label: item.toString(),
                                                      textColor: Colors.black,
                                                      fontSize: 14,
                                                      fontWeight: '600',
                                                    ),
                                                  );
                                                }).toList(),
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(width: 10,),
                                        IconButtonWidget(
                                          onTap: () {
                                            showDialog(
                                              context: context,
                                              barrierDismissible: true,
                                              builder: (BuildContext context) {
                                                return StatefulBuilder(builder: (context,setStateDialog){
                                                  return Center(
                                                    child: BaseNewarcPopup(
                                                        buttonColor: Color(0xFFE82525),
                                                        title: "Elimina Entrata",
                                                        buttonText: "Rimuovi",
                                                        onPressed: () async {
                                                          try{

                                                            // 1. If this rate is merged into another, remove it from that parent
                                                            if (rate.mergedIntoRateId?.isNotEmpty ?? false) {
                                                              final parentRate = widget.project!.fixedProperty!.newarcProjectFixedAssetsPropertyPagamento!
                                                                  .expand((element) => element.rate ?? [])
                                                                  .firstWhere(
                                                                    (r) => r.uniqueId == rate.mergedIntoRateId,
                                                                orElse: () => Rate.empty(),
                                                              );

                                                              if ((parentRate.uniqueId?.isNotEmpty ?? false) &&
                                                                  (parentRate.margeRateUniqueIDS?.contains(rate.uniqueId) ?? false)) {
                                                                parentRate.margeRateUniqueIDS?.remove(rate.uniqueId);

                                                                if (parentRate.margeRateUniqueIDS?.isEmpty ?? true) {
                                                                  parentRate.isMerged = false;
                                                                }
                                                              }
                                                            }

                                                            // 2. If this rate is a parent (others are merged into it), unmerge them
                                                            final childRates = widget.project!.fixedProperty!.newarcProjectFixedAssetsPropertyPagamento!
                                                                .expand((element) => element.rate ?? [])
                                                                .where((r){
                                                              return r.mergedIntoRateId == rate.uniqueId;
                                                            }).toList();

                                                            for (final child in childRates) {
                                                              child.mergedIntoRateId = "";
                                                              child.isMerged = false;
                                                            }

                                                            // 3. Remove the rate itself
                                                            rateList.remove(rate);

                                                            // 4. Reassign indexes
                                                            for (int i = 0; i < rateList.length; i++) {
                                                              rateList[i].index = i + 1;
                                                            }

                                                            // 5. Refresh UI
                                                            _setState(() {});
                                                          }catch(e){
                                                            print("---------- ERROR While Elimina Entrata------> ${e.toString()}");
                                                          }
                                                        },
                                                        column: Container(
                                                          width: 400,
                                                          padding: EdgeInsets.symmetric(vertical: 25),
                                                          child: Column(
                                                            mainAxisAlignment: MainAxisAlignment.center,
                                                            crossAxisAlignment: CrossAxisAlignment.center,
                                                            children: [
                                                              Center(
                                                                child: NarFormLabelWidget(
                                                                  label:  "Vuoi davvero eliminare questa entrata?" ,
                                                                  fontSize: 18,
                                                                  fontWeight: '700',
                                                                ),
                                                              ),
                                                              SizedBox(height: 10,),
                                                              Center(
                                                                child: NarFormLabelWidget(
                                                                  label:  "Assicurati prima che non sia già stata pagata!" ,
                                                                  fontSize: 18,
                                                                  fontWeight: '500',
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        )),
                                                  );
                                                });
                                              },
                                            );
                                          },
                                          isSvgIcon: true,
                                          backgroundColor: Colors.transparent,
                                          icon: 'assets/icons/delete.svg',
                                          iconColor: AppColor.greyColor,
                                        ),
                                      ],
                                    )
                                  ],
                                ),
                                SizedBox(height: 15,),
                                CustomTextFormField(
                                  isExpanded: false,
                                  minLines: 1,
                                  label: "Descrizione",
                                  controller: rate.descriptionController,
                                  hintText: "Es. Posa impianti completata",
                                  onChangedCallback: (value){
                                    if(value.toString().trim().isNotEmpty){
                                      _setState(() {
                                        rate.description = value;
                                        isSaveButtonEnableFlag = isSaveButtonEnabled();
                                      });
                                    }
                                  },
                                ),
                                SizedBox(height: 15,),
                                Row(
                                  children: [
                                    SizedBox(
                                      width: 300,
                                      child: Row(
                                        children: [
                                          Switch(
                                            value: rate.isMerged!,
                                            activeThumbColor: AppColor.white,
                                            activeTrackColor: Theme.of(context).primaryColor,
                                            onChanged: (bool value) async {
                                              _setState(() {
                                                rate.isMerged = !rate.isMerged!;
                                                isSaveButtonEnableFlag = isSaveButtonEnabled();
                                              });
                                            },
                                          ),
                                          SizedBox(
                                            width: 10,
                                          ),
                                          NarFormLabelWidget(
                                            label: 'Accorpa a esistente',
                                            fontSize: 12,
                                            fontWeight: '600',
                                            textColor: AppColor.black,
                                          ),
                                        ],
                                      ),
                                    ),
                                    //----- RATA DROPDOWN
                                    rate.isMerged! ?
                                    Column(
                                      children: [
                                        SizedBox(
                                          width: 300,
                                          child: Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              NarFormLabelWidget(
                                                label: "Seleziona esistente",
                                                fontSize: 14,
                                                fontWeight: '600',
                                                textColor: AppColor.greyColor,
                                              ),
                                            ],
                                          ),
                                        ),
                                        SizedBox(height: 5),
                                        SizedBox(
                                          width: 300,
                                          height: 51,
                                          child: FutureBuilder<List>(
                                              future: fetchRate(currentUID: rate.uniqueId ?? "",isMaterial: isMaterial),
                                              builder: (context, snapshot) {
                                                if (snapshot.hasData) {
                                                  return DropdownButtonFormField<String>(
                                                    key: ValueKey(rate.index),
                                                    isExpanded: true,
                                                    initialValue: rate.mergedIntoRateId?.isNotEmpty ?? false
                                                        ? rate.mergedIntoRateId
                                                        : null,
                                                    icon: Padding(
                                                      padding: EdgeInsets.only(right: 8),
                                                      child: SvgPicture.asset(
                                                        'assets/icons/arrow_down.svg',
                                                        width: 12,
                                                        color: Color(0xff7e7e7e),
                                                      ),
                                                    ),
                                                    style: TextStyle(
                                                      overflow: TextOverflow.ellipsis,
                                                      color: Colors.black,
                                                      fontSize: 12.0,
                                                      fontWeight: FontWeight.bold,
                                                      fontStyle: FontStyle.normal,
                                                    ),
                                                    borderRadius: BorderRadius.circular(8),
                                                    decoration: InputDecoration(
                                                      border: OutlineInputBorder(
                                                        borderRadius:
                                                        BorderRadius.all(Radius.circular(8)),
                                                        borderSide: BorderSide(
                                                          color: Color.fromRGBO(227, 227, 227, 1),
                                                          width: 1,
                                                        ),
                                                      ),
                                                      hintStyle: TextStyle(
                                                        color: Colors.grey,
                                                        fontSize: 15.0,
                                                        fontWeight: FontWeight.w800,
                                                        fontStyle: FontStyle.normal,
                                                        letterSpacing: 0,
                                                      ),
                                                      focusedBorder: OutlineInputBorder(
                                                        borderRadius:
                                                        BorderRadius.all(Radius.circular(8)),
                                                        borderSide: BorderSide(
                                                          color: Color.fromRGBO(227, 227, 227, 1),
                                                          width: 1,
                                                        ),
                                                      ),
                                                      contentPadding: EdgeInsets.symmetric(
                                                          horizontal: 12, vertical: 8),
                                                      fillColor: Colors.white,
                                                    ),
                                                    onChanged: (String? value) {
                                                      if (value?.isNotEmpty ?? false) {
                                                        _setState(() {
                                                          rate.mergedIntoRateId = value;
                                                          isSaveButtonEnableFlag = isSaveButtonEnabled();
                                                        });
                                                      }
                                                    },
                                                    dropdownColor: Colors.white,
                                                    items: snapshot.data
                                                        ?.map<DropdownMenuItem<String>>((item) {
                                                      return DropdownMenuItem<String>(
                                                        value: item['value'],
                                                        child: NarFormLabelWidget(
                                                          label: item['label']!,
                                                          textColor: Colors.black,
                                                          fontSize: 14,
                                                          fontWeight: '600',
                                                        ),
                                                      );
                                                    }).toList(),
                                                  );
                                                } else if (snapshot.hasError) {
                                                  return Container(
                                                    width: 30,
                                                    height: 30,
                                                    alignment: Alignment.center,
                                                  );
                                                }
                                                return Center(
                                                  child: CircularProgressIndicator(
                                                    color: Theme.of(context).primaryColor,
                                                  ),
                                                );
                                              }),
                                        ),
                                      ],
                                    ) : SizedBox.shrink(),
                                  ],
                                ),
                              ],),
                          );
                        }),
                        Align(
                          alignment: Alignment.bottomCenter,
                          child: NarLinkWidget(
                            fontSize: 14,
                            fontWeight: '600',
                            textColor: Theme.of(context).primaryColor,
                            text: '+ Aggiungi rata',
                            textDecoration: TextDecoration.underline,
                            onClick: () {
                              int newIndex = rateList.length + 1;
                              Rate newRate = Rate.empty();
                              newRate.index = newIndex;
                              newRate.isPaid = false;
                              newRate.paidDate = null;
                              rateList.add(newRate);
                              _setState(() {});
                            },
                          ),
                        ),
                        SizedBox(height: 20),
                        isMaterial ? SizedBox() :
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            NarFormLabelWidget(
                              label: 'Opzioni',
                              fontSize: 16,
                              fontWeight: 'bold',
                              textColor: Colors.black,
                            ),
                            SizedBox(height: 10),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.all(0),
                                  child: Switch(
                                    value: _newNewarcProjectUscite.contractor ?? false,
                                    activeThumbColor: Theme.of(context).primaryColor,

                                    onChanged: (bool value) async {
                                      _setState(() {
                                        isSaveButtonEnableFlag = isSaveButtonEnabled();
                                        _newNewarcProjectUscite.contractor = value;

                                        /* If penalty has been disabled then empty the penalty amount */
                                        if (value == false) {
                                          _newNewarcProjectUscite.hasPenalty = false;
                                          resetPenalty();
                                        }
                                      });
                                    },
                                  ),
                                ),
                                SizedBox(
                                  width: 5,
                                ),
                                NarFormLabelWidget(
                                    label: 'Appaltatore',
                                    fontSize: 14,
                                    textColor: Colors.black)
                              ],
                            ),
                            _newNewarcProjectUscite.contractor ?? false
                                ? Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Expanded(
                                  child: Row(
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      SizedBox(
                                        width: 20,
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.all(0),
                                        child: Switch(
                                          value: _newNewarcProjectUscite.hasPenalty ?? false,
                                          activeThumbColor:
                                          Theme.of(context).primaryColor,
                                          onChanged: (bool value) async {
                                            _setState(() {
                                              isSaveButtonEnableFlag = isSaveButtonEnabled();
                                              _newNewarcProjectUscite.hasPenalty = value;

                                              /* If penalty has been disabled then empty the penalty amount */
                                              if (value == false) {
                                                resetPenalty();
                                              }
                                            });
                                          },
                                        ),
                                      ),
                                      SizedBox(
                                        width: 5,
                                      ),
                                      NarFormLabelWidget(
                                          label: 'Penale',
                                          fontSize: 14,
                                          textColor: Colors.black)
                                    ],
                                  ),
                                ),
                                _newNewarcProjectUscite.hasPenalty ?? false
                                    ? CustomTextFormField(
                                  label: "Penale concordata",
                                  controller: contPenalty,
                                  suffixIcon: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment:
                                    MainAxisAlignment.center,
                                    children: [
                                      NarFormLabelWidget(
                                        label: '€/giorno ',
                                        fontSize: 14,
                                        textColor: Color.fromRGBO(
                                            181, 181, 181, 1),
                                      ),
                                    ],
                                  ),
                                  validator: (value) {
                                    if (value == '') {
                                      return 'Required!';
                                    }
                                    if (isNumber(value) == false) {
                                      return 'Not a valid value';
                                    }

                                    return null;
                                  },
                                )
                                    : SizedBox(
                                  height: 0,
                                ),
                                SizedBox(width: 7),
                                _newNewarcProjectUscite.hasPenalty ?? false
                                    ? CustomTextFormField(
                                  label: "Inizio lavori concordata",
                                  controller: contJobStart,
                                  suffixIcon: Container(
                                    height: 17,
                                    width: 17,
                                    // margin: EdgeInsets.only(left: 2),
                                    decoration: BoxDecoration(
                                      color: Colors.transparent,
                                      borderRadius:
                                      BorderRadius.circular(4),
                                    ),
                                    padding:
                                    EdgeInsets.symmetric(horizontal: 10),
                                    child: Image.asset(
                                      'assets/icons/calendar.png',
                                      color: Color(0xff7B7B7B),
                                    ),
                                  ),
                                  // validationMessage: 'Required!',
                                  validator: (value) {
                                    if (value == '') {
                                      return 'Required!';
                                    }

                                    return null;
                                  },
                                  onTap: () async {
                                    isSaveButtonEnableFlag = isSaveButtonEnabled();
                                    DateTime? pickedDate =
                                    await showDatePicker(
                                        context: context,
                                        initialDate: contJobStart.text == ''
                                            ? DateTime.now()
                                            : DateTime.tryParse(
                                            formatDateForParsing(
                                                contJobStart
                                                    .text))!,
                                        firstDate: DateTime(1950),
                                        lastDate: DateTime(2300));

                                    if (pickedDate != null) {
                                      jobStart = pickedDate.millisecondsSinceEpoch;
                                      String formattedDate =
                                      DateFormat('dd/MM/yyyy')
                                          .format(pickedDate);
                                      _setState(() {
                                        contJobStart.text = formattedDate; //set output date to TextField value.
                                      });
                                    } else {}
                                  },
                                )
                                    : SizedBox(height: 0),
                                SizedBox(width: 7),
                                _newNewarcProjectUscite.hasPenalty ?? false
                                    ? CustomTextFormField(
                                  label: "Fine lavori concordata",
                                  controller: contJobEnd,
                                  suffixIcon: Container(
                                    height: 17,
                                    width: 17,
                                    // margin: EdgeInsets.only(left: 2),
                                    decoration: BoxDecoration(
                                      color: Colors.transparent,
                                      borderRadius:
                                      BorderRadius.circular(4),
                                    ),
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 10),
                                    child: Image.asset(
                                      'assets/icons/calendar.png',
                                      color: Color(0xff7B7B7B),
                                    ),
                                  ),
                                  // validationMessage: 'Required!',
                                  validator: (value) {
                                    if (value == '') {
                                      return 'Required!';
                                    }

                                    return null;
                                  },
                                  onTap: () async {
                                    isSaveButtonEnableFlag = isSaveButtonEnabled();
                                    DateTime? pickedDate =
                                    await showDatePicker(
                                        context: context,
                                        initialDate: contJobEnd.text == ''
                                            ? DateTime.now()
                                            : DateTime.tryParse(
                                            formatDateForParsing(contJobEnd.text))!,
                                        firstDate: DateTime(1950),
                                        lastDate: DateTime(2300));

                                    if (pickedDate != null) {
                                      jobEnd = pickedDate.millisecondsSinceEpoch;

                                      String formattedDate =
                                      DateFormat('dd/MM/yyyy')
                                          .format(pickedDate);
                                      _setState(() {
                                        contJobEnd.text =
                                            formattedDate; //set output date to TextField value.
                                      });
                                    } else {}
                                  },
                                )
                                    : SizedBox(height: 85),
                              ],
                            )
                                : SizedBox(height: 0),
                          ],
                        )
                      ],
                    ),
                  ),
                ));
          });
        });
  }

  resetPenalty() {
    contPenalty.text = '';
    jobStart = DateTime.now().millisecondsSinceEpoch;
    jobEnd = DateTime.now().millisecondsSinceEpoch;

    DateTime startDate = DateTime.fromMillisecondsSinceEpoch(jobStart!);
    DateTime endDate = DateTime.fromMillisecondsSinceEpoch(jobEnd!);
    contJobStart.text = (startDate.day > 9
        ? startDate.day.toString()
        : '0' + startDate.day.toString()) +
        '/' +
        (startDate.month > 9
            ? startDate.month.toString()
            : '0' + startDate.month.toString()) +
        '/' +
        startDate.year.toString();
    contJobEnd.text = (endDate.day > 9
        ? endDate.day.toString()
        : '0' + endDate.day.toString()) +
        '/' +
        (endDate.month > 9
            ? endDate.month.toString()
            : '0' + endDate.month.toString()) +
        '/' +
        endDate.year.toString();
  }

  formatDateForParsing(String dateString) {
    List splittedDate = dateString.split('/');
    return splittedDate[2] + '-' + splittedDate[1] + '-' + splittedDate[0];
  }

  Future<void> showAddPercentagePopup({required VoidCallback onPercentageAdded}) async {
    TextEditingController perController = TextEditingController();
    List<String> formErrorMessage = [];
    await showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (__context, _setState) {
            return Center(
                child: BaseNewarcPopup(
                  title: "Aggiungi sottocategoria",
                  buttonText: "Aggiungi",
                  formErrorMessage: formErrorMessage,
                  onPressed: () async {
                    _setState((){
                      formErrorMessage.clear();
                      formErrorMessage.add("Salvataggio in corso...");
                    });
                    try {
                      User? user = FirebaseAuth.instance.currentUser;
                      if (user == null) return;

                      final String uid = user.uid;
                      final int per = int.tryParse(perController.text.trim()) ?? 0;
                      final collectionRef = FirebaseFirestore.instance
                          .collection(appConfig.COLLECT_NEWARCPROJECTFIXEDASSETSPROPERTYPERCENTAGE);

                      await collectionRef.add({
                        "percentage": per,
                        "insertTimestamp": DateTime.now().millisecondsSinceEpoch,
                        "uid": uid,
                      });

                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Salvato");
                      });
                      onPercentageAdded();
                    } catch (e) {
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Si è verificato un errore.");
                      });
                      log("Error While Adding percentage -----> ${e.toString()}");
                    }
                  },
                  buttonColor: Theme.of(context).primaryColor,
                  column: Container(
                    width: 400,
                    child: CustomTextFormField(
                      isExpanded: false,
                      isNumber: true,
                      isPercentage: true,
                      label: "Digita il nuovo percentuale",
                      validator: (value) {
                        if (value == '') {
                          return 'Required!';
                        }
                        return null;
                      },
                      controller: perController,
                    ),
                  ),
                ));
          });
        });
  }

  Future<List> fetchPercentage() async {
    try {
      List<NewarcProjectFixedAssetsPropertyPercentage> _newarcProjectFixedAssetsPropertyPercentage = [];
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance
          .collection(appConfig.COLLECT_NEWARCPROJECTFIXEDASSETSPROPERTYPERCENTAGE);

      collectionSnapshot = await collectionSnapshotQuery
          .orderBy('insertTimestamp', descending: true)
          .get();

      if (collectionSnapshot.docs.length > 0) {
        for (var element in collectionSnapshot.docs) {
          try {
            NewarcProjectFixedAssetsPropertyPercentage _tmp =
            NewarcProjectFixedAssetsPropertyPercentage.fromDocument(
                element.data(), element.id);
            _newarcProjectFixedAssetsPropertyPercentage.add(_tmp);
          } catch (e) {
            print("ERROR Percentage ---> $e");
          }
        }
      }


      if (_newarcProjectFixedAssetsPropertyPercentage.length > 0) {
        return _newarcProjectFixedAssetsPropertyPercentage.map((e) {
          return {'value': "${e.firebaseId}|${e.percentage}", 'label': "${e.percentage}%"};
        }).toList();
      } else {
        return [
          {'value': '', 'label': ''}
        ];
      }
    } catch (e, s) {
      print('error: $e');
      print(s);
      return [
        {'value': '', 'label': ''}
      ];
    }
  }

  Future<List> fetchRate({required String currentUID,required bool isMaterial}) async {
    try {
      List<Rate> _rates = [];

      if(isMaterial){
        if ((widget.project?.materialUscite?.length ?? 0) > 0) {
          for (NewarcProjectPagamento element in widget.project!.materialUscite!) {
            for (Rate rate in element.rate!) {
              if(!(rate.isMerged ?? false) && rate.uniqueId != currentUID){
                _rates.add(rate);
              }
            }
          }
        }
      }else{
        if ((widget.project?.vendorAndProfessionalsUscite?.length ?? 0) > 0) {
          for (NewarcProjectPagamento element in widget.project!.vendorAndProfessionalsUscite!) {
            element.categoryName = _initSuppliers
                .where((val) => val['value'] == element.vendorUserId)
                .first['label'];
            for (Rate rate in element.rate!) {
              if(!(rate.isMerged ?? false) && rate.uniqueId != currentUID){
                rate.category = _initSuppliers
                    .where((val) => val['value'] == element.vendorUserId)
                    .first['label'];
                _rates.add(rate);
              }
            }
          }
        }
      }




      if (_rates.isNotEmpty) {
        return _rates.map((e) {
          return {
            'value': e.uniqueId,
            'label': "${e.category} - RATA ${e.index} - ${e.percentage ?? 0}%"
          };
        }).toList();
      } else {
        return [{'value': '', 'label': ''}];
      }
    } catch (e, s) {
      print('error: $e');
      print(s);
      return [{'value': '', 'label': ''}];
    }
  }

  Future<void> showEditBudgetPopup({required isMaterial}) async {
    TextEditingController budgetController = TextEditingController(text: isMaterial ? budgetMaterial.toString() :  budget.toString());
    List<String> formErrorMessage = [];
    await showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (__context, _setState) {
            return Center(
                child: BaseNewarcPopup(
                  title: "Inserisci Budget",
                  buttonText: "Salva",
                  formErrorMessage: formErrorMessage,
                  onPressed: () async {
                    _setState((){
                      formErrorMessage.clear();
                      formErrorMessage.add("Salvataggio in corso...");
                    });
                    try{
                      double priceOnchange = double.tryParse(budgetController.text.trim().replaceAll('.', '').replaceAll(',', '.').toString()) ?? 0.0;

                      if(isMaterial){
                        double spendPercent = (_calculateTotal(widget.project?.materialUscite ?? []) / priceOnchange) * 100;
                        if (spendPercent <= 25) {
                          spendColorMaterial = Color(0xff4E9A7A);
                        } else if (spendPercent > 25 && spendPercent <= 100) {
                          spendColorMaterial = Color(0xffFFC702);
                        } else if (spendPercent > 100) {
                          spendColorMaterial = Color(0xffE82525);
                        }
                        setState(() {
                          budgetMaterial = priceOnchange;
                          widget.project?.budgetGastisciMaterialiRistrutturazione = priceOnchange;
                        });
                        await FirebaseFirestore.instance
                            .collection(appConfig.COLLECT_NEWARC_PROJECTS)
                            .doc(widget.project!.id)
                            .update({"budgetGastisciMaterialiRistrutturazione" : priceOnchange});
                      }else{
                        double spendPercent = (_calculateTotal(widget.project?.vendorAndProfessionalsUscite ?? []) / priceOnchange) * 100;
                        if (spendPercent <= 25) {
                          spendColor = Color(0xff4E9A7A);
                        } else if (spendPercent > 25 && spendPercent <= 100) {
                          spendColor = Color(0xffFFC702);
                        } else if (spendPercent > 100) {
                          spendColor = Color(0xffE82525);
                        }
                        setState(() {
                          budget = priceOnchange;
                          widget.project?.budgetDitteProfRistrutturazione = priceOnchange;
                        });
                        await FirebaseFirestore.instance
                            .collection(appConfig.COLLECT_NEWARC_PROJECTS)
                            .doc(widget.project!.id)
                            .update({"budgetDitteProfRistrutturazione" : priceOnchange});
                      }

                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Salvato");
                      });
                    }catch(e){
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Si è verificato un errore.");
                      });
                      log("Error while editing budget ----> ${e.toString()}");
                    }
                  },
                  buttonColor: Theme.of(context).primaryColor,
                  column: Container(
                    width: 400,
                    child: CustomTextFormField(
                      isExpanded: false,
                      isMoney: true,
                      label: "Budget",
                      controller: budgetController,
                      validator: (value) {
                        if (value == '') {
                          return 'Required!';
                        }
                        return null;
                      },
                    ),
                  ),
                ));
          });
        });
  }
}
